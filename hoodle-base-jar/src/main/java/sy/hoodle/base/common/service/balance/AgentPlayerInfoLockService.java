package sy.hoodle.base.common.service.balance;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import sy.hoodle.base.common.dao.AgentPlayerInfoDao;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.function.FiveConsumer;
import sy.hoodle.base.common.function.FourConsumer;
import sy.hoodle.base.common.redis.RedisDelService;
import sy.hoodle.base.common.service.balance.vo.BalanceChangeDirection;
import sy.hoodle.base.common.service.balance.vo.PlayerBalanceUpdateParams;
import sy.hoodle.base.common.util.ProBaseBigDecimalUtil;
import sy.hoodle.base.common.util.ProBaseCollectionUtil;
import sy.hoodle.base.common.util.ProBaseJsonUtil;
import sy.hoodle.base.common.util.ProBaseMapUtil;
import sy.hoodle.base.common.util.ProBaseTimeUtil;
import sy.hoodle.base.common.util.ProJPAQueryFactoryUtil;
import sy.hoodle.tool.redis.utils.ProRedisLockUtil;
import sy.hoodle.tool.redis.utils.RedisLockResult;

/**
 * AgentPlayerInfo 锁定账变接口入口
 * 
 * <AUTHOR>
 * @date 2025-07-28 11:11:54
 */
@Slf4j
@Service
public class AgentPlayerInfoLockService {

	@Autowired
	private AgentPlayerInfoLockInternalService agentPlayerInfoLockInternalService;

	@Autowired
	private AgentPlayerInfoDao agentPlayerInfoDao;

	@Autowired
	private ProJPAQueryFactoryUtil proJPAQueryFactoryUtil;

	@Autowired
	private ProRedisLockUtil redisLockUtil;

	private static String LOCK_PREFIX = "AgentPlayerInfoAccountChange:";

	/**
	 * 主要维护玩家可用余额
	 * 
	 * @param playerId
	 * @param incrPlayerAvailableBalance
	 * @param con
	 */
	public void incrPlayerAvailableBalance(String createBy, int billType, String remark, long playerId,
			BigDecimal incrPlayerAvailableBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		incr(createBy, billType, remark, playerId, incrPlayerAvailableBalance, BigDecimal.ZERO, fiv);
	}

	/**
	 * 主要维护家冻结余额
	 * 
	 * @param playerId
	 * @param incrPlayerBlockedBalance
	 * @param con
	 */
	public void incrPlayerBlockedBalance(String createBy, int billType, String remark,
			long playerId,
			BigDecimal incrPlayerBlockedBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		incr(createBy, billType, remark, playerId, BigDecimal.ZERO, incrPlayerBlockedBalance, fiv);
	}

	/**
	 * 拒绝在事务中执行,因为是后修改,添加的代码,这里的作用是,</br>
	 * 明确提示给调用方,账变不能在外部事务中执行,</br>
	 * 避免账变成功了,外部又回滚,干扰其他序列化执行的代码</br>
	 * 
	 * 增加余额
	 * 
	 * @param playerId         用户id
	 * @param availableBalance 玩家可用余额,必须是正数
	 * @param blockedBalance   玩家冻结余额
	 * @param con              提供给外部调用的匿名函数,这个里面的代码,将会随事务一起提交
	 */
	public void incr(String createBy, int billType, String remark, long playerId, BigDecimal incrPlayerAvailableBalance,
			BigDecimal incrPlayerBlockedBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		// 代码检查,并阻止被嵌套在事务中
		agentPlayerInfoLockInternalService.incr_check(playerId, incrPlayerAvailableBalance, incrPlayerBlockedBalance);
		// 账变锁定
		redisLockUtil.around(LOCK_PREFIX + playerId, ProBaseTimeUtil.millisecond._1_MINUTE,
				ProBaseTimeUtil.millisecond._3_MINUTE, () -> {
			// 查询出代理玩家信息
			AgentPlayerInfo agentPlayerInfo = agentPlayerInfoDao.getByPlayerId(playerId);
			if (agentPlayerInfo == null) {
				log.error("找不到用户:{}", playerId);
				throw new RuntimeException("找不到用户");
			}
					agentPlayerInfoLockInternalService.accountChange(createBy, billType, remark, agentPlayerInfo,
							incrPlayerAvailableBalance, incrPlayerBlockedBalance, fiv);
		});
		// 删除玩家信息
		agentPlayerInfoLockInternalService.delAgentPlayerInfo(playerId);
	}

	/**
	 * 主要维护玩家冻结余额
	 * 
	 * @param playerId
	 * @param decrPlayerAvailableBalance
	 * @param con
	 */
	public void decrPlayerBlockedBalance(String createBy, int billType, String remark, Long playerId,
			BigDecimal decrPlayerBlockedBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		decr(createBy, billType, remark, playerId, BigDecimal.ZERO, decrPlayerBlockedBalance, fiv);
	}

	/**
	 * 主要维护玩家可用余额
	 * 
	 * @param playerId
	 * @param decrPlayerAvailableBalance
	 * @param con
	 */
	public void decrPlayerAvailableBalance(String createBy, int billType, String remark, Long playerId,
			BigDecimal decrPlayerAvailableBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		decr(createBy, billType, remark, playerId, decrPlayerAvailableBalance, BigDecimal.ZERO, fiv);
	}

	/**
	 * 拒绝在事务中执行,因为是后修改,添加的代码,这里的作用是,</br>
	 * 明确提示给调用方,账变不能在外部事务中执行,</br>
	 * 避免账变成功了,外部又回滚,干扰其他序列化执行的代码</br>
	 * 
	 * 扣减余额
	 * 
	 * @param playerId         用户id
	 * @param availableBalance 玩家可用余额,必须是负数
	 * @param blockedBalance   玩家冻结余额
	 * @param con              提供给外部调用的匿名函数,这个里面的代码,将会随事务一起提交
	 */
	public void decr(String createBy, int billType, String remark, Long playerId, BigDecimal decrPlayerAvailableBalance,
			BigDecimal decrPlayerBlockedBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		// 账变锁定
		redisLockUtil.around(LOCK_PREFIX + playerId, ProBaseTimeUtil.millisecond._1_MINUTE,
				ProBaseTimeUtil.millisecond._3_MINUTE, () -> {
			// 查询出代理玩家信息
			AgentPlayerInfo agentPlayerInfo = agentPlayerInfoDao.getByPlayerId(playerId);
			if (agentPlayerInfo == null) {
				log.error("找不到用户:{}", playerId);
				throw new RuntimeException("找不到用户");
			}
			// 代码检查,并阻止被嵌套在事务中
			agentPlayerInfoLockInternalService.decr_check(playerId, agentPlayerInfo.getPlayerAvailableBalance(),
					decrPlayerAvailableBalance, decrPlayerBlockedBalance);
					agentPlayerInfoLockInternalService.accountChange(createBy, billType, remark, agentPlayerInfo,
					decrPlayerAvailableBalance,
							decrPlayerBlockedBalance, fiv);
		});
		// 删除玩家信息
		agentPlayerInfoLockInternalService.delAgentPlayerInfo(playerId);
	}


    /**
	 * 拒绝在事务中执行,因为是后修改,添加的代码,这里的作用是,</br>
	 * 明确提示给调用方,账变不能在外部事务中执行,</br>
	 * 避免账变成功了,外部又回滚,干扰其他序列化执行的代码</br>
	 * 更新玩家余额的核心方法。 </br>
	 * 该方法接收一个 PlayerBalanceUpdateParams 对象，解构参数并根据方向执行相应的余额更新逻辑。
	 *
	 * 批量账变
	 *
	 * @param params 包含玩家ID、变动金额、变动方向和回调函数的参数对象。
	 */
	public void updatePlayerBalance(List<PlayerBalanceUpdateParams> tempParams) {
		if (ProBaseCollectionUtil.isEmpty(tempParams)) {
			log.error("发现异常,账变接口账变参数为空");
			return;
		}
		Set<Long> playerIdSet;
		// 参数校验
		for (PlayerBalanceUpdateParams playerBalanceUpdateParams : tempParams) {
			Long playerId = playerBalanceUpdateParams.getPlayerId();
			BigDecimal changePlayerAvailableBalance = playerBalanceUpdateParams.getChangePlayerAvailableBalance();
			BigDecimal changePlayerBlockedBalance = playerBalanceUpdateParams.getChangePlayerBlockedBalance();
			BalanceChangeDirection direction = playerBalanceUpdateParams.getDirection();
			switch (direction) {
			case INCR:
				/** 表示资金增加 */
				// 代码检查,并阻止被嵌套在事务中
				agentPlayerInfoLockInternalService.incr_check(playerId, changePlayerAvailableBalance,
						changePlayerBlockedBalance);
				break;
			case DECR:
				/** 表示资金减少 */
				// 代码检查,并阻止被嵌套在事务中,扣款初始化校验时先,设置为一个超大值,避免余额检查,后续单独进行余额判断
				agentPlayerInfoLockInternalService.decr_check(playerId, BigDecimal.valueOf(Long.MAX_VALUE),
						changePlayerAvailableBalance, changePlayerBlockedBalance);
				break;
			}
		}
		// 拷贝,避免是不可修改list,对params进行排序,入账在前,出账在后
		List<PlayerBalanceUpdateParams> params = new ArrayList<>(tempParams);
		params.sort((o1, o2) -> {
			BalanceChangeDirection direction = o1.getDirection();
			return BalanceChangeDirection.INCR.equals(direction) ? -1 : 1;
		});
		HashMap<String, RedisLockResult> lockMap = new HashMap<>();
		try {
			// 批量加锁
			for (PlayerBalanceUpdateParams playerBalanceUpdateParams : params) {
				Long playerId = playerBalanceUpdateParams.getPlayerId();
				String lockKey = LOCK_PREFIX + playerId;
				if (!lockMap.containsKey(lockKey)) {
					RedisLockResult redisLockResult = redisLockUtil.needUnlockTryLock(lockKey,
							ProBaseTimeUtil.millisecond._1_MINUTE, ProBaseTimeUtil.millisecond._3_MINUTE);
					if (redisLockResult.isTryLock()) {
						lockMap.put(lockKey, redisLockResult);
					} else {
						throw new RuntimeException("获取锁失败");
					}
				}
			}
			
			// 提取用户id,找出所有用户,以进行存在校验
			playerIdSet = ProBaseCollectionUtil.extractSet(params, PlayerBalanceUpdateParams::getPlayerId);
			List<AgentPlayerInfo> agentPlayerInfoList = agentPlayerInfoDao.getAllByIds(playerIdSet);
			if (playerIdSet.size() != agentPlayerInfoList.size()) {
				log.error("找不到用户:{}", playerIdSet);
				throw new RuntimeException("找不到用户");
			}
			
			// 扣款余额判断
			Map<Long, List<BigDecimal>> changeBalanceMap = ProBaseMapUtil.classification(params,
					PlayerBalanceUpdateParams::getPlayerId, PlayerBalanceUpdateParams::getChangePlayerAvailableBalance);
			for (AgentPlayerInfo agentPlayerInfo : agentPlayerInfoList) {
				Long playerId = agentPlayerInfo.getPlayerId();
				// 用户余额
				BigDecimal playerAvailableBalance = agentPlayerInfo.getPlayerAvailableBalance();
				// 汇总用户账变
				List<BigDecimal> changeBalanceList = changeBalanceMap.get(playerId);
				BigDecimal totalChangeBalance = ProBaseCollectionUtil.sum(changeBalanceList);
				if (ProBaseBigDecimalUtil.oneMaxOneMin(BigDecimal.ZERO,
						playerAvailableBalance.add(totalChangeBalance))) {
					throw new RuntimeException("用户账变扣款金额大于余额");
				}
			}

			Map<Long, AgentPlayerInfo> agentPlayerInfoMap = ProBaseMapUtil.signClassification(agentPlayerInfoList,
					AgentPlayerInfo::getPlayerId);
			
			// 开启事务,进行批量更新
			proJPAQueryFactoryUtil.txUpdate(() -> {
				for (PlayerBalanceUpdateParams playerBalanceUpdateParams : params) {
					int billType = playerBalanceUpdateParams.getBillType();
					String createBy = playerBalanceUpdateParams.getCreateBy();
					String remark = playerBalanceUpdateParams.getRemark();
					Long playerId = playerBalanceUpdateParams.getPlayerId();
					BigDecimal changePlayerAvailableBalance = playerBalanceUpdateParams
							.getChangePlayerAvailableBalance();
					BigDecimal changePlayerBlockedBalance = playerBalanceUpdateParams.getChangePlayerBlockedBalance();
					FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv = playerBalanceUpdateParams
							.getFiv();
					// 查询出代理玩家信息
					AgentPlayerInfo agentPlayerInfo = agentPlayerInfoMap.get(playerId);
					agentPlayerInfoLockInternalService.accountChange(createBy, billType, remark, agentPlayerInfo,
							changePlayerAvailableBalance, changePlayerBlockedBalance, fiv);
					// 修改 agentPlayerInfo 的 playerAvailableBalance playerBlockedBalance
					agentPlayerInfo.setPlayerAvailableBalance(
							agentPlayerInfo.getPlayerAvailableBalance().add(changePlayerAvailableBalance));
					agentPlayerInfo.setPlayerBlockedBalance(
							agentPlayerInfo.getPlayerBlockedBalance().add(changePlayerBlockedBalance));
				}
			});
			
		} catch (Exception e) {
			log.error("批量账变方法发现异常:{}", ProBaseJsonUtil.toStr(params), e);
			throw e;
		} finally {
			lockMap.forEach((lockKey, redisLockResult) -> {
				if (redisLockResult != null && redisLockResult.isTryLock()) {
					redisLockUtil.unlock(lockKey, redisLockResult.getLockVal());
				}
			});
		}
		for (Long playerId : playerIdSet) {
			// 删除玩家信息
			agentPlayerInfoLockInternalService.delAgentPlayerInfo(playerId);
		}
	}

}
