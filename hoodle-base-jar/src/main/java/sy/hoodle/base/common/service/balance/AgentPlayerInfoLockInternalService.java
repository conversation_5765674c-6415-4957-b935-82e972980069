package sy.hoodle.base.common.service.balance;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import sy.hoodle.base.common.dao.AgentPlayerAccountBillDao;
import sy.hoodle.base.common.dao.AgentPlayerInfoDao;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.function.FiveConsumer;
import sy.hoodle.base.common.redis.RedisDelService;
import sy.hoodle.base.common.util.ProBaseBigDecimalUtil;
import sy.hoodle.base.common.util.ProBaseJsonUtil;

/**
 * AgentPlayerInfo 锁定账变接口,内部实际执行接口
 * 
 * <AUTHOR>
 * @date 2025-07-28 11:11:54
 */
@Slf4j
@Service
public class AgentPlayerInfoLockInternalService {

	@Autowired
	private AgentPlayerInfoDao agentPlayerInfoDao;

	@Autowired
	private AgentPlayerAccountBillDao agentPlayerAccountBillDao;

	@Autowired
	private ThreadPoolTaskExecutor redisTaskExecutor;

	@Autowired
	private RedisDelService redisDelService;

	/**
	 * 清空玩家缓存信息
	 */
	public void delAgentPlayerInfo(Long playerId) {
		redisTaskExecutor.execute(() -> {
			boolean b = redisDelService.delAgentPlayerInfo(playerId);
			if (!b) {
				log.warn("redisDelService.delAgentPlayerInfo 失败, id={}", playerId);
			}
		});
	}

	@Transactional(propagation = Propagation.NEVER)
	public void incr_check(long playerId, BigDecimal incrPlayerAvailableBalance, BigDecimal incrPlayerBlockedBalance) {
		if (incrPlayerAvailableBalance == null || incrPlayerBlockedBalance == null) {
			log.error("用户{}账变异常,玩家可用余额变化:{},玩家冻结余额变化:{}", playerId, incrPlayerAvailableBalance, incrPlayerBlockedBalance);
			throw new RuntimeException("用户账变金额不能为null");
		}
		if (ProBaseBigDecimalUtil.oneMaxOneMin(BigDecimal.ZERO, incrPlayerAvailableBalance)) {
			log.error("用户{}账变异常,玩家可用余额变化:{},玩家冻结余额变化:{}", playerId, incrPlayerAvailableBalance, incrPlayerBlockedBalance);
			throw new RuntimeException("用户入账金额不能小于0");
		}
	}


	@Transactional(propagation = Propagation.NEVER)
	public void decr_check(Long playerId, BigDecimal playerAvailableBalance, BigDecimal decrPlayerAvailableBalance,
			BigDecimal decrPlayerBlockedBalance) {
		if (decrPlayerAvailableBalance == null || decrPlayerBlockedBalance == null) {
			log.error("用户{}账变异常,玩家可用余额变化:{},玩家冻结余额变化:{}", playerId, decrPlayerAvailableBalance, decrPlayerBlockedBalance);
			throw new RuntimeException("用户账变金额不能为null");
		}
		if (ProBaseBigDecimalUtil.oneMaxOneMin(decrPlayerAvailableBalance, BigDecimal.ZERO)) {
			log.error("用户{}账变异常,玩家可用余额变化:{},玩家冻结余额变化:{}", playerId, decrPlayerAvailableBalance, decrPlayerBlockedBalance);
			throw new RuntimeException("用户出账金额不能大于0");
		}
		if (ProBaseBigDecimalUtil.oneMaxOneMin(decrPlayerAvailableBalance.abs(), playerAvailableBalance)) {
			log.error("用户{}账变,玩家可用余额变化:{},玩家冻结余额变化:{}", playerId, decrPlayerAvailableBalance, decrPlayerBlockedBalance);
			throw new RuntimeException("用户账变扣款金额大于余额");
		}
	}

	/**
	 * 统一事务账变接口,进行一些通用操作,默认只能被同包类访问
	 * 
	 * @param createBy                     账变创建人
	 * @param billType                     账变类型
	 * @param remark                       备注
	 * @param agentPlayerInfo              玩家信息
	 * @param changePlayerAvailableBalance 可用余额变化
	 * @param changePlayerBlockedBalance   冻结余额变化
	 * @param con                          事务附带更新代码
	 */
	@Transactional
	public void accountChange(String createBy, int billType, String remark, AgentPlayerInfo agentPlayerInfo,
			BigDecimal changePlayerAvailableBalance,
			BigDecimal changePlayerBlockedBalance,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		// 克隆,记录玩家信息之前的状态
		AgentPlayerInfo beforeAgentPlayerInfo = ProBaseJsonUtil.clone(agentPlayerInfo);
		String agentNo = agentPlayerInfo.getAgentNo();
		Long playerId = agentPlayerInfo.getPlayerId();
		BigDecimal beforePlayerAvailableBalance = agentPlayerInfo.getPlayerAvailableBalance();
		BigDecimal beforePlayerBlockedBalance = agentPlayerInfo.getPlayerBlockedBalance();
		log.info("{} 用户{}账变前,玩家可用余额:{},玩家冻结余额:{}", remark, playerId, beforePlayerAvailableBalance,
				beforePlayerBlockedBalance);
		agentPlayerInfo.setPlayerAvailableBalance(
				changePlayerAvailableBalance.add(beforePlayerAvailableBalance));
		agentPlayerInfo.setPlayerBlockedBalance(
				changePlayerBlockedBalance.add(beforePlayerBlockedBalance));
		// 调用函数,执行同步附带操作,将玩家账变前,账变后对象传回
		// 添加账变记录
		AgentPlayerAccountBill entity = addAgentPlayerAccountBill(
				agentPlayerInfo,
				changePlayerAvailableBalance,
				beforePlayerAvailableBalance,
				agentPlayerInfo.getPlayerAvailableBalance(), 
				billType, 
				createBy, 
				remark);
		fiv.accept(beforeAgentPlayerInfo, changePlayerAvailableBalance, changePlayerBlockedBalance, agentPlayerInfo,
				entity);
		log.info("{} 用户{}账变后,玩家可用余额:{},玩家冻结余额:{}", remark, playerId, agentPlayerInfo.getPlayerAvailableBalance(),
				agentPlayerInfo.getPlayerBlockedBalance());
		agentPlayerAccountBillDao.save(entity);
		// 调用已有方法进行更新
		agentPlayerInfoDao.updatePlayerAvailableBalance(agentNo, playerId, changePlayerAvailableBalance,
				changePlayerBlockedBalance);
	}

	// 添加账变记录
	public AgentPlayerAccountBill addAgentPlayerAccountBill(AgentPlayerInfo playerInfo, BigDecimal amount,
			BigDecimal balanceBeforeAmount, BigDecimal balanceAfterAmount, int billType,
			String createBy, String billRemark) {
		AgentPlayerAccountBill entity = new AgentPlayerAccountBill();
		entity.setAgentNo(playerInfo.getAgentNo());
		entity.setAgentName(playerInfo.getAgentName());
		entity.setAgentType(playerInfo.getAgentType());
		entity.setPlayerId(playerInfo.getPlayerId());
		entity.setPlayerName(playerInfo.getPlayerName());
		entity.setPlayerBillNo(craeteBillNo());
		entity.setBillType(billType);
		entity.setBillRemark(billRemark);
		entity.setPlayerBalanceBefore(balanceBeforeAmount);
		entity.setBillAmount(amount);
		entity.setPlayerBalanceAfter(balanceAfterAmount);
		entity.setIsDelete(0);
		entity.setCreateBy(createBy);
		entity.setCreateTime(new Timestamp(System.currentTimeMillis()));
		entity.setUpdateTime(entity.getCreateTime());
		entity.setPid(playerInfo.getPid());
		entity.setPids(playerInfo.getPids());
		return entity;
	}

	public static String craeteBillNo() {
		DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
		String strDate2 = dtf2.format(LocalDateTime.now());
		return strDate2 + generate(10, true);
	}

	public static String generate(int length, boolean isNumber) {
		String uuid = UUID.randomUUID().toString().replace("-", "");
		if (isNumber) {
			uuid = uuid.replace("a", "1").replace("b", "2").replace("c", "3").replace("d", "4").replace("e", "5")
					.replace("f", "6");
		}
		if (uuid.length() < length) {
			uuid += generate(length - uuid.length(), isNumber);
		} else {
			uuid = uuid.substring(uuid.length() - length, uuid.length());
		}
		return uuid;
	}

}
