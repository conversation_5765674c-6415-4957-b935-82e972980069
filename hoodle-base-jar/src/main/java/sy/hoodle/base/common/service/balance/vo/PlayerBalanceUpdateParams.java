package sy.hoodle.base.common.service.balance.vo;


import java.math.BigDecimal;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import lombok.Getter;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.function.FiveConsumer;
import sy.hoodle.base.common.function.FourConsumer;

/**
 * 账变类型包装 封装玩家余额更新操作所需的参数。 </br>
 * 包括玩家ID、变动金额、变动方向以及一个操作完成后的回调函数。
 * 
 * <AUTHOR>
 * @date 2025-07-28 03:34:52
 */
@Getter
public class PlayerBalanceUpdateParams {
	/** 玩家的唯一标识ID */
	private Long playerId;
	/** 账变创建人 */
	private String createBy;
	/** 账变类型 */
	private int billType;
	/** 账变的金额，实际增加或减少由 direction 决定 */
	private BigDecimal changePlayerAvailableBalance;
	/** 玩家冻结余额 */
	private BigDecimal changePlayerBlockedBalance;
	/** 账变的具体方向：增加 (INCREASE) 或 减少 (DECREASE) */
	private BalanceChangeDirection direction;
	/** 操作完成后执行的回调函数，接收更新后的 AgentPlayerInfo 对象 */
	private FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv;
	// 账变备注
	private String remark;

	/**
	 * 构造函数，用于创建 PlayerBalanceUpdateParams 实例。
	 *
	 * @param playerId                   玩家的唯一标识ID
	 * @param changeAmount               账变的金额（正值），实际增减由 direction 决定
	 * @param changePlayerBlockedBalance 玩家冻结余额
	 * @param direction                  账变的具体方向（INCREASE 或 DECREASE）
	 * @param con                        操作完成后执行的回调函数
	 */
	public PlayerBalanceUpdateParams(String createBy, int billType, String remark, Long playerId,
			BigDecimal changePlayerAvailableBalance,
			BigDecimal changePlayerBlockedBalance, BalanceChangeDirection direction,
			FiveConsumer<AgentPlayerInfo, BigDecimal, BigDecimal, AgentPlayerInfo, AgentPlayerAccountBill> fiv) {
		this.createBy = createBy;
		this.billType = billType;
		this.remark = remark;
		this.playerId = playerId;
		this.changePlayerAvailableBalance = changePlayerAvailableBalance;
		this.changePlayerBlockedBalance = changePlayerBlockedBalance;
		this.direction = direction;
		this.fiv = fiv;
	}

}