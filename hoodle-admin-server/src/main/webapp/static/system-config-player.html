<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8"/>
    <meta charset="utf-8"/>
    <title>用户配置</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/pace/pace-theme-flash.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen"/>
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"/>
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css"/>
    <!-- END CSS TEMPLATE -->
</head>

<body>
<div class="page-iframe" id="app">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="thisPanel" class="grid simple settings">
                    <div class="grid-title no-border">
                        <h4>用户配置</h4>
                    </div>
                    <div class="grid-body border-top">
                        <form name="search-params" class="form-horizontal">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="agent" class="form-control"></select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="grid-body border-top">
                        <div class="row">
                            <div class="col-md-12">
                                <h4>允许登录失败次数</h4>
                                <div class="tab-space">
                                    <p>用户登录密码输入错误最大允许次数。</p>
                                    <input name="loginFailedNum" type="text" class="form-control" placeholder="登录失败次数">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>允许资金密码失败次数</h4>
                                <div class="tab-space">
                                    <p>用户资金密码输入错误最大允许次数。</p>
                                    <input name="paymentPasswordFailedNum" type="text" class="form-control" placeholder="资金密码失败次数">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>最高奖级</h4>
                                <div class="tab-space">
                                    <p>彩票用户以及玩法最高奖级，请慎重修改。</p>
                                    <input name="sysCode" type="text" class="form-control" placeholder="奖级">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>最高返点</h4>
                                <div class="tab-space">
                                    <p>彩票用户以及玩法最高返点，请慎重修改。</p>
                                    <input name="sysPoint" type="text" class="form-control" placeholder="返点">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>最高可投注奖级</h4>
                                <div class="tab-space">
                                    <p>彩票用户以及玩法最高可投注奖级，不能高于最高奖级且不能低于最高返点对应的奖级，配置为0时不生效，请慎重修改。</p>
                                    <input name="sysCodeMax" type="text" class="form-control" placeholder="最高可投注奖级">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>最低可投注奖级</h4>
                                <div class="tab-space">
                                    <p>彩票用户以及玩法最低可投注奖级，不能高于最高奖级且不能低于最高返点对应的奖级，配置为0时不生效，请慎重修改。</p>
                                    <input name="sysCodeMin" type="text" class="form-control" placeholder="最低可投注奖级">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>单注投注金额</h4>
                                <div class="tab-space">
                                    <p>单注投注金额，请填写整数数字，请慎重修改。</p>
                                    <input name="sysUnitMoney" type="text" class="form-control" placeholder="金额">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>标准盘玩法总开关</h4>
                                <div class="tab-space">
                                    <div class="radio radio-success">
                                        <input id="methodTypeStandard_yes" name="methodTypeStandard" type="radio" value="true">
                                        <label for="methodTypeStandard_yes">开启</label>
                                        <input id="methodTypeStandard_no" name="methodTypeStandard" type="radio" value="false">
                                        <label for="methodTypeStandard_no">关闭</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>卡单总开关</h4>
                                <div class="tab-space">
                                    <div class="radio radio-success">
                                        <input id="lockAccount_yes" name="lockAccount" type="radio" value="true">
                                        <label for="lockAccount_yes">开启</label>
                                        <input id="lockAccount_no" name="lockAccount" type="radio" value="false">
                                        <label for="lockAccount_no">关闭</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>IP卡单开关</h4>
                                <div class="tab-space">
                                    <div class="radio radio-success">
                                        <input id="lockAccountIp_yes" name="lockAccountIp" type="radio" value="true">
                                        <label for="lockAccountIp_yes">开启</label>
                                        <input id="lockAccountIp_no" name="lockAccountIp" type="radio" value="false">
                                        <label for="lockAccountIp_no">关闭</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>自动开启平级账号</h4>
                                <div class="tab-space">
                                    <p>新用户默认开启平级账号，已注册用户不受影响。多个奖级请用英文“,”分割。例如：（1960或者1958,1960）</p>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <input name="autoEqualCode" type="text" class="form-control">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>给下级转账</h4>
                                <div class="tab-space">
                                    <p>关闭给下级转账后，所有用户不能给下级转账，开启之后<font color="#f35958">已经开通给下级转账</font>的用户才可以转账。</p>
                                    <div class="radio radio-success">
                                        <input id="allowTransferToDown_yes" type="radio" name="allowTransferToDown" value="true">
                                        <label for="allowTransferToDown_yes">开启</label>
                                        <input id="allowTransferToDown_no" type="radio" name="allowTransferToDown" value="false">
                                        <label for="allowTransferToDown_no">关闭</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>给上级转账</h4>
                                <div class="tab-space">
                                    <p>关闭给上级转账后，所有用户不能给上级转账，开启之后<font color="#f35958">已经开通给上级转账</font>的用户才可以转账。</p>
                                    <div class="radio radio-success">
                                        <input id="allowTransferToUp_yes" type="radio" name="allowTransferToUp" value="true">
                                        <label for="allowTransferToUp_yes">开启</label>
                                        <input id="allowTransferToUp_no" type="radio" name="allowTransferToUp" value="false">
                                        <label for="allowTransferToUp_no">关闭</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h4>上级给下级下分</h4>
                                <div class="tab-space">
                                    <p>关闭给上级给下级下分后，所有用户不能给下级下分，开启之后<font color="#f35958">已经开通上级给下级下分</font>的用户才可以转账。</p>
                                    <div class="radio radio-success">
                                        <input id="applyUpDedTransfer_yes" type="radio" name="applyUpDedTransfer" value="true" v-model="sys_config_data.APPLY_UP_DED_TRANSFER">
                                        <label for="applyUpDedTransfer_yes">开启</label>
                                        <input id="applyUpDedTransfer_no" type="radio" name="applyUpDedTransfer" value="false" v-model="sys_config_data.APPLY_UP_DED_TRANSFER">
                                        <label for="applyUpDedTransfer_no">关闭</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="pull-right">
                                <button data-command="save" type="button" class="btn btn-success btn-cons"><i class="fa fa-save"></i> 保存修改</button>
                                <button data-command="reset" type="button" class="btn btn-cons"><i class="fa fa-undo"></i> 重置</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="account-type-table" class="grid simple" style="width:1200px">
                    <div class="grid-title no-border">
                        <h4>用户类型</h4>
                    </div>
                    <div class="grid-body border-top">
                        <div class="row">
                            <div class="col-md-2">
                                <select name="agent" class="form-control"></select>
                            </div>
                            <div class="col-md-2">
                                <button data-command="add" type="button" class="btn btn-success btn-cons"><i class="fa fa-plus"></i> 添加类型</button>
                            </div>
                        </div>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th width="90px" class="text-center">唯一编码</th>
                                <th width="90px" class="text-center">用户组</th>
                                <th width="110px" class="text-center">名称</th>
                                <th width="80px" class="text-center">上级编码</th>
                                <th width="60px" class="text-center">级别</th>
                                <th width="120px" class="text-center">可开户用户类型</th>
                                <th width="90px" class="text-center">彩票最低奖级</th>
                                <th width="90px" class="text-center">彩票最高奖级</th>
                                <th width="100px" class="text-center">平级开号</th>
                                <th width="100px" class="text-center">平级开号限定奖级</th>
                                <th width="90px" class="text-center">平级开号最低奖级</th>
                                <th width="90px" class="text-center">平级开号最高奖级</th>
                                <th class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/jquery.json.min.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="js/system-config-player.js" type="text/javascript"></script>
<script src="assets/js/vue.min.js" type="text/javascript"></script>

<script>
	const vue_app = new Vue({
        el: '#app',
        data: {
        	sys_config_data:{}
        },
        methods: {
          update_sys_config_data(sys_config_data){
        	  this.sys_config_data = sys_config_data;
        	  if(!sys_config_data.APPLY_UP_DED_TRANSFER){
        		  sys_config_data.APPLY_UP_DED_TRANSFER = false;
        	  }
          }
        }
    });
</script>

</body>
</html>
