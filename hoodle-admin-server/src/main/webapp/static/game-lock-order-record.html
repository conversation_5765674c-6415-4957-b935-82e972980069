<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8"/>
    <meta charset="utf-8"/>
    <title>卡单记录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css"/>
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"/>
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css"/>
    <!-- END CSS TEMPLATE -->
</head>

<body>
<div class="page-iframe">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="table-data-list" class="grid simple">
                    <div class="grid-title no-border">
                        <h4><span class="semi-bold">卡单记录列表</span></h4>
                    </div>
                    <div class="grid-body border-top">
                        <form name="search-params" class="form-horizontal">
                            <div class="row">
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="agentNo" placeholder="代理商商户号"/>
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="agentName" placeholder="代理商名称"/>
                                </div>
                                <div class="col-md-2">
                                    <input name="playerName" type="text" class="form-control" placeholder="用户名"/>
                                </div>
                                <div class="col-md-2">
                                    <select name="lockType" class="form-control">
                                        <option value="" selected="selected">卡单类型</option>
                                        <option value="1">个人</option>
                                        <option value="9">全平台</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input name="sOrderTime" data-init="datepicker" type="text" class="form-control" placeholder="日期大于"/>
                                </div>
                                <div class="col-md-2">
                                    <input name="eOrderTime" data-init="datepicker" type="text" class="form-control" placeholder="日期小于"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-2">
                                    <button name="search" type="button" class="btn btn-primary">
                                        <i class="fa fa-search"></i> 查询结果
                                    </button>
                                </div>
                            </div>
                        </form>
                        <table class="table table-bordered table-hover">
                            <thead>
                            <tr>
                                <th class="text-center">代理商</th>
                                <th class="text-center">卡单任务编号</th>
                                <th class="text-center">订单号</th>
                                <th class="text-center">用户名</th>
                                <th class="text-center">卡单类型</th>
                                <th class="text-center">彩种</th>
                                <th class="text-center">期号</th>
                                <th class="text-center">玩法</th>
                                <th class="text-center">投注内容</th>
                                <th class="text-center">投注金额</th>
                                <th class="text-center">中奖金额</th>
                                <th class="text-center">卡单时间</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div class="page-list"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="js/game-lock-order-record.js" type="text/javascript"></script>
</body>

</html>
