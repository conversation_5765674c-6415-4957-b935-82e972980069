$(document).ready(function() {

	var thisTable = $('.content');
	var form = thisTable.find('form[name="search-params"]');

	var TableConfig = function() {
		var loadAgent = function(cb) {
			var e = thisTable.find('select[name="agent"]');
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
			App.staticPost(url, {}, function(result) {
				var item = result.data;
				$.each(item, function(i, v) {
					e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
				});
				e.trigger('change');
			});
			if (cb) {
				cb();
			}
		}
		var doListConfig = function() {
			var agentNo = form.find('select[name="agent"]').val();
			if (null == agentNo) {
				return;
			}
			var url = Route.PATH + Route.System.PATH + Route.System.LIST_LOTTERY_CONFIG;
			App.ajaxPost(url, { agentNo: agentNo }, function(res) {
				buildConfig(res);
			});
		}
		var buildConfig = function(data) {
			 thisTable.find('input[name="REBATE_LEVEL"][value="' + data.REBATE_LEVEL + '"]').trigger('click');
		}

		form.find('select[name="agent"]').change(function() {
			init();
		});

		loadAgent();
		var init = function() {
			doListConfig();
		}

		return {
			init: init
		}
	}();

	thisTable.find('[data-command="reset"]').click(function() {
		thisTable.find('input[name="REBATE_LEVEL"][value="0"]').trigger('click');
	});

	thisTable.find('[data-command="save"]').click(function() {
		var rebateLevel = thisTable.find('input[name="REBATE_LEVEL"]:checked').val();
		var agentNo = $('form[name="search-params"]').find('select[name="agent"]').val();
		if (agentNo == "") {
			App.msg('info', '请先选择平台代理商');
			return;
		}
		var data = { agentNo: agentNo, rebateLevel: rebateLevel };
		var url = Route.PATH + Route.System.PATH + Route.System.UPDATE_LOTTERY_CONFIG;
		App.ajaxPost(url, data, function() {
			App.msg('success', '操作成功');
			TableConfig.init();
		});
	});

	TableConfig.init();

});