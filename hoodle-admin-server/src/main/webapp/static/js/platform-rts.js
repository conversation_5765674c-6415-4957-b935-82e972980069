$(document).ready(function() {

	var thisTable = $('#table-data-list');
	var p = thisTable.find('form[name="search-params"]');

	p.find('input[name="sHour"]').val('00:00');
	p.find('input[name="sDate"]').val(moment().format('YYYY-MM-DD'));
	p.find('input[name="eHour"]').val('00:00');
	p.find('input[name="eDate"]').val(moment().add(1, 'days').format('YYYY-MM-DD'));

	var TableConfig = function() {
		var loadAgent = function(cb) {
			var e = thisTable.find('select[name="agent"]');
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
			App.staticPost(url, {}, function(result) {
				var item = result.data;
				$.each(item, function(i, v) {
					e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
				});
				e.trigger('change');
			});
			if (cb) {
				cb();
			}
		}

		var doListConfig = function() {
			var getParams = function() {
				var agentNo = p.find('select[name="agent"]').val();
				var sDate = p.find('input[name="sDate"]').val().trim();
				var sHour = p.find('input[name="sHour"]').val().trim();
				var eDate = p.find('input[name="eDate"]').val().trim();
				var eHour = p.find('input[name="eHour"]').val().trim();
				var playerName = p.find('input[name="playerName"]').val().trim();
				var sTime = moment(sDate + ' ' + sHour + ':00').format('YYYY-MM-DD HH:mm:ss');
				var eTime = moment(eDate + ' ' + eHour + ':00').format('YYYY-MM-DD HH:mm:ss');
				return { agentNo: agentNo, playerName: playerName, sTime: sTime, eTime: eTime }
			}
			if (null == getParams().agentNo) {
				return;
			}
			var url = Route.PATH + Route.PLATFORM_RTS;
			App.ajaxPost(url, getParams(), function(res) {
				buildData(res);
			});
		}

		var buildData = function(data) {
			console.log(data,'表格数据')
			$(document).ready(function() {
				// 存款的length = 数组的长度 + 人工上分 + 上下级转账 + 合计 + thead两行 + 底部分割1行
				let depositLength = data.RechargeList.length + 1 + 1 + 1 + 2 + 2;
				// 取款的length = 数组的长度 + 人工下分 + 上下级转账 + 合计 + thead两行 + 底部分割1行
				let withdrawLength = data.WithdrawList.length + 1 + 1 + 1 + 2 + 2;
				// 自营彩票的length = 5 + thead一行 + 底部分割1行
				let selfLength = 5 + 1 + 1;
				// 取存款、取款、自营彩票的最大长度
				let leftTopMax = Math.max(depositLength, withdrawLength, selfLength);

				// 活动的length = 数组的长度 + 合计 + thead一行
				let activityLength = data.ActivityList.length + 1 + 2;
				// 分红的length = 数组的长度 + 合计 + thead一行
				let bonusLength = data.DividendList.length + 1 + 2;
				// 工资的length = 数组的长度 + 合计 + thead一行
				let salaryLength = data.SalaryList.length + 1 + 2;
				// 资金修正+余额的length
				let fundLength = 11;
				// 取活动、分红、工资、资金修正+余额的最大长度
				let leftBottomMax = Math.max(activityLength, bonusLength, salaryLength, fundLength);

				// 三方游戏的length = 数组的长度 + 合计 + thead一行
				let thirdLength = data.ThridGameList.length + 1 + 2;
				let max = Math.max(leftTopMax + leftBottomMax, thirdLength);

				// 存款的空白长度
				let depositBlankLength = leftTopMax - depositLength;
				// 取款的空白长度
				let withdrawBlankLength = leftTopMax - withdrawLength;
				// 自营彩票的空白长度
				let selfBlankLength = leftTopMax - selfLength;
				// 活动的空白长度
				let activityBlankLength = leftBottomMax - activityLength;
				// 分红的空白长度
				let bonusBlankLength = leftBottomMax - bonusLength;
				// 工资的空白长度
				let salaryBlankLength = leftBottomMax - salaryLength;
				// 资金修正+余额的空白长度
				let fundBlankLength = leftBottomMax - fundLength;
				// 三方游戏的空白长度
				let thirdBlankLength = max - thirdLength;
				
				// 三方表格
			    var $sfTableBody = $('#sfTable tbody');
			    $sfTableBody.empty(); //先清空tbody
				if(data.ThridGameList.length>0){
					$.each(data.ThridGameList, function(index, item) {
				        var row = '<tr>';
				        row += '<td>' + item[1] + '</td>';
				        row += '<td>' + item[2] + '</td>';
				        row += '</tr>';
				        $sfTableBody.append(row); // 将新行添加到tbody中
				    });
				}
				if(thirdBlankLength>0){
					var kTr = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < thirdBlankLength; i ++) {
						kTr += `<tr><td></td><td></td></tr>`
					}
					$sfTableBody.append(kTr)
				}
				// 增加总计数据
				$sfTableBody.append('<tr><td></td><td></td></tr><tr><td>合计</td><td>' + data.ThridGameTotalAmount + '</td></tr>');
				
				// 活动表格
				var $activeTableBody = $('#activeTable tbody');
				$activeTableBody.empty(); //先清空tbody
				if(data.ActivityList.length>0){
					$.each(data.ActivityList, function(index, item) {
				        var row = '<tr>';
				        row += '<td>' + item[0] + '</td>';
				        row += '<td>' + item[1] + '</td>';
				        row += '</tr>';
				        $activeTableBody.append(row); // 将新行添加到tbody中
				    });
				}
				if(activityBlankLength>0){
					var kTr = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < activityBlankLength; i ++) {
						kTr += `<tr><td></td><td></td></tr>`
					}
					$activeTableBody.append(kTr)
				}
				// 增加总计数据
				$activeTableBody.append('<tr><td></td><td></td></tr><tr><td>活动合计</td><td>' + data.ActityTotalAmount + '</td></tr>');
				
				// 分红表格
				var $fhTableBody = $('#fhTable tbody');
				$fhTableBody.empty(); //先清空tbody
				if(data.DividendList.length>0){
					$.each(data.DividendList, function(index, item) {
				        var row = '<tr>';
				        row += '<td>' + item[0] + '</td>';
				        row += '<td>' + item[1] + '</td>';
				        row += '</tr>';
				        $fhTableBody.append(row); // 将新行添加到tbody中
				    });
				}
				if(bonusBlankLength>0){
					var kTr = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < bonusBlankLength; i ++) {
						kTr += `<tr><td></td><td></td></tr>`
					}
					$fhTableBody.append(kTr)
				}
				// 增加总计数据
				$fhTableBody.append('<tr><td></td><td></td></tr><tr><td>分红合计</td><td>' + data.DividendTotalAmount + '</td></tr>');
				
				// 工资表格
				var $gzTableBody = $('#gzTable tbody');
				$gzTableBody.empty(); //先清空tbody
				if(data.SalaryList.length>0){
					$.each(data.SalaryList, function(index, item) {
				        var row = '<tr>';
				        row += '<td>' + item[0] + '</td>';
				        row += '<td>' + item[1] + '</td>';
				        row += '</tr>';
				        $gzTableBody.append(row); // 将新行添加到tbody中
				    });
				}
				if(salaryBlankLength>0){
					var kTr = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < salaryBlankLength; i ++) {
						kTr += `<tr><td></td><td></td></tr>`
					}
					$gzTableBody.append(kTr)
				}
				// 增加总计数据
				$gzTableBody.append('<tr><td></td><td></td></tr><tr><td>工资合计</td><td>' + data.SalaryTotalAmount + '</td></tr>');

				// 存款表格
				var $ckTableBody = $('#ckTable tbody');
				$ckTableBody.empty(); //先清空tbody
				// 增加人工上分、上下级转账数据
				$ckTableBody.append(`
					<tr>
						<td>人工上分</td><td>` + data.RechargeSystemInAmount + `</td><td>` + data.RechargeSystemInFreeAmount + `</td>
					</tr>
					<tr>
						<td>上下级转账（转入）</td><td>` + data.RechargeTransferInAmount + `</td><td>` + data.RechargeTransferInFreeAmount + `</td>
					</tr>`);
				if(data.RechargeList.length>0){
					$.each(data.RechargeList, function(index, item) {
				        var row = '<tr>';
				        row += '<td>' + item[0] + '</td>';
				        row += '<td>' + item[1] + '</td>';
						row += '<td>' + item[2] + '</td>';
				        row += '</tr>';
				        $ckTableBody.append(row); // 将新行添加到tbody中
				    });
				}
				if(depositBlankLength>0){
					var kTr = `<tr><td></td><td></td><td></td></tr>`
					for(var i = 1; i < depositBlankLength; i ++) {
						kTr += `<tr><td></td><td></td><td></td></tr>`
					}
					$ckTableBody.append(kTr)
				}
				// 增加总计数据
				$ckTableBody.append(`
					<tr>
						<td></td><td></td><td></td>
					</tr>
					<tr>
						<td>存款合计</td><td>` + data.RechargeTotalAmount + `</td><td>` + data.RechargeTotalFreeAmount + `</td>
					</tr>
					<tr>
						<td></td><td></td><td></td>
					</tr>`);

				// 取款表格
				var $qkTableBody = $('#qkTable tbody');
				$qkTableBody.empty(); //先清空tbody
				// 增加人工下分、上下级转账数据
				$qkTableBody.append(				`
					<tr>
						<td>人工下分</td><td>` + data.WithdrawSystemOutAmount + `</td><td>` + data.WithdrawSystemOutFreeAmount + `</td>
					</tr>
					<tr>
						<td>上下级转账（转出）</td><td>` + data.WithdrawTransferOutAmount + `</td><td>` + data.WithdrawTransferOutFreeAmount + `</td>
					</tr>`);
				if(data.WithdrawList.length>0){
					$.each(data.WithdrawList, function(index, item) {
				        var row = '<tr>';
				        row += '<td>' + item[0] + '</td>';
				        row += '<td>' + item[1] + '</td>';
						row += '<td>' + item[2] + '</td>';
				        row += '</tr>';
				        $qkTableBody.append(row); // 将新行添加到tbody中
				    });
				}
				if(withdrawBlankLength>0){
					var kTr = `<tr><td></td><td></td><td></td></tr>`
					for(var i = 1; i < withdrawBlankLength; i ++) {
						kTr += `<tr><td></td><td></td><td></td></tr>`
					}
					$qkTableBody.append(kTr)
				}
				// 增加总计数据
				$qkTableBody.append(`
					<tr>
						<td></td><td></td><td></td>
					</tr>
					<tr>
						<td>取款合计</td><td>` + data.WithdrawTotalAmount + `</td><td>` + data.WithdrawTotalFreeAmount + `</td>
					</tr>
					<tr>
						<td></td><td></td><td></td>
					</tr>`);

				// 自营彩票表格
				var $zycpTableBody = $('#zycpTable tbody');
				$zycpTableBody.empty(); //先清空tbody
				$zycpTableBody.append(
					`<tr>
						<td>投注</td><td>` + data.LotteryBetAmount + `</td>
					</tr>
					<tr>
						<td>派奖</td><td>` + data.LotteryBounsAmount + `</td>
					</tr>
					<tr>
						<td>返点</td><td>` + data.LotteryPointAmount + `</td>
					</tr>
					<tr>
						<td>抽水</td><td>` + data.LotteryPumpAmount + `</td>
					</tr>
					<tr>
						<td>盈亏</td><td>` + data.LotteryProfitAmount + `</td>
					</tr><tr><td></td><td></td></tr>`);
				if(selfBlankLength>0){
					var kTr = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < selfBlankLength; i ++) {
						kTr += `<tr><td></td><td></td></tr>`
					}
					$zycpTableBody.append(kTr)
				}
				
				// 资金修正表格
				var $zjTableBody = $('#zjTableBody');
				$zjTableBody.empty(); //先清空tbody
				$zjTableBody.append(
					`<tr>
						<td>修改资金（正）</td><td>` + data.TransferInAmount + `</td>
					</tr>
					<tr>
						<td>修改资金（负）</td><td>` + data.TransferOutAmount + `</td>
					</tr>
					<tr><td></td><td></td></tr>
					<tr>
						<td>修正合计</td><td>` + data.TransferTotalAmount + `</td>
					</tr><tr><td></td><td></td></tr>`);
				var $yeTableBody = $('#yeTableBody');
				$yeTableBody.empty(); //先清空tbody
				$yeTableBody.append(
					`<tr>
						<td>彩票余额</td><td>` + data.LotteryAmount + `</td>
					</tr>
					<tr>
						<td>三方余额</td><td>` + data.ThridGameTotalAmount + `</td>
					</tr>
					<tr><td></td><td></td></tr>
					<tr>
						<td>平台总余额</td><td>` + data.TotalAmount + `</td>
					</tr>`);
				if(fundBlankLength>0){
					var kTr = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < Math.floor(fundBlankLength / 2); i ++) {
						kTr += `<tr><td></td><td></td></tr>`
					}
					$zjTableBody.append(kTr)
					var kTr1 = `<tr><td></td><td></td></tr>`
					for(var i = 1; i < (fundBlankLength - Math.floor(fundBlankLength / 2)); i ++) {
						kTr1 += `<tr><td></td><td></td></tr>`
					}
					$yeTableBody.append(kTr1)
				}
			});
		}

		p.find('select[name="agent"]').change(function() {
			init();
			$(this).off("change")
		});

		p.find('button[name="search"]').click(function() {
			init();
		});

		loadAgent();
		var init = function() {
			doListConfig();
		}

		return {
			init: init
		}
	}();
	
	// 初始化日期控件
    $('[data-init="datepicker"]').datepicker({
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });
    // 初始化时间控件
    $('[data-init="clockpicker"]').clockpicker({
        autoclose: true,
        align: 'left'
    });

	TableConfig.init();
});