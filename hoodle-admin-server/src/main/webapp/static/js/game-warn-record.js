// 彩种映射表 - 动态从接口获取
var lotteryMap = {};
var lotteryMapLoaded = false;

// 玩法映射表 - 动态从接口获取
var gameplayMap = {};
var gameplayMapLoaded = false;

// 加载彩种映射数据
var loadLotteryMap = function(callback) {
    if (lotteryMapLoaded) {
        if (callback) callback();
        return;
    }

    var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.STATIC_LIST_INFO;
    console.log('加载彩种映射数据，接口URL:', url);

    $.ajax({
        type: 'POST',
        url: url,
        data: {},
        dataType: 'json',
        success: function(result) {
            console.log('彩种接口响应:', result);

            if (result && Array.isArray(result)) {
                // 直接返回数组格式
                $.each(result, function(i, item) {
                    if (item.lottery && item.gameName) {
                        lotteryMap[item.lottery] = item.gameName;
                    }
                });
                lotteryMapLoaded = true;
                console.log('彩种映射加载完成:', lotteryMap);
            } else {
                console.warn('彩种接口返回格式不正确:', result);
            }

            if (callback) callback();
        },
        error: function(xhr, status, error) {
            console.error('加载彩种映射失败:', error);
            if (callback) callback();
        }
    });
};

// 加载玩法映射数据
var loadGameplayMap = function(dataList, callback) {
    console.log('开始加载当前数据中彩种的玩法映射');

    if (!dataList || dataList.length === 0) {
        console.log('数据列表为空，跳过玩法映射加载');
        gameplayMapLoaded = true;
        if (callback) callback();
        return;
    }

    // 从当前数据中提取唯一的彩种
    var lotterySet = new Set();
    $.each(dataList, function(i, item) {
        if (item.lotteryGameplay) {
            lotterySet.add(item.lotteryGameplay);
        }
        if (item.disableLottery) {
            lotterySet.add(item.disableLottery);
        }
    });

    var lotteryList = Array.from(lotterySet);
    var completedCount = 0;
    var totalCount = lotteryList.length;

    console.log('当前数据中的彩种数量:', totalCount, '彩种列表:', lotteryList);

    if (totalCount === 0) {
        gameplayMapLoaded = true;
        if (callback) callback();
        return;
    }

    // 为每个彩种获取玩法
    $.each(lotteryList, function(index, lottery) {
        var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.LIST_LOTTERY_METHOD;

        $.ajax({
            type: 'POST',
            url: url,
            data: { lottery: lottery },
            dataType: 'json',
            success: function(result) {
                console.log('彩种', lottery, '玩法数量:', result && result.data ? result.data.length : 0);

                if (result && (result.success || result.code === "0")) {
                    // WebJson格式
                    $.each(result.data, function(i, item) {
                        if (item.methodCode && item.methodName) {
                            gameplayMap[item.methodCode] = item.methodName;
                        }
                    });
                } else {
                    console.warn('彩种', lottery, '玩法接口返回格式不正确');
                }

                completedCount++;

                // 所有彩种的玩法都加载完成
                if (completedCount >= totalCount) {
                    gameplayMapLoaded = true;
                    console.log('当前数据彩种玩法映射加载完成，总玩法数量:', Object.keys(gameplayMap).length);
                    if (callback) callback();
                }
            },
            error: function(xhr, status, error) {
                console.error('加载彩种', lottery, '玩法映射失败:', error);
                completedCount++;

                if (completedCount >= totalCount) {
                    gameplayMapLoaded = true;
                    console.log('玩法映射加载完成（部分失败），总玩法数量:', Object.keys(gameplayMap).length);
                    if (callback) callback();
                }
            }
        });
    });
};

// 格式化预警彩种文本 - 全局函数
var getDisableLotteryText = function(value) {
    if (lotteryMap[value]) {
        return lotteryMap[value];
    }
    return value; // 如果没有映射，返回原值
};

// 格式化玩法文本 - 全局函数
var getGameplayText = function(value) {
    if (gameplayMap[value]) {
        return gameplayMap[value];
    }

    return value; // 如果没有映射，返回原值
};

$(document).ready(function() {

    // 亏损预警记录管理
    var WarnRecordTable = function() {
        var thisTable = $('#table-warn-record');
        var thisPageList = thisTable.find('.page-list');

        var getParams = function() {
            var form = thisTable.find('form[name="search-params-warn"]');
            console.log('status:', form.find('select[name="warnStatus"]').val());
            return {
                status: form.find('select[name="warnStatus"]').val() || null
            };
        };

        var ajaxUrl = Route.PATH + Route.System.PATH + Route.GameLottery.SEARCH_EARLY_WARN_RECORD;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 10,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: getParams,
            beforeSend: function () {
                blockUI(thisTable);
            },
            complete: function () {
                unblockUI(thisTable);
            },
            success: function (data, fullResponse) {
                buildData(fullResponse);
            },
            pageError: function (response) {
                App.dialog(response.message);
            },
            emptyData: function () {
                thisTable.find('table > tbody').html('<tr><td colspan="9" class="text-center">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data) {
            var tbody = thisTable.find('table > tbody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                return;
            }

            $.each(data.list, function(i, item) {
                var row = '<tr>';
                row += '<td class="text-center">' + (item.id || '') + '</td>';
                row += '<td class="text-center">' + (item.agentName || '-') + '</td>';
                row += '<td class="text-center">' + (item.lotteryGameplayName || '-') + '</td>';
                row += '<td class="text-center">' + (item.methodName || '-') + '</td>';
                row += '<td class="text-center">' + (item.methodCode || '-') + '</td>';
                row += '<td class="text-center">' + formatAmount(item.warnLossAmount || 0) + '</td>';
                row += '<td class="text-center">' + formatDateTime(item.lastWarnTime || item.createTime) + '</td>';
                row += '<td class="text-center">' + getWarnStatusText(item.status || '1') + '</td>';
                row += '<td class="text-center">' + getOperationText(item.status || '1', item.id) + '</td>';
                row += '</tr>';
                tbody.append(row);
            });
        };

        // 格式化金额
        var formatAmount = function(amount) {
            if (!amount) return '0.00';
            return parseFloat(amount).toFixed(2);
        }

        // 格式化日期时间
        var formatDateTime = function(dateTime) {
            if (!dateTime) return '';
            return moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
        }

        // 预警状态文本转换
        var getWarnStatusText = function(status) {
            switch(status) {
                case '1': return '已预警';
                case '2': return '已清除';
                default: return '未知';
            }
        }

        // 操作文本转换
        var getOperationText = function(status, id) {
            switch(status) {
                case '1': return '<button class="btn btn-primary btn-sm" onclick="clearWarn(' + id + ')"><i class="fa fa-check"></i> 清除</button>';
                case '2': return '<span class="text-muted">无操作</span>';
                default: return '<span class="text-muted">无操作</span>';
            }
        }

        // 绑定搜索按钮事件
        thisTable.find('button[name="search-warn"]').click(function() {
            pagination.reload();
        });



        var init = function() {
            // 彩种映射已在全局加载，直接初始化分页
            pagination.init();
        }

        var reload = function() {
            var data = getParams();
            doSearch(data);
        }

        return {
            init: init,
            reload: reload
        }
    }();

    // 自动禁用记录管理
    var AutoDisableTable = function() {
        var thisTable = $('#table-auto-disable');
        var thisPageList = thisTable.find('.page-list');
        var getParams = function() {
            return {};
        };

        var ajaxUrl = Route.PATH + Route.System.PATH + Route.GameLottery.SEARCH_EARLY_WARN_AUTO_DISABLE_RECORD;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 10,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: getParams,
            beforeSend: function () {
                blockUI(thisTable);
            },
            complete: function () {
                unblockUI(thisTable);
            },
            success: function (data, fullResponse) {
                buildData(fullResponse);
            },
            pageError: function (response) {
                App.dialog(response.message);
            },
            emptyData: function () {
                thisTable.find('table > tbody').html('<tr><td colspan="7" class="text-center">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data) {
            var tbody = thisTable.find('table > tbody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                return;
            }

            // 先加载当前数据中彩种的玩法映射，然后显示数据
            loadGameplayMap(data.list, function() {
                $.each(data.list, function(i, item) {
                    var row = '<tr>';
                    row += '<td class="text-center">' + (item.id || '') + '</td>';
                    row += '<td class="text-center">' + (item.agentName || '') + '</td>';
                    row += '<td class="text-center">' + (item.disableLotteryName || '-') + '</td>';
                    row += '<td class="text-center">' + (item.methodName || '-') + '</td>';
                    row += '<td class="text-center">' + (item.methodCode || '-') + '</td>';
                    row += '<td class="text-center">' + formatAmount(item.autoDisableLossAmount) + '</td>';
                    row += '<td class="text-center">' + formatDateTime(item.disableTime) + '</td>';
                    row += '</tr>';
                    tbody.append(row);
                });
            });
        };



        // 格式化金额
        var formatAmount = function(amount) {
            if (!amount) return '0.00';
            return parseFloat(amount).toFixed(2);
        }

        // 格式化日期时间
        var formatDateTime = function(dateTime) {
            if (!dateTime) return '';
            return moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
        }



        // 绑定搜索按钮事件
        thisTable.find('button[name="search-disable"]').click(function() {
            pagination.reload();
        });

        var init = function() {
            // 彩种映射已在全局加载，直接初始化分页
            pagination.init();
        }

        var reload = function() {
            var data = getParams();
            doSearch(data);
        }

        return {
            init: init,
            reload: reload
        }
    }();

    // 全局函数 - 清除预警
    window.clearWarn = function(id) {
        if (typeof swal !== 'undefined') {
            swal({
                title: '确认清除',
                text: '确定要清除这条预警记录吗？清除后将无法恢复。',
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: '确定清除',
                cancelButtonText: '取消'
            }).then(function(result) {
                if (result.value) {
                    performClearWarn(id);
                }
            });
        } else {
            var modalHtml =
                '<div class="modal fade" id="clearWarnModal" tabindex="-1">' +
                '  <div class="modal-dialog modal-sm">' +
                '    <div class="modal-content">' +
                '      <div class="modal-header">' +
                '        <h4 class="modal-title"><i class="fa fa-exclamation-triangle text-warning"></i> 确认清除</h4>' +
                '      </div>' +
                '      <div class="modal-body">' +
                '        <p>确定要清除这条预警记录吗？</p>' +
                '        <p class="text-muted small">清除后将无法恢复。</p>' +
                '      </div>' +
                '      <div class="modal-footer">' +
                '        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>' +
                '        <button type="button" class="btn btn-danger" id="confirmClearBtn">确定清除</button>' +
                '      </div>' +
                '    </div>' +
                '  </div>' +
                '</div>';

            // 移除已存在的模态框
            $('#clearWarnModal').remove();

            // 添加新的模态框
            $('body').append(modalHtml);

            // 绑定确认按钮事件
            $('#confirmClearBtn').click(function() {
                $('#clearWarnModal').modal('hide');
                performClearWarn(id);
            });

            // 显示模态框
            $('#clearWarnModal').modal('show');
        }
    };

    // 执行清除操作
    function performClearWarn(id) {
        var url = Route.PATH + Route.System.PATH + Route.GameLottery.CLEAR_EARLY_WARN;
        var data = { id: id };

        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(result) {
                console.log('清除预警响应:', result);
                if (result && (result.success || result.code === "0")) {
                    App.msg('success', '预警记录已清除');
                    WarnRecordTable.reload();
                } else {
                    var errorMsg = result && result.message ? result.message : '清除失败';
                    App.msg('error', errorMsg);
                }
            },
            error: function(xhr, status, error) {
                console.error('清除预警请求错误:', xhr.responseText);
                var errorMsg = '清除失败';
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response && response.message) {
                        errorMsg = response.message;
                    }
                } catch (e) {
                    // 解析失败，使用默认错误信息
                }
                App.msg('error', errorMsg);
            }
        });
    }

    WarnRecordTable.init();
    AutoDisableTable.init();
});
