$(document).ready(function(){
    var TableDataList = (function(){
        var thisTable = $('#table-data-list');
        var p = thisTable.find('form[name="search-params"]');
        var thisPageList = thisTable.find('.page-list');

        var getParams = function(){
            var agentNo = p.find('input[name="agentNo"]').val().trim();
            var agentName = p.find('input[name="agentName"]').val().trim();
            var lockType = p.find('select[name="lockType"]').val();
            var lockStatus = p.find('select[name="lockStatus"]').val();
            return {
                agentNo: agentNo,
                agentName: agentName,
                lockType: lockType,
                lockStatus: lockStatus
            }
        };

        var ajaxData = function(){
            return $.extend({}, getParams());
        };

        var ajaxUrl = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.SEARCH_LOCK_TASK;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 20,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: ajaxData,
            //load_first_page:false,
            beforeSend: function(){
                blockUI(thisTable);
            },
            complete: function(){
                unblockUI(thisTable);
            },
            success: function(data){
                buildData(data);
            },
            pageError: function(response){
                App.dialog(response.message);
            },
            emptyData: function(){
                thisTable
                    .find('table > tbody')
                    .html('<tr><td colspan="20">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data){
            thisTable.find('table > tbody').html('');
            $.each(data, function(i, v){
                var btnStatus = '&nbsp';
                if(v.bean.lockStatus === 1){
                    btnStatus = '<button data-command="stopTask" class="btn btn-primary btn-xs btn-mini"  type="button"><i class="fa fa-ban"></i>暂停</button>&nbsp;';
                }
                if(v.bean.lockStatus === 0){
                    btnStatus = '<button data-command="startTask" class="btn btn-primary btn-xs btn-mini"  type="button"><i class="fa fa-check"></i>启用</button>&nbsp;';
                }

                var lockStatus = '已完成';
                if(v.bean.lockStatus === 0){
                    lockStatus = '未启用';
                }
                if(v.bean.lockStatus === 1){
                    lockStatus = '卡单中';
                }

                var luckNumberInfo = '一直卡';
                if(v.bean.lockType === 1){
                    if(v.bean.lockNumber === 1){
                        luckNumberInfo = '卡一次';
                    }
                }
                var array = [
                    v.bean.id,
                    v.bean.agentName,
                    DataFormat.GameLottery.lockType(v.bean.lockType),
                    v.lotteryName,
                    v.playerName,
                    luckNumberInfo,
                    v.bean.amountMix + '~' + v.bean.amountMax,
                    lockStatus,
                    v.bean.creater,
                    moment(v.bean.createTime).format('YYYY-MM-DD HH:mm:ss'),
                    btnStatus +
                    '<button data-command="deleteTask" class="btn btn-primary btn-xs btn-mini" type="button"><i class="fa fa-remove"></i> 删除</button>'
                ];
                var tr = App.buildTbody(array);
                tr.find('td').addClass('text-center');
                // 状态
                tr.find('[data-command="stopTask"]').click(function(){
                    doStoplockTask(v.bean.id);
                });
                // 状态
                tr.find('[data-command="startTask"]').click(function(){
                    doStartlockTask(v.bean.id);
                });
                // 状态
                tr.find('[data-command="deleteTask"]').click(function(){
                    doDeletelockTask(v.bean.id);
                });
                thisTable.find('table > tbody').append(tr);
            });
        };

        var doStoplockTask = function(id){
            // 正常状态
            App.dialog('确定暂停该卡单任务？', '确定', function(){
                var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.START_LOCK_TASK;
                var data = {id: id};
                App.ajaxPost(url, data, function(){
                    reload();
                    App.msg('success', '操作成功');
                });
            });
        };

        var doStartlockTask = function(id){
            // 正常状态
            App.dialog('确定启用该卡单任务？', '确定', function(){
                var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.START_LOCK_TASK;
                var data = {id: id};
                App.ajaxPost(url, data, function(){
                    reload();
                    App.msg('success', '操作成功');
                });
            });
        };
        var doDeletelockTask = function(id){
            // 正常状态
            App.dialog('确定删除该卡单任务？', '确定', function(){
                var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.DELETE_LOCK_TASK;
                var data = {id: id};
                App.ajaxPost(url, data, function(){
                    reload();
                    App.msg('success', '操作成功');
                });
            });
        };

        p.find('button[name="add-lock-task"]').click(function(){
            AddLockTaskModal.show();
        });

        var init = function(){
            pagination.init();
        };

        var reload = function(){
            pagination.init();
        };

        p.find('button[name="search"]').click(function(){
            reload();
        });

        return {
            init: init,
            reload: reload
        };
    })();

    var AddLockTaskModal = (function(){
        var modal = $('#modal-add-lock-task');
        var form = modal.find('form');
        var initForm = function(){
            form.validate({
                rules: {
                    lockType: {
                        required: true
                    },
                    amountMix: {
                        number: true
                    },
                    amountMax: {
                        number: true
                    }
                },
                messages: {
                    lockType: {
                        required: '该字段不能为空！'
                    }
                },
                invalidHandler: function(event, validator){
                },
                errorPlacement: function(error, element){
                    if($(element).closest('.form-group').attr('novalidate') != 'true'){
                        $(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
                    }
                },
                highlight: function(element){
                    if($(element).closest('.form-group').attr('novalidate') != 'true'){
                        $(element).closest('.form-group').removeClass('has-success').addClass('has-error');
                    }
                },
                unhighlight: function(element){
                    if($(element).closest('.form-group').attr('novalidate') != 'true'){
                        $(element).closest('.form-group').removeClass('has-error').addClass('has-success');
                        $(element).closest('.form-group').find('.help-block').empty().hide();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
                    }
                }
            });
            modal.find('[data-command="submit"]').click(function(){
                if(form.validate().form()){
                    doSubmit();
                }
            });
            form.find('select[name="lockType"]').change(function(){
                if($(this).val() === "1"){
                    form.find('input[name="playerNameList"]').parents('.form-group').show();
                    form.find('select[name="lockNumber"]').parents('.form-group').show();
                }else{
                    form.find('input[name="playerNameList"]').parents('.form-group').hide();
                    form.find('select[name="lockNumber"]').parents('.form-group').hide();
                }
            });
        };

        var doSubmit = function(){
            var agentNo = form.find('select[name="agentNo"]').val();
            var lockType = form.find('select[name="lockType"]').val();
            var lockNumber = form.find('select[name="lockNumber"]').val();
            var playerNameList = form.find('input[name="playerNameList"]').val().trim();
            if(lockType !== "1"){
                playerNameList = '';
                lockNumber = 0;
            }
            var issue = form.find('input[name="issue"]').val().trim();
            var amountMix = Number(form.find('input[name="amountMix"]').val());
            var amountMax = Number(form.find('input[name="amountMax"]').val());
            if(amountMax < amountMix){
                App.msg('error', '最小值不能大于最大值');
                return;
            }
            var lockRemark = form.find('input[name="lockRemark"]').val().trim();
            var lottery = form.find('select[name="lottery"]').val();
            var data = {
                agentNo: agentNo,
                lockType: lockType,
                lockNumber: lockNumber,
                playerNameList: playerNameList,
                issue: issue,
                amountMix: amountMix,
                amountMax: amountMax,
                lockRemark: lockRemark,
                lottery: lottery
            };
            var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.ADD_LOCK_TASK;
            App.ajaxPost(url, data, function(){
                modal.modal('hide');
                App.msg('success', '操作成功');
                TableDataList.reload();
                form.find('select[name="lockType"]').find('option').eq(0).attr('selected', true);
                form.find('input[name="playerNameList"]').val('');
                form.find('input[name="issue"]').val('');
                form.find('input[name="amountMix"]').val(0);
                form.find('input[name="amountMax"]').val(10000);
                form.find('input[name="lockRemark"]').val('');
                form.find('.fs-option.selected').trigger('click')
            });
        };
        var loadLottery = function(){
            var agentNo = form.find('select[name="agentNo"]').val();
            var e = form.find('select[name="lottery"]');
            e.empty();
            e.append('<option value="">全部</option>');
            var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.LIST_LOTTERY_AGENT;
            App.staticPost(url, {agentNo: agentNo}, function(result){
                if(result && result.data){
                    $.each(result.data, function(i, v){
                        e.append('<option value="' + v.lottery + '">' + v.gameName + '</option>');
                    });
                }
            });
        };

        var loadAgent = function(){
            var e = form.find('select[name="agentNo"]');
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
            App.staticPost(url, {}, function(result){
                $.each(result.data, function(i, v){
                    e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
                });
                loadLottery();
                e.change(loadLottery);
            });
        };

        var reset = function(){
            var formGroup = form.find('.form-group');
            form.find('.has-feedback').show();
            formGroup.removeClass('has-error');
            formGroup.removeClass('has-success');
            formGroup.find('.glyphicon').hide();
            formGroup.find('.help-block').empty().hide();
            form[0].reset();
            form.find('[data-visable="issue"]').hide();
        };

        var show = function(){
            reset();
            modal.modal('show');
        };

        var init = function(){
            loadAgent();
            initForm();
        };

        return {
            init: init,
            show: show
        };
    })();

    TableDataList.init();
    AddLockTaskModal.init();
});
