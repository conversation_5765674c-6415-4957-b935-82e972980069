var App = function() {

	var uuid = function(l) {
		var text = '';
		var arr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		for (var i = 0; i < l; i++) {
			text += arr.charAt(Math.floor(Math.random() * arr.length));
		}
		return text;
	}

	var urlParam = function(paramName) {
		var ss = window.location.search.substring(1),
			i, val, params = ss.split("&");
		for (i = 0; i < params.length; i++) {
			val = params[i].split("=");
			if (val[0] == paramName) {
				return unescape(val[1]);
			}
		}
		return null;
	}

	var ajaxPost = function(url, data, success, beforeSend, complete, error) {
		$.ajax({
			type: 'post',
			url: url,
			data: data,
			dataType: 'json',
			beforeSend: function() {
				if ($.isFunction(beforeSend)) {
					beforeSend();
				}
			},
			success: function(response) {
				if (response.code == '0') {
					if ($.isFunction(success)) {
						success(response.data);
					}
				}
				if (response.code != '0') {
					if (error) {
						error(response.message);
					} else {
						msg('error', response.message);
					}
				}
			},
			complete: function() {
				if ($.isFunction(complete)) {
					complete();
				}
			}
		});
	}

	function objectToQueryString(obj) {
		return Object.keys(obj).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`).join('&');
	}

	var ajaxformPost = function(url, data, success, beforeSend, complete, error) {
		// 创建一个 FormData 对象
		var formData = new FormData();

		// 追加文件
		const fileInput = document.getElementById('video');
		formData.append('video', fileInput.files[0], encodeURIComponent(fileInput.files[0].name));
		var urlWithParams = `${url}?${objectToQueryString(data)}`;

		$('#modal-add .modal-footer').hide();
		$('#loading').show();

		$.ajax({
			type: 'POST',
			url: urlWithParams,
			data: formData,
			processData: false,  // 不处理数据
			contentType: false,  // 不设置内容类型
			beforeSend: function() {
				if ($.isFunction(beforeSend)) {
					beforeSend();
				}
			},
			success: function(response) {
				$('#loading').hide();
				$('#modal-add .modal-footer').show();

				if (response.code == '0') {
					if ($.isFunction(success)) {
						success(response.data);
					}
				}
				if (response.code != '0') {
					if (error) {
						error(response.message);
					} else {
						msg('error', response.message);
					}
				}
			},
			complete: function() {
				$('#loading').hide();
				$('#modal-add .modal-footer').show();
				if ($.isFunction(complete)) {
					complete();
				}
			}

		});
	}

	var uploadMultipleFilesWithKeys = function(url, files, data, success, beforeSend, complete, error) {
		// 创建一个 FormData 对象
		var formData = new FormData();

		// 追加文件
		for (var i = 0; i < files.length; i++) {
			const fileInput = document.getElementById(files[i].inputId);

			// 检查 fileInput 是否存在
			if (!fileInput) {
				console.error(`找不到id为${files[i].inputId}的文件输入框`);
				if (error) {
					error(`找不到id为${files[i].inputId}的文件输入框`);
				}
				continue;  // 如果找不到对应的 fileInput，跳过这个文件上传
			}

			// 检查文件是否选择
			if (fileInput.files.length > 0) {
				formData.append(files[i].key, fileInput.files[0], encodeURIComponent(fileInput.files[0].name));
			} else {
				console.error(`文件输入框 ${files[i].inputId} 没有选择文件`);
				if (error) {
					error(`文件输入框 ${files[i].inputId} 没有选择文件`);
				}
			}
		}

		// 拼接 URL 参数
		var urlWithParams = `${url}?${objectToQueryString(data)}`;

		// 显示加载状态
		$('#loading').show();

		$.ajax({
			type: 'POST',
			url: urlWithParams,
			data: formData,
			processData: false,  // 不处理数据
			contentType: false,  // 不设置内容类型
			beforeSend: function() {
				if ($.isFunction(beforeSend)) {
					beforeSend();
				}
			},
			success: function(response) {
				$('#loading').hide();

				if (response.code == '0') {
					if ($.isFunction(success)) {
						success(response.data);
					}
				} else {
					if (error) {
						error(response.message);
					} else {
						msg('error', response.message);
					}
				}
			},
			complete: function() {
				$('#loading').hide();
				if ($.isFunction(complete)) {
					complete();
				}
			},
			error: function(jqXHR, textStatus, errorThrown) {
				$('#loading').hide();
				if (error) {
					error(textStatus + ': ' + errorThrown);
				} else {
					console.error(textStatus + ': ' + errorThrown);
				}
			}
		});
	}

	var staticPost = function(url, data, success) {
		$.ajax({
			type: 'post',
			url: url,
			data: data,
			dataType: 'json',
			success: function(response) {
				if ($.isFunction(success)) {
					success(response);
				}
			}
		});
	}

	var buildTbody = function(data) {
		var tr = $('<tr>');
		$.each(data, function(i, v) {
			tr.append('<td>' + v + '</td>');
		});
		return tr;
	}

	var dialog = function(message, sText, sCall, cText, cCall) {
		if (!sText) {
			sText = '确认';
		}
		if (!sCall) {
			sCall = function() {
			}
		}
		if (!cText) {
			cText = '取消';
		}
		if (!cCall) {
			cCall = function() {
			}
		}
		bootbox.dialog({
			message: message,
			title: '提示消息',
			buttons: {
				success: {
					label: '<i class="fa fa-check"></i> ' + sText,
					className: 'btn-primary',
					callback: sCall
				},
				danger: {
					label: '<i class="fa fa-undo"></i> ' + cText,
					className: 'btn-default',
					callback: cCall
				}
			}
		});
	}

	var msg = function(type, text) {
		var time = moment().format('YYYY-MM-DD HH:mm:ss');
		var message = '<span style="font-size:12px;">' + time + '</span><br/>' + text;

		Messenger().post({
			message: message,
			type: type,
			showCloseButton: true
		});
	}

	var diffNow = function(time) {
		var sTime = moment();
		var eTime = moment(time);
		var days = eTime.diff(sTime, 'days');
		if (days > 0) {
			var hours = eTime.subtract(days, 'days').diff(sTime, 'hours');
			return days + '天' + hours + '小时';
		}
		var hours = eTime.diff(sTime, 'hours');
		if (hours > 0) {
			var minutes = eTime.subtract(hours, 'hours').diff(sTime, 'minutes');
			return hours + '小时' + minutes + '分钟';
		}
		var minutes = eTime.diff(sTime, 'minutes');
		if (minutes > 0) {
			var seconds = eTime.subtract(minutes, 'minutes').diff(sTime, 'seconds');
			return minutes + '分钟' + seconds + '秒';
		}
		var seconds = eTime.diff(sTime, 'seconds');
		if (seconds > 0) {
			return seconds + '秒';
		}
		return 0;
	}

	return {
		uuid: uuid,
		urlParam: urlParam,
		ajaxPost: ajaxPost,
		ajaxformPost: ajaxformPost,
		uploadMultipleFilesWithKeys: uploadMultipleFilesWithKeys,
		staticPost: staticPost,
		buildTbody: buildTbody,
		dialog: dialog,
		msg: msg,
		diffNow: diffNow
	}

}();

var Route = {
	PATH: "./api",
	LOGIN: "/login",
	LOGOUT: "/logout",
	// 即时统计
	PLATFORM_RTS: "/platform-rts",
	// 自助对账
	PLATFORM_ARS: "/platform-ARS",
	// 任务状态
	TASK_STATUS: "/task-status",
	System: {
		PATH: "/system",
		// 列出系统白名单
		LIST_SYSTEM_WHITELIST: "/list-system-whitelist",
		// 添加系统白名单
		ADD_SYSTEM_WHITELIST: "/add-system-whitelist",
		// 启用系统白名单
		ENABLE_SYSTEM_WHITELIST: "/enable-system-whitelist",
		// 禁用系统白名单
		DISABLED_SYSTEM_WHITELIST: "/disbaled-system-whitelist",
		// 删除系统白名单
		DELETE_SYSTEM_WHITELIST: "/delete-system-whitelist",
		// 列出系统公告
		LIST_SYSTEM_NOTICE: "/list-system-notice",
		// 添加系统公告
		ADD_SYSTEM_NOTICE: "/add-system-notice",
		// 更新系统公告状态
		UPDATE_SYSTEM_NOTICE_STATUS: "/update-system-notice-status",
		// 更新系统公告置顶
		UPDATE_SYSTEM_NOTICE_STICK: "/update-system-notice-stick",
		// 编辑系统公告
		EDIT_SYSTEM_NOTICE: "/edit-system-notice",
		// 删除系统公告
		DELETE_SYSTEM_NOTICE: "/delete-system-notice",
		// 查询/更系统客服配置
		LIST_ONLINE_SERVICE: "/list-online-service",
		// 更新系统客服配置
		UPDATE_ONLINE_SERVICE: "/update-online-service",
		// 列出商务合作配置
		LIST_BUSINESS_COOPERATE: "/list-business-cooperate",
		// 更新商务合作配置
		UPDATE_BUSINESS_COOPERATE: "/update-business-cooperate",
		// 列出用户配置
		LIST_PLAYER_SERVICE_CONFIG: "/list-player-service-config",
		// 更新用户配置
		UPDATE_PLAYER_SYSTEM_CONFIG: "/update-player-system-config",
		// 列出聊天室配置
		LIST_CHAT_SERVICE_CONFIG: "/list-chat-service-config",
		// 更新聊天室配置
		UPDATE_CHAT_SYSTEM_CONFIG: "/update-chat-system-config",
		// 列出用户试玩配置
		LIST_TRY_SERVICE_CONFIG: "/list-try-service-config",
		// 更新用户试玩配置
		UPDATE_TRY_SYSTEM_CONFIG: "/update-try-system-config",
		// 列出游戏配置
		LIST_GAME_SERVICE_CONFIG: "/list-game-service-config",
		// 更新游戏配置
		UPDATE_GAME_SYSTEM_CONFIG: "/update-game-system-config",
		// 列出系统白名单
		LIST_LIVE_LINE: "/list-live-line",
		// 添加系统白名单
		ADD_LIVE_LINE: "/add-live-line",
		// 启用系统白名单
		EDIT_LIVE_LINE: "/edit-live-line",
		// 禁用系统白名单
		ENABLE_LIVE_LINE: "/enable-live-line",
		SORT_LIVE_LINE: "/sort-live-line",

		// 查询系统客服配置
		LIST_DOWNLOAD_CONFIG: "/list-download-config",
		// 更新系统客服配置
		UPDATE_DOWNLOAD_CONFIG: "/update-download-config",
		// 查询提现配置
		LIST_WITHDRAW_CONFIG: "/list-withdraw-config",
		// 更新提现配置
		UPDATE_WITHDRAW_CONFIG: "/update-withdraw-config",
		LIST_LOTTERY_CONFIG: "/list-lottery-config",
		UPDATE_LOTTERY_CONFIG: "/update-lottery-config",
		SYSTEM_LINE: "/system-line",
		// 用户类型列表
		LIST_PLAYER_ACCOUNT_TYPE: "/list-player-account-type",
		// 添加用户类型
		ADD_PLAYER_ACCOUNT_TYPE: "/add-player-account-type",
		// 修改用户类型
		UPDATE_PLAYER_ACCOUNT_TYPE: "/update-player-account-type",
		// 删除用户类型
		DELETE_PLAYER_ACCOUNT_TYPE: "/delete-player-account-type",
		SEARCH_AGENT_THIRD_LEVEL: "/search-agent-third-level",
		// 平台服务国家列表查询
		PLATFORM_COUNTRY_LIST : "/platform-country-list",
		// 平台服务国家启禁用
		PLATFORM_COUNTRY_ENABLE : "/platform-country-enable",
		// 平台服务国家排序
		PLATFORM_COUNTRY_SORT : "/platform-country-sort",
		// 平台发言词库启新增
		PLATFORM_THESAURUS_ADD : "/platform-thesaurus-add",
		// 平台发言词库启删除
		PLATFORM_THESAURUS_DELETE : "/platform-thesaurus-delete",
		// 平台发言词库启修改
		PLATFORM_THESAURUS_UPDATE : "/platform-thesaurus-update",
		// 平台发言词库列表查询
		PLATFORM_THESAURUS_LIST : "/platform-thesaurus-list",
		// 平台发言词库启禁用
		PLATFORM_THESAURUS_ENABLE : "/platform-thesaurus-enable",
		// 平台发言词库排序
		PLATFORM_THESAURUS_SORT : "/platform-thesaurus-sort",
		// USDT 汇率
		USDT_EXCHANGE_RATE: "/usdt-exchange-rate"

	},
	Admin: {
		PATH: "/admin",
		// 获取登录用户信息
		LOGIN_INFO: "/login-info",
		// 搜索用户
		SEARCH_ACCOUNT: "/search-account",
		// 列出用户
		LIST_ACCOUNT: "/list-account",
		// 添加用户
		ADD_ACCOUNT: "/add-account",
		// 更新账户状态
		UPDATE_ACCOUNT_STATUS: "/update-account-status",
		// 修改登录密码
		MODIFY_LOGIN_PASSWORD: "/modify-login-password",
		// 修改操作密码
		MODIFY_OPERATION_PASSWORD: "/modify-operation-password",
		// 修改我的登录密码
		MODIFY_MY_LOGIN_PASSWORD: "/modify-my-login-password",
		// 修改我的操作密码
		MODIFY_MY_OPERATION_PASSWORD: "/modify-my-operation-password",
		// 更新谷歌登录
		UPDATE_GOOGLE_LOGIN: "/update-google-login",
		// 重置谷歌绑定
		RESET_GOOGLE_BIND: "/reset-google-bind",
		// 列出角色
		LIST_ROLE: "/list-role",
		// 添加角色
		ADD_ROLE: "/add-role",
		// 更新角色
		UPDATE_ROLE: "/update-role",
		// 删除角色
		DELETE_ROLE: "/delete-role",
		// 列出菜单
		LIST_MENU: "/list-menu",
		// 更新菜单
		UPDATE_MENU: "/update-menu",
		// 列出权限
		LIST_ACCESS: "/list-access",
		// 更新权限
		UPDATE_ACCESS: "/update-access",
		// 列出目标菜单
		LIST_ACCOUNT_MENU: "/list-account-menu",
		// 列出目标权限
		LIST_ACCOUNT_ACCESS: "/list-account-access",
		// 添加目标权限
		ADD_ACCOUNT_ACCESS: "/add-account-access",
		// 删除目标权限
		DELETE_ACCOUNT_ACCESS: "/delete-account-access",
		// 重置目标权限
		RESET_ACCOUNT_ACCESS: "/reset-account-access",
		// 列出角色权限
		LIST_ROLE_ACCESS: "/list-role-access",
		// 添加角色权限
		ADD_ROLE_ACCESS: "/add-role-access",
		// 删除角色权限
		DELETE_ROLE_ACCESS: "/delete-role-access",
		// 查询管理员日志
		SEARCH_ADMIN_LOG: "/search-admin-log",
		// 查询管理员登录日志
		SEARCH_ADMIN_LOGIN_LOG: "/search-admin-login-log",
		// 修改查询报表的限制天数
		MODIFY_QUERY_LIMIT: "/modify-query-limit",
		// 下载后台操作日志
		DOWNLOAD_ADMIN_LOG: "/download-admin-log",
		// 添加目标权限全部
		ADD_ACCOUNT_ACCESS_ALL: "/add-account-access-all",
		// 删除目标权限全部
		DELETE_ACCOUNT_ACCESS_ALL: "/delete-account-access-all",
		// 添加角色权限全部
		ADD_ROLE_ACCESS_ALL: "/add-role-access-all",
		// 删除角色权限全部
		DELETE_ROLE_ACCESS_ALL: "/delete-role-access-all"
	},
	Agent: {
		PATH: "/agent",

		LIST_AGENT: "/list-agent",
		LIST_THIRD_AGENT: "/list-third-agent",
		LIST_PLATFORM_AGENT: "/list-platform-agent",

		LIST_AGENT_GAME_LOTTERY_INFO: "/list-agent-game-lottery-info",
		UPDATE_BAN_STATUS: "/update-ban-status",
		UPDATE_RECOMMEND: "/update-recommend",

		// 添加代理商
		ADD_AGENT: "/add-agent",
		AGENT_SYNC_THIRD_PLATFORM: "/agent-sync-third-platform",
		AGENT_SUPPLEMENT_PLATFORM_CONFIG: "/agent-supplement-platform-config",
		UPDATE_AGENT: "/update-agent",
		// 代理商列表
		SEARCH_AGENT: "/search-agent",
		//代理商绑定uid
		BINDING_UID: "/binding-uid",

		// 变更玩家父级
		UPDATE_PID: "/update-pid",
		//启用/禁用代理商
		UPDATE_AGENT_STATUS: "/update-agent-status",
		MANUAL_AGENT_RECHARGE: "/manual-agent-recharge",
		MANUAL_AGENT_ADD_BALANCE: "/manual-agent-add-balance",
		MANUAL_AGENT_WITHDRAW: "/manual-agent-withdraw",
		GET_AGENT_SECRETKEY: "/get-agent-secretkey",
		UPDATE_AGENT_WHITELIST: "/update-agent-whitelist",


		//清除登录错误次数
		RESET_ACCOUNT_LOGIN_FAILED_NUM: "/update-account-login-fail-num",

		//清除资金密码错误次数
		RESET_PAYMENT_PASSWORD_FAILED_NUM: "/update-payment-password-fail-num",

		// 添加代理商用户
		ADD_AGENT_ACCOUNT: "/add-agent-account",
		// 代理商账号列表
		SEARCH_AGENT_ACCOUNT: "/search-agent-account",
		// 添加代理商的玩家账号
		ADD_PLAYER_ACCOUNT: "/add-player-account",
		SAVE_PLATFORM_LINE: "/save-platform-line",
		LIST_PLATFORM_LINE: "/list-platform-line",
		SEARCH_PLAYER: "/search-player",
		GET_PLAYER_DETAILS: "/get-player-details",
		UPDATE_PLAYER_STATUS: "/update-player-status",
		UPDATE_PLAYER_MARKER_STATUS: "/update-player-marker-status",
		GET_DEFAULT_ACCOUNT_PASSWORD: "/get-default-account-password",
		MODIFY_LOGIN_PASSWORD: "/modify-login-password",
		MODIFY_WITHDRAW_PASSWORD: "/modify-withdraw-password",
		MODIFY_WITHDRAW_NAME: "/modify-withdraw-name",
		RESET_ACCOUNT_LOCK_TIME: "/reset-account-lock-time",
		CLEAR_ACCOUNT_WITHDRAW_LIMIT: "/clear-account-withdraw-limit",
		MODIFY_GOOGLE_LOGIN: "/modify-google-login",
		RESET_GOOGLE_BIND: "/reset-google-bind",
		RESET_ACCOUNT_PASSWORD_ERROR_COUNT: "/reset-account-password-error-count",
		PREPARE_MODIFY_POINT: "/prepare-modify-point",
		MODIFY_POINT: "/modify-point",
		MODIFY_LINE_POINT: "/modify-line-point",
		INCREASE_LINE_POINT: "/increase-line-point",
		MODIFY_PLAYER_TYPE: "/modify-player-type",
		MODIFY_EQUAL_LEVEL: "/modify-equal-level",
		MODIFY_ALLOW_TRANSFER: "/modify-allow-transfer",

		// 修改上级给下级下分权限
		MODIFY_APPLY_UP_DED_TRANSFER: "/modify-apply-up-ded-transfer",

		MANUAL_PLAYER_RECHARGE: "/manual-player-recharge",
		MANUAL_PLAYER_WITHDRAW: "/manual-player-withdraw",
		SEARCH_AGENT_BILL: "/search-agent-bill",
		SEARCH_AGENT_RECHARGE_RECORD: "/search-agent-recharge-record",
		// 补充值未到账
		PATCH_ACCOUNT_RECHARGE: "/patch-account-recharge",
		SEARCH_AGENT_WITHDRAW_RECORD: "/search-agent-withdraw-record",
		SEARCH_PLAYER_BILL: "/search-player-bill",
		SEARCH_PLAYER_GAME_RECORD: "/search-player-game-record",
		SEARCH_PLAYER_TRANSFER_RECORD: "/search-player-transfer-record",
		CANCEL_PLAYER_GAME_RECORD: "/cancel-player-game-record",
		GET_PLAYER_GAME_RECORD_DETAILS: "/get-player-game-details",
		SEARCH_PLAYER_GAME_REPORT: "/search-player-game-report",
		SEARCH_PLAYER_OPINION: "/search-player-opinion",
		PROCESS_PLAYER_OPINION: "/process-player-opinion",
		SEARCH_PLAYER_REWARD: "/search-player-reward",
		SEARCH_AGENT_LOGIN_LOG: "/search-agent-login-log",
		CANCEL_PLAYER_RECHARGE: "/calcel-player-recharge",
		SEARCH_PLAYER_RECHARGE_RECORD: "/search-player-recharge-record",
		GET_PLAYER_RECHARGE_RECORD_DETAIL: "/get-player-recharge-record-detail",
		SEARCH_PLAYER_WITHDRAW_RECORD: "/search-player-withdraw-record",
		UPDATE_PLAYER_WITHDRAW_RECORD_STATUS: "/update-player-withdraw-record-status",
		MANUAL_WITHDRAW_DEDUCTION: "/manual-withdraw-deduction",
		SEARCH_PLAYER_CARD_BANK: "/search-player-card-bank",
		ADD_PLAYER_CARD_BANK: "/add-player-card-bank",
		ENABLE_PLAYER_CARD_BANK: "/enable-player-card-bank",
		EDIT_PLAYER_CARD_BANK: "/edit-player-card-bank",
		UNLOCK_PLAYER_CARD_BANK: "/unlock-player-card-bank",
		LOCK_PLAYER_CARD_BANK: "/lock-player-card-bank",
		DELETE_PLAYER_CARD_BANK: "/delete-player-card-bank",
		SEARCH_PLAYER_CARD_USDT: "/search-player-card-usdt",
		ADD_PLAYER_CARD_USDT: "/add-player-card-usdt",
		ENABLE_PLAYER_CARD_USDT: "/enable-player-card-usdt",
		EDIT_PLAYER_CARD_USDT: "/edit-player-card-usdt",
		UNLOCK_PLAYER_CARD_USDT: "/unlock-player-card-usdt",
		LOCK_PLAYER_CARD_USDT: "/lock-player-card-usdt",
		DELETE_PLAYER_CARD_USDT: "/delete-player-card-usdt",
		SEARCH_ACCOUNT_ACTION_LOG: "/search-account-action-log",
		CLEAN_AGENT_DATA: "/clean-agent-data",
		SEARCH_AGENT_THIRD_LEVEL: "/search-agent-third-level",
		SEARCH_PLAYER_CARD_ALIPAY: "/search-player-card-alipay",
		ADD_PLAYER_CARD_ALIPAY: "/add-player-card-alipay",
		ENABLE_PLAYER_CARD_ALIPAY: "/enable-player-card-alipay",
		EDIT_PLAYER_CARD_ALIPAY: "/edit-player-card-alipay",
		UNLOCK_PLAYER_CARD_ALIPAY: "/unlock-player-card-alipay",
		LOCK_PLAYER_CARD_ALIPAY: "/lock-player-card-alipay",
		DELETE_PLAYER_CARD_ALIPAY: "/delete-player-card-alipay",
		SEARCH_PLAYER_CARD_M: "/search-player-card-m",
        ADD_PLAYER_CARD_M: "/add-player-card-m",
        ENABLE_PLAYER_CARD_M: "/enable-player-card-m",
        EDIT_PLAYER_CARD_M: "/edit-player-card-m",
        UNLOCK_PLAYER_CARD_M: "/unlock-player-card-m",
        LOCK_PLAYER_CARD_M: "/lock-player-card-m",
        DELETE_PLAYER_CARD_M: "/delete-player-card-m",
		SEARCH_PLAYER_CARD_HH5: "/search-player-card-hh5",
        ADD_PLAYER_CARD_HH5: "/add-player-card-hh5",
        ENABLE_PLAYER_CARD_HH5: "/enable-player-card-hh5",
        EDIT_PLAYER_CARD_HH5: "/edit-player-card-hh5",
        UNLOCK_PLAYER_CARD_HH5: "/unlock-player-card-hh5",
        LOCK_PLAYER_CARD_HH5: "/lock-player-card-hh5",
        DELETE_PLAYER_CARD_HH5: "/delete-player-card-hh5",
		SEARCH_PLAYER_CARD_OKG: "/search-player-card-okg",
        ADD_PLAYER_CARD_OKG: "/add-player-card-okg",
        ENABLE_PLAYER_CARD_OKG: "/enable-player-card-okg",
        EDIT_PLAYER_CARD_OKG: "/edit-player-card-okg",
        UNLOCK_PLAYER_CARD_OKG: "/unlock-player-card-okg",
        LOCK_PLAYER_CARD_OKG: "/lock-player-card-okg",
        DELETE_PLAYER_CARD_OKG: "/delete-player-card-okg"
	},
	AgentBill: {
		PATH: "/agent-bill-record",
		LIST_AGENT_BILL_RECORD: "/list-agent-bill-record",
		BILL_PAYMENT_CONFIRM: "/bill-payment-confirm"
	},
	Promote: {
		PATH: "/promote",
		SEARCH_PROMOTE_CONFIG: "/search-promote-config",
		ADD_PROMOTE_CONFIG: "/add-promote-config",
		EDIT_PROMOTE_CONFIG: "/edit-promote-config",
		SEARCH_COMMISSION_RECORD: "/search-commission-record",
		SEARCH_REBATE_RECORD: "/search-rebate-record",
	},
	GameLottery: {
		PATH: "/game-lottery",

		STATIC_LIST_TYPE: "/static-list-type",
		STATIC_LIST_INFO: "/static-list-info",
		STATIC_LIST_METHOD: "/static-list-method",

		// 游戏概率统计分析
		PROBABILITY_ANALYSIS: "/probability-analysis",

		LIST_LOTTERY_TYPE: "/list-lottery-type",
		UPDATE_LOTTERY_TYPE_STATUS: "/update-lottery-type-status",
		UPDATE_PRE_STEP_LOTTERY_TYPE: "/update-pre-step-lottery-type",
		UPDATE_NEXT_STEP_LOTTERY_TYPE: "/update-next-step-lottery-type",

		SEARCH_GAME_VIDEO: "/search-game-video",
		IMPORT_GAME_VIDEO: "/import-game-video",
		// 	播放游戏开奖视频
		PLAY_GAME_VIDEO: "/play-game-video",

		// 	播放游戏开奖视频
		PUSH_GAME_VIDEO: "/push-game-video",

		// 	修改游戏开奖视频
		EDIT_GAME_VIDEO: "/edit-game-video",

		// 	禁用游戏开奖视频
		BAN_GAME_VIDEO: "/ban-game-video",

		// 	删除游戏开奖视频
		DELETE_GAME_VIDEO: "/delete-game-video",

		LIST_LOTTERY_INFO: "/list-lottery-info",
		EDIT_LOTTERY_INFO: "/edit-lottery-info",
		UPDATE_LOTTERY_INFO_STATUS: "/update-lottery-info-status",
		UPDATE_LOTTERY_INFO_MAINTAIN_STATUS: "/update-lottery-info-maintain-status",
		UPDATE_LOTTERY_INFO_KILL_STATUS: "/update-lottery-info-kill-status",
		UPDATE_PRE_STEP_LOTTERY_INFO: "/update-pre-step-lottery-info",
		UPDATE_NEXT_STEP_LOTTERY_INFO: "/update-next-step-lottery-info",

		SET_KILL_PROPORTION: "/set-kill-proportion",
		// 设置杀率
		EDIT_KILLRATE_SETTING: "/edit-killrate-setting",
		// 请求杀率设置
		REQUEST_KILLRATE_SETTING: "/request-killrate-setting",

		// 代理商绑定游戏直播间
		LIST_LOTTERY_AGENT: "/list-lottery-agent",
		// 代理商绑定游戏直播间（按游戏类型分组）
		LIST_LOTTERY_AGENT_BIND: "/list-lottery-agent-bind",
		BIND_AGENT_GAME_LOTTERY_INFO: "/bind-agent-game-lottery-info",

		// 游戏分组
		LIST_GAME_LOTTERY_GROUP: "/list-game-lottery-group",
		ADD_GAME_LOTTERY_GROUP: "/add-game-lottery-group",
		EDIT_GAME_LOTTERY_GROUP: "/edit-game-lottery-group",
		DELETE_GAME_LOTTERY_GROUP: "/delete-game-lottery-group",
		// 游戏列表
		LIST_GAME_LOTTERY_INFO_LIST: "/list-game-lottery-info-list",
		STATUS_GAME_LOTTERY_INFO_LIST: "/status-game-lottery-info-list",
		EDIT_GAME_LOTTERY_INFO_LIST: "/edit-game-lottery-info-list",

		SEARCH_GAME_LIVE_RECORD: "/search-game-live-record",

		// 列出游戏玩法
		LIST_LOTTERY_METHOD: "/list-lottery-method",
		// 编辑游戏玩法
		UPDATE_LOTTERY_METHOD: "/update-lottery-method",
		UPDATE_LOTTERY_METHOD_BONUS: "/update-lottery-method-bonus",
		UPDATE_LOTTERY_METHOD_STATUS: "/update-lottery-method-status",

		// 列出游戏开奖时间
		LIST_LOTTERY_OPEN_TIME: "/list-lottery-open-time",
		// 列出游戏开奖号码
		LIST_LOTTERY_OPEN_CODE: "/list-lottery-open-code",
		SUBMIT_LOTTERY_OPEN_CODE: "/submit-lottery-open-code",
		ADD_LOTTERY_OPEN_CODE: "/add-lottery-open-code",
		UPDATE_LOTTERY_OPEN_CODE: "/update-lottery-open-code",
		// 反结算
		REVISE_LOTTERY_OPEN_CODE: "/revise-lottery-open-code",

		// 撤销
		CANCEL_LOTTERY_OPEN_CODE: "/cancel-lottery-open-code",

		SEARCH_GAME_ORDER_ISSUE_RECORD: "/search-game-order-issue-record",
		SEARCH_GIFT: "/search-gift",
		ADD_GIFT: "/add-gift",
		EDIT_GIFT: "/edit-gift",
		UPDATE_GIFT_SORT: "/update-gift-sort",
		BAN_GIFT: "/ban-gift",
		DELETE_GIFT: "/delete-gift",
		//排行榜
		PROFIT_RANKING: "/profit-ranking",
		ADD_PROFIT_RANKING: "/add-profit-ranking",
		UPDATE_PROFIT_RANKING: "/update-profit-ranking",
		DELETE_PROFIT_RANKING: "/delete-profit-ranking",
		// 亏损量预警配置
		SEARCH_EARLY_WARN_CONFIG: "/search-early-warn-config",
		ADD_EARLY_WARN_CONFIG: "/add-early-warn-config",
		UPDATE_EARLY_WARN_CONFIG: "/update-early-warn-config",
		UPDATE_EARLY_WARN_CONFIG_STATUS: "/update-early-warn-config-status",
		GET_EARLY_WARN_CONFIG: "/get-early-warn-config",
		SET_EARLY_WARN_CONFIG_LOTTERY: "/set-early-warn-config-lottery",

		// 亏损量预警记录
		SEARCH_EARLY_WARN_RECORD: "/search-early-warn-record",
		SEARCH_EARLY_WARN_RECORD_COUNT: "/search-early-warn-record-count",
		CLEAR_EARLY_WARN: "/clear-early-warn",
		BATCH_CLEAR_EARLY_WARN: "/batch-clear-early-warn",
		SEARCH_EARLY_WARN_AUTO_DISABLE_RECORD: "/search-early-warn-auto-disable-record",
        // 卡单
        SEARCH_LOCK_TASK: "/search-lock-task",
        ADD_LOCK_TASK: "/add-lock-task",
        START_LOCK_TASK: "/start-lock-task",
        DELETE_LOCK_TASK: "/delete-lock-task",
        SEARCH_LOCK_ORDER_RECORD: "/search-lock-order_record"
	},
	// 抽水配置
	GamePumpConfig: {
		PATH: "/game-pump-config",
		LIST: "/list",
		GET: "/get",
		SAVE: "/save",
		UPDATE: "/update",
		DELETE: "/delete",
		UPDATE_STATUS: "/updateStatus"
	},
	Report: {
		PATH: "/report",
		//手动生成报表
		MANUAL_REPORT: "/manual-report",

		// 	玩家报表
		SEARCH_PLAYER_REPORT: "/search-player-report",

		// 	三方玩家报表
		SEARCH_THIRD_PLAYER_REPORT: "/search-third-player-report",

		// 	平台玩家报表
		SEARCH_PLATFORM_PLAYER_REPORT: "/search-platform-player-report",

		// 代理商报表
		SEARCH_AGENT_REPORT: "/search-agent-report",

		// 三方代理商报表
		SEARCH_THIRD_AGENT_REPORT: "/search-third-agent-report",

		// 平台代理商报表
		SEARCH_PLATFORM_AGENT_REPORT: "/search-platform-agent-report",

		// 游戏总报表
		SEARCH_GAME_TYPE_REPORT: "/search-game-type-report",

		// 游戏玩法报表
		SEARCH_GAME_METHOD_REPORT: "/search-game-method-report",

		// 游戏彩种报表
		SEARCH_GAME_LOTTERY_REPORT: "/search-game-lottery-report",

		SEARCH_PLATFORM_AGENT_PLAYER_TEAM_REPORT: "/search-platform-agent-player-team-report",

		SEARCH_PLATFORM_AGENT_PLAYER_PERSON_REPORT: "/search-platform-agent-player-person-report",

		SEARCH_PLATFORM_AGENT_PLAYER_TEAM_TOTAL_REPORT: "/search-platform-agent-player-team-total-report",

	},
	Monopoly: {
		PATH: "/monopoly",

		GET_CONFIG_INFO: "/get-config-info",


		SEARCH_BET_RECORD: "/search-bet-record",


		SEARCH_PLAYER_BET_DETAIL: "/search-player-bet-detail",


		SEARCH_POOL_BILL: "/search-pool-bill",

		// 	获取配置信息
		ADD_CONFIG_INFO: "/add-config-info",
		EDIT_CONFIG_INFO: "/edit-config-info",

		// 	奖金池管理员减
		EDIT_POOL_SUB: "/edit-pool-sub",
		// 	奖金池管理员加
		EDIT_POOL_PLUS: "/edit-pool-plus",
		EDIT_RISE_AMOUNT: "/edit-rise-amount"
	},
	Operations: {
		PATH: "/operations",

		// 查询盈亏走势
		SEARCH_PROFIT_TABLE: "/search-profit-table",

		// 查询注册玩家走势
		SEARCH_REGISTER_PLAYER_TABLE: "/search-register-player-table",

		// 查询游戏玩家走势
		SEARCH_ACTIVITY_PLAYER_TABLE: "/search-activity-player-table",
	},
	Activity: {
		PATH: "/activity",
		LIST_ACTIVITY: "/list-activity",
		AGENT_LIST_ACTIVITY: "/agent-list-activity",
		// 列出活动配置
		SEARCH_ACTIVITY_CONFIG: "/search-activity-config",
		// 活动活动配置
		GET_ACTIVITY_CONFIG: "/get-activity-config",
		// 添加活动配置
		ADD_ACTIVITY_CONFIG: "/add-activity-config",
		// 更新活动配置
		UPDATE_ACTIVITY_CONFIG: "/update-activity-config",
		UPLOAD_ACTIVITY_IMAGE: "/upload-activity-image",
		// 更新活动配置规则
		UPDATE_ACTIVITY_CONFIG_RULES: "/update-activity-config-rules",
		// 更新活动状态
		UPDATE_ACTIVITY_CONFIG_STATUS: "/update-activity-config-status",
		// 删除活动
		DELETE_ACTIVITY_CONFIG_STATUS: "/delete-activity-config-status",
		// 列出活动奖励记录
		SEARCH_ACTIVITY_REWARD_RECORD: "/search-activity-reward-record",
		// 获取互斥活动列表
		GET_MUTUAL_EXCLUSION: "/get-mutual-exclusion",
		// 更新互斥活动清单
		UPDATE_MUTUAL_EXCLUSION: "/update-mutual-exclusion"
	},
	Contract: {
		PATH: "/contract",
		LIST_PLATFORM_CONTRACT: "/list-platform-contract",
		ADD_PLATFORM_CONTRACT: "/add-platform-contract",
		EDIT_PLATFORM_CONTRACT: "/edit-platform-contract",
		PLATFORM_CONTRACT_STATUS: "/platform-contract-status",
		REFRESH_PLATFORM_CONTRACT: "/refresh-platform-contract",
		DELETE_PLATFORM_CONTRACT: "/delete-platform-contract",
		LIST_PLAYER_CONTRACT: "/list-player-contract",
		EDIT_PLAYER_CONTRACT: "/edit-player-contract",
		DELETE_PLAYER_CONTRACT: "/delete-player-contract",
		LIST_CONTRACT_RECORD: "/list-contract-record",
		DRAW_CONTRACT_RECORD: "/draw-contract-record"
	},
	Payment: {
		PATH: "/payment",
		LIST_PAYMENT_BANK: "/list-payment-bank",
		SEARCH_PAYMENT_THIRD: "/search-payment-third",
		UPDATE_PAYMENT_THIRD: "/update-payment-third",

		LIST_PAYMENT_USDT_HUILV: "/list-payment-usdt-huilv",
		UPDATE_PAYMENT_USDT_HUILV: "/update-payment-usdt-huilv",

		SEARCH_PAYMENT_TRANSFER: "/search-payment-transfer",
		ADD_PAYMENT_TRANSFER: "/add-payment-transfer",
		UPDATE_PAYMENT_TRANSFER: "/update-payment-transfer",
		CLEAR_PAYMENT_TRANSFER: "/clear-payment-transfer",
		DELETE_PAYMENT_TRANSFER: "/delete-payment-transfer",
		UPDATE_PAYMENT_TRANSFER_STATUS: "/update-payment-transfer-status",

		SEARCH_PAYMENT_THIRD_PAY: "/search-payment-third-pay",
		ADD_PAYMENT_THIRD_PAY: "/add-payment-third-pay",
		UPDATE_PAYMENT_THIRD_PAY: "/update-payment-third-pay",
		UPDATE_PAYMENT_THIRD_PAY_KEY: "/update-payment-third-pay-key",
		CLEAR_PAYMENT_THIRD_PAY: "/clear-payment-third-pay",
		DELETE_PAYMENT_THIRD_PAY: "/delete-payment-third-pay",
		UPDATE_PAYMENT_THIRD_PAY_STATUS: "/update-payment-third-pay-status",

		SEARCH_PAYMENT_THIRD_REMIT: "/search-payment-third-remit",
		ADD_PAYMENT_THIRD_REMIT: "/add-payment-third-remit",
		UPDATE_PAYMENT_THIRD_REMIT: "/update-payment-third-remit",
		UPDATE_PAYMENT_THIRD_REMIT_KEY: "/update-payment-third-remit-key",
		DELETE_PAYMENT_THIRD_REMIT: "/delete-payment-third-remit",
		UPDATE_PAYMENT_THIRD_REMIT_STATUS: "/update-payment-third-remit-status",

		UPDATE_WITHDRAW_REMIT_ID: "/update-withdraw-remit-id",
		LIST_PAYMENT_THRID_REMIT_NORMAL: "/list-payment-thrid-remit-normal",
		LOCK_PLAYER_WITHDRAW: "/lock-player-withdraw",
		UNLOCK_PLAYER_WITHDRAW: "/unlock-player-withdraw",
		REFUSE_PLAYER_WITHDRAW: "/refuse-player-withdraw",
		COMPLETED_PLAYER_WITHDRAW: "/completed-player-withdraw",
		// 找出符合条件的USDT交易记录
		LIST_PAYMENT_USDT_TX_HASH: "/list-payment-usdt-tx-hash"
	},
	Live: {
		PATH: "/live",
		SEARCH_LIVE_INFO: "/search-live-info"
	},
	Flyingthrow: {
		PATH: "/flyingthrow",
		SEARCH_FLYINGTHROW: "/search-flyingthrow",
		ADD_FLYINGTHROW: "/add-flyingthrow",
		UPDATE_FLYINGTHROW: "/update-flyingthrow",
		UPDATE_FLYINGTHROW_STATUS: "/update-flyingthrow-status"
	},
	GameSimulation: {
		PATH: "/game-simulation",
		// 列出游戏账号
		SEARCH_GAME_ACCOUNT: "/search-game-account",
		// 列出转账记录
		SEARCH_GAME_TRANSFER: "/search-game-transfer",
		// 修改账号状态
		MODIFY_GAME_ACCOUNT_STATUS: "/modify-game-account-status",
		// 列出游戏
		SEARCH_SIMULATION_GAME: "/search-simulation-game",
		// 修改游戏状态
		MODIFY_GAME_STATUS: "/modify-game-status",
		// 获取所有游戏平台
		STATIC_LIST_PLATFORM: "/static-list-platform",
		// 获取所有生效游戏平台
		STATIC_LIST_PLATFORM_ACTIVE: "/static-list-platform-active",
		// 新增游戏
		ADD_GAME_SIMULATION: "/add-game-simulation",
		// 修改游戏
		EDIT_GAME_SIMULATION: "/edit-game-simulation",
		// 修改密钥
		EDIT_GAME_SIMULATION_KEY: "/edit-game-simulation-key",
		// 列出游戏记录
		SEARCH_GAME_PLAYRECORD: "/search-game-playrecord",
		// 列出游戏个人报表
		SEARCH_GAME_REPORT: "/search-game-report",
		// 列出游戏平台报表
		SEARCH_GAME_INFO_REPORT: "/search-game-info-report",
		// 列出所有游戏（game_simulation_type_platform）
		LIST_ALL_GAMES: "/list-all-games",
		// 列出所有游戏（game_simulation）
		LIST_ALL_SIMULATION_GAMES: "/list-all-game-simulations",
		// 游戏即时统计
		FINANCIAL_STATISTIC: "/financial-statistic",
		// 查询修正游戏报表请求
		SEARCH_GAME_REPORT_RECRAWL: "/search-game-report-recrawl",
		// 新增修正游戏报表请求
		ADD_GAME_REPORT_RECRAWL: "/add-game-report-recrawl",
		// 查询Flash Tech订单
		SEARCH_FLASH_TECH_REPORT: "/search-flash-tech-report",
		// 查询游戏平台详细订单
		SEARCH_GAME_PLATFORM_DETAIL_ORDER: "/search-game-platform-detail-order",
		// 强制转出第三方游戏余额
		MANUAL_TRANSFER_GAME_TO_LOTTERY: "/manual-transfer-game-to-lottery",
		// 强制转出第三方游戏余额
		MANUAL_TRANSFER_GAME_TO_LOTTERY2: "/manual-transfer-game-to-lottery2",
		// 列出游戏团队报表
		SEARCH_GAME_TEAM_REPORT : "/search-game-team-report",
		SEARCH_GAME_TEAM_REPORT_NEW : "/search-game-team-report-new",
		SEARCH_GAME_TEAM_TOTAL_REPORT : "/search-game-team-total-report",
		// 查询平台场馆列表
		PLAT_PLATFORM_LIST: "/plat-platform-list",
		// 更改平台场馆状态
		PLAT_PLATFORM_UPDATE_STATUS: "/plat-platform-update-status",
		// 更改平台场馆推荐
		PLAT_PLATFORM_UPDATE_RECOMMEND: "/plat-platform-update-recommend"
	},
	Account: {
		PATH: "/account",
		// 列出用户类型
		STATIC_LIST_ACCOUNT_TYPE: "/static-list-account-type",
	},
	PlatformAgentPlayer: {
		PATH: "/platform-agent-player",
		SEARCH_PLATFORM_AGENT_PLAYER_REPORT: "/search-platform-agent-player-report",
	}
}

var DataFormat = {
	Default: {
		status: function(code) {
			if (code == 0) {
				return '正常';
			} else {
				return '禁用';
			}
		},
		enable: function(code) {
			if (code == 1) {
				return '启用';
			} else {
				return '禁用';
			}
		},
		language: function(code) {
			if (code == 1) {
				return '中文';
			}
			if (code == 2) {
				return '英文';
			}
		},
		agentBillPayStatus: function(code) {
			if (code == 0) {
				return '未支付';
			}
			if (code == 1) {
				return '支付中';
			}
			if (code == 2) {
				return '已支付';
			}
		},
		playerType: function(code) {
			if (code == 1) {
				return '玩家';
			}
			if (code == 2) {
				return '代理';
			}
			if (code == 9) {
				return '游客';
			}
			return '未知';
		},
		settleStatus: function(code) {
			if (code == -1) {
				return '待确认';
			}
			if (code == 0) {
				return '未开奖';
			}
			if (code == 1) {
				return '已开奖';
			}
			if (code == 2) {
				return '已撤销';
			}
		},
		useStatus: function(code) {
			if (code == 0) {
				return '未使用';
			}
			if (code == 1) {
				return '已使用';
			}
		},
		videoFormat: function(code) {
			if (code == 1) {
				return 'MP4';
			}
			if (code == 2) {
				return 'FLV';
			}
			if (code == 3) {
				return 'MKV';
			}
			if (code == 4) {
				return 'MOV';
			}
			if (code == 5) {
				return 'AVI';
			}
			if (code == 6) {
				return 'TS';
			}
			if (code == 7) {
				return 'WebM';
			}
			return '未知';
		}
	},
	Promote: {
		execute: function(code) {
			if (code == 1) {
				return '日发放';
			} else if (code == 2) {
				return '周发放';
			} else if (code == 3) {
				return '半月发放';
			} else if (code == 4) {
				return '月发放';
			} else if (code == 5) {
				return '季度发放';
			}
		},
		promoteType: function(code) {
			if (code == 1) {
				return '彩票';
			} else if (code == 2) {
				return '三方';
			}else {
				return '未知';
			}
		}
	},
	Activity: {
		status: function(code) {
			if (code == -2) {
				return '暂停';
			}
			if (code == -1) {
				return '禁用';
			}
			if (code == 0) {
				return '正常';
			}
		},
		drawType: function(code) {
			if (code == 1) {
				return '系统自动';
			}
		},
		drawStatus: function(code) {
			if (code == 0) {
				return '未发放';
			} else if (code == 1) {
				return '已发放';
			}
		},
		activityTypeName: function(code) {
			if (code === 1) {
				return '邀请活动';
			} else if (code === 2) {
				return '新人活动';
			} else if (code === 3) {
				return '首充活动';
			} else if (code === 4) {
				return '每日USDT首充活动';
			} else if (code === 5) {
				return '彩票工资';
			} else if (code === 6) {
				return '彩票分红';
			} else if (code === 7) {
				return '三方工资';
			} else if (code === 8) {
				return '三方分红';
			} else {
				return '未知';
			}
		}
	},
	Contract: {
		contractType: function(code) {
			if(code === 1) {
				return "彩票契约工资";
			}else if(code === 2) {
				return "彩票契约分红";
			}else if(code === 3) {
				return "三方契约工资";
			}else if(code === 4) {
				return "三方契约分红";
			}else {
				return "未知";
			}
		},
		targetType: function(code) {
			if(code === 1) {
				return "团队包含本人";
			}else if(code === 2) {
				return "团队不包含本人";
			}else {
				return "未知";
			}
		},
		cycleType: function(code) {
			if(code === 1) {
				return "日";
			}else if(code === 2) {
				return "周";
			}else if(code === 3) {
				return "半月";
			}else if(code === 4) {
				return "月";
			}else if(code === 5) {
				return "10分钟";
			}else if(code === 6) {
				return "20分钟";
			}else if(code === 7) {
				return "30分钟";
			}else if(code === 8) {
				return "1小时";
			}else {
				return "未知";
			}
		},
		drawType: function(code) {
			if(code === 1) {
				return "人工发放";
			}else if(code === 2) {
				return "自动发放";
			}else {
				return "未知";
			}
		},
		contractStatus: function(code) {
			if(code === 1) {
				return "启用";
			}else if(code === 2) {
				return "禁用";
			}else {
				return "未知";
			}
		},
		achieveStatus: function(code) {
			if(code === 1) {
				return "未达标";
			}else if(code === 2) {
				return "已达标";
			}else {
				return "未知";
			}
		},
		drawStatus: function(code) {
			if(code === 0) {
				return "待处理";
			}else if(code === 1) {
				return "已发放";
			}else if(code === 2) {
				return "已拒绝";
			}else {
				return "未知";
			}
		}
	},
	PlayerCard: {
		cardStatus: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
			return '未知';
		}
	},
	Agent: {
		currency: function(code) {
			if (code === 2) {
				return 'CNY';
			} else if (code === 1) {
				return 'USDT';
			}
			return '';
		},
		branching: function(code) {
			if (code === 1) {
				return '是';
			} else if (code === 0) {
				return '否';
			}
			return '';
		},
		settleMode: function(code) {
			if (code === 1) {
				return '预付模式';
			} else if (code === 2) {
				return '月结模式';
			}
			return '';
		},
		agentType: function(code) {
			if (code === 2) {
				return '平台';
			} else if (code === 1) {
				return '三方游戏';
			}
			return '';
		},
		agentLevel: function(code) {
			if (code === 1) {
				return '股东';
			} else if (code === 2) {
				return '总代理商';
			} else if (code === 3) {
				return '代理商';
			}
			return '厂商';
		},
		lineType: function(code) {
			if (code === 0) {
				return 'WEB';
			} else if (code === 1) {
				return 'APP';
			} else if (code === 2) {
				return 'SERVER';
			} else if (code === 3) {
				return 'API';
			}
			return '未知';
		}
	},
	AgentAccount: {
		billType: function(code) {
			if (code === 1) {
				return '代理商上分';
			}
			if (code === 2) {
				return '代理商下分';
			}
			if (code === 3) {
				return '玩家上分';
			}
			if (code === 4) {
				return '玩家下分';
			}
			if (code === 5) {
				return '活动';
			}
			if (code === 6) {
				return '额度增加';
			}
			if (code === 7) {
				return '额度减少';
			}
			if (code === 8) {
				return '额度恢复';
			}
		},
		withdrawType: function(code) {
			if (code === 0) {
				return '线上提现';
			}
			if (code === 1) {
				return '人工下分';
			}
		},
		rechargeType: function(code) {
			if (code === 0) {
				return '线上充值';
			}
			if (code === 1) {
				return '人工充值';
			}
		},
	},
	AgentPlayer: {
		status: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
			if (code == -2) {
				return '冻结';
			}
		},
		bindStatus: function(code) {
			if (code == 0) {
				return '未绑定';
			}
			if (code == 1) {
				return '已绑定';
			}
		},
		orderStatus: function(code) {
			if (code === 0) {
				return '待结算';
			}
			if (code === 1) {
				return '未中奖';
			}
			if (code === 2) {
				return '已中奖';
			}
			if (code === 3) {
				return '已撤单';
			}
			if (code === 4) {
				return '已作废';
			}
		},
		billType: function(code) {
			if (code === 1) {
				return '充值';
			}
			if (code === 2) {
				return '提现';
			}
			if (code === 3) {
				return '投注';
			}
			if (code === 4) {
				return '派奖';
			}
			if (code === 5) {
				return '佣金';
			}
			if (code === 6) {
				return '活动';
			}
			if (code === 7) {
				return '管理员增';
			}
			if (code === 8) {
				return '管理员减';
			}
			if (code === 9) {
				return '打赏';
			}
			if (code === 10) {
				return '系统撤单';
			}
			if (code === 11) {
				return '提现退回';
			}
			if (code === 12) {
				return '修正派奖';
			}
			if (code === 13) {
				return '用户撤单';
			}
			if (code === 15) {
				return '返水';
			}
			if (code === 16) {
				return '中心转三方';
			}
			if (code === 17) {
				return '三方转中心';
			}
			if (code === 18) {
				return '契约工资';
			}
			if (code === 19) {
				return '契约分红';
			}
			if (code === 21) {
				return '上下级转账';
			}
			if (code === 22) {
				return '代理返点';
			}
			return '未知类型';
		},
		transferType: function(code) {
			if (code === 1) {
				return '转入游戏';
			}
			if (code === 2) {
				return '转出游戏';
			}
		},
		rewardType: function(code) {
			if (code === 1) {
				return '打赏';
			}
			if (code === 2) {
				return '送礼物';
			}
		},

		onlineStatus: function(code) {
			if (code === 0) {
				return '离线';
			}
			if (code === 1) {
				return '在线';
			}
		},
		markerStatus: function(code) {
			if (code === 0) {
				return '正常';
			}
			if (code === 1) {
				return '异常';
			}
		},
		feedbackType: function(code) {
			if (code === 1) {
				return '进入游戏慢';
			}
			if (code === 2) {
				return '游戏投注卡';
			}
			if (code === 3) {
				return '视频太卡';
			}
			if (code === 4) {
				return '主播不给力';
			}
			if (code === 5) {
				return '游戏界面不喜欢';
			}
			if (code === 6) {
				return '画质不清晰';
			}
			if (code === 7) {
				return '其他';
			}
		},
		processStatus: function(code) {
			if (code === 0) {
				return '未处理';
			}
			if (code === 1) {
				return '已处理';
			}
		},
	},

	dealMethodType: {
		dealMethod: function(code) {
			if (code == 0) {
				return '系统充值';
			}
			if (code == 1) {
				return '自动发放';
			}
			return '未知';
		},
		dealCycleType: function(code) {
			if (code == 1) {
				return '日结';
			}
			if (code == 2) {
				return '周结';
			}
			if (code == 3) {
				return '半月结';
			}
			if (code == 4) {
				return '月结';
			}
			if (code == 5) {
				return '季度结';
			}
			return '未知';
		},
	},
	Payment: {
		type: function(code){
			if(code === "BANK_PAY"){
				return "银行卡";
			} else if(code === "DECP_PAY"){
				return "数字人民币";
			} else if(code === "USDT_TRC20_PAY"){
				return "USDT-TRC20";
			} else if(code === "WECHAT_PAY"){
				return "微信";
			} else if(code === "ALIPAY_PAY"){
				return "支付宝";
			} else if(code === "QQ_PAY"){
				return "QQ";
			} else if(code === "DY_PAY"){
				return "抖音";
			} else if(code === "TB_PAY"){
				return "淘宝";
			} else if(code === "JD_PAY"){
				return "京东";
			} else if(code === "M_PAY"){
				return "M币";
			} else if(code === "BOBI_PAY"){
				return "波币";
			} else if(code === "HH5_PAY"){
				return "HH5币";
			} else if(code === "OKG_PAY"){
				return "OKG币";
			}
			return "未知";
		}
	},
	PlayerRecharge: {
		method: function(code) {
			if (code == 0) {
				return '系统充值';
			}
			if (code == 1) {
				return 'USDT充值';
			}
			if (code == 2) {
				return '三方支付';
			}
			return '未知';
		},
		payMethod: function(code) {
			if (code == 0) {
				return '未知';
			}
			if (code == 1) {
				return '自动处理';
			}
			if (code == 2) {
				return '手动处理';
			}
			return '未知';
		},
		orderStatus: function(code) {
			if (code == -2) {
				return '失败';
			} else if (code == -1) {
				return '已取消';
			} else if (code == 0) {
				return '待支付';
			} else if (code == 1) {
				return '已完成';
			} else if (code == 2) {
				return '审核中';
			}
			return '未知';
		},
		payType: function(code) {
			if (code == 0) {
				return '银行卡';
			} else if (code == 1) {
				return 'USDT-TRC20';
			} else if (code == 2) {
				return '支付宝';
			} else if (code == 3) {
				return 'M币';
			} else if (code == 4) {
				return 'HH5币';
			} else if (code == 5) {
				return 'OKG币';
			} else if (code == 6) {
				return '数字人民币';
			} else if (code == 7) {
				return '微信';
			} else if (code == 8) {
				return 'QQ';
			} else if (code == 9) {
				return '抖音';
			} else if (code == 10) {
				return '淘宝';
			} else if (code == 11) {
				return '京东';
			} else if (code == 12) {
				return '波币';
			} else if (code == 99) {
				return '其他';
			} else {
				return '其他';
			}
		}
	},
	PlayerWithdraw: {
		orderStatus: function(code) {
			if (code == 0) {
				return '待处理';
			}
			if (code == 1) {
				return '已完成';
			}
			if (code == -1) {
				return '已拒绝';
			}
			return '未知类型：' + code;
		},
		checkStatus: function(code, user) {
			if (code == 0) {
				return '等待审核';
			}
			if (code == 1) {
				var text = '审核通过';
				if (user) {
					return text + '(审核人：' + user + ')';
				}
				return text;
			}
			if (code == -1) {
				var text = '审核不通过';
				if (user) {
					return text + '(审核人：' + user + ')';
				}
				return text;
			}
			return '未知类型：' + code;
		},
		lockStatus: function(code, user) {
			if (code == 0) {
				return '未锁定';
			}
			if (code == 1) {
				var text = '已锁定';
				if (user) {
					return text + '(锁定人：' + user + ')';
				}
				return text;
			}
		},
		payStatus: function(code) {
			if (code == 0) {
				return '待打款';
			}
			if (code == 1) {
				return '打款中';
			}
			if (code == 2) {
				return '打款完成';
			}
			if (code == -1) {
				return '打款失败';
			}
		},
		payMethod: function(code, user) {
			if (code == 0) {
				return '未知方式';
			}
			if (code == 1) {
				var text = '自动打款';
				if (user) {
					return text + '(类型：' + user + ')';
				}
				return text;
			}
			if (code == 2) {
				var text = '手动打款';
				if (user) {
					return text + '(打款人：' + user + ')';
				}
				return text;
			}
		},
		remitType: function(code) {
			if (code == 0) {
				return '银行卡';
			} else if (code == 1) {
				return 'USDT-TRC20';
			} else if (code == 2) {
				return '支付宝';
			} else if (code == 3) {
				return 'M币';
			} else if (code == 4) {
				return 'HH5币';
			} else if (code == 5) {
				return 'OKG币';
			} else if (code == 6) {
				return '数字人民币';
			} else if (code == 7) {
				return '微信';
			} else if (code == 8) {
				return 'QQ';
			} else if (code == 9) {
				return '抖音';
			} else if (code == 10) {
				return '淘宝';
			} else if (code == 11) {
				return '京东';
			} else if (code == 12) {
				return '波币';
			} else if (code == 100) {
				return '人工下分';
			} else if (code == 99) {
				return '其他';
			} else {
				return '其他';
			}
		}
	},
	PlayerCard: {
		cardStatus: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
		},
		cardType: function(code) {
			if (code == 1) {
				return '银行卡';
			}
			if (code == 2) {
				return 'USTD';
			}
		}
	},
	AdminAccount: {
		status: function(code) {
			if (code === 0) {
				return '正常';
			}
			if (code === -1) {
				return '禁用';
			}
		},
		onlineStatus: function(code) {
			if (code === 0) {
				return '离线';
			}
			if (code === 1) {
				return '在线';
			}
		}
	},
	AdminRole: {
		status: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
		}
	},
	AdminMenu: {
		status: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
		}
	},
	AdminAccess: {
		status: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
		}
	},
	GameLottery: {
		killStatus: function(code) {
			if (code == 0) {
				return '开启';
			} else {
				return '关闭';
			}
		},
		gameStatus: function(code) {
			if (code === 0) {
				return '启用';
			} else {
				return '禁用';
			}
		},
		liveStatus: function(code) {
			if (code == 1) {
				return '直播中';
			} else if (code == 0) {
				return '已下播';
			} else {
				return '未知';
			}
		},
		maintainStatus: function(code) {
			if (code == 0) {
				return '正常';
			} else if (code == -1) {
				return '维护中';
			} else if (code == -2) {
				return '建设中';
			} else {
				return '未知';
			}
		},
		pushEventType: function(code) {
			if (code == 1) {
				return '推流';
			} else if (code == 0) {
				return '断流';
			} else {
				return '未知';
			}
		},
		pushArea: function(code) {
			if (code > 7) {
				return '海外';
			} else {
				return '国内';
			}
		},
		methodType: function(code) {
			if (code === 1) {
				return '标准';
			}
			if (code === 2) {
				return '双面';
			}
			if (code === 3) {
				return '标准+双面';
			}
		},
		model: function(code) {
			if (code === 'yuan') {
				return '元';
			}
			if (code === 'jiao') {
				return '角';
			}
			if (code === 'fen') {
				return '分';
			}
			if (code === 'li') {
				return '厘';
			}
		},
		lockType: function(code){
			if(code === 1){
				return "个人";
			}else if(code === 9){
				return "全平台";
			}else{
				return "未知";
			}
		}
	},
	Gift: {
		giftCategory: function(code) {
			if (code == 1) {
				return '推荐';
			} else if (code == 2) {
				return '等级';
			} else {
				return '未知';
			}
		},
		giftSeLevel: function(code) {
			if (code == 0) {
				return 'V0';
			} else if (code == 1) {
				return 'V1';
			} else if (code == 2) {
				return 'V2';
			} else if (code == 3) {
				return 'V3';
			} else {
				return '未知';
			}
		}
	},
	GameMonopoly: {
		bonusStatus: function(code) {
			if (code == 0) {
				return '未爆奖';
			} else if (code == 1) {
				return '爆奖';
			} else {
				return '退单';
			}
		},
		billType: function(code) {
			if (code == 1) {
				return '计入奖池';
			} else if (code == 2) {
				return '爆奖';
			} else if (code == 5) {
				return '爆奖修正';
			} else if (code == 3) {
				return '系统撤单';
			} else {
				return '未知';
			}
		}
	},
	GameSimulation: {
		groupType: function(code) {
			if (code === 1) {
				return '彩票';
			}else if (code === 2) {
				return '真人';
			}else if (code === 3) {
				return '电子';
			}else if (code === 4) {
				return '棋牌';
			}else if (code === 5) {
				return '体育';
			}else if (code === 6) {
				return '电竞';
			}else {
				return '未知';
			}
		}
	},
	GameSimulationTransfer: {
		status: function(code) {
			if (code == 0) {
				return '处理中';
			}
			if (code == 1) {
				return '成功';
			}
			if (code == 3) {
				return '标记成功';
			}
			if (code == -1) {
				return '失败';
			}
			if (code == -2) {
				return '已结束';
			}
			if (code == -3) {
				return '标记失败';
			}
		},
		direction: function(code) {
			if (code == 'IN') {
				return '转入';
			}
			if (code == 'OUT') {
				return '转出';
			}
		}
	},
	GameSimulationReportRecrawl: {
		status: function(code) {
			if (code == 0) {
				return '等待处理';
			}
			if (code == 1) {
				return '处理中';
			}
			if (code == 2) {
				return '处理结束';
			}
		}
	},
	Account: {
		accountType: function(code) {
			if (code == 1) {
				return '中心账户';
			}
			if (code == 2) {
				return '游戏账户';
			}
			if (code == 3) {
				return '余额宝';
			}
		},
		type: function(code) {
			if (code == 0) {
				return '玩家';
			}
			if (code == 1) {
				return '代理';
			}
		},
		status: function(code) {
			if (code == 0) {
				return '正常';
			}
			if (code == -1) {
				return '禁用';
			}
			if (code == -2) {
				return '冻结';
			}
		},
		onlineStatus: function(code) {
			if (code == 0) {
				return '离线';
			}
			if (code == 1) {
				return '在线';
			}
		},
		bindStatus: function(code) {
			if (code == 0) {
				return '未绑定';
			}
			if (code == 1) {
				return '已绑定';
			}
		},
		pids: function(list) {
			if (list.length == 0) {
				return '无';
			}
			var rs = '';
			$.each(list, function(i, v) {
				if (i != 0) {
					rs += ' - ';
				}
				rs += v;
			});
			return rs;
		}
	},
	AccountType: {
		equalLevel: function(code) {
			if (code === 0) {
				return '不允许';
			}
			if (code === 1) {
				return '允许';
			}
		}
	},
}

//加载关注用户
var LotteryAttention;

//如果是子窗口打开该js ，不允许加载
if (window.parent.LotteryAttention) {
} else {
	LotteryAttention = function() {
		//关注红标用户集合(单独页面集合)
		var attention_tab_list;
		//关注红标用户姓名集合(单独页面集合)
		var attention_tab_list2;

		// 判断该用户是否存在
		var pdUser = function(username) {
			if (!attention_tab_list) {
				return false;
			}
			for (var i = 0; i < attention_tab_list.length; i++) {
				if (attention_tab_list[i] && attention_tab_list[i] == username) {
					return true;
				}
			}
			return false;
		}

		// 判断该用户姓名是否存在
		var pdWithdraw = function(withdrawName) {
			if (!attention_tab_list2) {
				return false;
			}
			for (var i = 0; i < attention_tab_list2.length; i++) {
				if (attention_tab_list[i] && attention_tab_list2[i] == withdrawName) {
					return true;
				}
			}
			return false;
		}

		var getAttentionUser = function() {
			return attention_tab_list;
		}

		var getAttentionUser2 = function() {
			return attention_tab_list2;
		}



		return {
			pdUser: pdUser,
			getAttentionUser: getAttentionUser,
			getAttentionUser2: getAttentionUser2,
			pdWithdraw: pdWithdraw
		}

	}();
}

var Validation = function(){
	var isDecimal = function(str){
		// 大于等于0的小数或整数
		return /^\d+(\.\d+)?$/.test(str);
	};
	var isScalePoint = function(str){
		// 0.01~99.99
		return /^\d{1,2}(\.\d{1,2})?$/.test(str);
	};
	var isInteger = function(str){
		// 整数
		return /^\d+$/.test(str);
	};

	return {
		isDecimal: isDecimal,
		isScalePoint: isScalePoint,
		isInteger: isInteger
	}
}();

window.onload = function() {
	// 添加自定义验证方法
	if($ !== undefined && $ !== null && $.validator !== undefined && $.validator !== null){
		$.validator.addMethod("oneDecimal", function(value, element){
			return this.optional(element) || /^\d+(\.\d{1})?$/.test(value);
		}, "请输入有效的数字（最多1位小数）");
	}
};

// 此代码为Richard于2025/3/27日提交，尚不清楚基用途。此代码会导致不弹出操作成功的消息提示，故注释掉。
// // 在合适的地方，添加以下代码来覆盖默认的AJAX success处理
// $(document).ajaxSuccess(function(event, xhr, settings) {
//     try {
//         // 尝试解析响应JSON
//         var response = JSON.parse(xhr.responseText);
//
//         // 如果状态码为0（成功）且没有标记为隐藏成功消息，则默认不显示成功消息
//         // 这会阻止全局"请求成功"提示
//         if (response.code === '0' || response.code === 0) {
//             // 关闭任何现有的成功消息
//             $('.messenger-message.success').find('.messenger-close').click();
//
//             // 阻止默认的成功消息
//             event.stopPropagation();
//         }
//     } catch (e) {
//         // 如果不是JSON响应，则忽略
//         console.log('非JSON响应，忽略消息处理');
//     }
// });
