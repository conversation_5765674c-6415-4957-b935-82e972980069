$(document).ready(function () {

    var TableDataList = function () {

        var thisTable = $('#table-data-list');
        var p = thisTable.find('form[name="search-params"]');
        var thisPageList = thisTable.find('.page-list');
        // 排序字段
        var orderField = "";
        // 排序方式
        var orderWay = "";
        var orderThList = thisTable.find('th[data-command="orderTh"]');
        var profitAmountOrderTh = thisTable.find('th[data-value="profitAmount"]');

        p.find('input[name="sTime"]').val(moment().format('YYYY-MM-DD'));
        p.find('input[name="eTime"]').val(moment().add(1, 'days').format('YYYY-MM-DD'));

        var getParams = function () {
            var agentSelect = p.find('select[name="agentName"]');
            var agentNo = agentSelect.val(); // 获取选中的agentno
            var agentName = agentSelect.find('option:selected').data('agentname') || ''; // 获取对应的agentName
            var gameTypeCode = p.find('select[name="gameType"]').val();
            var lottery = p.find('select[name="gameLottery"]').val();
            var methodType = p.find('select[name="methodType"]').val();
            var methodCode = p.find('select[name="gameMethod"]').val();
            var sTime = p.find('input[name="sTime"]').val().trim();
            var eTime = p.find('input[name="eTime"]').val().trim();

            return {
                agentNo: agentNo, // 使用agentno作为请求参数
                agentName: agentName, // 保留agentName用于显示
                gameTypeCode: gameTypeCode,
                lottery: lottery,
                methodCode: methodCode,
                methodType: methodType,
                sTime: sTime,
                eTime: eTime,
                orderField: orderField,
                orderWay: orderWay
            }
        };

        var ajaxData = function () {
            return $.extend({}, getParams());
        };

        var ajaxUrl = Route.PATH + Route.Report.PATH + Route.Report.SEARCH_GAME_METHOD_REPORT;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 20,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: ajaxData,
            beforeSend: function () {
                blockUI(thisTable);
            },
            complete: function () {
                unblockUI(thisTable);
            },
            success: function (data) {
                buildData(data);
            },
            pageError: function (response) {
                App.dialog(response.message);
            },
            emptyData: function () {
                thisTable.find('table > tbody').html('<tr><td colspan="20">没有相关数据</td></tr>');
            }
        });

        var buildData = function (data) {
            thisTable.find('table > tbody').empty();
            $.each(data, function (i, v) {
                var array = [
                    v.gameTypeName,
                    v.gameName,
                    DataFormat.GameLottery.methodType(v.methodType),
                    v.methodName,
                    v.methodCode,
                    v.totalBetAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    v.totalBonusAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    v.totalRebateAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    v.totalPumpAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    v.totalProfitAmount.toLocaleString('en-US', {minimumFractionDigits: 3})
                ];
                var tr = App.buildTbody(array);
                tr.find('td').addClass('text-center');

                thisTable.find('table > tbody').append(tr);
            });
        };

        var loadGameType = function () {
            var e = p.find('select[name="gameType"]');
            e.append('<option value="">游戏类型</option>');
            var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.STATIC_LIST_TYPE;
            App.staticPost(url, {}, function (result) {
                $.each(result, function (i, v) {
                    e.append('<option value="' + v.gameTypeCode + '">' + v.gameTypeName + '</option>');
                });
                loadGameLottery(e.val());
            });
            e.change(function () {
                loadGameLottery(e.val());
            });
        }

        var loadGameLottery = function (gameTypeCode) {
            var e = p.find('select[name="gameLottery"]');
            e.empty();
            e.append('<option value="">游戏名称</option>');
            var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.STATIC_LIST_INFO;
            var data = {gameTypeCode: gameTypeCode};
            App.staticPost(url, data, function (result) {
                $.each(result, function (i, v) {
                    e.append('<option value="' + v.lottery + '">' + v.gameName + '</option>');
                });
                loadGameMethod(e.val());
            });
            e.change(function () {
                loadGameMethod(e.val());
            });
        }

        var loadGameMethod = function (lottery) {
            var methodType = p.find('select[name="methodType"]').val();
            var e = p.find('select[name="gameMethod"]');
            e.empty();
            e.append('<option value="">玩法名称</option>');
            var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.LIST_LOTTERY_METHOD;
            var data = {lottery: lottery, methodType: methodType};
            App.staticPost(url, data, function (result) {
				var listMap = {};
                $.each(result.data, function (i, v) {
					var key = v.methodName;
					var value = v.methodCode;
					if(undefined === listMap[key]){
						listMap[key] = value;
					} else {
						listMap[key] = listMap[key] + "," + value;
					}
                });
                $.each(listMap, function (key, value) {
					e.append('<option value="' + value + '">' + key + '</option>');
				});
                reorder(profitAmountOrderTh);
            });
            if (e.attr('data-init') != 'true') {
                e.change(reload);
            }
            e.attr('data-init', true);
        }

        var resetTitle = function() {
            // 重新设置标题
            orderThList.each(function(){
                var oth = $(this);
                var thTitle = oth.attr("data-field");
                oth.html(thTitle + "&nbsp;<i class='fa fa-sort'></i>");
            });
        };
        var reorder = function(orderTh){
            resetTitle();
            var newOrderField = orderTh.attr("data-value");
            if(orderField === "" || (orderField === newOrderField && newOrderField !== "profitAmount" && orderWay === "desc")){
                // 恢复为默认排序方式
                orderField = "profitAmount";
                orderWay = "desc";
                profitAmountOrderTh.html(profitAmountOrderTh.attr("data-field") + "&nbsp;<i class='fa fa-sort-desc'></i>");
            }else{
                var orderChar;
                if(orderField !== newOrderField || orderWay === "desc"){
                    orderWay = "asc";
                    orderChar = "&nbsp;<i class='fa fa-sort-asc'></i>";
                }else{
                    orderWay = "desc";
                    orderChar = "&nbsp;<i class='fa fa-sort-desc'></i>";
                }
                orderField = newOrderField;
                orderTh.html(orderTh.attr("data-field") + orderChar);
            }
            reload();
        }
        orderThList.click(function() {
            var orderTh = $(this);
            reorder(orderTh);
        });

        p.find('select[name="methodType"]').change(function() {
            loadGameMethod(p.find('select[name="gameLottery"]').val());
        });

        p.find('button[name="search"]').click(function () {
            pagination.init();
        });

        // 加载代理商列表
        var loadAgentList = function(callback) {
            var agentSelect = p.find('select[name="agentName"]');
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_AGENT;
            App.staticPost(url, {}, function(result) {
                var item = result.data;
                agentSelect.empty();
                agentSelect.append('<option value="">请选择平台</option>');
                $.each(item, function(i, v) {
                    agentSelect.append('<option value="' + v.agentNo + '" data-agentname="' + v.agentName + '">' + v.agentName + '</option>');
                });
                // 默认选中第一条数据（如果有数据的话）
                if (item && item.length > 0) {
                    agentSelect.val(item[0].agentNo);
                }

                // 代理商列表加载完成后执行回调
                if (typeof callback === 'function') {
                    callback();
                }
            });
        };

        var init = function () {
            resetTitle();
            loadGameType();
            loadAgentList(function() {
                // 代理商列表加载完成后，初始化分页
                pagination.init();
            });
        };

        var reload = function () {
            pagination.init();
        };

        return {
            init: init,
            reload: reload
        }
    }();

    // 初始化日期控件
    $('[data-init="datepicker"]').datepicker({
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    TableDataList.init();

});