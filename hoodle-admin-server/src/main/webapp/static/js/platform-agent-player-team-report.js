$(document).ready(function() {

	var playerNameSearch = '';
	var currentUpAgent = ''; // 当前上级代理
	var currentPid = null; // 当前pid参数
	var TableDataList = function() {

		var thisTable = $('#table-data-list');
		var thisPageList = thisTable.find('.page-list'); // 添加分页容器引用
		var p = thisTable.find('form[name="search-params"]');
		// 排序字段
		var orderField = "";
		// 排序方式
		var orderWay = "";
		var orderThList = thisTable.find('th[data-command="orderTh"]');
		var profitAmountOrderTh = thisTable.find('th[data-value="profitAmount"]');

		var getParams = function() {
			var agentSelect = p.find('select[name="agentName"]');
			var agentNo = agentSelect.val(); // 获取选中的agentno
			var agentName = agentSelect.find('option:selected').data('agentname') || ''; // 获取对应的agentName
			var playerName = p.find('input[name="playerName"]').val().trim();
			var sDate = p.find('input[name="sDate"]').val().trim();
			var eDate = p.find('input[name="eDate"]').val().trim();
			var params = {
				agentNo: agentNo, // 使用agentno作为请求参数
				agentName: agentName, // 保留agentName用于显示
				playerName: playerName,
				sDate: sDate,
				eDate: eDate,
				orderField: orderField,
				orderWay: orderWay
			};
			// 只有同时满足以下条件才传递pid：
			// 1. currentPid 不为 null
			// 2. playerName 不为空（用户输入了特定玩家名称）
			if (currentPid !== null && playerName.length > 0) {
				params.playerId = currentPid;
			}
			return params;
		}

		// 获取团队总计数据
		var loadTeamTotalData = function() {
			var totalUrl = Route.PATH + Route.Report.PATH + Route.Report.SEARCH_PLATFORM_AGENT_PLAYER_TEAM_TOTAL_REPORT;
			var params = getParams();

			App.ajaxPost(totalUrl, params, function(totalData) {
				updateSummaryDisplay(totalData);
			}, function() {
				blockUI(thisTable);
			}, function() {
				unblockUI(thisTable);
			});
		}

		var ajaxUrl = Route.PATH + Route.Report.PATH + Route.Report.SEARCH_PLATFORM_AGENT_PLAYER_TEAM_REPORT;
		console.log("ajaxUrl : " + ajaxUrl);

		// 使用标准分页组件
		var pagination = $.pagination({
			render: thisPageList,
			pageSize: 20,
			ajaxType: 'post',
			ajaxUrl: ajaxUrl,
			ajaxData: getParams,
			beforeSend: function() {
				blockUI(thisTable);
			},
			complete: function() {
				unblockUI(thisTable);
			},
			success: function(data) {
				buildData(data);
				// 数据加载成功后，加载团队统计数据
				loadTeamTotalData();
			},
			pageError: function(response) {
				App.dialog(response.message);
			},
			emptyData: function() {
				thisTable.find('#table-team').html('<tr><td colspan="12">没有相关数据</td></tr>');
			}
		});

		var buildData = function(data) {
			thisTable.find('#table-team').empty();
			if (data != null && data.length > 0) {
				$.each(data, function(i, v) {
					var agentNames = v.agentName || ''; // 获取代理商名称，如果不存在则为空字符串
					var array =[
						agentNames, // 添加代理商名称
						v.playerName+"&nbsp;&nbsp;("+v.downCount+")",
						v.teamAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 余额
						v.totalTransferInAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),  // 充值金额
						v.totalTransferOutAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 取款
						v.totalHandlingFee.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 手续费
						v.totalBetAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),		// 投注金额
						v.totalBonusAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),		// 派奖金额
						v.totalRebateAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 返点
						v.totalPumpAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),		// 抽水
						v.totalRewardAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 打赏金额
						v.totalActivityAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 活动金额
						v.totalSalaryAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 工资
						v.totalDivsAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 分红
						v.totalPlayerTransInAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 上下级 转入
						v.totalPlayerTransOutAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 上下级 转出
						v.totalProfitAmount.toLocaleString('en-US', {minimumFractionDigits: 3})   // 盈亏金额
					];

					var tr = App.buildTbody(array);
					tr.find('td').addClass('text-center');
					// 将当前行数据保存到tr的data属性中
					tr.data('agentNo', v.agentNo);
					tr.data('playerName', v.playerName);
					var thisLink = '';
					var playerName = p.find('input[name="playerName"]').val().trim();
					if (v.queryNameFlag && v.playerName === playerName) {
						thisLink = $('<span>').html(v.playerName + "&nbsp;&nbsp;(" + v.downCount + ")");
					} else {
						thisLink = $('<a>').html(v.playerName + "&nbsp;&nbsp;(" + v.downCount + ")");
						thisLink.click(function () {
							// 从父级tr元素获取保存的数据
							var currentAgentNo = $(this).closest('tr').data('agentNo');
							var currentPlayerName = $(this).closest('tr').data('playerName');
							doSearch(currentAgentNo, currentPlayerName);
						});
					}
					//标红
					if (window.parent.LotteryAttention.pdUser(v.playerName) == true) {
						tr.find('td').eq(1).html(thisLink).addClass("bg-red"); // 修改为第二列 (索引1)
						tr.find('td a').eq(0).css("color", "white");
					} else {
						tr.find('td').eq(1).html(thisLink); // 修改为第二列 (索引1)
					}
					thisTable.find('#table-team').append(tr);
				});
			} else {
				thisTable.find('#table-team').html('<tr><td colspan="12">没有相关数据</td></tr>'); // 修改colspan为12
			}
		}

		var ajaxPersonUrl = Route.PATH + Route.Report.PATH + "/search-platform-agent-player-person-report";

		var getParamsPerson = function() {
			var agentSelect = p.find('select[name="agentName"]');
			var agentNo = agentSelect.val(); // 获取选中的agentno
			var agentName = agentSelect.find('option:selected').data('agentname') || ''; // 获取对应的agentName
			var playerName = p.find('input[name="playerName"]').val().trim();
			var sDate = p.find('input[name="sDate"]').val().trim();
			var eDate = p.find('input[name="eDate"]').val().trim();
			if(null === playerName || playerName.trim() === ''){
				thisTable.find('#table-person').empty();
			}

			var params = {
				agentNo: agentNo, // 使用agentno作为请求参数
				agentName: agentName, // 保留agentName用于显示
				playerName: playerName,
				sDate: sDate,
				eDate: eDate
			};
			return params;
		}

		var paginationPerson = function(){
			App.ajaxPost(ajaxPersonUrl, getParamsPerson(), function(result){
				thisTable.find('#table-person').empty();
				if (result != null && null != result.data) {
					var v = result.data;
					// 安全格式化函数
					var agentNames = v.agentName || '';
					var array = [
						agentNames, // 添加代理商名称
						v.playerName + "&nbsp;&nbsp;(" + v.downCount + ")",
						v.teamAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 余额
						v.totalTransferInAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),  // 充值金额
						v.totalTransferOutAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 取款
						v.totalHandlingFee.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 手续费
						v.totalBetAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),		// 投注金额
						v.totalBonusAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),		// 派奖金额
						v.totalRebateAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 返点
						v.totalPumpAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),		// 抽水
						v.totalRewardAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 打赏金额
						v.totalActivityAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 活动金额
						v.totalSalaryAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 工资
						v.totalDivsAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 分红
						v.totalPlayerTransInAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 上下级 转入
						v.totalPlayerTransOutAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),	// 上下级 转出
						v.totalProfitAmount.toLocaleString('en-US', {minimumFractionDigits: 3})   // 盈亏金额
					];
					var tr = App.buildTbody(array);
					tr.find('td').addClass('text-center');
					// 将当前行数据保存到tr的data属性中
					tr.data('agentNo', v.agentNo);
					tr.data('playerName', v.playerName);
					thisTable.find('#table-person').append(tr);
				} else {
					thisTable.find('#table-person').html('<tr><td colspan="12">没有相关数据</td></tr>'); // 修改colspan为12
				}
			});
		}

		// 更新汇总显示 - 修改为使用接口返回的数据
		var updateSummaryDisplay = function (totalData) {
			if (totalData) {
				$('#summary-teamAvailableBalance').text((totalData.teamAvailableBalance || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); // 余额
				$('#summary-transferIn').text((totalData.totalTransferInAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); // 充值
				$('#summary-transferOut').text((totalData.totalTransferOutAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); // 提现
				$('#summary-handlingFee').text((totalData.totalHandlingFee || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); // 手续费
				$('#summary-bet').text((totalData.totalBetAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	 //投注
				$('#summary-bonus').text((totalData.totalBonusAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	//派奖
				$('#summary-rebateAmount').text((totalData.totalRebateAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); 	// 返点
				$('#summary-pumpAmount').text((totalData.totalPumpAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	 //抽水
				$('#summary-reward').text((totalData.totalRewardAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); // 打赏
				$('#summary-activity').text((totalData.totalActivityAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})); // 活动
				$('#summary-salaryAmount').text((totalData.totalSalaryAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	// 工资
				$('#summary-divsAmount').text((totalData.totalDivsAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	 // 分红
				$('#summary-playerTransInAmount').text((totalData.totalPlayerTransInAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	 //上下级转帐转入
				$('#summary-playerTransOutAmount').text((totalData.totalPlayerTransOutAmount || 0).toLocaleString('en-US', {minimumFractionDigits: 2}));	 //上下级转帐转出
				// 盈亏金额特殊处理，显示正负号
				var profitAmount = totalData.totalProfitAmount || 0;
				var profitText = (profitAmount >= 0 ? '+' : '') + profitAmount.toLocaleString('en-US', {minimumFractionDigits: 2});
				$('#summary-profit').text(profitText);  		// 盈亏
				$('#summary-profit').css('color', profitAmount >= 0 ? '#28a745' : '#dc3545');

				// 更新上级代理显示
				if (totalData.pidsAndPlayerNames && totalData.pidsAndPlayerNames.length > 0) {
					var $agentContainer = $('#summary-agent');
					$agentContainer.empty(); // 清空原内容
					totalData.pidsAndPlayerNames.forEach(function (pidAndName, index) {
						// 分割 "playerId|playerName" 格式的字符串
						var parts = pidAndName.split('|');
						if (parts.length >= 2) {
							var playerId = parts[0];
							var playerName = parts[1];

							// 创建可点击的链接，显示玩家名称
							var $playerLink = $('<a href="javascript:void(0);" style="color: #007bff; text-decoration: underline; margin-right: 5px;">' + playerName + '</a>');

							// 绑定点击事件，使用playerId进行查询
							$playerLink.click(function () {
								console.log('Clicked player:', playerName, 'ID:', playerId);
								// 调用搜索函数，传入playerId和playerName作为参数
								searchByPid(playerId, playerName);
							});

							$agentContainer.append($playerLink);

							// 如果不是最后一个，添加箭头分隔符
							if (index < totalData.pidsAndPlayerNames.length - 1) {
								$agentContainer.append('<span style="margin: 0 5px; color: #666;"> > </span>');
							}
						}
					});

					$agentContainer.show();
					currentUpAgent = totalData.pidsAndPlayerNames.join(','); // 保存完整的上级代理信息
				} else {
					$('#summary-agent').text('无').show();
					currentUpAgent = '';
				}
			} else {
				// 如果没有数据，显示默认值
				$('#summary-teamAvailableBalance').text('0.00');
				$('#summary-transferIn').text('0.00');
				$('#summary-transferOut').text('0.00');
				$('#summary-bet').text('0.00');
				$('#summary-bonus').text('0.00');
				$('#summary-activity').text('0.00');
				$('#summary-rebate').text('0.00');
				$('#summary-pump').text('0.00');
				$('#summary-reward').text('0.00');
				$('#summary-point').text('0.00');
				$('#summary-profit').text('0.00');
				$('#summary-agent').text('无').show();
				currentUpAgent = '';
			}
		}

		var reorder = function (orderTh) {
			var newOrderField = orderTh.attr("data-value");
			// 重新设置标题
			orderThList.each(function () {
				var oth = $(this);
				var thTitle = oth.attr("data-field");
				oth.html(thTitle);
			});
			if(orderField === "" || (orderField === newOrderField && newOrderField !== "profitAmount" && orderWay === "desc")){
				// 恢复为默认排序方式
				orderField = "profitAmount";
				orderWay = "desc";
				profitAmountOrderTh.html(profitAmountOrderTh.attr("data-field") + " ∨");
			}else{
				var orderChar;
				if(orderField !== newOrderField || orderWay === "desc"){
					orderWay = "asc";
					orderChar = " ∧";
				}else{
					orderWay = "desc";
					orderChar = " ∨";
				}
				orderField = newOrderField;
				orderTh.html(orderTh.attr("data-field") + orderChar);
			}
			pagination.init();
			paginationPerson();
			loadTeamTotalData();
		}
		orderThList.click(function() {
			var orderTh = $(this);
			reorder(orderTh);
		});

		// 修改搜索按钮的点击事件
		p.find('button[name="search"]').click(function() {
			currentPid = null; // 清除pid参数
			pagination.init();
			paginationPerson();
			loadTeamTotalData();

		});

		// 加载代理商列表
		var loadAgentList = function(callback) {
			var agentSelect = p.find('select[name="agentName"]');
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_AGENT;
			App.staticPost(url, {}, function(result) {
				var item = result.data;
				agentSelect.empty();
				agentSelect.append('<option value="">请选择平台</option>');
				$.each(item, function(i, v) {
					agentSelect.append('<option value="' + v.agentNo + '" data-agentname="' + v.agentName + '">' + v.agentName + '</option>');
				});
				// 默认选中第一条数据（如果有数据的话）
				if (item && item.length > 0) {
					agentSelect.val(item[0].agentNo);
				}

				// 代理商列表加载完成后执行回调
				if (typeof callback === 'function') {
					callback();
				}
			});
		};

		// 修改doSearch函数
		var doSearch = function(agentNo, playerName) {
			var agentSelect = p.find('select[name="agentName"]');

			if (agentNo) {
				// 直接设置agentNo作为选中值
				agentSelect.val(agentNo);
			} else {
				agentSelect.val('');
			}

			p.find('input[name="playerName"]').val(playerName);
			playerNameSearch = playerName;
			currentPid = null; // 清除pid参数
			pagination.init();
			paginationPerson();
			loadTeamTotalData(); // 添加这行来加载团队统计数据

		}

		// 添加根据pid搜索的函数
		var searchByPid = function(pid, playerName) {
			console.log('Searching by pid:', pid);

			// 填充玩家名称到搜索框（而不是清空）
			if (playerName) {
				p.find('input[name="playerName"]').val(playerName);
				playerNameSearch = playerName;
			} else {
				p.find('input[name="playerName"]').val('');
				playerNameSearch = '';
			}


			// 设置当前pid
			currentPid = pid;

			// 调用分页加载数据
			pagination.init();
			paginationPerson();
			loadTeamTotalData();
		}

		// 修改init函数
		var init = function() {
			// 首先加载代理商列表，完成后再加载数据
			loadAgentList(function() {
				// 代理商列表加载完成后，初始化分页
				pagination.init();
			});
		}

		var initPerson = function() {
			paginationPerson();
		};

		return {
			init: init,
			initPerson: initPerson
		}

	}();

	// 初始化日期控件
	$('[data-init="datepicker"]').datepicker({
		language: 'zh-CN',
		autoclose: true,
		todayHighlight: true,
		format: 'yyyy-mm-dd'
	});

	// 设置默认日期
	var today = new Date();
	var tomorrow = new Date(today);
	tomorrow.setDate(today.getDate() + 1);
	// 格式化日期为 yyyy-mm-dd
	var formatDate = function(date) {
		var year = date.getFullYear();
		var month = ('0' + (date.getMonth() + 1)).slice(-2);
		var day = ('0' + date.getDate()).slice(-2);
		return year + '-' + month + '-' + day;
	};

	// 设置默认日期值
	$('input[name="sDate"]').val(formatDate(today));
	$('input[name="eDate"]').val(formatDate(tomorrow));

	TableDataList.init();
});