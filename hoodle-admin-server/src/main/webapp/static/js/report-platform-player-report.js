$(document).ready(function () {

    var TableDataList = function () {

        var thisTable = $('#table-data-list');
        var p = thisTable.find('form[name="search-params"]');
        var thisPageList = thisTable.find('.page-list');
        // 排序字段
        var orderField = "";
        // 排序方式
        var orderWay = "";
        var orderThList = thisTable.find('th[data-command="orderTh"]');
        var profitAmountOrderTh = thisTable.find('th[data-value="profitAmount"]');

        p.find('input[name="sTime"]').val(moment().format('YYYY-MM-DD'));
        p.find('input[name="eTime"]').val(moment().add(1, 'days').format('YYYY-MM-DD'));

        var getParams = function () {
            // var agentNo = p.find('input[name="agentNo"]').val().trim();
            // var agentName = p.find('input[name="agentName"]').val().trim();
            var agentSelect = p.find('select[name="agentName"]');
            var agentNo = agentSelect.val(); // 获取选中的agentno
            var agentName = agentSelect.find('option:selected').data('agentname') || ''; // 获取对应的agentName
            var playerName = p.find('input[name="playerName"]').val().trim();
            var sTime = p.find('input[name="sTime"]').val().trim();
            var eTime = p.find('input[name="eTime"]').val().trim();

            return {
                agentNo: agentNo,
                agentName: agentName,
                playerName: playerName,
                sTime: sTime,
                eTime: eTime,
                orderField: orderField,
                orderWay: orderWay
            }
        };

        var ajaxData = function () {
            return $.extend({}, getParams());
        };

        var ajaxUrl = Route.PATH + Route.Report.PATH + Route.Report.SEARCH_PLATFORM_PLAYER_REPORT;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 20,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: ajaxData,
            beforeSend: function () {
                blockUI(thisTable);
            },
            complete: function () {
                unblockUI(thisTable);
            },
            success: function (data) {
                buildData(data);
            },
            pageError: function (response) {
                App.dialog(response.message);
            },
            emptyData: function () {
                thisTable.find('table > tbody').html('<tr><td colspan="20">没有相关数据</td></tr>');
            }
        });

        var buildData = function (data) {
            thisTable.find('table > tbody').empty();
            $.each(data, function (i, v) {
                var array = [
                    v.agentName,                                                                    // 代理商名称
                    v.playerName,                                                                   // 玩家用户名
                    v.playerAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}),   // 彩票余额
                    v.totalTransferInAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),    // 充值金额
                    v.totalTransferOutAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),   // 提现金额
                    v.totalHandlingFee.toLocaleString('en-US', {minimumFractionDigits: 3}),    // 手续费
                    v.totalBetAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),           // 投注金额
                    v.totalBonusAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),         // 派奖金额
                    v.totalRebateAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),        // 返点金额
                    v.totalPumpAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),         // 抽水
                    v.totalRewardAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),        // 打赏金额
                    v.totalActivityAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),      // 活动金额
                    v.totalSalaryAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),        // 工资
                    v.totalDivsAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),          // 分红
                    v.totalUpAndDownTransferIn.toLocaleString('en-US', {minimumFractionDigits: 3}),    // 上下级转帐-转入
                    v.totalUpAndDownTransferOut.toLocaleString('en-US', {minimumFractionDigits: 3}),   // 上下级转帐-转出
                    v.totalProfitAmount.toLocaleString('en-US', {minimumFractionDigits: 3})         // 盈亏金额
                ];
                var tr = App.buildTbody(array);
                tr.find('td').addClass('text-center');
                thisTable.find('table > tbody').append(tr);
            });
        };

        var resetTitle = function () {
            // 重新设置标题
            orderThList.each(function(){
                var oth = $(this);
                var thTitle = oth.attr("data-field");
                oth.html(thTitle + "&nbsp;<i class='fa fa-sort'></i>");
            });
        };
        var reorder = function(orderTh){
            resetTitle();
            var newOrderField = orderTh.attr("data-value");
            if(orderField === "" || (orderField === newOrderField && newOrderField !== "profitAmount" && orderWay === "desc")){
                // 恢复为默认排序方式
                orderField = "profitAmount";
                orderWay = "desc";
                profitAmountOrderTh.html(profitAmountOrderTh.attr("data-field") + "&nbsp;<i class='fa fa-sort-desc'></i>");
            }else{
                var orderChar;
                if(orderField !== newOrderField || orderWay === "desc"){
                    orderWay = "asc";
                    orderChar = "&nbsp;<i class='fa fa-sort-asc'></i>";
                }else{
                    orderWay = "desc";
                    orderChar = "&nbsp;<i class='fa fa-sort-desc'></i>";
                }
                orderField = newOrderField;
                orderTh.html(orderTh.attr("data-field") + orderChar);
            }
            reload();
        }
        orderThList.click(function() {
            var orderTh = $(this);
            reorder(orderTh);
        });

        p.find('button[name="search"]').click(function () {
            pagination.init();
        });

        // 加载代理商列表
        var loadAgentList = function(callback) {
            var agentSelect = p.find('select[name="agentName"]');
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_AGENT;
            App.staticPost(url, {}, function(result) {
                var item = result.data;
                agentSelect.empty();
                agentSelect.append('<option value="">请选择平台</option>');
                $.each(item, function(i, v) {
                    agentSelect.append('<option value="' + v.agentNo + '" data-agentname="' + v.agentName + '">' + v.agentName + '</option>');
                });
                // 默认选中第一条数据（如果有数据的话）
                if (item && item.length > 0) {
                    agentSelect.val(item[0].agentNo);
                }

                // 代理商列表加载完成后执行回调
                if (typeof callback === 'function') {
                    callback();
                }
            });
        };

        var init = function () {
            reorder(profitAmountOrderTh);
            // 首先加载代理商列表，完成后再加载数据
            loadAgentList(function() {
                // 代理商列表加载完成后，初始化分页
                pagination.init();
            });
        };

        var reload = function () {
            pagination.init();
        };



        p.find('button[name="search"]').click(function() {
            pagination.init();
        });

        return {
            init: init,
            reload: reload
        }
    }();

    // 初始化日期控件
    $('[data-init="datepicker"]').datepicker({
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });






// 报表生成按钮点击事件
    $(document).on('click', 'button[name="generateReport"]', function() {
        showReportGenerateModal();
    });

    // 显示报表生成模态窗口
    var showReportGenerateModal = function() {
        loadAgentList();

        // 设置默认日期为当天
        var today = new Date();
        var todayStr = today.getFullYear() + '-' +
            String(today.getMonth() + 1).padStart(2, '0') + '-' +
            String(today.getDate()).padStart(2, '0');

        $('#reportGenerateModal input[name="sDate"]').val(todayStr);
        $('#reportGenerateModal input[name="eDate"]').val(todayStr);

        $('#reportGenerateModal').modal('show');
    };

    // 加载代理商列表
    var loadAgentList = function() {
        var agentSelect = $('#reportGenerateModal select[name="agentNo"]');
        agentSelect.find('option:not(:first)').remove();

        var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_AGENT;
        App.staticPost(url, {}, function(result) {
            var item = result.data;
					agentSelect.empty();
					agentSelect.append('<option value="">请选择代理商</option>');
					$.each(item, function(i, v) {
						agentSelect.append('<option value="' + v.agentNo + '" data-agentname="' + v.agentName + '">' + v.agentName + '</option>');
					});
					// 默认选中第一条数据（如果有数据的话）
					if (item && item.length > 0) {
						agentSelect.val(item[0].agentNo);
					}
        }, function(error) {
            App.dialog('加载代理商列表失败：' + error.message);
        });
    };

    // 生成报表
    var generateReport = function() {
        var form = $('#reportGenerateForm');
        var agentNo = form.find('select[name="agentNo"]').val();
        var playerName = form.find('input[name="playerName"]').val().trim();
        var sDate = form.find('input[name="sDate"]').val().trim();
        var eDate = form.find('input[name="eDate"]').val().trim();

        // 表单验证 - 代理商和玩家名称必填
        if (!agentNo || agentNo === '') {
            App.dialog('请选择代理商，此字段为必填项');
            return;
        }
        if (!sDate) {
            App.dialog('请选择开始日期');
            return;
        }
        if (!eDate) {
            App.dialog('请选择结束日期');
            return;
        }

        // 日期验证
        if (new Date(sDate) > new Date(eDate)) {
            App.dialog('开始日期不能大于结束日期');
            return;
        }

        var url = Route.PATH + Route.Report.PATH + Route.Report.MANUAL_REPORT;
        var params = {
            agentNo: agentNo,
            playerName: playerName,
            sDate: sDate,
            eDate: eDate
        };

        // 显示加载状态
        var confirmBtn = $('#confirmGenerateReport');
        confirmBtn.prop('disabled', true).text('生成中...');

        App.staticPost(url, params, function(result) {
            confirmBtn.prop('disabled', false).text('生成报表');
            // 检查返回的code字段
            if (result.code == '0' || result.code == 0) {
                // 成功时
                $('#reportGenerateModal').modal('hide');
                App.dialog('报表生成成功！');
                // 清空表单
                form[0].reset();
            } else {
                // code不为0时，显示错误信息
                App.dialog('报表生成失败：' + (result.message || '未知错误'));
            }
        }, function(error) {
            confirmBtn.prop('disabled', false).text('生成报表');
            App.dialog('报表生成失败：' + (error.message || '未知错误'));
        });
    };

    // 报表生成确认按钮点击事件
    $(document).on('click', '#confirmGenerateReport', function() {
        generateReport();
    });

    // 模态窗口显示时初始化日期控件
    $('#reportGenerateModal').on('shown.bs.modal', function() {
        $(this).find('[data-init="datepicker"]').datepicker({
            language: 'zh-CN',
            autoclose: true,
            todayHighlight: true,
            format: 'yyyy-mm-dd'
        });
    });

    TableDataList.init();


});