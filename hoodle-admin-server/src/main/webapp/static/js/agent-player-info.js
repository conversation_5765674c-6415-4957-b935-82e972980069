$(document).ready(function() {
	var agenNoSearch = '';
	var playerNameSearch = '';
    var TableDataList = function() {
        var thisTable = $('#table-data-list');
        var p = thisTable.find('form[name="search-params"]');
        var thisPageList = thisTable.find('.page-list');
		// 排序字段
		var orderField = "";
		// 排序方式
		var orderWay = "";
		var orderThList = thisTable.find('th[data-command="orderTh"]');
		var selectedAgentNo = ''; // 当前选中的代理商
		var agentList = []; // 代理商列表
		var accountTypeList = []; // 账户类型列表

		p.find('input[name="sTime"]').val(moment().format('YYYY-MM-DD'));
    	p.find('input[name="eTime"]').val(moment().add(1, 'days').format('YYYY-MM-DD'));


		// 加载代理商列表
		var loadAgentList = function(callback) {
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
			App.staticPost(url, {}, function (result) {
				agentList = result.data || [];
				var agentSelect = p.find('select[name="agentNo"]');
				agentSelect.empty();
				agentSelect.append('<option value="">请选择代理商</option>');

				$.each(agentList, function(i, agent) {
					agentSelect.append('<option value="' + agent.agentNo + '">' + agent.agentName + '</option>');
				});
				if (callback) {
					callback();
				}
			});
		};

		// 加载账户类型列表
		var loadAccountTypeList = function(agentNo, callback) {
			if (!agentNo) {
				var accountTypeSelect = p.find('select[name="accountType"]');
				accountTypeSelect.empty();
				accountTypeSelect.append('<option value="">请选择账户层级</option>');
				if (callback) callback();
				return;
			}

			var url = Route.PATH + Route.System.PATH + Route.System.LIST_PLAYER_ACCOUNT_TYPE;
			App.staticPost(url, {agentNo: agentNo}, function(result) {
				accountTypeList = result.data || [];
				var accountTypeSelect = p.find('select[name="accountType"]');
				accountTypeSelect.empty();
				accountTypeSelect.append('<option value="">请选择账户层级</option>');

				$.each(accountTypeList, function(i, accountType) {
					accountTypeSelect.append('<option value="' + accountType.code + '">' + accountType.name + '</option>');
				});

				if (callback) {
					callback();
				}
			});
		};

		// 代理商选择变化事件
		p.find('select[name="agentNo"]').change(function() {
			var selectedAgentNo = $(this).val();
			loadAccountTypeList(selectedAgentNo);
		});

        var getParams = function() {
			var agentNo = p.find('select[name="agentNo"]').val();
			var accountType = p.find('select[name="accountType"]').val();
        	var playerName = p.find('input[name="playerName"]').val().trim();
			var playerType = p.find('select[name="playerType"]').val();
			var playerStatus = p.find('select[name="playerStatus"]').val();
			var onlineStatus = p.find('select[name="onlineStatus"]').val();
			var markersStatus = p.find('select[name="markersStatus"]').val();
			var minAmount = p.find('input[name="minAmount"]').val().trim();
			var maxAmount = p.find('input[name="maxAmount"]').val().trim();
			return {
				agentType: 2,
				agentNo: agentNo,
				accountType: accountType,
				playerType: playerType,
        		playerName: playerName,
				playerStatus: playerStatus,
				onlineStatus: onlineStatus,
				markersStatus: markersStatus,
				amountGt: minAmount,
				amountLt: maxAmount,
				orderField: orderField,
				orderWay: orderWay
        	}
        }

        var ajaxUrl = Route.PATH + Route.Agent.PATH + Route.Agent.SEARCH_PLAYER;
        var pagination = $.pagination({
			render: thisPageList,
			pageSize: 20,
			ajaxType: 'POST',
			ajaxUrl: ajaxUrl,
			ajaxData: getParams,
			beforeSend: function() {
				blockUI(thisTable);
			},
			complete: function() {
				unblockUI(thisTable);
			},
			success: function(data) {
				buildData(data);
			},
			pageError: function(response) {
				App.dialog(response.message);
			},
			emptyData: function() {
				thisTable.find('table > tbody').html('<tr><td colspan="20">没有相关数据</td></tr>');
			}
		});

		var buildData = function (data) {
			thisTable.find('table > tbody').empty();
			thisTable.find("#total-query-info").hide();
			if(data.length > 0) {
				var totalQueryInfo = data[0].totalQueryInfo;
				if(!($.isEmptyObject(totalQueryInfo))){
					var playerName = totalQueryInfo.playerName;
					var totalCount = totalQueryInfo.totalCount;
					var totalAgentCount = totalQueryInfo.totalAgentCount;
					var totalPlayerCount = totalQueryInfo.totalPlayerCount;
					thisTable.find("#playerName").text("代理：" + playerName);
					thisTable.find("#totalCount").text("直属下级：" + totalCount + "人");
					thisTable.find("#totalAgentCount").text("代理：" + totalAgentCount + "人");
					thisTable.find("#totalPlayerCount").text("玩家：" + totalPlayerCount + "人");
					thisTable.find("#total-query-info").show();
				}
			}
			$.each(data, function (i, v) {
				var btnStatus = '<i class="fa fa-check"></i> 启用';
				if (v.playerStatus == 0) {
					btnStatus = '<i class="fa fa-ban"></i> 禁用';
				}
				var btnMarkersStatus = '<i class="fa fa-bell"></i> 标记为正常 ';
				if (v.markersStatus == 0) {
					btnMarkersStatus = '<i class="fa fa-bell-slash"></i> 标记为异常 ';
				}

				var playerStatusText = DataFormat.Default.status(v.playerStatus);
				var markersStatusText = DataFormat.AgentPlayer.markerStatus(v.markersStatus);

				// 设置红色文字样式
				var playerStatusClass = v.playerStatus == 1 ? 'text-danger' : '';
				var markersStatusClass = v.markersStatus == 1 ? 'text-danger' : '';

				var array = [
					v.agentName,
					v.playerName,
					v.playerNick,
					v.accountTypeName,
					v.playerAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}),
					v.lotteryCode,
					v.point,
					moment(v.createTime).format('YYYY-MM-DD'),
					`<span class="${playerStatusClass}">${playerStatusText}</span>`,
					`<span class="${markersStatusClass}">${markersStatusText}</span>`,
					DataFormat.AgentPlayer.onlineStatus(v.onlineStatus),
					v.device == null ? '无' : v.device,
					'<button data-command="adminAdd" class="btn btn-primary btn-xs btn-mini" type="button"> 充值 </button> ' +
					'<button data-command="status" class="btn btn-primary btn-xs btn-mini" type="button">' + btnStatus + '</button> ' +
					'<button data-command="markersStatus" class="btn btn-primary btn-xs btn-mini" type="button">' + btnMarkersStatus + '</button> ' +
					'<button data-command="details" class="btn btn-primary btn-xs btn-mini" type="button"><i class="fa fa-share"></i> 更多 </button>'
				];
				var tr = App.buildTbody(array);
				tr.find('td').addClass('text-center');
				var thisLink;
				if (v.agentNo === agenNoSearch && v.playerName === playerNameSearch) {
					thisLink = $('<a>').html('<i class="fa fa-hand-o-left"></i> ' + v.playerName);
					thisLink.click(function() {
						doSearch(v.agentNo, v.upPlayerName);
					});
				}else {
					thisLink = $('<a>').html(v.playerName);
					thisLink.click(function() {
						doSearch(v.agentNo, v.playerName);
					});
				}
				tr.find('td').eq(1).html(thisLink);

				// 状态
				tr.find('[data-command="status"]').click(function () {
					doUpdateStatus(v.playerId, v.playerStatus);
				});

				// 标记状态
				tr.find('[data-command="markersStatus"]').click(function () {
					doUpdateMarkerStatus(v.playerId, v.markersStatus);
				});

				// 管理员增
				tr.find('[data-command="adminAdd"]').click(function () {
					AdminAddModal.show(v.playerId, v.playerName);
				});

				// 显示详情
				tr.find('[data-command="details"]').click(function () {
					showDetails(v.playerId);
				});
				thisTable.find('table > tbody').append(tr);
			});
		}

		var showDetails = function (id) {
			var swidth = window.screen.width;
			var sheight = window.screen.height;
			var dwidth = 860;
			var dheight = 800;
			var top = (sheight - dheight) / 2 - 50;
			var left = (swidth - dwidth) / 2;
			window.open('./player-details.html?id=' + id, '_blank', 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=yes, resizable=no, copyhistory=no, top=' + top + ', left=' + left + ', width=' + dwidth + ', height=' + dheight);
		}

        var doUpdateStatus = function(id, status) {
        	// 正常状态
        	if (status == 0) {
        		App.dialog('确定禁用该玩家？', '确定', function() {
        			var url = Route.PATH + Route.Agent.PATH + Route.Agent.UPDATE_PLAYER_STATUS;
        			var data = { id: id, status: 1 };
                    App.ajaxPost(url, data, function() {
                    	reload();
                    	App.msg('success', '操作成功');
                    });
        		});
        	}
        	// 禁用状态
        	if (status == 1) {
        		App.dialog('确定启用该玩家？', '确定', function() {
        			var url = Route.PATH + Route.Agent.PATH + Route.Agent.UPDATE_PLAYER_STATUS;
        			var data = { id: id, status: 0 };
                    App.ajaxPost(url, data, function() {
                    	reload();
                    	App.msg('success', '操作成功');
                    });
        		});
        	}
        }

        var doUpdateMarkerStatus = function(id, status) {
        	// 正常状态
        	if (status == 0) {
        		App.dialog('确定该用户是异常状态？', '确定', function() {
        			var url = Route.PATH + Route.Agent.PATH + Route.Agent.UPDATE_PLAYER_MARKER_STATUS;
        			var data = { id: id, status: 1 };
                    App.ajaxPost(url, data, function() {
                    	reload();
                    	App.msg('success', '操作成功');
                    });
        		});
        	}
        	// 禁用状态
        	if (status == 1) {
        		App.dialog('确定恢复该用户至不是异常状态？', '确定', function() {
        			var url = Route.PATH + Route.Agent.PATH + Route.Agent.UPDATE_PLAYER_MARKER_STATUS;
        			var data = { id: id, status: 0 };
                    App.ajaxPost(url, data, function() {
                    	reload();
                    	App.msg('success', '操作成功');
                    });
        		});
        	}
        }

/*        var loadType = function() {
        	var e = p.find('select[name="agent"]');
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.SEARCH_AGENT;
            App.staticPost(url, {}, function(result) {
				var list = result.data.list;
				if(list.length > 1){
					e.append('<option value="">全部</option>');
				};
            	$.each(list, function(i, v) {
					e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
            	});
            });
        }*/

		var resetTitle = function() {
			// 重新设置标题
			orderThList.each(function(){
				var oth = $(this);
				var thTitle = oth.attr("data-field");
				oth.html(thTitle + "&nbsp;<i class='fa fa-sort'></i>");
			});
		};
		var reorder = function(orderTh){
			resetTitle();
			var newOrderField = orderTh.attr("data-value");
			if(orderField === newOrderField && orderWay === "desc"){
				// 恢复为默认排序方式
				orderField = "";
				orderWay = "";
			}else{
				var orderChar;
				if(orderField !== newOrderField || orderWay === "desc"){
					orderWay = "asc";
					orderChar = "&nbsp;<i class='fa fa-sort-asc'></i>";
				}else{
					orderWay = "desc";
					orderChar = "&nbsp;<i class='fa fa-sort-desc'></i>";
				}
				orderField = newOrderField;
				orderTh.html(orderTh.attr("data-field") + orderChar);
			}
			reload();
		}
		orderThList.click(function() {
			var orderTh = $(this);
			reorder(orderTh);
		});

        p.find('button[name="search"]').click(function() {
        	pagination.init();
        });

		p.find('button[name="add"]').click(function () {
			AddModal.show();
		});

        var init = function() {
			resetTitle();
			// 先加载代理商列表，然后在回调中初始化分页
			loadAgentList(function() {
				pagination.init();
			});
        }

        var reload = function() {
			// 先加载代理商列表，然后在回调中初始化分页
			loadAgentList(function() {
				pagination.init();
			});
        }

		var doSearch = function(agentNo, upPlayerName) {
			p.find('select[name="agentNo"]').find('option[value="' + agentNo + '"]').attr('selected', true);
			p.find('input[name="playerName"]').val(upPlayerName);
			agenNoSearch = agentNo;
			playerNameSearch = upPlayerName;

			pagination.init();
			pagination.reload();
		}

        return {
            init: init,
            reload: reload
        }
    }();

	var AdminAddModal = function () {
		var modal = $('#modal-admin-add');
		var form = modal.find('form');
		var initForm = function () {
			form.validate({
				rules: {
					amount: {
						required: true,
						number: true,
						range: [-*************, *********]
					},
					password: {
						required: true
					}
				},
				messages: {
					amount: {
						required: '该字段不能为空！',
						number: '请填写正确的数字！',
						range: "单次操作金额最高为50000"
					},
					password: {
						required: '该字段不能为空！'
					}
				},
				invalidHandler: function () {
				},
				errorPlacement: function (error, element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
						$(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
					}
				},
				highlight: function (element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').removeClass('has-success').addClass('has-error');
					}
				},
				unhighlight: function (element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').removeClass('has-error').addClass('has-success');
						$(element).closest('.form-group').find('.help-block').empty().hide();
						$(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
					}
				}
			});
			modal.find('[data-command="submit"]').click(function () {
				if (form.validate().form()) {
					doSubmit();
				}
			});
		}

		var doSubmit = function () {
			var id = form.find('input[name="id"]').val().trim();
			var type = form.find('select[name="type"]').val();
			var amount = form.find('input[name="amount"]').val().trim();
			var password = form.find('input[name="password"]').val().trim();
			var remarks = form.find('input[name="remarks"]').val().trim();
			var data = {
				id: id,
				opType: type,
				password: password,
				amount: amount,
				remarks: remarks
			}
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.MANUAL_PLAYER_RECHARGE;
			App.ajaxPost(url, data, function () {
				TableDataList.init();
				modal.modal('hide');
				App.msg('success', '操作成功');
			});
		}

		var reset = function () {
			var formGroup = form.find('.form-group');
			formGroup.removeClass('has-error');
			formGroup.removeClass('has-success');
			formGroup.find('.glyphicon').hide();
			formGroup.find('.help-block').empty().hide();
			form.find('[data-field="accountType"]').hide();
			form.find('[data-field="limit"]').hide();
			form[0].reset();
		}

		var show = function (id, playerName) {
			if (id) {
				reset();
				form.find('input[name="id"]').val(id);
				form.find('input[name="playerName"]').val(playerName);
				modal.modal('show');
			}
		}

		var init = function () {
			initForm();
		}

		return {
			init: init,
			show: show
		}

	}();

	var AdminMinusModal = function () {
		var modal = $('#modal-admin-minus');
		var form = modal.find('form');
		var initForm = function () {
			form.validate({
				rules: {
					amount: {
						required: true,
						number: true,
						range: [-*************, *********]
					},
					password: {
						required: true
					}
				},
				messages: {
					amount: {
						required: '该字段不能为空！',
						number: '请填写正确的数字！',
						range: "单次操作金额最高为50000"
					},
					password: {
						required: '该字段不能为空！'
					}
				},
				invalidHandler: function () {
				},
				errorPlacement: function (error, element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
						$(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
					}
				},
				highlight: function (element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').removeClass('has-success').addClass('has-error');
					}
				},
				unhighlight: function (element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').removeClass('has-error').addClass('has-success');
						$(element).closest('.form-group').find('.help-block').empty().hide();
						$(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
					}
				}
			});
			modal.find('[data-command="submit"]').click(function () {
				if (form.validate().form()) {
					doSubmit();
				}
			});
		}

		var doSubmit = function () {
			var id = form.find('input[name="id"]').val().trim();
			var amount = form.find('input[name="amount"]').val().trim();
			var password = form.find('input[name="password"]').val().trim();
			var remarks = form.find('input[name="remarks"]').val().trim();
			var data = {
				id: id,
				password: password,
				amount: amount,
				remarks: remarks
			}
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.MANUAL_PLAYER_WITHDRAW;
			App.ajaxPost(url, data, function () {
				TableDataList.init();
				modal.modal('hide');
				App.msg('success', '操作成功');
			});
		}

		var reset = function () {
			var formGroup = form.find('.form-group');
			formGroup.removeClass('has-error');
			formGroup.removeClass('has-success');
			formGroup.find('.glyphicon').hide();
			formGroup.find('.help-block').empty().hide();
			form.find('[data-field="accountType"]').hide();
			form.find('[data-field="limit"]').hide();
			form[0].reset();
		}

		var show = function (id, playerName) {
			if (id) {
				reset();
				form.find('input[name="id"]').val(id);
				form.find('input[name="playerName"]').val(playerName);
				modal.modal('show');
			}
		}

		var init = function () {
			initForm();
		}

		return {
			init: init,
			show: show
		}

	}();

	var AddModal = function () {
		var modal = $('#modal-add');
		var form = modal.find('form');
		var initForm = function () {
			form.validate({
				rules: {
					playerName: {
						required: true
					},
					point: {
						required: true,
						min: 0,
						oneDecimal: true
					}
				},
				messages: {
					playerName: {
						required: "该字段不能为空！"
					},
					point: {
						required: "该字段不能为空！",
						min: "不能小于0！",
						oneDecimal: "最多1位小数！"
					}
				},
				invalidHandler: function () {
				},
				errorPlacement: function (error, element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
						$(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
					}
				},
				highlight: function (element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').removeClass('has-success').addClass('has-error');
					}
				},
				unhighlight: function (element) {
					if ($(element).closest('.form-group').attr('novalidate') != 'true') {
						$(element).closest('.form-group').removeClass('has-error').addClass('has-success');
						$(element).closest('.form-group').find('.help-block').empty().hide();
						$(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
					}
				}
			});
			modal.find('[data-command="submit"]').click(function () {
				if (form.validate().form()) {
					doSubmit();
				}
			});
		}

		var doSubmit = function () {
			var playerName = form.find('input[name="playerName"]').val();
			var playerType = form.find('select[name="playerType"]').val();
			var agentNo = form.find('select[name="agent"]').val();
			var parentPlayerName = form.find('input[name="parentPlayerName"]').val();
			var point = form.find('input[name="point"]').val();
			var data = {
				playerName: playerName,
				playerType: playerType,
				parentPlayerName: parentPlayerName,
				point: point,
				agentNo: agentNo
			}

			var url = Route.PATH + Route.Agent.PATH + Route.Agent.ADD_PLAYER_ACCOUNT;
			App.ajaxPost(url, data, function () {
				modal.modal('hide');
				TableDataList.init();
				App.msg('success', '操作成功');
			});
		}

		var reset = function () {
			var formGroup = form.find('.form-group');
			formGroup.removeClass('has-error');
			formGroup.removeClass('has-success');
			formGroup.find('.glyphicon').hide();
			formGroup.find('.help-block').empty().hide();
			form[0].reset();
		}

		var loadAgent = function () {
			var e = form.find('select[name="agent"]');
			var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
			App.staticPost(url, {}, function (result) {
				var item = result.data;
				$.each(item, function (i, v) {
					e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
				});
				e.trigger('change');
			});
		}

		var show = function () {
			reset();
			modal.find('[data-field="tips"]').html('用户名是由5到15个字母或数字组成的,且至少包含1个字母和1个数字');
			modal.modal('show');
		}

		var init = function () {
			loadAgent();
			initForm();
		}

		return {
			init: init,
			show: show
		}
	}();

    // 初始化日期控件
	$('[data-init="datepicker"]').datepicker({
		language: 'zh-CN',
		autoclose: true,
		todayHighlight: true,
		format: 'yyyy-mm-dd'
	});

    TableDataList.init();
	AddModal.init();
	AdminAddModal.init();
	AdminMinusModal.init();
});
