$(document).ready(function(){

    var TableDataList = function(){

        var thisTable = $('#table-data-list');
        var p = thisTable.find('form[name="search-params"]');
        p.find('input[name="sOrderTime"]').val(moment().add(-7, 'days').format('YYYY-MM-DD'));
        p.find('input[name="eOrderTime"]').val(moment().add(1, 'days').format('YYYY-MM-DD'));
        var thisPageList = thisTable.find('.page-list');

        var getParams = function(){
            var agentNo = p.find('input[name="agentNo"]').val().trim();
            var agentName = p.find('input[name="agentName"]').val().trim();
            var playerName = p.find('input[name="playerName"]').val().trim();
            var lockType = p.find('select[name="lockType"]').val();
            var sLockTime = p.find('input[name="sOrderTime"]').val().trim();
            var eLockTime = p.find('input[name="eOrderTime"]').val().trim();
            return {
                agentNo: agentNo,
                agentName: agentName,
                playerName: playerName,
                lockType: lockType,
                sLockTime: sLockTime,
                eLockTime: eLockTime
            }
        }

        var ajaxUrl = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.SEARCH_LOCK_ORDER_RECORD;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 20,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: getParams,
            beforeSend: function(){
                blockUI(thisTable);
            },
            complete: function(){
                unblockUI(thisTable);
            },
            success: function(data){
                buildData(data);
            },
            pageError: function(response){
                App.dialog(response.message);
            },
            emptyData: function(){
                thisTable.find('table > tbody').html('<tr><td colspan="20">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data){
            thisTable.find('table > tbody').empty();
            $.each(data, function(i, v){
                var array = [
                    v.bean.agentName,
                    v.bean.lockId,
                    v.bean.billno,
                    v.bean.playerName,
                    DataFormat.GameLottery.lockType(v.bean.lockType),
                    v.lotteryName,
                    v.bean.issue,
                    v.methodName,
                    v.bean.content,
                    v.bean.money,
                    v.bean.winMoney,
                    moment(v.bean.lockTime).format('YYYY-MM-DD HH:mm:ss'),
                ];
                var tr = App.buildTbody(array);
                tr.find('td').addClass('text-center');
                // 状态
                thisTable.find('table > tbody').append(tr);
            });
        }

        p.find('button[name="search"]').click(function(){
            pagination.init();
        });

        var init = function(){
            pagination.init();
        }

        var reload = function(){
            pagination.init();
        }

        return {
            init: init,
            reload: reload
        }
    }();

    // 初始化日期控件
    $('[data-init="datepicker"]').datepicker({
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    TableDataList.init();
});
