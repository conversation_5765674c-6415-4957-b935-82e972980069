$(document).ready(function(){

    var playerSettings = function(){
        const LIST_URL = Route.PATH + Route.System.PATH + Route.System.LIST_PLAYER_SERVICE_CONFIG;
        const UPDATE_URL = Route.PATH + Route.System.PATH + Route.System.UPDATE_PLAYER_SYSTEM_CONFIG;
        var thisPanel = $('#thisPanel');

        // 初始化设置
        var initSettings = function(){
            var agentNo = thisPanel.find('select[name="agent"]').val();
            if(null == agentNo){
                return;
            }
            App.ajaxPost(LIST_URL, {agentNo: agentNo}, function(res){
                buildConfig(res);
            });
        };
        var buildConfig = function(data){
			vue_app.update_sys_config_data(data);
            thisPanel.find('input[name="loginFailedNum"]').val(data.LOGIN_FAILED_NUM);
            thisPanel.find('input[name="paymentPasswordFailedNum"]').val(data.PAYMENT_PASSWORD_FAILED_NUM);
            thisPanel.find('input[name="sysCode"]').val(data.SYS_CODE);
            thisPanel.find('input[name="sysPoint"]').val(data.SYS_POINT);
            thisPanel.find('input[name="sysCodeMax"]').val(data.SYS_CODE_MAX);
            thisPanel.find('input[name="sysCodeMin"]').val(data.SYS_CODE_MIN);
            thisPanel.find('input[name="sysUnitMoney"]').val(data.SYS_UNIT_MONEY);
            var methodTypeStandard = data.METHOD_TYPE_STANDARD === "true";
            thisPanel.find('input[id="methodTypeStandard_yes"]').prop('checked', methodTypeStandard);
            thisPanel.find('input[id="methodTypeStandard_no"]').prop('checked', !methodTypeStandard);
            var lockAccount = data.LOCK_ACCOUNT === "true";
            thisPanel.find('input[id="lockAccount_yes"]').prop('checked', lockAccount);
            thisPanel.find('input[id="lockAccount_no"]').prop('checked', !lockAccount);
            var lockAccountIp = data.LOCK_ACCOUNT_IP === "true";
            thisPanel.find('input[id="lockAccountIp_yes"]').prop('checked', lockAccountIp);
            thisPanel.find('input[id="lockAccountIp_no"]').prop('checked', !lockAccountIp);
            thisPanel.find('input[name="autoEqualCode"]').val(data.AUTO_EQUAL_CODE);
            thisPanel.find('input[id="allowTransferToDown_yes"]').prop('checked', data.ALLOW_TRANSFER_TO_DOWN === "true");
            thisPanel.find('input[id="allowTransferToDown_no"]').prop('checked', data.ALLOW_TRANSFER_TO_DOWN !== "true");
            thisPanel.find('input[id="allowTransferToUp_yes"]').prop('checked', data.ALLOW_TRANSFER_TO_UP === "true");
            thisPanel.find('input[id="allowTransferToUp_no"]').prop('checked', data.ALLOW_TRANSFER_TO_UP !== "true");
        }

        // 保存设置
        var saveSettings = function(){
            var agentNo = thisPanel.find('select[name="agent"]').val();
            if(!agentNo){
                App.msg('info', '请先选择平台代理商');
                return;
            }
            var loginFailedNum = thisPanel.find('input[name="loginFailedNum"]').val();
            var paymentPasswordFailedNum = thisPanel.find('input[name="paymentPasswordFailedNum"]').val();
            var sysCode = thisPanel.find('input[name="sysCode"]').val();
            var sysPoint = thisPanel.find('input[name="sysPoint"]').val();
            var sysCodeMax = thisPanel.find('input[name="sysCodeMax"]').val();
            var sysCodeMin = thisPanel.find('input[name="sysCodeMin"]').val();
            var sysUnitMoney = thisPanel.find('input[name="sysUnitMoney"]').val();
            var methodTypeStandard = thisPanel.find('input[name="methodTypeStandard"]:checked').val() === 'true';
            var lockAccount = thisPanel.find('input[name="lockAccount"]:checked').val() === 'true';
            var lockAccountIp = thisPanel.find('input[name="lockAccountIp"]:checked').val() === 'true';
            var autoEqualCode = thisPanel.find('input[name="autoEqualCode"]').val();
            var allowTransferToDown = thisPanel.find('input[name="allowTransferToDown"]:checked').val();
            var allowTransferToUp = thisPanel.find('input[name="allowTransferToUp"]:checked').val();
            var applyUpDedTransfer = thisPanel.find('input[name="applyUpDedTransfer"]:checked').val();
            var d = {
                loginFailedNum: loginFailedNum,
                paymentPasswordFailedNum: paymentPasswordFailedNum,
                sysCode: sysCode,
                sysPoint: sysPoint,
                sysCodeMax: sysCodeMax,
                sysCodeMin: sysCodeMin,
                sysUnitMoney: sysUnitMoney,
                methodTypeStandard: methodTypeStandard,
                lockAccount: lockAccount,
                lockAccountIp: lockAccountIp,
                autoEqualCode: autoEqualCode,
                allowTransferToDown: allowTransferToDown,
                applyUpDedTransfer: applyUpDedTransfer,
                allowTransferToUp: allowTransferToUp
            };
            var data = {agentNo: agentNo, d: $.toJSON(d)};
            App.ajaxPost(UPDATE_URL, data, function(){
                App.msg('success', '操作成功');
                initSettings();
            });
        };

        // 重置设置
        var resetSettings = function(){
            initSettings();
        };

        var loadAgent = function(){
            var e = thisPanel.find('select[name="agent"]');
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
            App.staticPost(url, {}, function(result){
                var item = result.data;
                $.each(item, function(i, v){
                    e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
                });
                e.trigger('change');
            });
        };
        loadAgent();

        // 初始化事件绑定
        thisPanel.find('select[name="agent"]').change(function(){
            init();
        });
        thisPanel.find('button[data-command="save"]').click(function(){
            saveSettings();
        });
        thisPanel.find('button[data-command="reset"]').click(function(){
            resetSettings();
        });

        // 初始化函数
        var init = function(){
            initSettings();
        };

        return {
            init: init
        };
    }();

    var accountTypeTable = function(){
        // 添加全局变量存储完整数据
        var allUserTypesData = [];
        var thisTable = $('#account-type-table');

        var addNew = function(){
            // 初始时不生成任何用户类型选项
            var userTypeOptions = '<option value="">请先选择用户级别</option>';
            var innerHtml =
                '<tr>' +
                '<td class="text-center">' +
                '<input name="code" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="userGroup" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="name" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="parentCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="userLevel" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<select name="allowedUserType" class="form-control" multiple>' +
                userTypeOptions +
                '</select>' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="lotteryMinCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="lotteryMaxCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<select name="equalLevel" class="form-control">' +
                '<option value="0">不允许 </option>' +
                '<option value="1">允许</option>' +
                '</select>' +
                '</td>' +
                '<td class="text-center">' +
                '<select name="equalLevelCode" class="form-control">' +
                '<option value="0">不允许 </option>' +
                '<option value="1">允许</option>' +
                '</select>' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="equalLevelMinCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="equalLevelMaxCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<button name="save" type="button" class="btn btn-success btn-sm btn-small"><i class="fa fa-save"></i> 保存</button> ' +
                '<button name="remove" type="button" class="btn btn-sm btn-small"><i class="fa fa-save"></i> 删除</button>' +
                '</td>' +
                '</tr>';
            var thisRow = $(innerHtml);

            // 添加用户级别输入框的change事件监听
            thisRow.find('input[name="userLevel"]').on('input change', function(){
                var userLevel = $(this).val();
                var allowedUserTypeSelect = thisRow.find('select[name="allowedUserType"]');

                if(userLevel === '' || isNaN(parseInt(userLevel))) {
                    // 如果用户级别为空或不是数字，禁用下拉框并清空选项
                    allowedUserTypeSelect.prop('disabled', true);
                    allowedUserTypeSelect.html('<option value="">请先选择用户级别</option>');
                } else {
                    // 如果用户级别有效，启用下拉框并生成对应选项
                    var currentUserLevel = parseInt(userLevel);
                    var allUserTypes = getAllUserTypes();
                    var newUserTypeOptions = buildUserTypeOptions(currentUserLevel, allUserTypes, '');

                    allowedUserTypeSelect.prop('disabled', false);
                    allowedUserTypeSelect.html(newUserTypeOptions);

                    console.log('用户级别变更为:', currentUserLevel, '生成的选项:', newUserTypeOptions);
                }
            });
            thisRow.find('button[name="save"]').click(function(){
                var code = thisRow.find('input[name="code"]').val();
                var userGroup = thisRow.find('input[name="userGroup"]').val();
                var name = thisRow.find('input[name="name"]').val();
                var parentCode = thisRow.find('input[name="parentCode"]').val();
                var userLevel = thisRow.find('input[name="userLevel"]').val();
                var lotteryMinCode = thisRow.find('input[name="lotteryMinCode"]').val();
                var lotteryMaxCode = thisRow.find('input[name="lotteryMaxCode"]').val();
                var equalLevel = thisRow.find('select[name="equalLevel"]').val();
                var equalLevelCode = thisRow.find('select[name="equalLevelCode"]').val();
                var equalLevelMinCode = thisRow.find('input[name="equalLevelMinCode"]').val();
                var equalLevelMaxCode = thisRow.find('input[name="equalLevelMaxCode"]').val();
                var allowedUserType = thisRow.find('select[name="allowedUserType"]').val();
                var allowedUserTypeStr = allowedUserType ? allowedUserType.join(',') : '';
                if(code == ''){
                    App.msg('error', '请输入唯一编码');
                    return false;
                }
                if(userGroup == ''){
                    App.msg('error', '请输入用户组');
                    return false;
                }
                if(name == ''){
                    App.msg('error', '请输入名称');
                    return false;
                }
                if(userLevel == ''){
                    App.msg('error', '请输入级别');
                    return false;
                }
                if(lotteryMinCode == ''){
                    App.msg('error', '请输入最低奖级');
                    return false;
                }
                if(lotteryMaxCode == ''){
                    App.msg('error', '请输入最高奖级');
                    return false;
                }
                if(lotteryMinCode > lotteryMaxCode){
                    App.msg('error', '最低奖级不能高于最高奖级');
                    return false;
                }
                if(equalLevelCode == '1'){
                    if(equalLevelMinCode == ''){
                        App.msg('error', '平级开号限定奖级允许时,需要平级开号最低奖级');
                        return false;
                    }
                    if(equalLevelMinCode == '0'){
                        App.msg('error', '平级开号限定奖级允许时,平级开号最低奖级不能0');
                        return false;
                    }
                    if(equalLevelMaxCode == ''){
                        App.msg('error', '平级开号限定奖级允许时,需要平级开号最高奖级');
                        return false;
                    }
                    if(equalLevelMaxCode == '0'){
                        App.msg('error', '平级开号限定奖级允许时,平级开号最高奖级不能0');
                        return false;
                    }
                    if(equalLevelMinCode > equalLevelMaxCode){
                        App.msg('error', '平级开号限定奖级允许时,平级开号最低奖级不能高于平级开号最高奖级');
                        return false;
                    }
                    if(equalLevelMinCode > lotteryMaxCode){
                        App.msg('error', '平级开号最低奖级不能高于此层级最高奖级');
                        return false;
                    }
                    if(equalLevelMaxCode > lotteryMaxCode){
                        App.msg('error', '平级开号最高奖级不能高于此层级最高奖级');
                        return false;
                    }
                }
                var data = {
                    code: code,
                    userGroup: userGroup,
                    name: name,
                    parentCode: parentCode,
                    userLevel: userLevel,
                    lotteryMinCode: lotteryMinCode,
                    lotteryMaxCode: lotteryMaxCode,
                    equalLevel: equalLevel,
                    equalLevelCode: equalLevelCode,
                    equalLevelMinCode: equalLevelMinCode,
                    equalLevelMaxCode: equalLevelMaxCode,
                    allowedUserType: allowedUserTypeStr
                }
                doSave(data, function(){
                    doList();
                });
            });
            thisRow.find('button[name="remove"]').click(function(){
                thisRow.remove();
            });
            thisRow.find('select[name="equalLevelCode"]').change(function(){
                if(v.equalLevelCode == 1){
                    thisRow.find('input[name="equalLevelMinCode"]').attr('disabled', true);
                    thisRow.find('input[name="equalLevelMaxCode"]').attr('disabled', true);
                }else{
                    thisRow.find('input[name="equalLevelMinCode"]').attr('disabled', false);
                    thisRow.find('input[name="equalLevelMaxCode"]').attr('disabled', false);
                }
            });
            thisTable.find('tbody').append(thisRow);
        }

        var editData = function(oldEls, v){
           // 直接使用当前编辑行的userLevel
            var currentUserLevel = parseInt(v.userLevel);
            var allUserTypes = getAllUserTypes();
            var userTypeOptions = buildUserTypeOptions(currentUserLevel, allUserTypes, v.code);
            var innerHtml =
                '<tr>' +
                '<td class="text-center">' +
                '<input name="code" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="userGroup" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="name" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="parentCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="userLevel" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<select name="allowedUserType" class="form-control" multiple>' +
                userTypeOptions +
                '</select>' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="lotteryMinCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="lotteryMaxCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<select name="equalLevel" class="form-control">' +
                '<option value="0">不允许</option>' +
                '<option value="1">允许</option>' +
                '</select>' +
                '</td>' +
                '<td class="text-center">' +
                '<select name="equalLevelCode" class="form-control">' +
                '<option value="0">不允许 </option>' +
                '<option value="1">允许</option>' +
                '</select>' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="equalLevelMinCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<input name="equalLevelMaxCode" type="text" class="form-control input-sm">' +
                '</td>' +
                '<td class="text-center">' +
                '<button name="update" type="button" class="btn btn-success btn-sm btn-small"><i class="fa fa-save"></i> 更新</button>' +
                '</td>' +
                '</tr>';
            var thisRow = $(innerHtml);
            thisRow.find('input[name="code"]').val(v.code);
            thisRow.find('input[name="userGroup"]').val(v.userGroup);
            thisRow.find('input[name="name"]').val(v.name);
            thisRow.find('input[name="parentCode"]').val(v.parentCode);
            thisRow.find('input[name="userLevel"]').val(v.userLevel);
            thisRow.find('input[name="lotteryMinCode"]').val(v.lotteryMinCode);
            thisRow.find('input[name="lotteryMaxCode"]').val(v.lotteryMaxCode);
            thisRow.find('select[name="equalLevel"]').find('option[value="' + v.equalLevel + '"]').attr('selected', true);
            thisRow.find('select[name="equalLevelCode"]').find('option[value="' + v.equalLevelCode + '"]').attr('selected', true);
            thisRow.find('input[name="equalLevelMinCode"]').val(v.equalLevelMinCode);
            thisRow.find('input[name="equalLevelMaxCode"]').val(v.equalLevelMaxCode);

            if(v.deleteFlag != 0){
                thisRow.find('input[name="code"]').attr('disabled', true);
                thisRow.find('input[name="userGroup"]').attr('disabled', true);
                thisRow.find('input[name="parentCode"]').attr('disabled', true);
                thisRow.find('input[name="userLevel"]').attr('disabled', true);
            }
            thisRow.find('select[name="equalLevelCode"]').change(function(){
                if(v.equalLevelCode == 1){
                    thisRow.find('input[name="equalLevelMinCode"]').attr('disabled', true);
                    thisRow.find('input[name="equalLevelMaxCode"]').attr('disabled', true);
                }else{
                    thisRow.find('input[name="equalLevelMinCode"]').attr('disabled', false);
                    thisRow.find('input[name="equalLevelMaxCode"]').attr('disabled', false);
                }
            });
            // 设置allowedUserType的选中值
            if(v.allowedUserType) {
                var allowedTypes = v.allowedUserType.split(',');
                thisRow.find('select[name="allowedUserType"] option').each(function(){
                    if(allowedTypes.indexOf($(this).val()) !== -1) {
                        $(this).prop('selected', true);
                    }
                });
            }
            thisRow.find('button[name="update"]').click(function(){
                var code = thisRow.find('input[name="code"]').val();
                var userGroup = thisRow.find('input[name="userGroup"]').val();
                var name = thisRow.find('input[name="name"]').val();
                var parentCode = thisRow.find('input[name="parentCode"]').val();
                var userLevel = thisRow.find('input[name="userLevel"]').val();
                var lotteryMinCode = thisRow.find('input[name="lotteryMinCode"]').val();
                var lotteryMaxCode = thisRow.find('input[name="lotteryMaxCode"]').val();
                var equalLevel = thisRow.find('select[name="equalLevel"]').val();
                var equalLevelCode = thisRow.find('select[name="equalLevelCode"]').val();
                var equalLevelMinCode = thisRow.find('input[name="equalLevelMinCode"]').val();
                var equalLevelMaxCode = thisRow.find('input[name="equalLevelMaxCode"]').val();
                var allowedUserType = thisRow.find('select[name="allowedUserType"]').val();
                var allowedUserTypeStr = allowedUserType ? allowedUserType.join(',') : '';
                if(code == ''){
                    App.msg('error', '请输入唯一编码');
                    return false;
                }
                if(userGroup == ''){
                    App.msg('error', '请输入用户组');
                    return false;
                }
                if(name == ''){
                    App.msg('error', '请输入名称');
                    return false;
                }
                if(userLevel == ''){
                    App.msg('error', '请输入级别');
                    return false;
                }
                if(lotteryMinCode == ''){
                    App.msg('error', '请输入最低奖级');
                    return false;
                }
                if(lotteryMaxCode == ''){
                    App.msg('error', '请输入最高奖级');
                    return false;
                }
                if(lotteryMinCode > lotteryMaxCode){
                    App.msg('error', '最低奖级不能高于最高奖级');
                    return false;
                }
                if(equalLevelCode == '1'){
                    if(equalLevelMinCode == ''){
                        App.msg('error', '平级开号限定奖级允许时,需要平级开号最低奖级');
                        return false;
                    }
                    if(equalLevelMinCode == '0'){
                        App.msg('error', '平级开号限定奖级允许时,平级开号最低奖级不能0');
                        return false;
                    }
                    if(equalLevelMaxCode == ''){
                        App.msg('error', '平级开号限定奖级允许时,需要平级开号最高奖级');
                        return false;
                    }
                    if(equalLevelMaxCode == '0'){
                        App.msg('error', '平级开号限定奖级允许时,平级开号最高奖级不能0');
                        return false;
                    }
                    if(equalLevelMinCode > equalLevelMaxCode){
                        App.msg('error', '平级开号限定奖级允许时,平级开号最低奖级不能高于平级开号最高奖级');
                        return false;
                    }
                    if(equalLevelMinCode > lotteryMaxCode){
                        App.msg('error', '平级开号最低奖级不能高于此层级最高奖级');
                        return false;
                    }
                    if(equalLevelMaxCode > lotteryMaxCode){
                        App.msg('error', '平级开号最高奖级不能高于此层级最高奖级');
                        return false;
                    }
                }
                var data = {
                    id: v.id,
                    code: code,
                    userGroup: userGroup,
                    name: name,
                    parentCode: parentCode,
                    userLevel: userLevel,
                    lotteryMinCode: lotteryMinCode,
                    lotteryMaxCode: lotteryMaxCode,
                    equalLevel: equalLevel,
                    equalLevelCode: equalLevelCode,
                    equalLevelMinCode: equalLevelMinCode,
                    equalLevelMaxCode: equalLevelMaxCode,
                    allowedUserType: allowedUserTypeStr  // 添加这个字段
                }
                doUpdate(data, function(){
                    doList();
                });
            });
            oldEls.replaceWith(thisRow);
        }

        var doSave = function(data, cb){
            var agentNo = thisTable.find('select[name="agent"]').val();
            if(!agentNo){
                App.msg('info', '请先选择平台代理商');
                return;
            }
            data.agentNo = agentNo;
            var url = Route.PATH + Route.System.PATH + Route.System.ADD_PLAYER_ACCOUNT_TYPE;
            App.ajaxPost(url, data, function(){
                if(cb){
                    cb();
                }
                App.msg('success', '操作成功');
            });
        }

        var doDelete = function(id, cb){
            var url = Route.PATH + Route.System.PATH + Route.System.DELETE_PLAYER_ACCOUNT_TYPE;
            var data = {id: id};
            App.ajaxPost(url, data, function(){
                if(cb){
                    cb();
                }
                App.msg('success', '操作成功');
            });
        }

        var doUpdate = function(data, cb){
            var url = Route.PATH + Route.System.PATH + Route.System.UPDATE_PLAYER_ACCOUNT_TYPE;
            App.ajaxPost(url, data, function(){
                if(cb){
                    cb();
                }
                App.msg('success', '操作成功');
            });
        }

        var doList = function(){
            var agentNo = thisTable.find('select[name="agent"]').val();
            if(!agentNo){
                return;
            }
            var url = Route.PATH + Route.System.PATH + Route.System.LIST_PLAYER_ACCOUNT_TYPE;
            App.ajaxPost(url, {agentNo: agentNo}, buildList);
        }

        var buildList = function(data){
             // 保存完整的用户类型数据
            allUserTypesData = data || [];
            thisTable.find('tbody').empty();
            $.each(data, function(i, v){
                // 处理allowedUserType显示
                var allowedUserTypeDisplay = '';
                if(v.allowedUserType) {
                    var types = v.allowedUserType.split(',');
                    var typeNames = [];
                    types.forEach(function(type) {
                        if(type === 'player') {
                            typeNames.push('玩家');
                        } else if(type === 'agent') {
                            typeNames.push('代理');
                        } else {
                            // 查找自定义用户类型的名称
                            var foundType = data.find(function(item) {
                                return item.code === type;
                            });
                            if(foundType) {
                                typeNames.push(foundType.name);
                            } else {
                                typeNames.push(type); // 如果找不到，显示code
                            }
                        }
                    });
                    allowedUserTypeDisplay = typeNames.join(',');
                }
                var innerHtml =
                    '<tr>' +
                    '<td class="text-center">' + v.code + '</td>' +
                    '<td class="text-center">' + v.userGroup + '</td>' +
                    '<td class="text-center">' + v.name + '</td>' +
                    '<td class="text-center">' + (v.parentCode ? v.parentCode : '无') + '</td>' +
                    '<td class="text-center">' + v.userLevel + '</td>' +
                    '<td class="text-center">' + allowedUserTypeDisplay + '</td>' +
                    '<td class="text-center">' + v.lotteryMinCode + '</td>' +
                    '<td class="text-center">' + v.lotteryMaxCode + '</td>' +
                    '<td class="text-center">' + DataFormat.AccountType.equalLevel(v.equalLevel) + '</td>' +
                    '<td class="text-center">' + DataFormat.AccountType.equalLevel(v.equalLevelCode) + '</td>' +
                    '<td class="text-center">' + v.equalLevelMinCode + '</td>' +
                    '<td class="text-center">' + v.equalLevelMaxCode + '</td>' +
                    '<td class="text-center">' +
                    '<button name="edit" type="button" class="btn btn-sm btn-small"><i class="fa fa-edit"></i> 编辑</button> ' +
                    '<button name="remove" type="button" class="btn btn-sm btn-small"><i class="fa fa-times"></i> 删除</button>' +
                    '</td>' +
                    '</tr>';
                var thisRow = $(innerHtml);
                thisRow.find('button[name="edit"]').click(function(){
                    editData(thisRow, v);
                });
                thisRow.find('button[name="remove"]').click(function(){
                    App.dialog('确定该条数据删除？', '确定', function(){
                        doDelete(v.id, function(){
                            thisRow.remove();
                        });
                    });
                });
                thisTable.find('tbody').append(thisRow);
            });
        }


        // 获取所有用户类型的方法
        // 从现有表格中获取所有用户类型数据
        var getAllUserTypes = function() {
            // 直接返回从服务器获取的完整数据
            return allUserTypesData.map(function(item) {
                return {
                    code: item.code,
                    name: item.name,
                    userLevel: parseInt(item.userLevel)
                };
            });
        };


        // 根据当前用户级别和表格数据动态生成可选的用户类型
        var buildUserTypeOptions = function(currentUserLevel, allUserTypes, currentUserCode) {
            var options = '';

            // 如果当前类型是 player，不可配置任何用户类型
            if(currentUserCode === 'player') {
                console.log('当前类型为player，不可配置任何用户类型');
                return options;
            }

            // 如果当前类型是 agent，只能配置 player
            if(currentUserCode === 'agent') {
                options += '<option value="player">玩家</option><option value="agent">代理</option>';
                console.log('当前类型为agent，只能配置player类型');
                return options;
            }

            // 基础用户类型 - 玩家和代理总是显示（除非是自己）
            if(currentUserCode !== 'player') {
                options += '<option value="player">玩家</option>';
                console.log('添加选项: 玩家');
            }
            if(currentUserCode !== 'agent') {
                options += '<option value="agent">代理</option>';
                console.log('添加选项: 代理');
            }

            // 确保 allUserTypes 是数组并且有数据
            if(Array.isArray(allUserTypes) && allUserTypes.length > 0) {
                allUserTypes.forEach(function(type) {
                    console.log('检查类型:', type, '当前用户级别:', currentUserLevel, '类型级别:', type.userLevel);
                    // 显示下一级的用户类型（userLevel = currentUserLevel + 1）
                    // 排除基础类型（player、agent）和当前编辑的类型
                    if(type.userLevel === currentUserLevel + 1 &&
                    type.code !== 'player' &&
                    type.code !== 'agent' &&
                    type.code !== currentUserCode) {
                        options += '<option value="' + type.code + '">' + type.name + '</option>';
                        console.log('添加选项:', type.name, '(', type.code, ')');
                    } else {
                        console.log('跳过选项:', type.name, '原因: 级别不匹配或是基础类型');
                    }
                });
            } else {
                console.log('allUserTypes 为空或不是数组');
            }

            console.log('最终生成的options:', options);
            return options;
        };

        // 获取当前用户组
        var getCurrentUserGroup = function() {
            // 方法1：从全局变量获取
            if(window.currentUser && window.currentUser.userGroup) {
                return window.currentUser.userGroup;
            }

            // 方法2：从session storage获取
            var userInfo = sessionStorage.getItem('userInfo');
            if(userInfo) {
                try {
                    var user = JSON.parse(userInfo);
                    if(user.userGroup) {
                        return user.userGroup;
                    }
                } catch(e) {
                    console.log('解析用户信息失败:', e);
                }
            }

            // 方法3：从页面元素获取
            var userGroupEl = $('#currentUserGroup');
            if(userGroupEl.length && userGroupEl.val()) {
                return userGroupEl.val();
            }

            // 默认返回空
            return '';
        };

        var loadAgent = function(){
            var e = thisTable.find('select[name="agent"]');
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
            App.staticPost(url, {}, function(result){
                var item = result.data;
                $.each(item, function(i, v){
                    e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
                });
                e.trigger('change');
            });
        };

        thisTable.find('select[name="agent"]').change(function(){
            doList();
        });
        thisTable.find('[data-command="add"]').click(function(){
            addNew();
        });

        loadAgent();
        doList();
    };

    playerSettings.init();
    accountTypeTable();
});
