$(document).ready(function () {

    var id = App.urlParam('id');

    // 用户列表
    var AccountInfo = function () {

        var loadData = function () {
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.GET_PLAYER_DETAILS;
            var data = {id: id};
            App.ajaxPost(url, data, buildData);
        }

        var buildData = function (data) {
            buildAccount(data);
        }

        var buildAccount = function (data) {
			vue_app.update_player_data(data);
            var a = data;
            var t = $('#table-account')
            $('title').html(a.playerName + '用户详情');

            t.find('[data-field="id"]').html(a.playerId);
            t.find('[data-field="agentName"]').html(a.agentName);
            t.find('[data-field="playerName"]').html(a.playerName);
            t.find('[data-field="nickname"]').html(a.playerNick);
            t.find('[data-field="withdrawName"]').html(a.withdrawName);
            t.find('[data-field="googleBind"]').html(a.googleBind === 0 ? '未绑定' : '已绑定');
            t.find('[data-field="googleLogin"]').html(a.googleLogin === 0 ? '未开启' : '已开启');

            t.find('[data-field="level"]').html(a.playerLevelId);

			t.find('[data-field="playerAvailableBalance"]').html(a.playerAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}));
            t.find('[data-field="playerAvailableWithdrawBalance"]').html(a.playerAvailableWithdrawBalance.toLocaleString('en-US', {minimumFractionDigits: 3}));
			t.find('[data-field="lotteryCode"]').html(a.lotteryCode);

            t.find('[data-field="withdrawName"]').html(a.withdrawName == null ? '未绑定' : a.withdrawName);
            t.find('[data-field="withdrawPassword"]').html(a.withdrawPassword == null ? '未设置' : a.withdrawPassword);


            t.find('[data-field="registTime"]').html(moment(a.createTime).format('YYYY-MM-DD HH:mm:ss'));
            t.find('[data-field="loginTime"]').html(a.lastLoginTime == null ? '从未登录' : moment(a.lastLoginTime).format('YYYY-MM-DD HH:mm:ss'));

            var formatLockTime = '无';
            if (a.lockTime) {
                formatLockTime = App.diffNow(a.lockTime);
            }

            t.find('[data-field="withdrawBalance"]').html(data.playerAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}));

            t.find('[data-field="lockTime"]').html(formatLockTime);
            t.find('[data-field="status"]').html(DataFormat.AgentPlayer.status(a.playerStatus));
            t.find('[data-field="withdrawLimit"]').html(DataFormat.AgentPlayer.status(a.withdrawLimit));

            t.find('[data-field="onlineStatus"]').html(DataFormat.AgentPlayer.onlineStatus(a.onlineStatus));

            t.find('[data-field="bindStatus"]').html(DataFormat.AgentPlayer.bindStatus(a.bindStatus));

            t.find('[data-field="totalBalance"]').html(data.playerAvailableBalance.toLocaleString('en-US', {minimumFractionDigits: 3}) + '元');
			t.find('[data-field="email"]').html(a.email == null ? '':a.email);
			t.find('[data-field="telephone"]').html(a.telephone == null ? '':a.telephone);
			t.find('[data-field="accountTypeName"]').html(a.accountTypeName);
			t.find('[data-field="point"]').html(a.point);
            // 添加 lotteryCode 字段
            t.find('[data-field="lotteryCode"]').html(a.lotteryCode);
			t.find('[data-field="equalLevel"]').html(a.equalLevel === 0 ? "关闭" : "开启");
			t.find('[data-field="allowTransferToDown"]').html((a.allowTransfer === 1 || a.allowTransfer === 2) ? "开启" : "关闭");
			t.find('[data-field="allowTransferToUp"]').html((a.allowTransfer === 1 || a.allowTransfer === 3) ? "开启" : "关闭");
			t.find('[data-field="upPlayerNames"]').html(a.upPlayerNames);
            t.find('[data-field="loginFailedNum"]').html(a.loginFailedNum);
            t.find('[data-field="paymentPasswordFailedNum"]').html(a.paymentPasswordFailedNum);

            // t.find('[data-field="googleBind"]').html(a.googleBind ? '已绑定' : '未绑定');
            // t.find('[data-field="googleLogin"]').html(a.googleLogin ? '开启' : '关闭');
            // t.find('[data-field="chaseLimit"]').html(a.chaseLimit ? '开启' : '关闭');
            // t.find('[data-field="registLinkStatus"]').html(data.registLinkStatus ? '开启' : '关闭');


            // var errorDate = '';
            // if (a.passwordErrorDate != null) {
            //     errorDate = moment(a.passwordErrorDate).format('YYYY-MM-DD');
            // }
            // var isPasswordErrorLock = '(' + errorDate + '未达错误次数)';
            // if (data.isPasswordErrorLock == true) {
            //     isPasswordErrorLock = '(' + errorDate + '已达错误次数,无法登录)';
            // }
            // t.find('[data-field="passwordErrorCount"]').html(a.passwordErrorCount + ' ' + isPasswordErrorLock);


            var formatRemarks = a.markersStatus;
            if (formatRemarks == null || formatRemarks == '') {
                formatRemarks = '无';
            }
            t.find('[data-field="remarks"]').html(formatRemarks);

            // var accountAbnormal = '否';
            // var abnormalRemarks = '无';
            // t.find('[data-command="modify-abnormal"]').attr('data-abnormal', data.accountAbnormal.teamAbnormal);
            // if (data.accountAbnormal != null) {
            //     accountAbnormal = '<span class="label label-important">是</span>';
            //     if (data.accountAbnormal.teamAbnormal) {
            //         accountAbnormal += ' (整个线路)';
            //         t.find('[data-command="modify-abnormal"]').html('<i class="fa fa-bell-slash"></i> 取消线路异常');
            //     } else {
            //         accountAbnormal += ' (单个)';
            //         t.find('[data-command="modify-abnormal"]').html('<i class="fa fa-bell"></i> 线路异常');
            //     }
            //     if (data.accountAbnormal.remarks != null && data.accountAbnormal.remarks != '') {
            //         abnormalRemarks = data.accountAbnormal.remarks;
            //     }
            // } else {
            //     t.find('[data-command="modify-abnormal"]').html('<i class="fa fa-bell"></i> 线路异常');
            // }
            // t.find('[data-field="abnormal"]').html(accountAbnormal);
            // t.find('[data-field="abnormalRemarks"]').html(abnormalRemarks);

            // t.find('[data-command="modify-line-forbidden"]').attr('data-line-forbidden', data.accountForbiddenTeam);
            // if (data.accountForbiddenTeam) {
            //     t.find('[data-command="modify-line-forbidden"]').html('<i class="fa fa-unlock"></i> 线路启用');
            // } else {
            //     t.find('[data-command="modify-line-forbidden"]').html('<i class="fa fa-lock"></i> 线路禁用');
            // }

            // 禁用/启用账号按钮
            t.find('[data-command="update-status"]').attr('data-status', a.playerStatus);
            if (a.playerStatus >= 0) {
                t.find('[data-command="update-status"]').html('<i class="fa fa-ban"></i> 禁用账号');
            } else {
                t.find('[data-command="update-status"]').html('<i class="fa fa-check"></i> 启用账号');
            }

            // 禁用/启用谷歌验证
            t.find('[data-command="update-google-login"]').attr('google-login-flag', a.googleLogin);
            if (a.googleLogin) {
                t.find('[data-command="update-google-login"]').html('<i class="fa fa-ban"></i> 关闭用谷歌验证');
            } else {
                t.find('[data-command="update-google-login"]').html('<i class="fa fa-check"></i> 开启谷歌验证');
            }

            t.find('[data-command="modify-player-type"]').attr("data-value", a.playerType);
            t.find('[data-command="modify-point"]').attr('data-value', a.point.toFixed(1));
            // 禁用/启用同级开号
            t.find('[data-command="modify-equal-level"]').attr("equal-level", a.equalLevel);
            if (a.equalLevel === 1) {
                t.find('[data-command="modify-equal-level"]').html('<i class="fa fa-ban"></i> 关闭同级开号');
            } else {
                t.find('[data-command="modify-equal-level"]').html('<i class="fa fa-check"></i> 开启同级开号');
            }
            // 禁用/启用给下级转账
            t.find('[data-command="modify-allow-transfer-to-down"]').attr("allow-transfer", a.allowTransfer);
            if (a.allowTransfer === 1 || a.allowTransfer === 2) {
                t.find('[data-command="modify-allow-transfer-to-down"]').html('<i class="fa fa-ban"></i> 关闭给下级转账');
            } else {
                t.find('[data-command="modify-allow-transfer-to-down"]').html('<i class="fa fa-check"></i> 开启给下级转账');
            }
            // 禁用/启用给上级转账
            t.find('[data-command="modify-allow-transfer-to-up"]').attr("allow-transfer", a.allowTransfer);
            if (a.allowTransfer === 1 || a.allowTransfer === 3) {
                t.find('[data-command="modify-allow-transfer-to-up"]').html('<i class="fa fa-ban"></i> 关闭给上级转账');
            } else {
                t.find('[data-command="modify-allow-transfer-to-up"]').html('<i class="fa fa-check"></i> 开启给上级转账');
            }

            if (t.find('.button-group').attr('data-init') == 'true') {
                return false;
            }

            // 修改登录密码按钮
            t.find('[data-command="modify-login-password"]').off('click').click(function () {
                App.dialog('修改登录密码后账户在<font color="#f35958">' + 1 + '小时内</font>将无法取款，确定继续吗？', '继续修改', function () {
                    ModifyPasswordModal.show('login-password', '修改账户登录密码');
                });
            });

            // 修改资金密码按钮
            t.find('[data-command="modify-withdraw-password"]').off('click').click(function () {
                App.dialog('修改资金密码后账户在<font color="#f35958">' + 1 + '小时内</font>将无法取款，确定继续吗？', '继续修改', function () {
                    ModifyPasswordModal.show('withdraw-password', '修改账户资金密码');
                });
            });

            // 修改取款人按钮
            t.find('[data-command="modify-withdraw-name"]').off('click').click(function () {
                App.dialog('修改取款人后账户在<font color="#f35958">' + 1 + '小时内</font>将无法取款，确定继续吗？', '继续修改', function () {
                    ModifyWithdrawNameModal.show();
                });
            });

            // // 重置密保按钮
            // t.find('[data-command="reset-security"]').click(function () {
            //     App.dialog('重置密保后账户在<font color="#f35958">' + adminLockWithdrawForSecurity + '小时内</font>将无法取款，确定继续吗？', '确定', function () {
            //         var url = Route.PATH + Route.Agent.PATH + Route.Agent.RESET_ACCOUNT_SECURITY;
            //         var data = {playerId: id};
            //         App.ajaxPost(url, data, function () {
            //             AccountInfo.init();
            //             App.msg('success', '操作成功');
            //         });
            //     });
            // });

            // 账户充值
            // t.find('[data-command="recharge"]').click(function () {
            //     ManualAccountRechargeModal.show();
            // });


            // 账户状态
            // t.find('[data-command="update-status"]').click(function () {
            //     var status = $(this).attr('data-status');
            //     if (status >= 0) {
            //         ModifyAccountStatusModal.disable();
            //     } else {
            //         App.dialog('确定恢复该用户至正常状态？', '确定', function () {
            //             ModifyAccountStatusModal.normal();
            //         });
            //     }
            // });

            // 解除时间锁
            t.find('[data-command="reset-lock-time"]').off('click').click(function () {
                App.dialog('确定解除该账户的时间锁？', '确定', function () {
                    var data = {id: id};
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.RESET_ACCOUNT_LOCK_TIME;
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });











            // 清除登录错误次数
            t.find('[data-command="modify-login-failed-num"]').off('click').click(function () {
                App.dialog('确定清除登录错误次数？', '确定', function () {
                    var data = {id: id};
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.RESET_ACCOUNT_LOGIN_FAILED_NUM;
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });


            // 清除资金密码错误次数
            t.find('[data-command="modify-payment-password-failed-num"]').off('click').click(function () {
                App.dialog('清除资金密码错误次数？', '确定', function () {
                    var data = {id: id};
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.RESET_PAYMENT_PASSWORD_FAILED_NUM;
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });









            // 清空提款限制
            t.find('[data-command="clear-withdraw-limit"]').off('click').click(function () {
                App.dialog('确定要清空提款限制？', '确定', function () {
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.CLEAR_ACCOUNT_WITHDRAW_LIMIT;
                    var data = {id: id};
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });

            // 开启/关闭谷歌验证
            t.find('[data-command="update-google-login"]').off('click').click(function () {
                var flag = $(this).attr('google-login-flag');
                if (flag == '1') {
                    App.dialog('确定关闭谷歌验证？', '确定关闭', function () {
                        var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_GOOGLE_LOGIN;
                        var data = {id: id, flag: 0};
                        App.ajaxPost(url, data, function () {
                            AccountInfo.init();
                            App.msg('success', '操作成功');
                        });
                    });
                } else {
                    App.dialog('确定开启谷歌验证？', '确定开启', function () {
                        var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_GOOGLE_LOGIN;
                        var data = {id: id, flag: 1};
                        App.ajaxPost(url, data, function () {
                            AccountInfo.init();
                            App.msg('success', '操作成功');
                        });
                    });
                }
            });

            // 重置谷歌绑定
            t.find('[data-command="reset-google-login"]').off('click').click(function () {
                App.dialog('确定重置谷歌绑定？', '确定绑定', function () {
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.RESET_GOOGLE_BIND;
                    var data = {id: id};
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });

            // 重置密码错误次数
            t.find('[data-command="reset-password-error-count"]').off('click').click(function () {
                App.dialog('确定重置该账户的密码登入错误次数？', '确定', function () {
                    var data = {id: id};
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.RESET_ACCOUNT_PASSWORD_ERROR_COUNT;
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });

            // 修改返点
            t.find('[data-command="modify-point"]').off("click").click(function () {
                var value = $(this).attr('data-value');
                var url = Route.PATH + Route.Agent.PATH + Route.Agent.PREPARE_MODIFY_POINT;
                var data = {id: id};
                App.ajaxPost(url, data, function(res) {
                    ModifyPointModal.show(value, res.minPoint, res.maxPoint);
                });
            });
            // 线路统一降点
            t.find('[data-command="modify-line-point"]').off("click").click(function() {
                App.dialog('该代理以及下级所有会员返点下降0.1，确认继续？', '确认', function() {
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_LINE_POINT;
                    var data = {id: id};
                    App.ajaxPost(url, data, function() {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });
            // 线路统一升点
            t.find('[data-command="increase-line-point"]').off("click").click(function() {
                App.dialog('该代理以及下级所有会员返点上升0.1，确认继续？', '确认', function() {
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.INCREASE_LINE_POINT;
                    var data = {id: id};
                    App.ajaxPost(url, data, function() {
                        AccountInfo.init();
                        App.msg('success', '操作成功');
                    });
                });
            });
            // 修改用户类型
            t.find('[data-command="modify-player-type"]').off("click").click(function () {
                var playerType = $(this).attr('data-value');
                ModifyPlayerTypeModal.show(playerType);
            });
            // 开启/关闭同级开号
            t.find('[data-command="modify-equal-level"]').off("click").click(function () {
                var equalLevel = $(this).attr("equal-level");
                if (equalLevel === "1") {
                    App.dialog("确定关闭同级开号？", "确定关闭", function () {
                        var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_EQUAL_LEVEL;
                        var data = {id: id, equalLevel: 0};
                        App.ajaxPost(url, data, function () {
                            AccountInfo.init();
                            App.msg("success", "操作成功");
                        });
                    });
                } else {
                    App.dialog('确定开启同级开号？', '确定开启', function () {
                        var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_EQUAL_LEVEL;
                        var data = {id: id, equalLevel: 1};
                        App.ajaxPost(url, data, function () {
                            AccountInfo.init();
                            App.msg("success", "操作成功");
                        });
                    });
                }
            });
            // 开启/关闭给下级转账
            t.find('[data-command="modify-allow-transfer-to-down"]').off("click").click(function () {
                var allowTransfer = $(this).attr("allow-transfer");
                var tips = "开启";
                var newAllowTransfer = 1;
                if (allowTransfer === "1") {
                    tips = "关闭";
                    newAllowTransfer = 3;
                } else if(allowTransfer === "2") {
                    tips = "关闭";
                    newAllowTransfer = 0;
                } else if(allowTransfer === "0") {
                    newAllowTransfer = 2;
                }
                App.dialog("确定" + tips + "给下级转账？", "确定" + tips, function () {
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_ALLOW_TRANSFER;
                    var data = {id: id, allowTransfer: newAllowTransfer};
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg("success", "操作成功");
                    });
                });
            });
            // 开启/关闭给上级转账
            t.find('[data-command="modify-allow-transfer-to-up"]').off("click").click(function () {
                var allowTransfer = $(this).attr("allow-transfer");
                var tips = "开启";
                var newAllowTransfer = 1;
                if (allowTransfer === "1") {
                    tips = "关闭";
                    newAllowTransfer = 2;
                } else if(allowTransfer === "3") {
                    tips = "关闭";
                    newAllowTransfer = 0;
                } else if(allowTransfer === "0") {
                    newAllowTransfer = 3;
                }
                App.dialog("确定" + tips + "给上级转账？", "确定" + tips, function () {
                    var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_ALLOW_TRANSFER;
                    var data = {id: id, allowTransfer: newAllowTransfer};
                    App.ajaxPost(url, data, function () {
                        AccountInfo.init();
                        App.msg("success", "操作成功");
                    });
                });
            });
            // 修改上级用户
            t.find('[data-command="modify-parent-player"]').off("click").click(function () {
                App.dialog("确定修改上级用户？", "确定修改", function () {
                    ModifyParentPlayerModal.show(a.playerName, a.playerId, a.pid);
                });
            });
        }

        var init = function () {
            loadData();
        }

        return {
            init: init
        }
    }();

	vue_app.AccountInfo = AccountInfo;

    /**
     * 修改账户密码
     */
    var ModifyPasswordModal = function () {
        var modal = $('#modal-modify-password');
        var form = modal.find('form');
        var initForm = function () {
            form.validate({
                rules: {
                    password1: {
                        required: true,
                        minlength: 6
                    },
                    password2: {
                        required: true,
                        minlength: 6,
                        equalTo: 'input[name="password1"]'
                    }
                },
                messages: {
                    password1: {
                        required: '该字段不能为空！',
                        minlength: '至少输入{0}个字符',
                    },
                    password2: {
                        required: '该字段不能为空！',
                        equalTo: '两次密码不一致！',
                        minlength: '至少输入{0}个字符',
                    }
                },
                invalidHandler: function (event, validator) {
                },
                errorPlacement: function (error, element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
                    }
                },
                highlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-success').addClass('has-error');
                    }
                },
                unhighlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-error').addClass('has-success');
                        $(element).closest('.form-group').find('.help-block').empty().hide();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
                    }
                }
            });
            modal.find('[data-command="submit"]').off('click').click(function () {
                if (form.validate().form()) {
                    doSubmit();
                }
            });
        }

        var doSubmit = function () {
            var action = modal.attr('data-action');
            var password = form.find('input[name="password1"]').val();
            if (action == 'login-password') {
                var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_LOGIN_PASSWORD;
                var data = {id: id, password: password};
                App.ajaxPost(url, data, function () {
                    modal.modal('hide');
                    AccountInfo.init();
                    App.msg('success', '操作成功');
                });
            }
            if (action == 'withdraw-password') {
                var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_WITHDRAW_PASSWORD;
                var data = {id: id, password: password};
                App.ajaxPost(url, data, function () {
                    modal.modal('hide');
                    AccountInfo.init();
                    App.msg('success', '操作成功');
                });
            }
        }

        var reset = function () {
            var formGroup = form.find('.form-group');
            formGroup.removeClass('has-error');
            formGroup.removeClass('has-success');
            formGroup.find('.glyphicon').hide();
            formGroup.find('.help-block').empty().hide();
            form[0].reset();
        }

        var show = function (action, title) {
            if (action) {
                reset();
                modal.attr('data-action', action);
                modal.find('[data-field="title"]').html(title);
                modal.find('[data-field="tips"]').html('密码由6~16位数字和字母组合');
                if (action == 'login-password') {
                    form.find('input[name="password1"]').val('ab123456');
                    form.find('input[name="password2"]').val('ab123456');
                    modal.modal('show');
                } else {
                    modal.find('[data-field="notice"]').html('默认设置资金密码ab123456');
                    form.find('input[name="password1"]').val('ab123456');
                    form.find('input[name="password2"]').val('ab123456');
                    modal.modal('show');
                }
            }
        }

        var init = function () {
            initForm();
        }

        return {
            init: init,
            show: show
        }

    }();

    /**
     * 修改账户取款人
     */
    var ModifyWithdrawNameModal = function () {
        var modal = $('#modal-modify-withdraw-name');
        var form = modal.find('form');
        var initForm = function () {
            form.validate({
                rules: {
                    name: {
                        required: true
                    }
                },
                messages: {
                    name: {
                        required: '该字段不能为空！'
                    }
                },
                invalidHandler: function (event, validator) {
                },
                errorPlacement: function (error, element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
                    }
                },
                highlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-success').addClass('has-error');
                    }
                },
                unhighlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-error').addClass('has-success');
                        $(element).closest('.form-group').find('.help-block').empty().hide();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
                    }
                }
            });
            modal.find('[data-command="submit"]').off('click').click(function () {
                if (form.validate().form()) {
                    doSubmit();
                }
            });
        }

        var doSubmit = function () {
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_WITHDRAW_NAME;
            var name = form.find('input[name="name"]').val();
            var data = {id: id, name: name};
            App.ajaxPost(url, data, function () {
                modal.modal('hide');
                AccountInfo.init();
                App.msg('success', '操作成功');
            });
        }

        var reset = function () {
            var formGroup = form.find('.form-group');
            formGroup.removeClass('has-error');
            formGroup.removeClass('has-success');
            formGroup.find('.glyphicon').hide();
            formGroup.find('.help-block').empty().hide();
            form[0].reset();
        }

        var show = function () {
            reset();
            modal.modal('show');
        }

        var init = function () {
            initForm();
        }

        return {
            init: init,
            show: show
        }
    }();

    var ModifyPointModal = function() {
        var modal = $('#modal-modify-point');
        var form = modal.find('form');
        var initForm = function() {
            form.validate({
                rules: {
                    point: {
                        required: true,
                        min: 0,
                        oneDecimal: true
                    }
                },
                messages: {
                    point: {
                        required: "该字段不能为空！",
                        min: "不能小于0！",
                        oneDecimal: "最多1位小数！"
                    }
                },
                invalidHandler: function (event, validator) {},
                errorPlacement: function (error, element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
                    }
                },
                highlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-success').addClass('has-error');
                    }
                },
                unhighlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-error').addClass('has-success');
                        $(element).closest('.form-group').find('.help-block').empty().hide();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
                    }
                }
            });
            modal.find('[data-command="submit"]').off("click").click(function() {
                if (form.validate().form()) {
                    doSubmit();
                }
            });
        }

        var lastPoint = 0;
        var doSubmit = function() {
            var point = form.find('input[name="point"]').val().trim();
            var data = {
                id: id,
                point: point
            }
            if (point == lastPoint) {
                App.msg('info', '返点值没有任何改变');
                return;
            }
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_POINT;
            App.ajaxPost(url, data, function() {
                modal.modal('hide');
                AccountInfo.init();
                App.msg('success', '操作成功');
            });
        }

        var reset = function() {
            var formGroup = form.find('.form-group');
            formGroup.removeClass('has-error');
            formGroup.removeClass('has-success');
            formGroup.find('.glyphicon').hide();
            formGroup.find('.help-block').empty().hide();
            form[0].reset();
        }

        var show = function(point, minPoint, maxPoint) {
            reset();
            lastPoint = point; // 上次的值
            form.find('input[name="point"]').val(point);
            if (minPoint <= maxPoint) {
                var message = '返点修改范围最低' + minPoint.toFixed(1) + '至最高' + maxPoint.toFixed(1);
                form.find('[data-field="msg-point"]').html(message);
            } else {
                form.find('[data-field="msg-point"]').html('无法修改返点信息');
            }
            modal.modal('show');
        }

        var init = function() {
            initForm();
        }

        return {
            init: init,
            show: show
        }
    }();

    var ModifyPlayerTypeModal = function() {
        var modal = $('#modal-modify-player-type');
        var form = modal.find('form');

        var doSubmit = function() {
            var playerType = form.find('select[name="playerType"]').val();
            var data = {
                id: id,
                playerType: playerType
            }
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_PLAYER_TYPE;
            App.ajaxPost(url, data, function() {
                modal.modal('hide');
                AccountInfo.init();
                App.msg('success', '操作成功');
            });
        }

        var reset = function() {
            var formGroup = form.find('.form-group');
            formGroup.removeClass('has-error');
            formGroup.removeClass('has-success');
            formGroup.find('.glyphicon').hide();
            formGroup.find('.help-block').empty().hide();
            form[0].reset();
        }

        var show = function(playerType) {
            reset();
            form.find('select[name="playerType"]').find('option[value="' + playerType + '"]').attr('selected', true);
            modal.modal('show');
        }
        modal.find('[data-command="submit"]').off("click").click(function() {
            doSubmit();
        });

        return {
            show: show
        }
    }();

    /**
     * 修改上级用户
     */
    var ModifyParentPlayerModal = function () {
        var modal = $('#modal-modify-parent-player');
        var form = modal.find('form');

        var initForm = function () {
            form.validate({
                rules: {
                    pid: {
                        required: true,
                        digits: true
                    }
                },
                messages: {
                    pid: {
                        required: '该字段不能为空！',
                        digits: '请输入有效的用户ID'
                    }
                },
                invalidHandler: function (event, validator) {
                },
                errorPlacement: function (error, element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').find('.help-block').html('<i class="fa fa-warning"></i> ' + error.text()).show();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-ok').addClass('glyphicon-remove').show();
                    }
                },
                highlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-success').addClass('has-error');
                    }
                },
                unhighlight: function (element) {
                    if ($(element).closest('.form-group').attr('novalidate') != 'true') {
                        $(element).closest('.form-group').removeClass('has-error').addClass('has-success');
                        $(element).closest('.form-group').find('.help-block').empty().hide();
                        $(element).closest('.form-group').find('.glyphicon').removeClass('glyphicon-remove').addClass('glyphicon-ok').show();
                    }
                }
            });

            modal.find('[data-command="submit"]').off('click').click(function () {
                if (form.validate().form()) {
                    doSubmit();
                }
            });
        }

        var doSubmit = function () {
			var type = form.find('input[name="type"]:checked').val();
			var newPlayerName = form.find('input[name="newPlayerName"]').val();
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.UPDATE_PID;
            var data = {playerId: id, type:type, newPlayerName:newPlayerName};
            App.ajaxPost(url, data, function () {
                modal.modal('hide');
                AccountInfo.init();
                App.msg('success', '操作成功');
            });
        }

        var reset = function () {
            var formGroup = form.find('.form-group');
            formGroup.removeClass('has-error');
            formGroup.removeClass('has-success');
            formGroup.find('.glyphicon').hide();
            formGroup.find('.help-block').empty().hide();
            form[0].reset();
        }

        var show = function (playerName, playerId, currentPid) {
            reset();
            modal.find('[data-field="playerName"]').text(playerName);
            modal.find('[data-field="playerId"]').text(playerId);
            form.find('input[name="pid"]').val(currentPid || '');
            modal.modal('show');
        }

        var init = function () {
            initForm();
        }

        return {
            init: init,
            show: show
        }
    }();

    AccountInfo.init();
    ModifyPasswordModal.init();
    ModifyWithdrawNameModal.init();
    ModifyPointModal.init();
    ModifyParentPlayerModal.init();
});
