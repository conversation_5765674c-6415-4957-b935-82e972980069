$(document).ready(function() {

    // 亏损量预警配置管理
    var WarnConfigTable = function() {
        var thisTable = $('#table-warn-config');
        var thisPageList = thisTable.find('.page-list');

        var getParams = function() {
            return {};
        };

        var ajaxUrl = Route.PATH + Route.System.PATH + Route.GameLottery.SEARCH_EARLY_WARN_CONFIG;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 10,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: getParams,
            beforeSend: function () {
                blockUI(thisTable);
            },
            complete: function () {
                unblockUI(thisTable);
            },
            success: function (data, fullResponse) {
                buildData(fullResponse);
            },
            pageError: function (response) {
                App.dialog(response.message);
            },
            emptyData: function () {
                thisTable.find('table > tbody').html('<tr><td colspan="10" class="text-center">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data) {
            var tbody = thisTable.find('table > tbody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                return;
            }

            $.each(data.list, function(i, item) {
                var row = '<tr>';
                row += '<td class="text-center">' + item.id + '</td>';
                row += '<td class="text-center">' + item.agentName + '</td>';
                row += '<td class="text-center">' + item.warnLossAmount + '</td>';
                row += '<td class="text-center">' + item.warnDisableLossVolume + '</td>';
                row += '<td class="text-center">' + getMeasurementWayText(item.measurementWay) + '</td>';
                row += '<td class="text-center">' + getAutomaticDisableWayText(item.automaticDisableWay) + '</td>';
                row += '<td class="text-center">' + getWarnClearText(item.warnClear) + '</td>';
                row += '<td class="text-center">' + getStatusText(item.warnStatus) + '</td>';
                row += '<td class="text-center">' + (item.warnRemark || '') + '</td>';
                row += '<td class="text-center">';
                row += '<button class="btn btn-primary btn-sm" onclick="editConfig(' + item.id + ')" style="margin-right: 5px;"><i class="fa fa-edit"></i> 编辑</button>';
                if (item.warnStatus === '2') {
                    row += '<button class="btn btn-primary btn-sm" onclick="toggleStatus(' + item.id + ', \'1\')"><i class="fa fa-check-circle"></i> 启用</button>';
                } else {
                    row += '<button class="btn btn-primary btn-sm" onclick="toggleStatus(' + item.id + ', \'2\')"><i class="fa fa-ban"></i> 禁用</button>';
                }
                row += '</td>';
                row += '</tr>';
                tbody.append(row);
            });
        };



        // 文本转换函数
        var getMeasurementWayText = function(value) {
            return value === '1' ? '按彩种' : '按玩法';
        }

        var getAutomaticDisableWayText = function(value) {
            switch(value) {
                case '1': return '禁用彩种';
                case '2': return '禁用玩法';
                default: return '未知';
            }
        }

        var getWarnClearText = function(value) {
            return value === '1' ? '是' : '否';
        }

        var getStatusText = function(value) {
            return value === '1' ? '正常' : '禁用';
        }

        // 查询按钮事件
        thisTable.find('button[name="search"]').click(function() {
            pagination.reload();
        });

        // 添加配置按钮事件
        thisTable.find('button[name="add-config"]').click(function() {
            showAddConfigModal();
        });

        var init = function() {
            // 初始化分页组件
            pagination.init();
        }

        var reload = function() {
            pagination.reload();
        }

        return {
            init: init,
            reload: reload
        }
    }();

    // 添加/编辑配置模态框管理
    var ConfigModal = function() {
        var modal = $('#add-warn-config-modal');
        var form = $('#warn-config-form');

        var show = function(configData, isEdit) {
            if (configData) {
                // 编辑模式
                modal.find('.modal-title').text('编辑预警配置');
                form.find('input[name="id"]').val(configData.id);
                form.find('select[name="agent"]').find('option[value="' + configData.agentNo + '"]').attr('selected', true);
                form.find('input[name="warnLossAmount"]').val(configData.warnLossAmount);
                form.find('input[name="warnDisableLossVolume"]').val(configData.warnDisableLossVolume);
                form.find('select[name="measurementWay"]').val(configData.measurementWay);
                form.find('select[name="automaticDisableWay"]').val(configData.automaticDisableWay);
                form.find('select[name="warnClear"]').val(configData.warnClear);
                form.find('textarea[name="warnRemark"]').val(configData.warnRemark);
                form.find('select[name="agent"]').prop('disabled', true);
                // 触发计量方式联动
                $('#measurementWay').trigger('change');
            } else {
                // 添加模式
                modal.find('.modal-title').text('添加预警配置');
                form[0].reset();
                form.find('input[name="id"]').val('');
                // 设置默认值
                form.find('select[name="warnClear"]').val('1');
                form.find('select[name="measurementWay"]').val('1');
                // ⭐ 编辑模式下禁用代理商下拉框
                form.find('select[name="agent"]').prop('disabled', false);
                // 触发计量方式联动
                $('#measurementWay').trigger('change');
            }
            modal.modal('show');
        }

        var loadAgent = function() {
            var e = form.find('select[name="agent"]');
            var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_AGENT;
            App.staticPost(url, {}, function(result) {
                var item = result.data;
                $.each(item, function(i, v) {
                    e.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
                });
            });
        }

        var hide = function() {
            modal.modal('hide');
        }

        var save = function() {
            var formData = {
                agentNo: form.find('select[name="agent"]').val(),
                warnLossAmount: form.find('input[name="warnLossAmount"]').val(),
                warnDisableLossVolume: form.find('input[name="warnDisableLossVolume"]').val(),
                measurementWay: form.find('select[name="measurementWay"]').val(),
                warnWay: '1', // 固定为共有记录
                automaticDisableWay: form.find('select[name="automaticDisableWay"]').val(),
                warnClear: form.find('select[name="warnClear"]').val(),
                warnStatus: '1', // 默认启用
                warnRemark: form.find('textarea[name="warnRemark"]').val(),
                isDelete: 0
            };

            var id = form.find('input[name="id"]').val();
            var url, successMsg;

            if (id) {
                // 编辑
                formData.id = id;
                url = Route.PATH + Route.System.PATH + Route.GameLottery.UPDATE_EARLY_WARN_CONFIG;
                successMsg = '编辑成功';
            } else {
                // 添加
                url = Route.PATH + Route.System.PATH + Route.GameLottery.ADD_EARLY_WARN_CONFIG;
                successMsg = '添加成功';
            }

            console.log('保存配置请求:', {url: url, data: formData});

            // 使用jQuery直接调用，避免global.js的ajaxPost处理
            $.ajax({
                type: 'POST',
                url: url,
                data: formData,
                dataType: 'json',
                success: function(result) {
                    console.log('保存配置响应:', result);
                    if (result && (result.success || result.code === "0")) {
                        hide();
                        WarnConfigTable.reload();
                        App.msg('success', successMsg);
                    } else {
                        var errorMsg = '保存失败';
                        if (result && result.message) {
                            errorMsg = result.message;
                        } else if (!result) {
                            errorMsg = '服务器返回空响应，请检查服务器日志';
                        }
                        App.msg('error', errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('保存配置失败:', error);
                    App.msg('error', '请求失败: ' + error);
                }
            });
        }

        // 确定按钮事件
        $('#confirm-save').click(function() {
            if (form[0].checkValidity()) {
                // 额外的前端校验
                var measurementWay = form.find('select[name="measurementWay"]').val();
                var automaticDisableWay = form.find('select[name="automaticDisableWay"]').val();

                // 校验计量方式和自动禁用方式的匹配
                if ((measurementWay === '1' && automaticDisableWay !== '1') ||
                    (measurementWay === '2' && automaticDisableWay !== '2')) {
                    App.msg('warning', '计量方式和自动禁用方式不匹配，系统已自动调整');
                    // 自动调整
                    if (measurementWay === '1') {
                        form.find('select[name="automaticDisableWay"]').val('1');
                    } else if (measurementWay === '2') {
                        form.find('select[name="automaticDisableWay"]').val('2');
                    }
                }
                save();
            } else {
                App.msg('error', '请填写必填项');
            }
        });

        // 计量方式和自动禁用方式联动
        $(document).on('change', '#measurementWay', function() {
            var measurementWay = $(this).val();
            var automaticDisableWay = $('#automaticDisableWay');

            // 根据计量方式自动设置对应的自动禁用方式
            if (measurementWay === '1') {
                // 按彩种 → 禁用彩种
                automaticDisableWay.val('1');
            } else if (measurementWay === '2') {
                // 按玩法 → 禁用玩法
                automaticDisableWay.val('2');
            } else {
                // 未选择 → 清空选择
                automaticDisableWay.val('');
            }
        });

        var init = function() {
            loadAgent();
        }

        return {
            init: init,
            show: show,
            hide: hide
        }
    }();

    // 设置彩种模态框管理
    var LotteryModal = function() {
        var modal = $('#lotteryModal');
        var currentConfigId = null;
        var availableLotteries = [];
        var selectedLotteries = [];

        var loadLotteries = function() {
            // 加载真实彩种数据
            var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.LIST_LOTTERY_INFO;
            console.log('加载彩种列表URL:', url);

            App.ajaxPost(url, {}, function(result) {
                console.log('彩种列表响应:', result);
                if (result && (result.success || result.code === "0") && result.data && result.data.length > 0) {
                    availableLotteries = result.data.map(function(item) {
                        return {
                            lottery: item.lottery,
                            gameName: item.gameName || item.gameRoomName || item.lottery
                        };
                    });
                    console.log('成功加载彩种列表:', availableLotteries);
                } else {
                    console.error('彩种接口返回空数据或失败:', result);
                    availableLotteries = [];
                    App.msg('warning', '无法加载彩种列表，请检查系统配置');
                }
                renderLotteryLists();
            }, function(error) {
                console.error('加载彩种列表请求失败:', error);
                availableLotteries = [];
                App.msg('error', '加载彩种列表失败，请稍后重试');
                renderLotteryLists();
            });
        }

        var renderLotteryLists = function() {
            var availableList = $('#availableLotteries');
            var selectedList = $('#selectedLotteries');

            availableList.empty();
            selectedList.empty();

            // 渲染可选彩种
            $.each(availableLotteries, function(i, lotteryInfo) {
                if (!isLotterySelected(lotteryInfo.lottery)) {
                    var option = $('<option value="' + lotteryInfo.lottery + '">' + lotteryInfo.gameName + '</option>');
                    availableList.append(option);
                }
            });

            // 渲染已选彩种
            $.each(selectedLotteries, function(i, lotteryInfo) {
                var option = $('<option value="' + lotteryInfo.lottery + '">' + lotteryInfo.gameName + '</option>');
                selectedList.append(option);
            });
        }

        var isLotterySelected = function(lotteryCode) {
            return selectedLotteries.some(function(item) {
                return item.lottery === lotteryCode;
            });
        }

        var show = function(configId) {
            currentConfigId = configId;
            $('#configId').val(configId);
            $('#displayConfigId').text(configId);

            // 加载已选彩种
            loadSelectedLotteries(configId);
            // 加载可用彩种
            loadLotteries();

            modal.modal('show');
        }

        var loadSelectedLotteries = function(configId) {
            var url = Route.PATH + Route.System.PATH + Route.GameLottery.GET_EARLY_WARN_CONFIG;
            console.log('加载已选彩种URL:', url, 'configId:', configId);

            App.ajaxPost(url, {id: configId}, function(result) {
                console.log('已选彩种响应:', result);
                if (result && (result.success || result.code === "0") && result.data) {
                    var lotteryList = result.data.lotteryList || '';
                    console.log('彩种列表字符串:', lotteryList);

                    selectedLotteries = [];
                    if (lotteryList) {
                        try {
                            // 尝试解析JSON格式
                            if (lotteryList.startsWith('[') && lotteryList.endsWith(']')) {
                                var lotteryArray = JSON.parse(lotteryList);
                                selectedLotteries = lotteryArray.map(function(lottery) {
                                    return {lottery: lottery, gameName: lottery};
                                });
                            } else {
                                // 逗号分隔格式
                                var lotteryArray = lotteryList.split(',');
                                selectedLotteries = lotteryArray.map(function(lottery) {
                                    return {lottery: lottery.trim(), gameName: lottery.trim()};
                                });
                            }
                        } catch (e) {
                            console.error('解析彩种列表失败:', e);
                            selectedLotteries = [];
                        }
                    }
                    console.log('解析后的已选彩种:', selectedLotteries);
                } else {
                    console.warn('获取已选彩种失败:', result);
                    selectedLotteries = [];
                }
                renderLotteryLists();
            }, function(error) {
                console.error('加载已选彩种失败:', error);
                selectedLotteries = [];
                renderLotteryLists();
            });
        }

        // 添加彩种按钮
        $('#addLottery').click(function() {
            $('#availableLotteries option:selected').each(function() {
                var lottery = $(this).val();
                var name = $(this).text();
                selectedLotteries.push({lottery: lottery, gameName: name});
            });
            renderLotteryLists();
        });

        // 移除彩种按钮
        $('#removeLottery').click(function() {
            $('#selectedLotteries option:selected').each(function() {
                var lottery = $(this).val();
                selectedLotteries = selectedLotteries.filter(function(item) {
                    return item.lottery !== lottery;
                });
            });
            renderLotteryLists();
        });

        // 确定按钮
        $('#saveLottery').click(function() {
            var lotteryList = selectedLotteries.map(function(item) {
                return item.lottery;
            }).join(',');

            var url = Route.PATH + Route.System.PATH + Route.GameLottery.SET_EARLY_WARN_CONFIG_LOTTERY;
            var data = {
                id: currentConfigId,
                lotteryList: lotteryList
            };

            console.log('保存彩种设置:', {url: url, data: data});

            App.ajaxPost(url, data, function(result) {
                console.log('保存彩种设置响应:', result);
                if (result && result.success) {
                    App.msg('success', '彩种设置成功');
                    modal.modal('hide');
                } else {
                    App.msg('error', result.message || '彩种设置失败');
                }
            }, function(error) {
                console.error('保存彩种设置失败:', error);
                App.msg('error', '请求失败');
            });
        });

        return {
            show: show
        }
    }();

    // 全局函数
    window.showAddConfigModal = function() {
        ConfigModal.show();
    }

    window.editConfig = function(id) {
        // 获取配置详情
        var url = Route.PATH + Route.System.PATH + Route.GameLottery.GET_EARLY_WARN_CONFIG;
        console.log('编辑配置请求URL:', url);
        console.log('编辑配置ID:', id);

        $.ajax({
            url: url,
            type: 'POST',
            data: {id: id},
            success: function(result) {
                console.log('编辑配置响应:', result);
                if (result && (result.success || result.code === "0") && result.data) {
                    ConfigModal.show(result.data, true); // 传入true表示编辑模式
                } else {
                    App.msg('error', '加载配置数据失败: ' + (result.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error('编辑配置请求失败:', {xhr: xhr, status: status, error: error});
                console.error('响应内容:', xhr.responseText);
                App.msg('error', '请求失败: ' + error);
            }
        });
    }

    window.setLottery = function(configId) {
        LotteryModal.show(configId);
    }

    window.toggleStatus = function(id, status) {
        var action = status === '1' ? '启用' : '禁用';
        App.dialog('确定要' + action + '该配置吗？', '确定', function() {
            var url = Route.PATH + Route.System.PATH + Route.GameLottery.UPDATE_EARLY_WARN_CONFIG_STATUS;
            var data = {id: id, warnStatus: status};
            App.ajaxPost(url, data, function() {
                WarnConfigTable.reload();
                App.msg('success', action + '成功');
            });
        });
    }

    // 初始化页面
    WarnConfigTable.init();
    ConfigModal.init();
});
