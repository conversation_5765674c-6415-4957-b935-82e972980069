$(document).ready(function(){

    var TableDataList = function(){
        var thisTable = $('#table-data-list');
        var p = thisTable.find('form[name="search-params"]');
        var thisPageList = thisTable.find('.page-list');

        p.find('input[name="startCalculateCycleDate"]').val(moment().format('YYYY-MM-DD'));
        p.find('input[name="endCalculateCycleDate"]').val(moment().add(1, 'days').format('YYYY-MM-DD'));

        var getParams = function(){
            var agentNo = p.find('input[name="agentNo"]').val().trim();
            var agentName = p.find('input[name="agentName"]').val().trim();
            // var contractTitle = p.find('input[name="contractTitle"]').val().trim();
            var toPlayerName = p.find('input[name="toPlayerName"]').val().trim();
            var fromPlayerName = p.find('input[name="fromPlayerName"]').val().trim();
            var platformOnly = p.find('input[name="platformOnly"]').is(':checked');
            var contractType = p.find('select[name="contractType"]').val();
            var totalBetMin = p.find('input[name="totalBetMin"]').val().trim();
            var startCalculateCycleDate = p.find('input[name="startCalculateCycleDate"]').val().trim();
            var endCalculateCycleDate = p.find('input[name="endCalculateCycleDate"]').val().trim();
            var drawStatus = p.find('select[name="drawStatus"]').val();
            if(platformOnly){
                fromPlayerName = "sys";
            }
            return {
                agentNo: agentNo,
                agentName: agentName,
                // contractTitle: contractTitle,
                toPlayerName: toPlayerName,
                fromPlayerName: fromPlayerName,
                contractType: contractType,
                totalBetMin: totalBetMin,
                startCalculateCycleDate: startCalculateCycleDate,
                endCalculateCycleDate: endCalculateCycleDate,
                drawStatus: drawStatus
            }
        }

        var doSearch = function(){
            pagination.init();
        }
        var ajaxUrl = Route.PATH + Route.Contract.PATH + Route.Contract.LIST_CONTRACT_RECORD;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 10,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: getParams,
            beforeSend: function(){
                blockUI(thisTable);
            },
            complete: function(){
                unblockUI(thisTable);
            },
            success: function(data){
                buildData(data);
            },
            pageError: function(response){
                App.dialog(response.message);
            },
            emptyData: function(){
                thisTable.find('table > tbody').html('<tr><td colspan="20">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data){
            thisTable.find('table > tbody').empty();
            $.each(data, function(i, v){
                var btnDrawStatus = '';
                if(v.drawStatus === 0){
                    btnDrawStatus = '<button data-command="draw" class="btn btn-primary btn-xs btn-mini" type="button"><i class="fa fa-check"></i> 发放</button>';
                }

                var array = [
                    v.agentNo,
                    v.agentName,
                    v.contractCode,
                    v.contractTitle,
                    v.toPlayerName,
                    v.fromPlayerId === 0 ? "平台" : v.fromPlayerName,
                    v.totalBet.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    v.totalLoss.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    v.activeUser,
                    v.scalePoint.toLocaleString('en-US', {maximumFractionDigits: 2}) + '%',
                    v.drawAmount.toLocaleString('en-US', {minimumFractionDigits: 3}),
                    DataFormat.Contract.drawStatus(v.drawStatus),
                    v.calculateCycle,
                    moment(v.createTime).format('YYYY-MM-DD HH:mm:ss'),
                    '<button data-command="detail" class="btn btn-primary btn-xs btn-mini" type="button"><i class="fa fa-file-text-o"></i> 详情</button>&nbsp;' +
                    btnDrawStatus
                ];
                var tr = App.buildTbody(array);
                tr.find('td').addClass('text-center');
                // 详情
                tr.find('[data-command="detail"]').click(function(){
                    detailContractRecordModal.show(v);
                });
                // 发放
                tr.find('[data-command="draw"]').click(function(){
                    doDraw(v.id);
                });
                thisTable.find('table > tbody').append(tr);
            });
        }

        var doDraw = function(id){
            // 删除
            App.dialog('确定发放该契约发放记录吗？', '确定', function(){
                var url = Route.PATH + Route.Contract.PATH + Route.Contract.DRAW_CONTRACT_RECORD;
                var data = {id: id};
                App.ajaxPost(url, data, function(){
                    reload();
                    App.msg('success', '操作成功');
                });
            });
        };

        p.find('button[name="search"]').click(function(){
            reload();
        });

        var init = function(){
            reload();
        };
        var reload = function(){
            doSearch();
        };
        return {
            init: init
        };
    }();

    var detailContractRecordModal = function(){
        var modal = $("#detail-contract-record");

        var show = function(data){
            var drawTime = "";
            if(data.drawTime) {
                drawTime = moment(data.drawTime).format('YYYY-MM-DD HH:mm:ss');
            }
            modal.find('[data-field="agentNo"]').html(data.agentNo);
            modal.find('[data-field="agentName"]').html(data.agentName);
            modal.find('[data-field="contractCode"]').html(data.contractCode);
            modal.find('[data-field="contractTitle"]').html(data.contractTitle);
            modal.find('[data-field="toPlayerName"]').html(data.toPlayerName);
            modal.find('[data-field="fromPlayerName"]').html(data.fromPlayerId === 0 ? "平台" : data.fromPlayerName);
            modal.find('[data-field="totalRecharge"]').html(data.totalRecharge.toLocaleString('en-US', {minimumFractionDigits: 3}));
            modal.find('[data-field="totalBet"]').html(data.totalBet.toLocaleString('en-US', {minimumFractionDigits: 3}));
            modal.find('[data-field="totalLoss"]').html(data.totalLoss.toLocaleString('en-US', {minimumFractionDigits: 3}));
            modal.find('[data-field="activeUser"]').html(data.activeUser);
            modal.find('[data-field="scalePoint"]').html(data.scalePoint.toLocaleString('en-US', {maximumFractionDigits: 2}) + '%');
            modal.find('[data-field="drawAmount"]').html(data.drawAmount.toLocaleString('en-US', {minimumFractionDigits: 3}));
            modal.find('[data-field="downDrawAmount"]').html(data.downDrawAmount.toLocaleString('en-US', {minimumFractionDigits: 3}));
            modal.find('[data-field="achieveStatus"]').html(DataFormat.Contract.achieveStatus(data.achieveStatus));
            modal.find('[data-field="drawStatus"]').html(DataFormat.Contract.drawStatus(data.drawStatus));
            modal.find('[data-field="calculateCycle"]').html(data.calculateCycle);
            modal.find('[data-field="drawTime"]').html(drawTime);
            modal.find('[data-field="createTime"]').html(moment(data.createTime).format('YYYY-MM-DD HH:mm:ss'));
            modal.find('[data-field="remarks"]').html(data.remarks);
            modal.modal("show");
        };

        return {
            show: show
        };
    }();

    // 初始化日期控件
    $('[data-init="datepicker"]').datepicker({
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    TableDataList.init();
});
