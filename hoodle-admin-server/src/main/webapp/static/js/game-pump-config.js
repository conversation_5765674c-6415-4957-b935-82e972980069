// 全局用户层级映射
window.userLevelMap = {};
// 全局彩种映射
window.gameGroupMap = {};
// 全局代理商映射
window.agentMap = {};

// 统一加载彩种数据（从game_lottery_info表）
window.loadGameGroupData = function(callback) {
    var url = Route.PATH + Route.GameLottery.PATH + Route.GameLottery.LIST_GAME_LOTTERY_INFO_LIST;
    App.staticPost(url, {}, function(result) {
        var lotteryInfos = result.data;
        var gameGroups = [];

        if (lotteryInfos && lotteryInfos.length > 0) {
            // 建立彩种映射（使用lottery作为key，gameName作为显示名称）
            $.each(lotteryInfos, function(i, v) {
                window.gameGroupMap[v.lottery] = v.gameName;
                gameGroups.push({
                    gameGroupCode: v.lottery,
                    gameGroupName: v.gameName
                });
            });
        }

        console.log('彩种数据加载完成:', gameGroups);
        if (callback) {
            callback(gameGroups || []);
        }
    });
};

// 预加载用户层级映射
window.preloadUserLevelMapping = function(agents, callback) {
        if (agents && agents.length > 0) {
            var agentNos = agents.map(function(agent) { return agent.agentNo; }).join(',');
            var url = Route.PATH + Route.System.PATH + Route.System.LIST_PLAYER_ACCOUNT_TYPE;

            App.staticPost(url, { agentNos: agentNos }, function(result) {
                var item = result.data;
                if (item && item.length > 0) {
                    $.each(item, function(i, v) {
                        // 使用 agentNo_code 作为key来避免不同代理商间的冲突
                        var key = v.agentNo + '_' + v.code;
                        window.userLevelMap[key] = v.name;
                    });
                }

                console.log('用户层级映射加载完成:', window.userLevelMap);
                if (callback) {
                    callback();
                }
            });
        } else {
            // 如果没有代理商，直接执行回调
            if (callback) {
                callback();
            }
        }
};

// 统一加载代理商数据
window.loadAgentData = function(callback) {
    var url = Route.PATH + Route.Agent.PATH + Route.Agent.LIST_PLATFORM_AGENT;
    App.staticPost(url, {}, function(result) {
        var agents = result.data;
        if (agents && agents.length > 0) {
            // 建立代理商映射
            $.each(agents, function(i, v) {
                window.agentMap[v.agentNo] = v.agentName;
            });
        }

        console.log('代理商数据加载完成:', agents);
        if (callback) {
            callback(agents || []);
        }
    });
};

$(document).ready(function() {

    // 抽水配置管理
    var PumpConfigTable = function() {
        var thisTable = $('#table-pump-config');
        var thisPageList = thisTable.find('.page-list');

        var getParams = function() {
            var status = thisTable.find('select[name="status"]').val();
            var agentNo = thisTable.find('input[name="agentNo"]').val();
            var agentName = thisTable.find('input[name="agentName"]').val();
            return {
                status: status,
                agentNo: agentNo,
                agentName: agentName
            };
        };

        var ajaxData = function() {
            return getParams();
        };

        var ajaxUrl = Route.PATH + Route.GamePumpConfig.PATH + Route.GamePumpConfig.LIST;
        var pagination = $.pagination({
            render: thisPageList,
            pageSize: 10,
            ajaxType: 'post',
            ajaxUrl: ajaxUrl,
            ajaxData: ajaxData,
            beforeSend: function () {
                blockUI(thisTable);
            },
            complete: function () {
                unblockUI(thisTable);
            },
            success: function (data, fullResponse) {
                buildData(fullResponse);
            },
            pageError: function (response) {
            },
            emptyData: function () {
                thisTable.find('table > tbody').html('<tr><td colspan="7" class="text-center">没有相关数据</td></tr>');
            }
        });

        var buildData = function(data) {
            var tbody = thisTable.find('table > tbody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                return;
            }

            $.each(data.list, function(i, item) {
                var configTypeInfo = getConfigTypeInfo(item);
                var row = '<tr>';
                row += '<td class="text-center" style="word-break: break-all;">' + getAgentDisplayName(item.agentNo) + '</td>';
                row += '<td class="text-center">' + configTypeInfo.type + '</td>';
                row += '<td class="text-left" style="word-break: break-all; padding: 8px 12px;">' + configTypeInfo.content + '</td>';
                row += '<td class="text-center" style="word-break: break-all; padding: 8px 12px;">' + getPumpRulesText(item.pumpRules) + '</td>';
                row += '<td class="text-center">' + getStatusText(item.status) + '</td>';
                row += '<td class="text-center" style="font-size: 12px;">' + formatDateTime(item.createTime) + '</td>';
                row += '<td class="text-center">';
                row += '<button class="btn btn-primary btn-xs btn-mini" onclick="editConfig(' + item.id + ')" style="margin-right: 5px;"><i class="fa fa-edit"></i> 编辑</button>';
                if (item.status === 0) {
                    row += '<button class="btn btn-primary btn-xs btn-mini" onclick="toggleStatus(' + item.id + ', 1)" style="margin-right: 5px;"><i class="fa fa-check"></i> 启用</button>';
                } else {
                    row += '<button class="btn btn-primary btn-xs btn-mini" onclick="toggleStatus(' + item.id + ', 0)" style="margin-right: 5px;"><i class="fa fa-ban"></i> 禁用</button>';
                }
                row += '<button class="btn btn-primary btn-xs btn-mini" onclick="deleteConfig(' + item.id + ')"><i class="fa fa-trash"></i> 删除</button>';
                row += '</td>';
                row += '</tr>';
                tbody.append(row);
            });
        };

        // 获取抽水对象和内容信息
        var getConfigTypeInfo = function(item) {
            var specificPlayers = (item.specificPlayers || '').trim();
            var userLevel = (item.userLevel || '').trim();
            var gameGroupCode = (item.gameGroupCode || '').trim();

            if (specificPlayers) {
                return {
                    type: '特定用户',
                    content: getSpecificPlayersText(item.specificPlayers)
                };
            } else if (gameGroupCode && gameGroupCode !== '') {
                return {
                    type: '彩种',
                    content: getGameGroupText(item.gameGroupCode)
                };
            } else if (userLevel && userLevel !== '') {
                return {
                    type: '用户层级',
                    content: getUserLevelText(item.userLevel, item.agentNo)
                };
            } else {
                return {
                    type: '全平台',
                    content: '全部用户'
                };
            }
        };

        var getUserLevelText = function(userLevel, agentNo) {
            if (!userLevel || userLevel === '') return '全部';
            if (typeof userLevel === 'string') {
                var levels = userLevel.split(',').filter(function(v) { return v; });
                if (levels.length === 0) return '全部';

                var texts = [];
                levels.forEach(function(level) {
                    var key = agentNo + '_' + level;
                    if (window.userLevelMap[key]) {
                        texts.push(window.userLevelMap[key]);
                    } else {
                        console.log('用户层级映射中未找到:', key, '当前映射:', window.userLevelMap);
                    }
                });
                return texts.length > 5 ? texts.slice(0, 5).join(', ') + '... (' + texts.length + '个)' : texts.join(', ');
            }
            return userLevel;
        };

        var getGameGroupText = function(gameGroupCode) {
            if (!gameGroupCode || gameGroupCode === '') return '全部';
            if (typeof gameGroupCode === 'string') {
                var groups = gameGroupCode.split(',').filter(function(v) { return v; });
                if (groups.length === 0) return '全部';

                var texts = [];
                groups.forEach(function(group) {
                    // 使用lottery作为key查找gameName
                    if (window.gameGroupMap[group]) {
                        texts.push(window.gameGroupMap[group]);
                    } else {
                        texts.push(group); // 如果找不到映射，直接显示原值
                    }
                });
                return texts.length > 4 ? texts.slice(0, 4).join(', ') + '... (' + texts.length + '个)' : texts.join(', ');
            }
            return gameGroupCode;
        };

        var getSpecificPlayersText = function(specificPlayers) {
            if (!specificPlayers || specificPlayers.trim() === '') {
                return '全部';
            }
            var players = specificPlayers.split(',');
            if (players.length > 5) {
                return players.slice(0, 5).join(', ') + '... (' + players.length + '个用户)';
            }
            return specificPlayers;
        };

        var getPumpRulesText = function(pumpRules) {
            if (!pumpRules || pumpRules.length === 0) return '<span style="color: #999;">无规则</span>';

            if (pumpRules.length === 1) {
                var rule = pumpRules[0];
                var value = rule.pumpValue || rule.fixedAmount || rule.pumpRate || '0';
                if (rule.pumpMethod === 2) {
                    return '固定' + value;
                } else if (rule.pumpMethod === 1) {
                    return '比例' + value + '%';
                }
            }
            return pumpRules.length + '条阶梯规则';
        };

        var getStatusText = function(status) {
            if (status === 1) {
                return '<span class="label label-success">启用</span>';
            } else {
                return '<span class="label label-danger">禁用</span>';
            }
        };

        var formatDateTime = function(timestamp) {
            if (!timestamp) return '-';
            var date = new Date(timestamp);
            return date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0') + ' ' +
                   String(date.getHours()).padStart(2, '0') + ':' +
                   String(date.getMinutes()).padStart(2, '0') + ':' +
                   String(date.getSeconds()).padStart(2, '0');
        };

        var getAgentDisplayName = function(agentNo) {
            if (!agentNo) return '-';
            if (window.agentMap[agentNo]) {
                return window.agentMap[agentNo];
            }
            var modalOption = $('#pump-config-form select[name="agentNo"] option[value="' + agentNo + '"]').first();
            if (modalOption.length > 0) {
                return modalOption.text();
            }
            return agentNo;
        };

        // 添加配置按钮事件
        thisTable.find('button[name="add-config"]').click(function() {
            $('#pump-config-modal .modal-title').text('添加抽水配置');

            // 启用代理商选择
            $('#pump-config-form select[name="agentNo"]').prop('disabled', false);

            var form = $('#pump-config-form');
            form[0].reset();
            form.find('input[name="id"]').val('');

            // 清除验证状态
            form.find('.form-group').removeClass('has-error has-success');
            form.find('.help-block').empty();
            form.find('.glyphicon').hide();

            // 新增时重置配置类型和相关字段
            form.find('select[name="configType"]').val('');
            form.find('#userLevel-group, #gameGroup-group, #specificPlayers-group').hide();
            form.find('select[name="userLevel"]').val('');
            form.find('select[name="gameGroupCode"]').val('');
            form.find('textarea[name="specificPlayers"]').val('');

            // 初始化空的抽水规则
            window.initPumpRules([]);
            $('#pump-config-modal').modal('show');
        });

        // 搜索条件变化时自动刷新
        thisTable.find('select[name="status"]').change(function() {
            pagination.reload();
        });

        // 输入框回车搜索
        thisTable.find('input[name="agentNo"], input[name="agentName"]').keypress(function(e) {
            if (e.which === 13) { // 回车键
                pagination.reload();
            }
        });

        var init = function() {
            pagination.init();
        };

        return {
            init: init,
            reload: function() {
                pagination.reload();
            }
        };
    }();



    // 初始化代理商数据
    var loadAgents = function(agents) {
        var modalSelect = $('#pump-config-form select[name="agentNo"]');

        modalSelect.find('option:not(:first)').remove();

        if (agents && agents.length > 0) {
            $.each(agents, function(i, v) {
                modalSelect.append('<option value="' + v.agentNo + '">' + v.agentName + '</option>');
            });
        }
    };

    // 初始化用户层级数据
    window.loadUserLevels = function(agentNo, callback) {
        if (!agentNo) {
            console.log('loadUserLevels: agentNo为空');
            return;
        }

        var modalSelect = $('#pump-config-form select[name="userLevel"]');

        // 清除所有选项
        modalSelect.empty();

        var userLevels = [];
        for (var key in window.userLevelMap) {
            if (key.startsWith(agentNo + '_')) {
                var userLevel = key.replace(agentNo + '_', '');
                var name = window.userLevelMap[key];
                userLevels.push({ userLevel: userLevel, name: name });
            }
        }

        if (userLevels.length === 0) {
            var url = Route.PATH + Route.System.PATH + Route.System.LIST_PLAYER_ACCOUNT_TYPE;
            App.staticPost(url, { agentNos: agentNo }, function(result) {
                var item = result.data;
                if (item && item.length > 0) {
                    $.each(item, function(i, v) {
                        // 更新全局映射
                        var key = v.agentNo + '_' + v.code;
                        window.userLevelMap[key] = v.name;

                        if (v.agentNo === agentNo) {
                            userLevels.push({ userLevel: v.code, name: v.name });
                        }
                    });

                    // 按用户层级排序
                    userLevels.sort(function(a, b) {
                        return parseInt(a.userLevel) - parseInt(b.userLevel);
                    });

                    // 添加选项
                    $.each(userLevels, function(i, v) {
                        modalSelect.append('<option value="' + v.userLevel + '">' + v.name + '</option>');
                    });
                }

                // 执行回调函数
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        } else {
            // 按用户层级排序
            userLevels.sort(function(a, b) {
                return parseInt(a.userLevel) - parseInt(b.userLevel);
            });

            // 添加选项
            $.each(userLevels, function(i, v) {
                modalSelect.append('<option value="' + v.userLevel + '">' + v.name + '</option>');
            });

            // 执行回调函数
            if (callback && typeof callback === 'function') {
                callback();
            }
        }
    };

    // 初始化彩种数据（使用lottery信息）
    var loadGameGroups = function(gameGroups) {
        var modalSelect = $('#pump-config-form select[name="gameGroupCode"]');
        modalSelect.empty();

        if (gameGroups && gameGroups.length > 0) {
            $.each(gameGroups, function(i, v) {
                // 使用lottery作为value，gameName作为显示文本
                modalSelect.append('<option value="' + v.gameGroupCode + '">' + v.gameGroupName + '</option>');
            });
        }
    };

    $(document).on('click', '#add-pump-rule', function() {
        window.addPumpRule();
    });

    $(document).on('change', '#pump-config-form select[name="agentNo"]', function() {
        var agentNo = $(this).val();
        if (agentNo) {
            window.loadUserLevels(agentNo);
        } else {
            // 清空用户层级选项
            var modalSelect = $('#pump-config-form select[name="userLevel"]');
            modalSelect.empty();
        }
    });

    // 配置类型变化时的联动处理
    $(document).on('change', '#pump-config-form select[name="configType"]', function() {
        var configType = $(this).val();
        var form = $('#pump-config-form');

        // 隐藏所有配置相关的字段组
        form.find('#userLevel-group, #gameGroup-group, #specificPlayers-group').hide();

        // 清空所有字段的值
        form.find('select[name="userLevel"]').val('');
        form.find('select[name="gameGroupCode"]').val('');
        form.find('textarea[name="specificPlayers"]').val('');

        // 根据配置类型显示对应的字段组
        switch(configType) {
            case 'specificPlayers':
                form.find('#specificPlayers-group').show();
                break;
            case 'gameGroup':
                form.find('#gameGroup-group').show();
                break;
            case 'userLevel':
                form.find('#userLevel-group').show();
                // 当切换到用户层级时，需要加载对应代理商的用户层级数据
                var agentNo = form.find('select[name="agentNo"]').val();
                if (agentNo) {
                    window.loadUserLevels(agentNo);
                }
                break;
            case 'platform':
                // 全平台配置不需要显示额外字段
                break;
        }
    });



    // 抽水方式联动
    $(document).on('change', 'select[name="pumpMethod"]', function() {
        var container = $(this).closest('.pump-rule-item');
        var label = container.find('.pump-value-label');

        if ($(this).val() === '1') {
            label.html('抽水比例(%) <span style="color: red;">*</span>');
        } else {
            label.html('抽水金额 <span style="color: red;">*</span>');
        }
    });

    window.loadAgentData(function(agents) {

        // 加载彩种数据
        window.loadGameGroupData(function(gameGroups) {
            // 加载用户层级数据
            window.preloadUserLevelMapping(agents, function() {
                console.log('所有映射数据加载完成，初始化表格');
                PumpConfigTable.init();
            });

            // 初始化表单数据
            loadAgents(agents);
            loadGameGroups(gameGroups);
        });
    });

    // 搜索按钮事件
    $('button[name="search-btn"]').click(function() {
        PumpConfigTable.reload();
    });

    // 保存配置
    $('#confirm-save').click(function() {
        var form = $('#pump-config-form');
        var id = form.find('input[name="id"]').val();

        // 临时启用代理商选择框以获取值
        var agentSelect = form.find('select[name="agentNo"]');
        var wasDisabled = agentSelect.prop('disabled');
        agentSelect.prop('disabled', false);
        var agentNo = agentSelect.val();

        // 恢复禁用状态
        if (wasDisabled) {
            agentSelect.prop('disabled', true);
        }

        var configType = form.find('select[name="configType"]').val();
        var userLevel = form.find('select[name="userLevel"]').val(); // 多选返回数组
        var gameGroupCode = form.find('select[name="gameGroupCode"]').val(); // 多选返回数组
        var specificPlayers = form.find('textarea[name="specificPlayers"]').val().trim();
        var status = form.find('select[name="status"]').val();

        // 表单验证
        if (!agentNo) {
            App.msg('error', '请选择代理商');
            return;
        }

        if (!configType) {
            App.msg('error', '请选择抽水对象');
            return;
        }

        // 根据抽水对象验证对应字段
        switch(configType) {
            case 'specificPlayers':
                if (!specificPlayers) {
                    App.msg('error', '请输入特定用户');
                    return;
                }
                break;
            case 'gameGroup':
                if (!gameGroupCode || gameGroupCode.length === 0) {
                    App.msg('error', '请选择彩种');
                    return;
                }
                break;
            case 'userLevel':
                if (!userLevel || userLevel.length === 0) {
                    App.msg('error', '请选择用户层级');
                    return;
                }
                break;
            case 'platform':
                // 全平台不需要额外验证
                break;
        }

        if (specificPlayers) {
            var players = specificPlayers.split(',');
            for (var i = 0; i < players.length; i++) {
                var player = players[i].trim();
                if (player === '') {
                    App.msg('error', '特定用户账号不能为空，请检查逗号分隔格式');
                    return;
                }
                if (player.length < 2) {
                    App.msg('error', '用户账号长度不能少于2位：' + player);
                    return;
                }
            }
        }

        // 收集抽水规则
        var rules = [];
        var ruleValidationError = null;

        $('#pump-rules-container .pump-rule-item').each(function(index) {
            var ruleIndex = index + 1;
            var minAmount = $(this).find('input[name="minAmount"]').val().trim();
            var pumpMethod = $(this).find('select[name="pumpMethod"]').val();
            var pumpValue = $(this).find('input[name="pumpValue"]').val().trim();

            // 验证必填字段
            if (!minAmount) {
                ruleValidationError = '第 ' + ruleIndex + ' 条规则：玩家中奖金额不能为空';
                return false;
            }

            if (!pumpMethod) {
                ruleValidationError = '第 ' + ruleIndex + ' 条规则：请选择抽水类型';
                return false;
            }

            if (!pumpValue) {
                ruleValidationError = '第 ' + ruleIndex + ' 条规则：抽水值不能为空';
                return false;
            }

            // 验证数值格式
            var minAmountNum = parseFloat(minAmount);
            if (isNaN(minAmountNum) || minAmountNum < 0) {
                ruleValidationError = '第 ' + ruleIndex + ' 条规则：玩家中奖金额必须是大于等于0的数字';
                return false;
            }

            var pumpValueNum = parseFloat(pumpValue);
            if (isNaN(pumpValueNum) || pumpValueNum <= 0) {
                ruleValidationError = '第 ' + ruleIndex + ' 条规则：抽水值必须是大于0的数字';
                return false;
            }

            // 验证抽水比例范围
            if (pumpMethod == '1' && pumpValueNum > 100) {
                ruleValidationError = '第 ' + ruleIndex + ' 条规则：抽水比例不能超过100%';
                return false;
            }

            var rule = {
                minAmount: minAmountNum,
                pumpMethod: parseInt(pumpMethod),
                pumpValue: pumpValueNum
            };

            rules.push(rule);
        });

        // 检查验证错误
        if (ruleValidationError) {
            App.msg('error', ruleValidationError);
            return;
        }

        if (rules.length === 0) {
            App.msg('error', '请至少添加一条抽水规则');
            return;
        }

        // 按中奖金额强制排序（阶梯式）
        rules.sort(function(a, b) {
            return a.minAmount - b.minAmount;
        });

        var url = id ? Route.PATH + Route.GamePumpConfig.PATH + Route.GamePumpConfig.UPDATE : Route.PATH + Route.GamePumpConfig.PATH + Route.GamePumpConfig.SAVE;
        var data = {
            agentNo: agentNo,
            status: status,
            pumpRules: JSON.stringify(rules)
        };

        // 根据配置类型设置对应字段，其他字段设为空
        switch(configType) {
            case 'specificPlayers':
                data.specificPlayers = specificPlayers;
                data.userLevel = '';
                data.gameGroupCode = '';
                break;
            case 'gameGroup':
                data.gameGroupCode = Array.isArray(gameGroupCode) ? gameGroupCode.filter(function(v) { return v; }).join(',') : (gameGroupCode || '');
                data.specificPlayers = '';
                data.userLevel = '';
                break;
            case 'userLevel':
                data.userLevel = Array.isArray(userLevel) ? userLevel.filter(function(v) { return v; }).join(',') : (userLevel || '');
                data.specificPlayers = '';
                data.gameGroupCode = '';
                break;
            case 'platform':
                data.specificPlayers = '';
                data.userLevel = '';
                data.gameGroupCode = '';
                break;
        }

        if (id) {
            data.id = id;
        }

        App.ajaxPost(url, data, function() {
            App.msg('success', '保存成功');
            $('#pump-config-modal').modal('hide');
            PumpConfigTable.reload();
        });
    });




});

// 全局函数定义
window.removePumpRule = function(btn) {
    $(btn).closest('.pump-rule-item').remove();
    // 重新更新规则编号
    $('#pump-rules-container .pump-rule-item').each(function(i) {
        $(this).find('strong').html('抽水规则 ' + (i + 1));
    });
};

// 编辑配置
window.editConfig = function(id) {
    $('#pump-config-modal .modal-title').text('编辑抽水配置');
    $('#pump-config-form input[name="id"]').val(id);

    // 禁用代理商选择
    $('#pump-config-form select[name="agentNo"]').prop('disabled', true);

    // 加载配置数据
    App.ajaxPost(Route.PATH + Route.GamePumpConfig.PATH + Route.GamePumpConfig.GET, { id: id }, function(data) {
        var form = $('#pump-config-form');
        form.find('select[name="agentNo"]').val(data.agentNo);

        // 根据数据判断配置类型并设置
        var configType = '';
        var specificPlayers = (data.specificPlayers || '').trim();
        var userLevel = (data.userLevel || '').trim();
        var gameGroupCode = (data.gameGroupCode || '').trim();

        if (specificPlayers) {
            configType = 'specificPlayers';
        } else if (gameGroupCode && gameGroupCode !== '') {
            configType = 'gameGroup';
        } else if (userLevel && userLevel !== '') {
            configType = 'userLevel';
        } else {
            configType = 'platform';
        }

        form.find('select[name="configType"]').val(configType);

        // 隐藏所有配置相关的字段组
        form.find('#userLevel-group, #gameGroup-group, #specificPlayers-group').hide();

        // 根据配置类型显示对应的字段组并设置值
        switch(configType) {
            case 'specificPlayers':
                form.find('#specificPlayers-group').show();
                form.find('textarea[name="specificPlayers"]').val(data.specificPlayers || '');
                break;
            case 'gameGroup':
                form.find('#gameGroup-group').show();
                var gameGroupCodes = data.gameGroupCode ? data.gameGroupCode.split(',') : [''];
                form.find('select[name="gameGroupCode"]').val(gameGroupCodes);
                break;
            case 'userLevel':
                form.find('#userLevel-group').show();
                if (data.agentNo) {
                    window.loadUserLevels(data.agentNo, function() {
                        var userLevels = data.userLevel ? data.userLevel.split(',') : [''];
                        form.find('select[name="userLevel"]').val(userLevels);
                    });
                }
                break;
            case 'platform':
                // 全平台配置不需要显示额外字段
                break;
        }

        form.find('select[name="status"]').val(data.status);



        var rules = data.pumpRules || [];

        // 调用全局的initPumpRules函数
        window.initPumpRules(rules);
        $('#pump-config-modal').modal('show');
    });
};

// 删除配置
window.deleteConfig = function(id) {
    App.dialog('确定要删除这个配置吗？', '确定', function() {
        App.ajaxPost(Route.PATH + Route.GamePumpConfig.PATH + Route.GamePumpConfig.DELETE, { id: id }, function() {
            App.msg('success', '删除成功');
            if (typeof PumpConfigTable !== 'undefined' && PumpConfigTable.reload) {
                PumpConfigTable.reload();
            } else {
                location.reload();
            }
        });
    });
};

// 更新状态
window.toggleStatus = function(id, status) {
    var statusText = status == 1 ? '启用' : '禁用';
    App.dialog('确定要' + statusText + '这个配置吗？', '确定', function() {
        App.ajaxPost(Route.PATH + Route.GamePumpConfig.PATH + Route.GamePumpConfig.UPDATE_STATUS, { id: id, status: status }, function() {
            App.msg('success', '状态更新成功');
            if (typeof PumpConfigTable !== 'undefined' && PumpConfigTable.reload) {
                PumpConfigTable.reload();
            } else {
                location.reload();
            }
        });
    });
};

// 初始化抽水规则表单
window.initPumpRules = function(rules) {
    var container = $('#pump-rules-container');
    // 清空容器，但保留表头
    container.find('.pump-rule-item').remove();

    // 重新添加表头
    if (container.find('.row').length === 0) {
        container.append('<div class="row" style="margin-bottom: 10px; font-weight: bold; color: #666;">' +
            '<div class="col-sm-3">玩家中奖金额</div>' +
            '<div class="col-sm-3">抽水类型</div>' +
            '<div class="col-sm-3">抽水值</div>' +
            '<div class="col-sm-3">操作</div>' +
        '</div>');
    }

    if (rules.length === 0) {
        window.addPumpRule();
    } else {
        // 按最小金额排序
        rules.sort(function(a, b) {
            var minA = a.minAmount || 0;
            var minB = b.minAmount || 0;
            return minA - minB;
        });

        $.each(rules, function(i, rule) {
            window.addPumpRule(rule);
        });
    }
};

// 添加抽水规则（阶梯式）
window.addPumpRule = function(rule) {
    rule = rule || {};
    var container = $('#pump-rules-container');

    // 根据抽水方式获取抽水值
    var pumpValue = '';
    if (rule.pumpMethod == 1 && rule.pumpRate) {
        pumpValue = rule.pumpRate;
    } else if (rule.pumpMethod == 2 && rule.fixedAmount) {
        pumpValue = rule.fixedAmount;
    } else if (rule.pumpValue) {
        pumpValue = rule.pumpValue;
    }

    var html = '<div class="pump-rule-item row" style="margin-bottom: 10px; padding: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px;">' +
            '<div class="col-sm-3">' +
                '<input type="number" class="form-control" name="minAmount" value="' + (rule.minAmount || '') + '" step="1" min="0">' +
            '</div>' +
            '<div class="col-sm-3">' +
                '<select class="form-control" name="pumpMethod">' +
                    '<option value="1" ' + (rule.pumpMethod == 1 ? 'selected' : '') + '>百分比</option>' +
                    '<option value="2" ' + (rule.pumpMethod == 2 ? 'selected' : '') + '>固定金额</option>' +
                '</select>' +
            '</div>' +
            '<div class="col-sm-3">' +
                '<input type="number" class="form-control" name="pumpValue" value="' + pumpValue + '" step="0.01" min="0">' +
            '</div>' +
            '<div class="col-sm-3">' +
                '<button type="button" class="btn btn-default btn-sm" onclick="removePumpRule(this)" style="width: 100%;">' +
                    '<i class="fa fa-trash"></i> 删除' +
                '</button>' +
            '</div>' +
        '</div>';

    container.append(html);
};

// 删除抽水规则
window.removePumpRule = function(btn) {
    $(btn).closest('.pump-rule-item').remove();
};




