<!DOCTYPE html>
<html>
<head>
    <meta content="text/html;charset=UTF-8" http-equiv="content-type" />
    <meta charset="utf-8" />
    <title>亏损量预警配置</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" media="screen" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" media="screen" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->
</head>

<body>
    <div class="page-iframe">
        <div class="content">
            <!-- 亏损量预警配置列表 -->
            <div class="row">
                <div class="col-md-12">
                    <div id="table-warn-config" class="grid simple">
                        <div class="grid-title no-border">
                            <h4 style="width:80%"><span class="semi-bold">亏损量预警配置</span></h4>
                            <p style="margin: 10px 0 0 0; color: #666; font-size: 13px;">
                                <i class="fa fa-info-circle"></i>
                                系统最多支持两条预警配置：按彩种一条，按玩法一条，每种计量方式不能重复配置
                            </p>
                        </div>
                        <div class="grid-body border-top">
                            <form name="search-params" class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button name="search" type="button" class="btn btn-primary btn-sm">
                                            <i class="fa fa-search"></i> 查询结果
                                        </button>
                                        <button name="add-config" type="button" class="btn btn-primary btn-sm" style="margin-left: 10px;">
                                            <i class="fa fa-plus"></i> 添加预警配置
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center" width="6%">预警ID</th>
                                        <th class="text-center" width="10%">代理商名称</th>
                                        <th class="text-center" width="8%">预警亏损量</th>
                                        <th class="text-center" width="10%">自动禁用亏损量</th>
                                        <th class="text-center" width="8%">计量方式</th>
                                        <th class="text-center" width="12%">自动禁用方式</th>
                                        <th class="text-center" width="8%">预警可清除</th>
                                        <th class="text-center" width="5%">状态</th>
                                        <th class="text-center" width="15%">备注</th>
                                        <th class="text-center" width="20%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="config-tbody"></tbody>
                            </table>
                            <div class="page-list" id="config-page-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加预警配置模态框 -->
    <div class="modal fade" id="add-warn-config-modal" tabindex="-1" role="dialog" id="modal-edit">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">添加预警配置</h4>
                </div>
                <div class="modal-body">
                    <form id="warn-config-form" class="form-horizontal">
                        <input type="hidden" name="id" value="">

                        <div class="form-group">
                            <div class="form-group">
                                <input name="agentNo" type="hidden" />
                                <label class="col-sm-3 control-label">代理商名称</label>
                                <div class="col-sm-8">
                                    <select class="form-control" class="form-control" name="agent"></select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">预警亏损量 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="warnLossAmount" step="0.01" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">自动禁用亏损量 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="warnDisableLossVolume" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="col-sm-3 control-label">计量方式 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" name="measurementWay" required id="measurementWay">
                                    <option value="">请选择</option>
                                    <option value="1" selected>按彩种</option>
                                    <option value="2">按玩法</option>
                                </select>
                                <p class="help-block" style="color: #999; font-size: 12px; margin-top: 5px;">
                                    注意：每种计量方式只能配置一条记录，系统最多支持两条配置（按彩种一条，按玩法一条）
                                </p>
                            </div>
                        </div>
                        

                        <div class="form-group">
                            <label class="col-sm-3 control-label">自动禁用方式 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" name="automaticDisableWay" required id="automaticDisableWay">
                                    <option value="">请选择</option>
                                    <option value="1">禁用彩种</option>
                                    <option value="2">禁用玩法</option>
                                    <option value="3">全部</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="col-sm-3 control-label">预警是否可清除</label>
                            <div class="col-sm-8">
                                <select class="form-control" name="warnClear">
                                    <option value="1">是</option>
                                    <option value="2">否</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="col-sm-3 control-label">备注</label>
                            <div class="col-sm-8">
                                <textarea class="form-control" name="warnRemark" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="confirm-save">
                        <i class="fa fa-check"></i> 确定
                    </button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置彩种模态框 -->
    <div class="modal fade" id="set-lottery-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">设置彩种</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">配置ID</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="config-id" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">彩种选择</label>
                            <div class="col-sm-10">
                                <div class="row">
                                    <div class="col-sm-5">
                                        <label class="control-label">可选彩种</label>
                                        <div class="well" style="height: 300px; overflow-y: auto;">
                                            <div id="available-lottery-list">
                                                <!-- 可选彩种列表 -->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2 text-center" style="padding-top: 100px;">
                                        <button type="button" class="btn btn-primary btn-sm" id="add-lottery" style="margin-bottom: 10px; width: 60px;">
                                            <i class="fa fa-arrow-right"></i>
                                        </button>
                                        <br>
                                        <button type="button" class="btn btn-primary btn-sm" id="remove-lottery" style="width: 60px;">
                                            <i class="fa fa-arrow-left"></i>
                                        </button>
                                    </div>
                                    <div class="col-sm-5">
                                        <label class="control-label">已选彩种</label>
                                        <div class="well" style="height: 300px; overflow-y: auto;">
                                            <div id="selected-lottery-list">
                                                <!-- 已选彩种列表 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="confirm-lottery">
                        <i class="fa fa-check"></i> 确定
                    </button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置彩种模态框 -->
    <div class="modal fade" id="lotteryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">设置彩种</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="lotteryForm" class="form-horizontal">
                        <input type="hidden" name="configId" id="configId">

                        <div class="form-group">
                            <label class="col-sm-2 control-label">配置ID</label>
                            <div class="col-sm-10">
                                <p class="form-control-static" id="displayConfigId"></p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">彩种选择</label>
                            <div class="col-sm-10">
                                <div class="row">
                                    <div class="col-md-5">
                                        <label>可选彩种</label>
                                        <select id="availableLotteries" class="form-control" multiple style="height: 200px;">
                                         
                                        </select>
                                    </div>
                                    <div class="col-md-2 text-center" style="padding-top: 80px;">
                                        <button type="button" class="btn btn-primary btn-sm" id="addLottery" style="margin-bottom: 10px; width: 50px;">
                                            <i class="fa fa-arrow-right"></i>
                                        </button>
                                        <br>
                                        <button type="button" class="btn btn-warning btn-sm" id="removeLottery" style="width: 50px;">
                                            <i class="fa fa-arrow-left"></i>
                                        </button>
                                    </div>
                                    <div class="col-md-5">
                                        <label>已选彩种</label>
                                        <select id="selectedLotteries" class="form-control" multiple style="height: 200px; background-color: #f8f9fa;">

                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="saveLottery">
                        <i class="fa fa-check"></i> 确定
                    </button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN CORE JS FRAMEWORK -->
    <script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-ui/jquery-ui-1.10.1.custom.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
    <script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
    <!-- END CORE JS FRAMEWORK -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
    <script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="assets/js/core.js" type="text/javascript"></script>
    <!-- END CORE TEMPLATE JS -->
    <script src="js/global.js" type="text/javascript"></script>
    <script src="js/game-warn-config.js?v=20250107-3" type="text/javascript"></script>
</body>
</html>
