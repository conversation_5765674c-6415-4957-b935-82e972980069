<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <meta charset="utf-8" />
    <title>客服配置</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/pace/pace-theme-flash.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->
</head>

<body>
    <div class="page-iframe">
        <div class="content">
            <div class="row">
                <div class="col-md-12">
                    <div class="grid simple settings">
					    <div class="grid-title no-border">
					        <h4>彩票配置</h4>
					    </div>
					    <div class="grid-body border-top">
							<form name="search-params" class="form-horizontal">
								<div class="row">
									<div class="col-md-4">
                                        <select name="agent" class="form-control"></select>
                                    </div>
								</div>
							</form>
						</div>
					    <div id="thisPanel" class="grid-body border-top">
					        <div class="row">
					            <div class="col-md-12">
					                <h4>代理返点</h4>
	                                <div class="tab-space">
	                                    <p>非禁用时生效，发放规则如选中内容。</p>
	                                    <div class="radio radio-success">
	                                        <input id="rebateLevel_no" name="REBATE_LEVEL" type="radio" value="0">
	                                        <label for="rebateLevel_no">禁用</label>
	                                        <input id="rebateLevel_yes1" name="REBATE_LEVEL" type="radio" value="1">
	                                        <label for="rebateLevel_yes1">直属上级</label>
	                                        <input id="rebateLevel_yes2" name="REBATE_LEVEL" type="radio" value="2">
	                                        <label for="rebateLevel_yes2">团队上级</label>
	                                    </div>
	                                </div>
					            </div>
					        </div>
					        <div class="form-actions">
								<div class="pull-right">
									<button data-command="save" type="button" class="btn btn-success btn-cons"><i class="fa fa-save"></i> 保存修改</button>
								    <button data-command="reset" type="button" class="btn btn-cons"><i class="fa fa-undo"></i> 重置</button>
								</div>
					        </div>
					    </div>
					</div>
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN CORE JS FRAMEWORK-->
    <script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery.json.min.js" type="text/javascript"></script>
    <script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
    <!-- END CORE JS FRAMEWORK -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
    <script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="assets/js/core.js" type="text/javascript"></script>
    <!-- END CORE TEMPLATE JS -->
    <script src="js/global.js" type="text/javascript"></script>
    <script src="js/platform-lottery-config.js" type="text/javascript"></script>
</body>

</html>
