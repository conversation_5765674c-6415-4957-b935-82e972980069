<!DOCTYPE html>
<html lang="en">

<head>
    <meta content="text/html;charset=UTF-8" http-equiv="content-type"/>
    <meta charset="utf-8"/>
    <title>彩票游戏类型</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" media="screen" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" media="screen" rel="stylesheet"
          type="text/css"/>
    <link href="assets/plugins/bootstrap-datepicker/css/datepicker.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css"/>
    <!-- <PERSON><PERSON> PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"/>
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css"/>
    <!-- END CSS TEMPLATE -->
    <style>
        /* 表格样式优化 */
        .table-responsive {
            overflow-x: auto; /* 启用水平滚动 */
            margin-bottom: 20px;
        }

        .table th {
            color: #333; /* 表头字体颜色设置为#333 */
            font-weight: 600; /* 加粗字体 */
            white-space: nowrap; /* 表头文本不换行 */
        }
        .table td {
            white-space: nowrap; /* 单元格内容不换行 */
        }
        .table {
            table-layout: auto; /* 表格宽度自适应 */
            min-width: 1500px; /* 设置最小宽度确保所有列可见 */
        }

    </style>
</head>

<body>
<div class="page-iframe">
    <div class="content">
        <div class="row">
            <div class="col-md-12" style="background-color: #ffffff">
                <div class="grid simple" id="table-data-list">
                    <div class="grid-title no-border">
                        <h4><span class="semi-bold">平台玩家报表</span></h4>
                    </div>
                    <div class="grid-body border-top">
                        <form class="form-horizontal" name="search-params">
                            <div class="row">
<!--                                <div class="col-md-2">-->
<!--                                    <input class="form-control" name="agentNo" placeholder="平台编号"-->
<!--                                           type="text">-->
<!--                                </div>-->
<!--                                <div class="col-md-2">-->
<!--                                    <input class="form-control" name="agentName" placeholder="平台名称"-->
<!--                                           type="text">-->
<!--                                </div>-->
                                <div class="col-md-2">
                                    <select class="form-control" name="agentName" id="agentSelect">
                                        <option value="">请选择平台</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <input class="form-control" name="playerName" placeholder="玩家名称"
                                           type="text">
                                </div>
                            <div class="col-md-2">
                                <label>
                                    <input class="form-control" data-init="datepicker" name="sTime"
                                           placeholder="开始日期"
                                           type="text"/>
                                </label>
                            </div>
                            <div class="col-md-2">
                                <label>
                                    <input class="form-control" data-init="datepicker" name="eTime"
                                           placeholder="结束日期"
                                           type="text"/>
                                </label>
                            </div>
                            <div class="col-md-1">
                                <button class="btn btn-primary" name="search" type="button"><i
                                        class="fa fa-search"></i> 查询结果
                                </button>
                            </div>
                                <div class="col-md-1">
                                    <button class="btn btn-success" name="generateReport" type="button"><i
                                            class="fa fa-file-text"></i> 报表修正
                                    </button>
                                </div>
                            </div>
                            </div>
                    </form>
                    <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th class="text-center">平台名称</th>
                            <th class="text-center">玩家名称</th>
                            <th class="text-center" data-command="orderTh" data-field="账户余额" data-value="playerAvailableBalance">账户余额</th>
                            <th class="text-center" data-command="orderTh" data-field="充值" data-value="transferInAmount">充值</th>
                            <th class="text-center" data-command="orderTh" data-field="取款" data-value="transferOutAmount">取款</th>
                            <th class="text-center" data-command="orderTh" data-field="手续费" data-value="handlingFee">手续费</th>
                            <th class="text-center" data-command="orderTh" data-field="投注" data-value="betAmount">投注</th>
                            <th class="text-center" data-command="orderTh" data-field="派奖" data-value="bonusAmount">派奖</th>
                            <th class="text-center" data-command="orderTh" data-field="返点" data-value="rebateAmount">返点</th>
                            <th class="text-center" data-command="orderTh" data-field="抽水" data-value="pumpAmount">抽水</th>
                            <th class="text-center" data-command="orderTh" data-field="打赏" data-value="rewardAmount">打赏</th>
                            <th class="text-center" data-command="orderTh" data-field="活动" data-value="activityAmount">活动</th>
                            <th class="text-center" data-command="orderTh" data-field="工资" data-value="salaryAmount">工资</th>
                            <th class="text-center" data-command="orderTh" data-field="分红" data-value="divsAmount">分红</th>
                            <th class="text-center" data-command="orderTh" data-field="上下级转帐转入" data-value="upAndDownTransferIn">上下级转帐转入</th>
                            <th class="text-center" data-command="orderTh" data-field="上下级转帐转出" data-value="upAndDownTransferOut">上下级转帐转出</th>
                            <th class="text-center" data-command="orderTh" data-field="盈亏" data-value="profitAmount">盈亏</th>
                            <!-- 抽水与分红目前使用的同一个字段，上下级转帐 目前没有 -->
                        </tr>
                        </thead>
                        <tbody style="background-color: #fff"></tbody>
                    </table>
                    </div>
                    <div class="page-list"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>







<!-- 报表修正模态窗口 -->
<div class="modal fade" id="reportGenerateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">报表修正</h4>
            </div>
            <div class="modal-body">
                <form id="reportGenerateForm" class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">平台名称：</label>
                        <div class="col-sm-9">
                            <select name="agentNo" class="form-control" required>
                                <option value="">请选择平台名称</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">玩家名称：</label>
                        <div class="col-sm-9">
                            <input type="text" name="playerName" class="form-control" placeholder="请输入玩家名称" required>
                        </div>
                    </div>
                     <div class="form-group">
                        <label class="col-sm-3 control-label">开始日期：</label>
                        <div class="col-sm-9">
                            <input type="text" name="sDate" class="form-control" data-init="datepicker" placeholder="请选择开始日期" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">结束日期：</label>
                        <div class="col-sm-9">
                            <input type="text" name="eDate" class="form-control" data-init="datepicker" placeholder="请选择结束日期" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmGenerateReport">生成报表</button>
            </div>
        </div>
    </div>
</div>









<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-inputmask/jquery.inputmask.bundle.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js"
        type="text/javascript"></script>
<script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="js/report-platform-player-report.js?v=20250721-002" type="text/javascript"></script>
</body>

</html>
