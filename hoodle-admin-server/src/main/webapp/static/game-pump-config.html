<!DOCTYPE html>
<html>
<head>
    <meta content="text/html;charset=UTF-8" http-equiv="content-type" />
    <meta charset="utf-8" />
    <title>抽水配置</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" media="screen" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" media="screen" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->
</head>

<body>
    <div class="page-iframe">
        <div class="content">
            <!-- 抽水配置列表 -->
            <div class="row">
                <div class="col-md-12">
                    <div id="table-pump-config" class="grid simple">
                        <div class="grid-title no-border">
                            <h4 style="width:80%"><span class="semi-bold">抽水配置</span></h4>
                        </div>
                        <div class="grid-body border-top">
                            <form name="search-params" class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-2">
                                        <input type="text" class="form-control" name="agentNo" placeholder="代理商商户号">
                                    </div>
                                    <div class="col-md-2">
                                        <input type="text" class="form-control" name="agentName" placeholder="代理商名称">
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-control" name="status">
                                            <option value="">状态</option>
                                            <option value="1">启用</option>
                                            <option value="0">禁用</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button name="search-btn" type="button" class="btn btn-primary">
                                            <i class="fa fa-search"></i> 查询结果
                                        </button>
                                        <button name="add-config" type="button" class="btn btn-primary">
                                            <i class="fa fa-plus"></i> 添加抽水配置
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <table class="table table-bordered table-hover" style="table-layout: fixed; word-wrap: break-word;">
                                <thead>
                                    <tr>
                                        <th class="text-center" style="width: 15%;">代理商名称</th>
                                        <th class="text-center" style="width: 10%;">抽水对象</th>
                                        <th class="text-center" style="width: 25%;">对象内容</th>
                                        <th class="text-center" style="width: 10%;">抽水规则</th>
                                        <th class="text-center" style="width: 8%;">状态</th>
                                        <th class="text-center" style="width: 15%;">创建时间</th>
                                        <th class="text-center" style="width: 17%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div class="page-list" id="config-page-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑抽水配置模态框 -->
    <div class="modal fade" id="pump-config-modal" data-backdrop="static" tabindex="-1" role="dialog">
        <div class="modal-dialog" style="width: 800px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title semi-bold">抽水配置</h4>
                </div>
                <div class="modal-body">
                    <form id="pump-config-form" class="form-horizontal">
                        <input type="hidden" name="id" value="">

                        <div class="form-group has-feedback">
                            <label class="col-sm-2 control-label">代理商 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" name="agentNo" required>
                                    <option value="">请选择代理商</option>
                                </select>
                                <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="form-group has-feedback">
                            <label class="col-sm-2 control-label">抽水对象 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" name="configType" required>
                                    <option value="">请选择抽水对象</option>
                                    <option value="specificPlayers">特定用户</option>
                                    <option value="gameGroup">彩种</option>
                                    <option value="userLevel">用户层级</option>
                                    <option value="platform">全平台</option>
                                </select>
                                <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="form-group has-feedback" id="userLevel-group" style="display: none;">
                            <label class="col-sm-2 control-label">用户层级 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" name="userLevel" multiple style="height: 120px;">
                                    <!-- 多选选项 -->
                                </select>
                                <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="form-group has-feedback" id="specificPlayers-group" style="display: none;">
                            <label class="col-sm-2 control-label">特定用户 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <textarea class="form-control" name="specificPlayers" rows="3" placeholder="请输入特定用户账号，多个用户用逗号隔开，如：user1,user2,user3;"></textarea>
                                <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                <span class="help-block">多个用户用逗号隔开</span>
                            </div>
                        </div>

                        <div class="form-group has-feedback" id="gameGroup-group" style="display: none;">
                            <label class="col-sm-2 control-label">彩种 <span style="color: red;">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" name="gameGroupCode" multiple style="height: 120px;">
                                </select>
                                <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态</label>
                            <div class="col-sm-8">
                                <select class="form-control" name="status">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">抽水规则 <span style="color: red;">*</span></label>
                            <div class="col-sm-10">
                                <div id="pump-rules-container" style="background-color: #f5f5f5; padding: 15px; border-radius: 4px;">
                                    <div class="row" style="margin-bottom: 10px; font-weight: bold; color: #666;">
                                        <div class="col-sm-3">玩家中奖金额</div>
                                        <div class="col-sm-3">抽水类型</div>
                                        <div class="col-sm-3">抽水值</div>
                                        <div class="col-sm-3">操作</div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm" id="add-pump-rule" style="margin-top: 10px;">
                                    <i class="fa fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="confirm-save">
                        <i class="fa fa-check"></i> 确定
                    </button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN CORE JS FRAMEWORK -->
    <script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-ui/jquery-ui-1.10.1.custom.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
    <script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
    <!-- END CORE JS FRAMEWORK -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
    <script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="assets/js/core.js" type="text/javascript"></script>
    <!-- END CORE TEMPLATE JS -->
    <script src="js/global.js?v=20250719015" type="text/javascript"></script>
    <script src="js/game-pump-config.js?v=20250719015" type="text/javascript"></script>
</body>
</html>