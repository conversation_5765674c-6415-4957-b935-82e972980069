<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <meta charset="utf-8" />
    <title>平台即时统计</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/bootstrap-datepicker/css/datepicker.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrap-clockpicker/bootstrap-clockpicker.min.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->
    <style>
    	.table_box,.top_box,.bottom_box{
    		display:flex;
    		align-items: flex-start;
   			justify-content: flex-start;
    	}
    	.left_box{
    		width:80%;
    	}
    	.right_box{
    		width:20%;
    	}
    	.top_box .table,.top_box .table td:last-child,.bottom_box .table,.bottom_box .table td:last-child{
    		border-right:none !important;
    	}
    	.top_box .table{
    		margin-bottom:0;
    	}
    	.top_box .w-3{
    		width:calc(100% / 8 * 3);
    	}
    	.top_box .w-3 td{
    		width:calc(100% / 3);
    	}
    	.w-2{
    		width:calc(100% / 8 * 2);
    	}
    	.w-2 td,.right_box .table td{
    		width:calc(100% / 2);
    	}
    	.bottom_box .table thead:first-child td,.bottom_box .table{
    		border-top:none !important;
    	}
    	.table tr{
    		height:40px;
    		text-align:right;
    	}
    	.table thead td{
    		text-align:center;
    		font-weight:bold;
    	}
    	.table tr td{
    		padding:10px !important;
    	}
    </style>
</head>

<body>
    <div class="page-iframe">
        <div class="content">
            <div class="row">
                <div class="col-md-12">
                    <div id="table-data-list" class="grid simple">
                    	<div class="grid-title no-border">
                            <h4><span class="semi-bold">平台即时统计</span></h4>
                        </div>
                        <div class="grid-body border-top">
                            <form name="search-params" class="form-horizontal">
                            	<div class="row">
                            		<div class="col-md-2">
										<select class="form-control" name="agent"></select>
									</div>
									<div class="col-md-2">
										<input class="form-control" name="playerName" placeholder="用户名" type="text">
									</div>
	                                <div class="full-time-picker">
	                                    <input name="sDate" data-init="datepicker" type="text" class="form-control date" placeholder="开始时间" />
	                                    <input name="sHour" data-init="clockpicker" type="text" class="form-control time" />
	                                </div>
	                                <div class="full-time-picker">
	                                    <input name="eDate" data-init="datepicker" type="text" class="form-control date" placeholder="结束时间" />
	                                    <input name="eHour" data-init="clockpicker" type="text" class="form-control time" />
	                                </div>
	                                <button name="search" type="button" class="btn btn-primary"><i class="fa fa-search"></i> 统计结果</button>
                            	</div>
                            </form>
                            <div class="table_box">
                           		<div class="left_box">
                           			<div class="top_box">
                           				<table class="table table-bordered w-3" id="ckTable">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="3">存款</td>
	                           					</tr>
	                           					<tr>
	                           						<td>通道</td>
	                           						<td></td>
	                           						<td>手续费</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody></tbody>
	                           			</table>
                           				<table class="table table-bordered w-3" id="qkTable">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="3">取款</td>
	                           					</tr>
	                           					<tr>
	                           						<td>通道</td>
	                           						<td></td>
	                           						<td>手续费</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody></tbody>
	                           			</table>
	                           			<table class="table table-bordered w-2" id="zycpTable">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="2">自营彩票</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody>
	                           					<tr>
	                           						<td>投注</td>
	                           						<td>12.111</td>
	                           					</tr>
	                           					<tr>
	                           						<td>大唐棋牌</td>
	                           						<td>12.111</td>
	                           					</tr>
	                           					<tr>
	                           						<td>大唐棋牌</td>
	                           						<td>12.111</td>
	                           					</tr>
	                           					<tr>
	                           						<td></td>
	                           						<td></td>
	                           					</tr>
	                           				</tbody>
	                           			</table>
                           			</div>
                           			<div class="bottom_box">
                           				<table class="table table-bordered w-2" id="activeTable">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="2">活动</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody></tbody>
	                           			</table>
                           				<table class="table table-bordered w-2" id="fhTable">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="2">分红</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody></tbody>
	                           			</table>
                           				<table class="table table-bordered w-2" id="gzTable">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="2">工资</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody></tbody>
	                           			</table>
	                           			<table class="table table-bordered w-2">
	                           				<thead>
	                           					<tr>
	                           						<td colspan="2">资金修正</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody id="zjTableBody"></tbody>
	                           				<thead>
	                           					<tr>
	                           						<td colspan="2">余额</td>
	                           					</tr>
	                           				</thead>
	                           				<tbody id="yeTableBody"></tbody>
	                           			</table>
                           			</div>
                           		</div>
                           		<div class="right_box">
                           			<table class="table table-bordered" id="sfTable">
                           				<thead>
                           					<tr>
                           						<td colspan="2">三方游戏</td>
                           					</tr>
                           				</thead>
                           				<tbody></tbody>
                           			</table>
                           		</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN CORE JS FRAMEWORK-->
    <script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
    <script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
    <!-- END CORE JS FRAMEWORK -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
    <script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrap-clockpicker/bootstrap-clockpicker.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="assets/js/core.js" type="text/javascript"></script>
    <!-- END CORE TEMPLATE JS -->
    <script src="js/global.js" type="text/javascript"></script>
    <script src="js/platform-rts.js" type="text/javascript"></script>
</body>

</html>
