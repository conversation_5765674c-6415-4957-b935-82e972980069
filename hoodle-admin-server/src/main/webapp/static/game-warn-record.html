<!DOCTYPE html>
<html>
<head>
    <meta content="text/html;charset=UTF-8" http-equiv="content-type" />
    <meta charset="utf-8" />
    <title>亏损量预警记录</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" media="screen" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" media="screen" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->
</head>

<body>
    <div class="page-iframe">
        <div class="content">
            <!-- 亏损量预警记录 -->
            <div class="row">
                <div class="col-md-12">
                    <div id="table-warn-record" class="grid simple">
                        <div class="grid-title no-border">
                            <h4 style="width:80%"><span class="semi-bold">亏损量预警记录</span></h4>
                        </div>
                        <div class="grid-body border-top">
                            <form name="search-params-warn" class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-2">
                                        <select class="form-control" name="warnStatus">
                                            <option value="">状态</option>
                                            <option value="1">已预警</option>
                                            <option value="2">已清除</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button name="search-warn" type="button" class="btn btn-primary">
                                            <i class="fa fa-search"></i> 查询结果
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center" width="10%">预警ID</th>
                                        <th class="text-center" width="12%">代理商名称</th>
                                        <th class="text-center" width="10%">预警彩种</th>
                                        <th class="text-center" width="12%">预警玩法</th>
                                        <th class="text-center" width="12%">预警编码</th>
                                        <th class="text-center" width="10%">预警亏损量</th>
                                        <th class="text-center" width="18%">最后预警时间</th>
                                        <th class="text-center" width="10%">状态</th>
                                        <th class="text-center" width="15%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="warn-record-tbody"></tbody>
                            </table>
                            <div class="page-list" id="warn-record-page-list"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 自动禁用记录 -->
            <div class="row">
                <div class="col-md-12">
                    <div id="table-auto-disable" class="grid simple">
                        <div class="grid-title no-border">
                            <h4 style="width:80%"><span class="semi-bold">自动禁用记录</span></h4>
                        </div>
                        <div class="grid-body border-top">
                            <form name="search-params-disable" class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-2">
                                        <button name="search-disable" type="button" class="btn btn-primary">
                                            <i class="fa fa-search"></i> 查询结果
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center" width="10%">预警ID</th>
                                        <th class="text-center" width="12%">代理商名称</th>
                                        <th class="text-center" width="15%">禁用彩种</th>
                                        <th class="text-center" width="15%">禁用玩法</th>
                                        <th class="text-center" width="15%">禁用编码</th>
                                        <th class="text-center" width="15%">自动禁用亏损量</th>
                                        <th class="text-center" width="25%">自动禁用时间</th>
                                    </tr>
                                </thead>
                                <tbody id="disable-record-tbody"></tbody>
                            </table>
                            <div class="page-list" id="disable-record-page-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- BEGIN CORE JS FRAMEWORK -->
    <script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-ui/jquery-ui-1.10.1.custom.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
    <script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
    <!-- END CORE JS FRAMEWORK -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
    <script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="assets/js/core.js" type="text/javascript"></script>
    <!-- END CORE TEMPLATE JS -->
    <script src="js/global.js" type="text/javascript"></script>
    <script src="js/game-warn-record.js?v=20250107-22" type="text/javascript"></script>
</body>
</html>
