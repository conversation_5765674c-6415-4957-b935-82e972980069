<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <meta charset="utf-8" />
    <title>团队报表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/bootstrap-datepicker/css/datepicker.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/fselect.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->

    <style>
        /* 表格样式优化 */
        .table-responsive {
            overflow-x: auto; /* 启用水平滚动 */
            margin-bottom: 20px;
        }

        .table th {
            color: #333; /* 表头字体颜色设置为#333 */
            font-weight: 600; /* 加粗字体 */
            white-space: nowrap; /* 表头文本不换行 */
        }
        .table td {
            white-space: nowrap; /* 单元格内容不换行 */
        }
        .table {
            table-layout: auto; /* 表格宽度自适应 */
            min-width: 1500px; /* 设置最小宽度确保所有列可见 */
        }

    </style>

</head>

<body>
<div class="page-iframe">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="table-data-list" class="grid simple">
                    <!--                    <div class="grid-title no-border">-->
                    <!--                        <h4><span class="semi-bold">游戏团队报表</span></h4>-->
                    <!--                        <h6><span class="semi-bold">页面数据说明：</span></h6>-->
                    <!--                        <h6><span class="semi-bold">1、代理商名称和用户名同时指定时，返回的是查询用户（自己的报表）和查询直属下级的团队报表</span></h6>-->
                    <!--                        <h6><span class="semi-bold">2、其他情况为用户的团队数据（包括用户自己）总和</span></h6>-->

                    <!--                    </div>-->
                    <div class="grid-body border-top">
                        <form name="search-params" class="form-horizontal">
                            <div class="row">
                                <div class="col-md-2">
                                    <select class="form-control" name="agentName" id="agentSelect">
                                        <option value="">请选择平台</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input name="playerName" type="text" class="form-control" placeholder="玩家名称" />
                                </div>
                                <div class="col-md-2">
                                    <input name="sDate" data-init="datepicker" type="text" class="form-control" placeholder="开始日期" />
                                </div>
                                <div class="col-md-2">
                                    <input name="eDate" data-init="datepicker" type="text" class="form-control" placeholder="结束日期" />
                                </div>
                                <div class="col-md-2">
                                    <button name="search" type="button" class="btn btn-primary"><i class="fa fa-search"></i> 查询结果</button>
                                </div>
                            </div>
                        </form>



                        <!-- 统计汇总区域 -->
                        <div id="summary-section" class="row" style="margin: 15px 0; padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                            <div class="col-md-12">
                                <!-- 标题行 -->
                                <div class="row" style="margin-bottom: 10px; padding: 5px; background-color: #e9ecef; border-radius: 3px;">
                                    <div class="col-md-2" style="text-align: center; font-weight: bold; color: #495057;">
                                        团队数据总计
                                    </div>
                                    <div class="col-md-2" style="text-align: center; font-weight: bold; color: #495057;">
                                        上级代理: <span id="summary-agent" style="color: #007bff; cursor: pointer; text-decoration: underline;">-</span>
                                    </div>
                                    <div class="col-md-8"></div>
                                </div>
                                <!-- 数据行 -->
                                <div class="row">
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">账户余额</div>
                                        <div id="summary-teamAvailableBalance" style="color: #28a745;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">充值</div>
                                        <div id="summary-transferIn" style="color: #28a745;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">提现</div>
                                        <div id="summary-transferOut" style="color: #dc3545;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">手续费</div>
                                        <div id="summary-handlingFee" style="color: #dc3545;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">投注</div>
                                        <div id="summary-bet" style="color: #6c757d;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">派奖</div>
                                        <div id="summary-bonus" style="color: #17a2b8;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">返点</div>
                                        <div id="summary-rebateAmount" style="color: #fd7e14;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">抽水</div>
                                        <div id="summary-pumpAmount" style="color: #17a2b8;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">打赏</div>
                                        <div id="summary-reward" style="color: #e83e8c;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">活动</div>
                                        <div id="summary-activity" style="color: #fd7e14;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">工资</div>
                                        <div id="summary-salaryAmount" style="color: #20c997;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">分红</div>
                                        <div id="summary-divsAmount" style="color: #6f42c1;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">上下级转帐转入</div>
                                        <div id="summary-playerTransInAmount" style="color: #6f42c1;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px 4px; width: 7.6%;">
                                        <div style="font-weight: bold; color: #333;">上下级转帐转出</div>
                                        <div id="summary-playerTransOutAmount" style="color: #6f42c1;">0.00</div>
                                    </div>
                                    <div class="col-md-1" style="text-align: center; padding: 5px;">
                                        <div style="font-weight: bold; color: #333;">盈亏</div>
                                        <div id="summary-profit" style="color: #28a745; font-weight: bold;">0.00</div>
                                    </div>
                                    <div class="col-md-2"></div>
                                </div>
                            </div>
                        </div>


                        <!-- 添加表格标题 -->
                        <div class="row" style="margin-top: 15px; margin-bottom: 8px;">
                            <div class="col-md-12">
                                <h5 style="font-weight: bold; color: #333; text-align: left;">个人数据明细</h5>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="table1">
                                <thead>
                                <tr>
                                    <th  class="text-center" style="min-width: 8%">平台名称</th>
                                    <th  class="text-center" style="min-width: 8%">玩家名称</th>
                                    <th class="text-center"  data-field="账户余额" data-value="teamAvailableBalance" style="min-width: 8%">账户余额</th>
                                    <th  class="text-center"  data-field="充值" data-value="transferInAmount" style="min-width: 6%">充值</th>
                                    <th  class="text-center"  data-field="提现" data-value="transferOutAmount" style="min-width: 6%">取款</th>
                                    <th  class="text-center"  data-field="手续费" data-value="handlingFee" style="min-width: 6%">手续费</th>
                                    <th  class="text-center"  data-field="投注" data-value="betAmount" style="min-width: 6%">投注</th>
                                    <th  class="text-center"  data-field="派奖" data-value="bonusAmount" style="min-width: 6%">派奖</th>
                                    <th  class="text-center"  data-field="返点" data-value="rebateAmount" style="min-width: 6%">返点</th>
                                    <th  class="text-center"  data-field="抽水" data-value="pumpAmount" style="min-width: 6%">抽水</th>
                                    <th  class="text-center"  data-field="打赏" data-value="rewardAmount" style="min-width: 6%">打赏</th>
                                    <th  class="text-center"  data-field="活动" data-value="activityAmount" style="min-width: 6%">活动</th>
                                    <th  class="text-center"  data-field="工资" data-value="salaryAmount" style="min-width: 6%">工资</th>
                                    <th  class="text-center"  data-field="分红" data-value="divsAmount" style="min-width: 6%">分红</th>
                                    <th  class="text-center"  data-field="上下级转帐" data-value="playerTransInAmount" style="min-width: 8%">上下级转帐转入</th>
                                    <th  class="text-center"  data-field="上下级转帐" data-value="playerTransOutAmount" style="min-width: 8%">上下级转帐转出</th>
                                    <th  class="text-center"  data-field="盈亏" data-value="profitAmount" style="min-width: 6%">盈亏</th>
                                </tr>
                                </thead>
                                <tbody id="table-person"></tbody>
                            </table>
                        </div>

                        <!-- 添加表格标题 -->
                        <div class="row" style="margin-top: 15px; margin-bottom: 8px;">
                            <div class="col-md-12">
                                <h5 style="font-weight: bold; color: #333; text-align: left;">团队数据明细</h5>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="table2">
                                <thead>
                                <tr>
                                    <th  class="text-center" style="min-width: 8%">平台名称</th>
                                    <th  class="text-center" style="min-width: 8%">玩家名称</th>
                                    <th  class="text-center"  data-command="orderTh"  data-field="账户余额" data-value="teamAvailableBalance" style="min-width: 8%">账户余额</th>
                                    <th  class="text-center"  data-command="orderTh"  data-field="充值" data-value="transferInAmount" style="min-width: 6%">充值</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="提现" data-value="transferOutAmount" style="min-width: 6%">取款</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="手续费" data-value="handlingFee" style="min-width: 6%">手续费</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="投注" data-value="betAmount" style="min-width: 6%">投注</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="派奖" data-value="bonusAmount" style="min-width: 6%">派奖</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="返点" data-value="rebateAmount" style="min-width: 6%">返点</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="抽水" data-value="pumpAmount" style="min-width: 6%">抽水</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="打赏" data-value="rewardAmount" style="min-width: 6%">打赏</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="活动" data-value="activityAmount" style="min-width: 6%">活动</th>
                                    <th  class="text-center"  data-command="orderTh"  data-field="工资" data-value="salaryAmount" style="min-width: 6%">工资</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="分红" data-value="divsAmount" style="min-width: 6%">分红</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="上下级转帐" data-value="playerTransInAmount" style="min-width: 8%">上下级转帐转入</th>
                                    <th  class="text-center"  data-command="orderTh"  data-field="上下级转帐" data-value="playerTransOutAmount" style="min-width: 8%">上下级转帐转出</th>
                                    <th  class="text-center"  data-command="orderTh" data-field="盈亏" data-value="profitAmount" style="min-width: 6%">盈亏</th>
                                </tr>
                                </thead>
                                <tbody id="table-team"></tbody>
                            </table>
                        </div>
                        <div class="page-list"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END CONTAINER -->
<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="js/platform-agent-player-team-report.js?v=20250715-0050" type="text/javascript"></script>
<script src="assets/js/fselect.js" type="text/javascript"></script>
</body>

</html>
