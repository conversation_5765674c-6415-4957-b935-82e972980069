<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <meta charset="utf-8" />
    <title>团队报表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/plugins/bootstrap-datepicker/css/datepicker.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css" />
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" />
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/fselect.css" rel="stylesheet" type="text/css" />
    <!-- END CSS TEMPLATE -->
</head>

<body>
    <div class="page-iframe">
        <div class="content">
            <div class="row">
                <div class="col-md-12">
                    <div id="table-data-list" class="grid simple">
                        <div class="grid-title no-border">
                            <h4><span class="semi-bold">游戏团队报表</span></h4>
                            <h6><span class="semi-bold">页面数据说明：</span></h6>
                            <h6><span class="semi-bold">1、代理商名称和用户名同时指定时，返回的是查询用户（自己的报表）和查询直属下级的团队报表</span></h6>
                            <h6><span class="semi-bold">2、其他情况为用户的团队数据（包括用户自己）总和</span></h6>

                        </div>
                        <div class="grid-body border-top">
                            <form name="search-params" class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-2">
                                        <input type="text" class="form-control" name="agentName" placeholder="代理商名称"/>
                                    </div>
                                    <div class="col-md-2">
                                        <input name="playerName" type="text" class="form-control" placeholder="用户名" />
                                    </div>
                             <!--       <div class="col-md-2">
                                        <select name="accountType" class="form-control">
                                            <option value="">账户层级类型</option>
                                        </select>
                                    </div>-->
                                    <div class="col-md-2">
                                        <input name="sDate" data-init="datepicker" type="text" class="form-control" placeholder="开始日期" />
                                    </div>
                                    <div class="col-md-2">
                                        <input name="eDate" data-init="datepicker" type="text" class="form-control" placeholder="结束日期" />
                                    </div>
<!--                                    <div class="col-md-2">-->
<!--                                        <select name="platformCodes" data-init="platformCodes" class="form-control">-->
<!--                                            <option value="">请选择游戏平台</option>-->
<!--                                        </select>-->
<!--                                    </div>-->
                                    <div class="col-md-2">
                                        <button name="search" type="button" class="btn btn-primary"><i class="fa fa-search"></i> 查询结果</button>
                                    </div>
                                </div>
                            </form>
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th width="9%" class="text-center">代理商名称</th>
                                        <th width="9%" class="text-center">用户名</th>
                                        <th width="9%" class="text-center">三方游戏余额</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="转入金额" data-value="depositAmount">转入金额</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="转出金额" data-value="withdrawAmount">转出金额</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="下注金额" data-value="betAmount">下注金额</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="有效下注金额" data-value="validBetAmount">有效下注金额</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="活动金额" data-value="activityAmount">活动金额</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="契约工资" data-value="rebateAmount">契约工资</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="契约分红" data-value="commissionAmount">契约分红</th>
                                        <th width="9%" class="text-center" data-command="orderTh" data-field="盈亏" data-value="bonusAmount">盈亏</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div class="page-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END CONTAINER -->
    <!-- BEGIN CORE JS FRAMEWORK-->
    <script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
    <script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
    <!-- END CORE JS FRAMEWORK -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
    <script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
    <script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="assets/js/core.js" type="text/javascript"></script>
    <!-- END CORE TEMPLATE JS -->
    <script src="js/global.js" type="text/javascript"></script>
    <script src="js/game-simulation-team-report.js" type="text/javascript"></script>
    <script src="assets/js/fselect.js" type="text/javascript"></script>
</body>

</html>
