<!DOCTYPE html>
<html>

<head>
    <meta content="text/html;charset=UTF-8" http-equiv="content-type"/>
    <meta charset="utf-8"/>
    <title></title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" media="screen" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" media="screen" rel="stylesheet"
          type="text/css"/>
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"/>
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css"/>
    <!-- END CSS TEMPLATE -->
    <style type="text/css">
        .content > .row {
            margin-left: auto;
            margin-right: auto;
            width: 800px;
        }
    </style>
</head>

<body>
<div class="page-iframe" id="app">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="grid simple" id="table-account">
                    <div class="grid-title no-border">
                        <h4><span class="semi-bold">主账户基本信息</span></h4>
                    </div>
                    <div class="grid-body border-top">
                        <table class="table table-bordered">
                            <tbody>
                            <tr>
                                <td class="text-center" width="25%">代理商：</td>
                                <td data-field="agentName" width="25%"></td>
                                <td class="text-center" width="25%">档案编号：</td>
                                <td data-field="id" width="25%"></td>
                            </tr>
                            <tr>
                                <td class="text-center">用户名：</td>
                                <td data-field="playerName"></td>
                                <td class="text-center">用户昵称：</td>
                                <td data-field="nickname"></td>
                            </tr>
                            <tr>
                                <td class="text-center">取款人：</td>
                                <td data-field="withdrawName"></td>
                                <td class="text-center">用户类型：</td>
                                <td data-field="accountTypeName"></td>
                            </tr>
                            <tr>
                            	<td class="text-center">上级代理：</td>
                                <td data-field="upPlayerNames"></td>
                                <td class="text-center">登录错误次数：</td>
                                <td data-field="loginFailedNum"></td>
                            </tr>
                            <tr>
                                <td class="text-center">注册时间：</td>
                                <td data-field="registTime"></td>
                                <td class="text-center">登录时间：</td>
                                <td data-field="loginTime"></td>
                            </tr>
                            <tr>
                                <td class="text-center">锁定时间：</td>
                                <td data-field="lockTime"></td>
                                <td class="text-center">账号状态：</td>
                                <td data-field="status"></td>
                            </tr>
                            <tr>
                                <td class="text-center">谷歌绑定：</td>
                                <td data-field="googleBind"></td>
                                <td class="text-center">谷歌登录：</td>
                                <td data-field="googleLogin"></td>
                            </tr>
                            <tr>
                                <td class="text-center">在线状态：</td>
                                <td data-field="onlineStatus"></td>
                                <td class="text-center">VIP等级：</td>
                                <td data-field="level"></td>
                            </tr>
                            <tr>
								<td class="text-center">联系电话：</td>
								<td data-field="telephone"></td>
								<td class="text-center">电子邮箱：</td>
								<td data-field="email"></td>
							</tr>
                            <tr>
                                <td class="text-center">提款次数：</td>
                                <td data-field="withdrawLimit"></td>
                                <td class="text-center">返点：</td>
                                <td data-field="point"></td>
                            </tr>
							<tr>
                                <td class="text-center">给上级转账：</td>
                                <td data-field="allowTransferToUp"></td>
                                <td class="text-center">给下级转账：</td>
                                <td data-field="allowTransferToDown"></td>
							</tr>
							<tr>
								<td class="text-center">同级开号：</td>
								<td data-field="equalLevel"></td>
                                <td class="text-center">玩家奖级：</td>
                                <td data-field="lotteryCode"></td>
                            </tr>
							<tr>
                                <td class="text-center">账户余额：</td>
                                <td data-field="playerAvailableBalance"></td>
                                <td class="text-center">可提现余额：</td>
                                <td data-field="playerAvailableWithdrawBalance"></td>
							</tr>
                            <tr>
                                <td class="text-center">资金密码错误次数：</td>
                                <td data-field="paymentPasswordFailedNum"></td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="button-group align-center equal-width" data-init="false">
                            <button class="btn btn-primary btn-cons" data-command="modify-login-password" type="button">
                                <i class="fa fa-edit"></i> 修改登录密码
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-withdraw-password"
                                    type="button"><i class="fa fa-edit"></i> 修改资金密码
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-withdraw-name" type="button">
                                <i class="fa fa-edit"></i> 修改取款人
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="clear-withdraw-limit" type="button">
                                <i class="fa fa-undo"></i> 清空提款限制
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="reset-lock-time" type="button"><i
                                    class="fa fa-ban"></i> 解除时间锁
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="update-google-login" type="button"><i
                                    class="fa fa-recycle"></i> 启用谷歌验证
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="reset-google-login" type="button"><i
                                    class="fa fa-recycle"></i> 重置谷歌绑定
                            </button>
<!--                            <button class="btn btn-primary btn-cons" data-command="reset-password-error-count"-->
<!--                                    type="button"><i class="fa fa-undo"></i>-->
<!--                                <span style="font-size:smaller;">重置密码错误次数</span></button>-->
                            <button class="btn btn-primary btn-cons" data-command="modify-point" type="button">
                                <i class="fa fa-edit"></i> 修改返点
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-line-point" type="button">
                                <i class="fa fa-arrow-down"></i> 线路统一降点
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="increase-line-point" type="button">
                                <i class="fa fa-arrow-up"></i> 线路统一升点
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-player-type" type="button">
                                <i class="fa fa-edit"></i> 修改用户类型
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-equal-level" type="button">
                                <i class="fa fa-check"></i> 开启同级开号
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-allow-transfer-to-down" type="button">
                                <i class="fa fa-check"></i> 开启给下级转账
                            </button>
               
                            <button class="btn btn-primary btn-cons" data-command="modify-allow-transfer-to-up" type="button">
                                <i class="fa fa-check"></i> 开启给上级转账
                            </button>
               
                            <button class="btn btn-primary btn-cons" data-command="modify-parent-player" type="button">
                                <i class="fa fa-edit"></i> 线路转移
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-login-failed-num" type="button" style="width: 200px;">
                                <i class="fa fa-edit"></i> 清除登录密码错误次数
                            </button>
                            <button class="btn btn-primary btn-cons" data-command="modify-payment-password-failed-num" type="button" style="width: 200px;">
                                <i class="fa fa-edit"></i>清除资金密码错误次数
                            </button>
                            <button  @click="clickApplyUpDedTransfer()" style="width: 164px !important;" class="btn btn-primary btn-cons" data-command="modify-up-ded-down-transfer" type="button">
                                <i :class=" [ 'fa', player_data.applyUpDedTransfer ? 'fa-ban' : 'fa-check' ] "></i> {{ player_data.applyUpDedTransfer ? '关闭上级给下级下分' : '开启上级给下级下分' }} 
                            </button>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 修改密码 -->
        <div class="modal fade" data-backdrop="static" id="modal-modify-password" role="dialog" tabindex="-1">
            <div class="modal-dialog" style="width: 600px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-hidden="true" class="close" data-dismiss="modal" type="button">×</button>
                        <h4 class="semi-bold" data-field="title">修改密码</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">请输入新密码</label>
                                <div class="col-sm-6">
                                    <input class="form-control" name="password1" type="password">
                                    <span aria-hidden="true" class="glyphicon form-control-feedback"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">请重复新密码</label>
                                <div class="col-sm-6">
                                    <input class="form-control" name="password2" type="password">
                                    <span aria-hidden="true" class="glyphicon form-control-feedback"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">Tips: </label>
                                <div class="col-sm-6">
                                    <label class="control-label" data-field="tips"
                                           style="text-align: left; display: block;">
                                    </label>
                                </div>
                            </div>

                            <div class="form-group has-feedback">
                                <label class="col-sm-6 control-label" data-field="notice"></label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" data-command="submit" type="button"><i class="fa fa-check"></i>
                            确定
                        </button>
                        <button class="btn btn-default" data-dismiss="modal" type="button"><i class="fa fa-undo"></i> 取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 修改账户取款人 -->
        <div class="modal fade" data-backdrop="static" id="modal-modify-withdraw-name" role="dialog" tabindex="-1">
            <div class="modal-dialog" style="width: 600px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-hidden="true" class="close" data-dismiss="modal" type="button">×</button>
                        <h4 class="semi-bold" data-field="title">修改账户取款人</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">取款人姓名</label>
                                <div class="col-sm-6">
                                    <input class="form-control" name="name" type="text">
                                    <span aria-hidden="true" class="glyphicon form-control-feedback"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" data-command="submit" type="button"><i class="fa fa-check"></i>
                            确定
                        </button>
                        <button class="btn btn-default" data-dismiss="modal" type="button"><i class="fa fa-undo"></i> 取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 修改返点 -->
        <div id="modal-modify-point" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" style="width: 600px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 data-field="title" class="semi-bold">修改彩票返点</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">定位返点</label>
                                <div class="col-sm-6">
                                    <input name="point" type="text" class="form-control">
                                    <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                    <span class="help-block"></span>
                                    <span data-field="msg-point" class="help-msg"></span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-command="submit"><i class="fa fa-check"></i> 确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><i class="fa fa-undo"></i> 取消</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 修改用户类型 -->
        <div id="modal-modify-player-type" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" style="width: 600px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 data-field="title" class="semi-bold">修改用户类型</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">用户类型</label>
                                <div class="col-sm-6">
                                    <select class="form-control" name="playerType">
                                        <option value="2">代理</option>
                                        <option value="1">玩家</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-command="submit"><i class="fa fa-check"></i> 确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><i class="fa fa-undo"></i> 取消</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 修改上级用户 -->
        <div class="modal fade" data-backdrop="static" id="modal-modify-parent-player" role="dialog" tabindex="-1">
            <div class="modal-dialog" style="width: 600px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-hidden="true" class="close" data-dismiss="modal" type="button">×</button>
                        <h4 class="semi-bold">线路转移</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"></label>
                                <div class="col-sm-6">
                                    <div class="radio">
                                        <input id="type_0" type="radio" name="type" value="0" checked="checked">
                                        <label for="type_0">只转移下级用户</label>
                                        <input id="type_1" type="radio" name="type" value="1">
                                        <label for="type_1">转移整个线路</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">当前用户</label>
                                <div class="col-sm-6">
                                    <p class="form-control-static" data-field="playerName"></p>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">新的上级用户名</label>
                                <div class="col-sm-6">
                                    <input class="form-control" name="newPlayerName" type="text">
                                    <span aria-hidden="true" class="glyphicon form-control-feedback"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" data-command="submit" type="button"><i class="fa fa-check"></i> 确定</button>
                        <button class="btn btn-default" data-dismiss="modal" type="button"><i class="fa fa-undo"></i> 取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END CONTAINER -->
<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="js/player-details.js" type="text/javascript"></script>

<script src="assets/js/vue.min.js" type="text/javascript"></script>

<script>
	const vue_app = new Vue({
        el: '#app',
        data: {
        	AccountInfo: {},
        	player_data:{}
        },
        methods: {
          update_player_data(player_data){
        	  this.player_data = player_data;
          },
          clickApplyUpDedTransfer(){
        	  let this_ = this;
              var tips = this_.player_data.applyUpDedTransfer ? '关闭' : '开启';
              App.dialog("确定" + tips + "上级给下级下分？", "确定" + tips, function () {
            	  var id = this_.player_data.playerId;
            	  this_.player_data.applyUpDedTransfer = !this_.player_data.applyUpDedTransfer;
                  var url = Route.PATH + Route.Agent.PATH + Route.Agent.MODIFY_APPLY_UP_DED_TRANSFER;
                  var data = {id: id, applyUpDedTransfer: this_.player_data.applyUpDedTransfer};
                  App.ajaxPost(url, data, function () {
                	  this_.AccountInfo.init();
                      App.msg("success", "操作成功");
                  });
              });
          }
        }
    });
</script>



</body>

</html>
