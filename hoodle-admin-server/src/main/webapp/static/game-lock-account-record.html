<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8"/>
    <meta charset="utf-8"/>
    <title>平台卡单</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/bootstrap-datepicker/css/datepicker.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css"/>
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"/>
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/fselect.css" rel="stylesheet" type="text/css"/>
    <!-- END CSS TEMPLATE -->
</head>

<body>
<div class="page-iframe">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="table-data-list" class="grid simple">
                    <div class="grid-title no-border">
                        <h4><span class="semi-bold">卡单任务列表</span></h4>
                    </div>
                    <div class="grid-body border-top">
                        <form name="search-params" class="form-horizontal">
                            <div class="row">
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="agentNo" placeholder="代理商商户号"/>
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="agentName" placeholder="代理商名称"/>
                                </div>
                                <div class="col-md-2">
                                    <select name="lockType" class="form-control">
                                        <option value="" selected="selected">卡单类型</option>
                                        <option value="1">个人</option>
                                        <option value="9">全平台</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="lockStatus" class="form-control">
                                        <option value="" selected="selected">状态</option>
                                        <option value="0">未启用</option>
                                        <option value="1">卡单中</option>
                                        <option value="2">已完成</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <button name="search" type="button" class="btn btn-primary">
                                        <i class="fa fa-search"></i> 查询结果
                                    </button>
                                    <button name="add-lock-task" type="button" class="btn btn-primary">
                                        <i class="fa fa-plus"></i>添加卡单任务
                                    </button>
                                </div>
                            </div>
                        </form>
                        <table class="table table-bordered table-hover">
                            <thead>
                            <tr>
                                <th class="text-center">任务编号</th>
                                <th class="text-center">代理商</th>
                                <th class="text-center">类型</th>
                                <th class="text-center">游戏名称</th>
                                <th class="text-center">用户</th>
                                <th class="text-center">卡单策略</th>
                                <th class="text-center">投注金额</th>
                                <th class="text-center">状态</th>
                                <th class="text-center">添加人</th>
                                <th class="text-center">添加时间</th>
                                <th class="text-center" width="16%">操作</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div class="page-list"></div>
                    </div>
                </div>
            </div>
        </div>
        <form></form>
        <!-- 添加用户 -->
        <div id="modal-add-lock-task" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" style="width: 700px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 data-field="title" class="semi-bold">添加卡单任务</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">平台代理商</label>
                                <div class="col-sm-6">
                                    <select class="form-control" name="agentNo"></select>
                                </div>
                            </div>
                            <div class="form-group" novalidate="true">
                                <label class="col-sm-4 control-label">卡单类型</label>
                                <div class="col-sm-6">
                                    <select name="lockType" class="form-control">
                                        <option value="1">个人</option>
                                        <option value="9">全平台</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" novalidate="true">
                                <label class="col-sm-4 control-label">游戏种类</label>
                                <div class="col-sm-6">
                                    <select name="lottery" class="form-control">
                                        <option value="">全部</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">用户</label>
                                <div class="col-sm-6">
                                    <input name="playerNameList" type="text" class="form-control">
                                    <span>多个用户用逗号","分隔ex:user001,user002</span>
                                    <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">卡单策略</label>
                                <div class="col-sm-6">
                                    <select name="lockNumber" class="form-control">
                                        <option value="1">卡一次</option>
                                        <option value="10000">一直卡</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group has-feedback" data-visable="issue">
                                <label class="col-sm-4 control-label">期号</label>
                                <div class="col-sm-6">
                                    <input name="issue" type="text" class="form-control">
                                    <span>不填代表全部</span>
                                    <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">投注金额</label>
                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="number" class="form-control" name="amountMix" value="0">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="number" class="form-control" name="amountMax" value="10000">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-6">
                                    <input name="lockRemark" type="text" class="form-control">
                                    <span class="glyphicon form-control-feedback" aria-hidden="true"></span>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-command="submit"><i class="fa fa-check"></i> 确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><i class="fa fa-undo"></i> 取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="assets/js/fselect.js" type="text/javascript"></script>
<script src="js/game-lock-account-record.js" type="text/javascript"></script>
</body>

</html>
