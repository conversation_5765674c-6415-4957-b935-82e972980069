<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8"/>
    <meta charset="utf-8"/>
    <title>契约发放记录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <!-- BEGIN PLUGIN CSS -->
    <link href="assets/plugins/jquery-notifications/css/messenger.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/jquery-notifications/css/messenger-theme-flat.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="assets/plugins/bootstrap-datepicker/css/datepicker.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery.pagination/jquery.pagination.css" rel="stylesheet" type="text/css"/>
    <!-- END PLUGIN CSS -->
    <!-- BEGIN CORE CSS FRAMEWORK -->
    <link href="assets/plugins/boostrapv3/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/boostrapv3/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/animate.min.css" rel="stylesheet" type="text/css"/>
    <link href="assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"/>
    <!-- END CORE CSS FRAMEWORK -->
    <!-- BEGIN CSS TEMPLATE -->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom-icon-set.css" rel="stylesheet" type="text/css"/>
    <link href="assets/css/custom.css" rel="stylesheet" type="text/css"/>
    <!-- END CSS TEMPLATE -->
</head>

<body>
<div class="page-iframe">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="table-data-list" class="grid simple">
                    <div class="grid-title no-border">
                        <h4><span class="semi-bold">契约发放记录</span></h4>
                    </div>
                    <div class="grid-body border-top">
                        <form name="search-params" class="form-horizontal">
                            <div class="row">
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="agentNo" placeholder="代理商商户号"/>
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="agentName" placeholder="代理商名称"/>
                                </div>
                                <div class="col-md-2">
                                    <select name="contractType" class="form-control">
                                        <option value="">契约类型</option>
                                        <option value="1">彩票契约工资</option>
                                        <option value="2">彩票契约分红</option>
                                        <option value="3">三方契约工资</option>
                                        <option value="4">三方契约分红</option>
                                    </select>
                                </div>
                                <!--<div class="col-md-2">
                                    <input type="text" class="form-control" name="contractTitle" placeholder="契约标题"/>
                                </div>-->
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="toPlayerName" placeholder="契约用户"/>
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="fromPlayerName" placeholder="契约上级"/>
                                </div>
                                <div class="col-md-2">
                                    <div class="checkbox check-default">
                                        <input id="platformOnly" name="platformOnly" type="checkbox">
                                        <label for="platformOnly">只显示平台契约</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-2">
                                    <input type="text" class="form-control" name="totalBetMin" placeholder="投注量大于"/>
                                </div>
                                <div class="col-md-2">
                                    <input name="startCalculateCycleDate" data-init="datepicker" type="text" class="form-control" placeholder="开始日期" />
                                </div>
                                <div class="col-md-2">
                                    <input name="endCalculateCycleDate" data-init="datepicker" type="text" class="form-control" placeholder="结束日期" />
                                </div>
                                <div class="col-md-2">
                                    <select name="drawStatus" class="form-control">
                                        <option value="">发放状态</option>
                                        <option value="0">待处理</option>
                                        <option value="1">已发放</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button name="search" type="button" class="btn btn-primary">
                                        <i class="fa fa-search"></i> 查询结果
                                    </button>
                                </div>
                            </div>
                        </form>
                        <table class="table table-bordered table-hover">
                            <thead>
                            <tr>
                                <th class="text-center">代理商商户号</th>
                                <th class="text-center">代理商名称</th>
                                <th class="text-center">契约编号</th>
                                <th class="text-center">契约标题</th>
                                <th class="text-center">契约用户</th>
                                <th class="text-center">契约上级</th>
                                <th class="text-center">投注量</th>
                                <th class="text-center">亏损量</th>
                                <th class="text-center">活跃用户</th>
                                <th class="text-center">发放比例</th>
                                <th class="text-center">发放金额</th>
                                <th class="text-center">发放状态</th>
                                <th class="text-center">计算周期</th>
                                <th class="text-center">创建时间</th>
                                <th class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div class="page-list"></div>
                    </div>
                </div>
            </div>
        </div>
        <div id="detail-contract-record" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" style="width: 800px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="semi-bold" data-field="title">契约发放记录</h4>
                    </div>
                    <div class="modal-body">
                        <div class="grid simple">
                            <div class="grid-body border-top">
                                <table class="table table-bordered">
                                    <tbody>
                                    <tr>
                                        <td class="text-center" width="25%">代理商商户号：</td>
                                        <td data-field="agentNo" width="25%"></td>
                                        <td class="text-center" width="25%">代理商名称：</td>
                                        <td data-field="agentName" width="25%"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">契约编号：</td>
                                        <td data-field="contractCode"></td>
                                        <td class="text-center">契约标题：</td>
                                        <td data-field="contractTitle"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">契约用户：</td>
                                        <td data-field="toPlayerName"></td>
                                        <td class="text-center">契约上级：</td>
                                        <td data-field="fromPlayerName"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">充值量：</td>
                                        <td data-field="totalRecharge"></td>
                                        <td class="text-center">投注量：</td>
                                        <td data-field="totalBet"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">亏损量：</td>
                                        <td data-field="totalLoss"></td>
                                        <td class="text-center">活跃用户：</td>
                                        <td data-field="activeUser"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">发放比例：</td>
                                        <td data-field="scalePoint"></td>
                                        <td class="text-center">发放金额：</td>
                                        <td data-field="drawAmount"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">达标状态：</td>
                                        <td data-field="achieveStatus"></td>
                                        <td class="text-center">下级发放金额：</td>
                                        <td data-field="downDrawAmount"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">发放时间：</td>
                                        <td data-field="drawTime"></td>
                                        <td class="text-center">发放状态：</td>
                                        <td data-field="drawStatus"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">创建时间：</td>
                                        <td data-field="createTime" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">计算周期：</td>
                                        <td data-field="calculateCycle" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">备注：</td>
                                        <td data-field="remarks" colspan="3"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><i class="fa fa-undo"></i> 关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- BEGIN CORE JS FRAMEWORK-->
<script src="assets/plugins/jquery-1.8.3.min.js" type="text/javascript"></script>
<script src="assets/plugins/boostrapv3/js/bootstrap.min.js" type="text/javascript"></script>
<script src="assets/plugins/breakpoints.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-block-ui/jqueryblockui.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-numberAnimate/jquery.animateNumbers.js" type="text/javascript"></script>
<script src="assets/plugins/moment/moment.js" type="text/javascript"></script>
<!-- END CORE JS FRAMEWORK -->
<!-- BEGIN PAGE LEVEL JS -->
<script src="assets/plugins/jquery-notifications/js/messenger.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-notifications/js/messenger-theme-future.js" type="text/javascript"></script>
<script src="assets/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="assets/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
<script src="assets/plugins/jquery.pagination/jquery.pagination.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN CORE TEMPLATE JS -->
<script src="assets/js/core.js" type="text/javascript"></script>
<!-- END CORE TEMPLATE JS -->
<script src="js/global.js" type="text/javascript"></script>
<script src="js/contract-record.js" type="text/javascript"></script>
</body>
</html>
