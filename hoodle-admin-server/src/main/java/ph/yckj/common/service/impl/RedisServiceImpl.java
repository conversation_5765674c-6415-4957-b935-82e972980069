package ph.yckj.common.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import lombok.extern.slf4j.Slf4j;
import sy.hoodle.base.common.entity.*;
import ph.yckj.common.service.RedisService;
import ph.yckj.common.util.AbstractDao;
import ph.yckj.common.util.RedisKey;

@Slf4j
@Service
public class RedisServiceImpl extends AbstractDao implements RedisService {

	@Autowired
	private RedisTemplate<String, String> redisTemplate;

	@Override
	public boolean delSystemCacheVersion(String str) {
		String key = RedisKey.getSystemCacheKey(str);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public AgentPlayerInfo getAgentPlayerInfo(Long playerId) {
		AgentPlayerInfo result = null;
		String key = RedisKey.getAgentPlayerInfoKey(playerId);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			result = JSONObject.parseObject(s, AgentPlayerInfo.class);
		} else {
			result = getAgentPlayerInfoDao().getById(playerId);
			if (result != null) {
				boolean b = setAgentPlayerInfo(playerId, result);
				if (!b) {
					log.warn("getAgentPlayerInfo 失败， playerId=" + playerId);
				}
			}
		}
		return result;
	}

	private boolean setAgentPlayerInfo(Long playerId, AgentPlayerInfo entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getAgentPlayerInfoKey(playerId);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delAgentPlayerInfo(Long playerId) {
		String key = RedisKey.getAgentPlayerInfoKey(playerId);
		AgentPlayerInfo player = getAgentPlayerInfo(playerId);
		String agentNo = player.getAgentNo();
		String urlAuthKey = player.getUrlAuthKey();
		String playerName = player.getPlayerName();
		List<String> keys = new ArrayList<>();
		keys.add(key);
		if (player != null) {
			String key1 = RedisKey.getAgentPlayerInfoNameKey(agentNo, playerName);
			keys.add(key1);
			String key2 = RedisKey.getAgentPlayerInfoUrlAuthKeyKey(agentNo, playerName, urlAuthKey);
			keys.add(key2);
		}
		redisTemplate.delete(keys);

		return true;
	}

	@Override
	public AgentPlayerGameOrder getGameRecordDetail(Long orderId) {
		AgentPlayerGameOrder result = null;
		String key = RedisKey.getGameRecordDetailKey(orderId);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, AgentPlayerGameOrder.class);
		} else {
			result = getAgentPlayerGameOrderDao().getById(orderId);
			if (result != null) {
				boolean b = setGameRecordDetail(orderId, result);
				if (!b) {
					log.warn("getGameRecordDetail 失败， orderId=" + orderId);
				}
			}
		}
		return result;
	}

	private boolean setGameRecordDetail(Long orderId, AgentPlayerGameOrder entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameRecordDetailKey(orderId);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameRecordDetail(Long orderId) {
		String key = RedisKey.getGameRecordDetailKey(orderId);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<GiftInfo> getGiftInfoList() {
		List<GiftInfo> result = null;
		String key = RedisKey.getGiftInfoListKey();
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GiftInfo>>() {
			});
		} else {
			result = getGiftInfoDao().listAllForFrontend();
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGiftInfoList(result);
				if (!b) {
					log.warn("getGiftInfoList 失败");
				}
			}
		}
		return result;
	}

	private boolean setGiftInfoList(List<GiftInfo> entryList) {
		boolean result = false;
		if (entryList != null) {
			String key = RedisKey.getGiftInfoListKey();
			redisTemplate.opsForValue().set(key, JSONArray.toJSONString(entryList));
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGiftInfoList() {
		String key = RedisKey.getGiftInfoListKey();
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public GiftInfo getGiftInfo(String giftCode) {
		GiftInfo result = null;
		String key = RedisKey.getGiftInfoKey(giftCode);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, GiftInfo.class);
		} else {
			result = getGiftInfoDao().getByCode(giftCode);
			if (result != null) {
				boolean b = setGiftInfo(giftCode, result);
				if (!b) {
					log.warn("getGiftInfo 失败， giftCode=" + giftCode);
				}
			}
		}
		return result;
	}

	private boolean setGiftInfo(String giftCode, GiftInfo entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGiftInfoKey(giftCode);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGiftInfo(String giftCode) {
		String key = RedisKey.getGiftInfoKey(giftCode);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public AgentInfo getAgentInfo(String agentNo) {
		AgentInfo result = null;
		String key = RedisKey.getAgentInfoKey(agentNo);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, AgentInfo.class);
		} else {
			result = getAgentInfoDao().getByAgentNo(agentNo);
			if (result != null) {
				boolean b = setAgentInfo(agentNo, result);
				if (!b) {
					log.warn("getAgentInfo 失败， agentNo=" + agentNo);
				}
			}
		}
		return result;
	}

	private boolean setAgentInfo(String agentNo, AgentInfo entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getAgentInfoKey(agentNo);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delAgentInfo(String agentNo) {
		String key = RedisKey.getAgentInfoKey(agentNo);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public AgentPlayerInfo getAgentPlayerInfoName(String agentNo, String playerName) {
		AgentPlayerInfo result = null;
		String key = RedisKey.getAgentPlayerInfoNameKey(agentNo, playerName);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, AgentPlayerInfo.class);
		} else {
			result = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
			if (result != null) {
				boolean b = setAgentPlayerInfoName(agentNo, playerName, result);
				if (!b) {
					log.warn("getAgentPlayerInfoName 失败， playerName=" + agentNo + playerName);
				}
			}
		}
		return result;
	}

	private boolean setAgentPlayerInfoName(String agentNo, String playerName, AgentPlayerInfo entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getAgentPlayerInfoNameKey(agentNo, playerName);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delAgentPlayerInfoName(String agentNo, String playerName) {
		AgentPlayerInfo player = getAgentPlayerInfoName(agentNo, playerName);
		String key = RedisKey.getAgentPlayerInfoNameKey(agentNo, playerName);
		List<String> keys = new ArrayList<>();
		keys.add(key);
		if (player != null) {
			Long playerId = player.getPlayerId();
			String urlAuthKey = player.getUrlAuthKey();
			String key1 = RedisKey.getAgentPlayerInfoKey(playerId);
			keys.add(key1);
			String key2 = RedisKey.getAgentPlayerInfoUrlAuthKeyKey(agentNo, playerName, urlAuthKey);
			keys.add(key2);
		}
		redisTemplate.delete(keys);
		return true;
	}

	@Override
	public AgentPlayerInfo getAgentPlayerInfoUrlAuthKey(String agentNo, String playerName, String urlAuthKey) {
		AgentPlayerInfo result = null;
		String key = RedisKey.getAgentPlayerInfoUrlAuthKeyKey(agentNo, playerName, urlAuthKey);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, AgentPlayerInfo.class);
		} else {
			result = getAgentPlayerInfoDao().getByPlayerNameAndUrlAuthKey(agentNo, playerName, urlAuthKey);
			if (result != null) {
				boolean b = setAgentPlayerInfourlAuthKey(agentNo, playerName, urlAuthKey, result);
				if (!b) {
					log.warn("getAgentPlayerInfoUrlAuthKey 失败， playerName=" + agentNo + playerName + ", urlAuthKey="
							+ urlAuthKey);
				}
			}
		}
		return result;
	}

	private boolean setAgentPlayerInfourlAuthKey(String agentNo, String playerName, String urlAuthKey,
			AgentPlayerInfo entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getAgentPlayerInfoUrlAuthKeyKey(agentNo, playerName, urlAuthKey);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delAgentPlayerInfoUrlAuthKey(String agentNo, String playerName, String urlAuthKey) {
		String key = RedisKey.getAgentPlayerInfoUrlAuthKeyKey(agentNo, playerName, urlAuthKey);
		AgentPlayerInfo player = getAgentPlayerInfoUrlAuthKey(agentNo, playerName, urlAuthKey);
		List<String> keys = new ArrayList<>();
		keys.add(key);
		if (player != null) {
			Long playerId = player.getPlayerId();
			String key1 = RedisKey.getAgentPlayerInfoNameKey(agentNo, playerName);
			keys.add(key1);
			String key2 = RedisKey.getAgentPlayerInfoKey(playerId);
			keys.add(key2);
		}
		redisTemplate.delete(keys);

		return true;
	}

	@Override
	public List<GameLotteryOpenCode> getGameOpenCodeList(String lottery) {
		List<GameLotteryOpenCode> result = null;
		String key = RedisKey.getGameOpenCodeKey(lottery);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryOpenCode>>() {
			});
		} else {
			result = getGameLotteryOpenCodeDao().listByLottery(lottery, 100);
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGameOpenCode(lottery, result);
				if (!b) {
					log.warn("getGameOpenCodeList 失败， lottery=" + lottery);
				}
			}
		}
		return result;
	}

	private boolean setGameOpenCode(String lottery, List<GameLotteryOpenCode> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameOpenCodeKey(lottery);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameOpenCodeList(String lottery) {
		String key = RedisKey.getGameOpenCodeKey(lottery);
		redisTemplate.delete(key);
		return true;
	}

	private boolean setGameLotteryType(String gameTypeCode, GameLotteryType entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryTypeKey(gameTypeCode);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public GameLotteryType getGameLotteryType(String gameTypeCode) {
		GameLotteryType result = null;
		String key = RedisKey.getGameLotteryTypeKey(gameTypeCode);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, GameLotteryType.class);
		} else {
			result = getGameLotteryTypeDao().getByGameTypeCode(gameTypeCode);
			if (result != null) {
				boolean b = setGameLotteryType(gameTypeCode, result);
				if (!b) {
					log.warn("getGameLotteryType 失败， gameTypeCode=" + gameTypeCode);
				}
			}
		}
		return result;
	}

	@Override
	public boolean delGameLotteryType(String gameTypeCode) {
		String key = RedisKey.getGameLotteryTypeKey(gameTypeCode);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<GameLotteryType> getGameLotteryTypeList() {
		List<GameLotteryType> result = null;
		String key = RedisKey.getGameLotteryTypeKey();
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryType>>() {
			});
		} else {
			result = getGameLotteryTypeDao().listAll();
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGameLotteryType(result);
				if (!b) {
					log.warn("getGameLotteryType 失败， ");
				}
			}
		}
		return result;
	}

	private boolean setGameLotteryType(List<GameLotteryType> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryTypeKey();
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry));
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameLotteryTypeList() {
		String key = RedisKey.getGameLotteryTypeKey();
		redisTemplate.delete(key);
		return true;
	}

	private boolean setGameLotteryGroup(String gameGroupCode, GameLotteryGroup entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryGroupKey(gameGroupCode);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public GameLotteryGroup getGameLotteryGroup(String gameGroupCode) {
		GameLotteryGroup result;
		String key = RedisKey.getGameLotteryGroupKey(gameGroupCode);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, GameLotteryGroup.class);
		} else {
			result = getGameLotteryGroupDao().getByGameGroupCode(gameGroupCode);
			if (result != null) {
				boolean b = setGameLotteryGroup(gameGroupCode, result);
				if (!b) {
					log.warn("getGameLotteryGroup 失败， gameGroupCode=" + gameGroupCode);
				}
			}
		}
		return result;
	}

	@Override
	public boolean delGameLotteryGroup(String gameGroupCode) {
		String key = RedisKey.getGameLotteryGroupKey(gameGroupCode);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<GameLotteryGroup> getGameLotteryGroupList() {
		List<GameLotteryGroup> result;
		String key = RedisKey.getGameLotteryGroupKey();
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryGroup>>() {
			});
		} else {
			result = getGameLotteryGroupDao().listAll();
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGameLotteryGroup(result);
				if (!b) {
					log.warn("getGameLotteryGroup 失败， ");
				}
			}
		}
		return result;
	}

	private boolean setGameLotteryGroup(List<GameLotteryGroup> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryGroupKey();
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry));
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameLotteryGroupList() {
		String key = RedisKey.getGameLotteryGroupKey();
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<GameLotteryInfo> getGameLotteryInfoList() {
		List<GameLotteryInfo> result = null;
		String key = RedisKey.getGameLotteryInfoKey();
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryInfo>>() {
			});
		} else {
			result = getGameLotteryInfoDao().listAll();
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGameLotteryInfo(result);
				if (!b) {
					log.warn("getGameLotteryInfoList 失败， ");
				}
			}
		}
		return result;
	}

	private boolean setGameLotteryInfo(List<GameLotteryInfo> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryInfoKey();
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameLotteryInfoList() {
		String key = RedisKey.getGameLotteryInfoKey();
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public GameLotteryInfo getGameLotteryInfo(String lottery) {
		GameLotteryInfo result = null;
		String key = RedisKey.getGameLotteryInfoKey(lottery);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, GameLotteryInfo.class);
		} else {
			result = getGameLotteryInfoDao().getByLottery(lottery);
			if (result != null) {
				boolean b = setGameLotteryInfo(lottery, result);
				if (!b) {
					log.warn("getGameLotteryInfoList 失败， lottery=" + lottery);
				}
			}
		}
		return result;
	}

	private boolean setGameLotteryInfo(String lottery, GameLotteryInfo entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryInfoKey(lottery);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameLotteryInfo(String lottery) {
		String key = RedisKey.getGameLotteryInfoKey(lottery);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public boolean delGameLotteryMethodList(String gameTypeCode) {
		String key = RedisKey.getGameLotteryMethodKey(gameTypeCode);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public boolean delGameLotteryMethod(String gameTypeCode, String methodCode) {
		String key = RedisKey.getGameLotteryMethodKey(gameTypeCode);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<GameLotteryMethod> getGameLotteryMethodList(String gameTypeCode) {
		List<GameLotteryMethod> result = null;
		String key = RedisKey.getGameLotteryMethodKey(gameTypeCode);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryMethod>>() {
			});
		} else {
			result = getGameLotteryMethodDao().listByGameTypeCode(gameTypeCode);
			if (result != null) {
				boolean b = setGameLotteryMethod(gameTypeCode, result);
				if (!b) {
					log.warn("getGameLotteryMethodList失败， gameTypeCode=" + gameTypeCode);
				}
			}
		}
		return result;
	}

	@Override
	public GameLotteryMethod getGameLotteryMethod(String gameTypeCode, String methodCode) {
		GameLotteryMethod result = null;
		String key = RedisKey.getGameLotteryMethodKey(gameTypeCode, methodCode);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, GameLotteryMethod.class);
		} else {
			result = getGameLotteryMethodDao().getByGameTypeCodeAndMethodCode(gameTypeCode, methodCode);
			if (result != null) {
				boolean b = setGameLotteryMethod(gameTypeCode, methodCode, result);
				if (!b) {
                    log.warn("getGameLotteryMethod失败， gameTypeCode={}，methodCode={}", gameTypeCode, methodCode);
				}
			}
		}
		return result;
	}

	protected boolean setGameLotteryMethod(String gameTypeCode, List<GameLotteryMethod> entity) {
		boolean result = false;
		if (entity != null) {
			String key = RedisKey.getGameLotteryMethodKey(gameTypeCode);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entity), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	protected boolean setGameLotteryMethod(String gameTypeCode, String methodCode, GameLotteryMethod entity) {
		boolean result = false;
		if (entity != null) {
			String key = RedisKey.getGameLotteryMethodKey(gameTypeCode, methodCode);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entity), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public List<GameLotteryOpenTime> getGameLotteryOpenTimeList(String lottery) {
		List<GameLotteryOpenTime> result = null;
		String key = RedisKey.getGameLotteryOpenTimeKey(lottery);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryOpenTime>>() {
			});
		} else {
			result = getGameLotteryOpenTimeDao().listByLottery(lottery);
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGameLotteryOpenTime(lottery, result);
				if (!b) {
					log.warn("getGameLotteryOpenTimeList 失败， lottery=" + lottery);
				}
			}
		}
		return result;
	}

	private boolean setGameLotteryOpenTime(String lottery, List<GameLotteryOpenTime> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryOpenTimeKey(lottery);
			redisTemplate.opsForValue().set(key, JSONArray.toJSONString(entry));
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameLotteryOpenTimeList(String lottery) {
		String key = RedisKey.getGameLotteryOpenTimeKey(lottery);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<SystemConfig> getSystemConfigList(String group) {
		List<SystemConfig> result = null;
		String key = RedisKey.getSystemConfigKey(group);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<SystemConfig>>() {
			});
		} else {
			result = getSystemConfigDao().listByGroup(group);
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setSystemConfig(group, result);
				if (!b) {
					log.warn("getSystemConfigList 失败， group=" + group);
				}
			}
		}
		return result;
	}

	private boolean setSystemConfig(String group, List<SystemConfig> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getSystemConfigKey(group);
			redisTemplate.opsForValue().set(key, JSONArray.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delSystemConfigList(String group) {
		String key = RedisKey.getSystemConfigKey(group);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public SystemConfig getSystemConfig(String group, String kkey) {
		SystemConfig result = null;
		String key = RedisKey.getSystemConfigKey(group, kkey);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, SystemConfig.class);
		} else {
			result = getSystemConfigDao().getSystemConfigByGroupAndKey(group, kkey);
			if (result != null) {
				boolean b = setSystemConfig(group, kkey, result);
				if (!b) {
					log.warn("getSystemConfig 失败， group=" + group + "，key=" + key);
				}
			}
		}
		return result;
	}

	private boolean setSystemConfig(String group, String kkey, SystemConfig entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getSystemConfigKey(group, kkey);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delSystemConfig(String group, String kkey) {
		String key = RedisKey.getSystemConfigKey(group, kkey);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public List<GameLotteryOpenNotice> getGameLotteryOpenNotice(Long playerId) {
		List<GameLotteryOpenNotice> result = null;
		String key = RedisKey.getGameLotteryOpenNoticeKey(playerId);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s) && !"[]".equals(s)) {
			result = JSONArray.parseObject(s, new TypeReference<List<GameLotteryOpenNotice>>() {
			});
		} else {
			result = getGameLotteryOpenNoticeDao().listAll(playerId);
			if (!CollectionUtils.isEmpty(result)) {
				boolean b = setGameLotteryOpenNotice(playerId, result);
				if (!b) {
					log.warn("getGameLotteryOpenNotice 失败， playerId=" + playerId);
				}
			}
		}
		return result;
	}

	private boolean setGameLotteryOpenNotice(Long playerId, List<GameLotteryOpenNotice> entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getGameLotteryOpenNoticeKey(playerId);
			redisTemplate.opsForValue().set(key, JSONArray.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delGameLotteryOpenNotice(Long playerId) {
		String key = RedisKey.getGameLotteryOpenNoticeKey(playerId);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public boolean setTelegramPushBet(String billno, String message) {
		String key = RedisKey.getTelegramPushBetKey();
		redisTemplate.opsForHash().put(key, billno, message);
		return true;
	}

	@Override
	public boolean setTelegramPushBouns(String billno, String message) {
		String key = RedisKey.getTelegramPushBounsKey();
		redisTemplate.opsForHash().put(key, billno, message);
		return true;
	}

	@Override
	public int setLoginGuestIp(String ip) {
		String key = RedisKey.getLoginGuestIpKey();
		Integer value = (Integer) redisTemplate.opsForHash().get(key, ip);
		if (null == value) {
			value = 1;
		} else if (value < 100) {
			value = Math.toIntExact(redisTemplate.opsForHash().increment(key, ip, 1L));
		}
		return value;
	}

	@Override
	public String getLoginGuestInfoKey(String ip, String loginKey) {
		String key = RedisKey.getLoginGuestInfoKey();
		Object object = redisTemplate.opsForHash().get(key, ip + "_" + loginKey);
		if (null != object) {
			return String.valueOf(object);
		}
		return null;
	}

	@Override
	public boolean setLoginGuestInfoKey(String ip, String loginKey, String playerName) {
		String key = RedisKey.getLoginGuestInfoKey();
		redisTemplate.opsForHash().put(key, ip + "_" + loginKey, playerName);
		return true;
	}

	@Override
	public boolean cleanGuest() {
		String ipKey = RedisKey.getLoginGuestIpKey();
		String infoKey = RedisKey.getLoginGuestInfoKey();
		redisTemplate.delete(Arrays.asList(ipKey, infoKey));
		return true;
	}

	@Override
	public boolean deleteKey(String key) {
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public PaymentBank getPaymentBank(Long id) {
		PaymentBank result = null;
		String key = RedisKey.getPaymentBankKey(id);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, PaymentBank.class);
		} else {
			result = getPaymentBankDao().getById(id);
			if (result != null) {
				boolean b = setPaymentBank(id, result);
				if (!b) {
					log.warn("getPaymentBank 失败， id=" + id);
				}
			}
		}
		return result;
	}

	private boolean setPaymentBank(Long id, PaymentBank entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getPaymentBankKey(id);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public PaymentThird getPaymentThird(Long id) {
		PaymentThird result = null;
		String key = RedisKey.getPaymentThirdKey(id);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, PaymentThird.class);
		} else {
			result = getPaymentThirdDao().getById(id);
			if (result != null) {
				boolean b = setPaymentThird(id, result);
				if (!b) {
					log.warn("getPaymentThird 失败， id=" + id);
				}
			}
		}
		return result;
	}

	private boolean setPaymentThird(Long id, PaymentThird entry) {
		boolean result = false;
		if (entry != null) {
			String key = RedisKey.getPaymentThirdKey(id);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public Map<String, String> getGameLotteryOpenCodeLastedIssue() {
		Map<String, String> result = new HashMap<String, String>();
		String key = RedisKey.getGameLotteryOpenCodeLastedIssueKey();
		Map<Object, Object> s = redisTemplate.opsForHash().entries(key);
		if (null != s && s.size() > 0) {
			for (Map.Entry<Object, Object> entry : s.entrySet()) {
				result.put(entry.getKey().toString(), entry.getValue().toString());
			}
		}
		return result;
	}

	@Override
	public String getGameLotteryOpenCodeLastedIssue(String lottery) {
		String result = "";
		String key = RedisKey.getGameLotteryOpenCodeLastedIssueKey();
		String s = (String) redisTemplate.opsForHash().get(key, lottery);
		if (!StringUtils.isEmpty(s)) {
			result = s;
		} else {
			List<GameLotteryOpenCode> list = getGameLotteryOpenCodeDao().listByLottery(lottery, 1);
			if (!CollectionUtils.isEmpty(list)) {
				GameLotteryOpenCode entity = list.get(0);
				if (null != entity) {
					String issue = entity.getGameIssue();
					boolean bool = setGameLotteryOpenCodeLastedIssue(lottery, issue);
					if (bool) {
						result = issue;
					} else {
						log.warn("getGameLotteryOpenCodeLastedIssue 失败， lottery=" + lottery);
					}
				}
			}
		}
		return result;
	}

	@Override
	public boolean setGameLotteryOpenCodeLastedIssue(String lottery, String issue) {
		if (StringUtils.isEmpty(lottery) || StringUtils.isEmpty(issue)) {
			return false;
		}
		String key = RedisKey.getGameLotteryOpenCodeLastedIssueKey();
		redisTemplate.opsForHash().put(key, lottery, issue);
		return true;
	}

	@Override
	public boolean delGameMonopolyConfigInfo(String agentNo, String lottery) {
		String key = RedisKey.getGameMonopolyConfigInfoKey(agentNo, lottery);
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public GameMonopolyConfigInfo getGameMonopolyConfigInfo(String agentNo, String lottery) {
		GameMonopolyConfigInfo result = null;
		String key = RedisKey.getGameMonopolyConfigInfoKey(agentNo, lottery);
		String s = redisTemplate.opsForValue().get(key);
		if (!StringUtils.isEmpty(s)) {
			return JSONObject.parseObject(s, GameMonopolyConfigInfo.class);
		} else {
			result = getGameMonopolyConfigInfoDao().getByLottery(agentNo, lottery);
			if (result != null) {
				boolean b = setGameMonopolyConfigInfo(lottery, result);
				if (!b) {
					log.warn("getGameMonopolyConfigInfo 失败， lottery=" + lottery);
				}
			}
		}
		return result;
	}

	private boolean setGameMonopolyConfigInfo(String lottery, GameMonopolyConfigInfo entry) {
		boolean result = false;
		if (entry != null) {
			String agentNo = entry.getAgentNo();
			String key = RedisKey.getGameMonopolyConfigInfoKey(agentNo, lottery);
			redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
			result = true;
		}
		return result;
	}

	@Override
	public boolean delAgentFlyingThrowList() {
		String key = RedisKey.getAgentFlyingThrowKey();
		redisTemplate.delete(key);
		return true;
	}

	@Override
	public boolean delAgentFlyingThrow(String agentNo) {
		String key = RedisKey.getAgentFlyingThrowKey(agentNo);
		redisTemplate.delete(key);
		return true;
	}
	
	public boolean setRebateLevel(String agentNo, PlatformConfig entry) {
		boolean result = false;
        if (entry != null) {
            String key = RedisKey.getRebateLevelKey(agentNo);
            redisTemplate.opsForValue().set(key, JSONObject.toJSONString(entry), 1, TimeUnit.DAYS);
            result = true;
        }
        return result;
	}

}
