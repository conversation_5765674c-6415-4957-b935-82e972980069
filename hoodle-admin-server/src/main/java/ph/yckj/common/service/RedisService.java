package ph.yckj.common.service;

import java.util.List;
import java.util.Map;

import sy.hoodle.base.common.entity.*;

public interface RedisService {

	boolean delSystemCacheVersion(String key);

	AgentPlayerInfo getAgentPlayerInfo(Long playerId);

	boolean delAgentPlayerInfo(Long playerId);

	AgentPlayerGameOrder getGameRecordDetail(Long orderId);

	boolean delGameRecordDetail(Long orderId);

	List<GiftInfo> getGiftInfoList();

	boolean delGiftInfoList();

	GiftInfo getGiftInfo(String giftCode);

	boolean delGiftInfo(String giftCode);

	AgentInfo getAgentInfo(String agentNo);

	boolean delAgentInfo(String agentNo);

	AgentPlayerInfo getAgentPlayerInfoName(String agentNo, String playerName);

	boolean delAgentPlayerInfoName(String agentNo, String playerName);

	boolean delAgentPlayerInfoUrlAuthKey(String agentNo, String playerName, String urlAuthKey);

	AgentPlayerInfo getAgentPlayerInfoUrlAuthKey(String agentNo, String playerName, String urlAuthKey);

	List<GameLotteryOpenCode> getGameOpenCodeList(String lottery);

	boolean delGameOpenCodeList(String lottery);

	GameLotteryType getGameLotteryType(String gameTypeCode);

	boolean delGameLotteryType(String gameTypeCode);

	List<GameLotteryType> getGameLotteryTypeList();

	boolean delGameLotteryTypeList();

	GameLotteryGroup getGameLotteryGroup(String gameTypeCode);

	boolean delGameLotteryGroup(String gameGroupCode);

	List<GameLotteryGroup> getGameLotteryGroupList();

	boolean delGameLotteryGroupList();

	List<GameLotteryInfo> getGameLotteryInfoList();

	boolean delGameLotteryInfoList();

	GameLotteryInfo getGameLotteryInfo(String lottery);

	boolean delGameLotteryInfo(String lottery);

	boolean delGameLotteryMethodList(String gameTypeCode);

	boolean delGameLotteryMethod(String gameTypeCode, String methodCode);

	List<GameLotteryMethod> getGameLotteryMethodList(String gameTypeCode);

	GameLotteryMethod getGameLotteryMethod(String gameTypeCode, String methodCode);

	List<GameLotteryOpenTime> getGameLotteryOpenTimeList(String lottery);

	boolean delGameLotteryOpenTimeList(String lottery);

	List<SystemConfig> getSystemConfigList(String group);

	boolean delSystemConfigList(String group);

	SystemConfig getSystemConfig(String group, String key);

	boolean delSystemConfig(String group, String kkey);

	List<GameLotteryOpenNotice> getGameLotteryOpenNotice(Long playerId);

	boolean delGameLotteryOpenNotice(Long playerId);

	boolean setTelegramPushBet(String billno, String message);

	boolean setTelegramPushBouns(String billno, String message);

	int setLoginGuestIp(String ip);

	String getLoginGuestInfoKey(String ip, String loginKey);

	boolean setLoginGuestInfoKey(String ip, String loginKey, String playerName);

	boolean cleanGuest();

	boolean deleteKey(String key);

	PaymentBank getPaymentBank(Long id);

	PaymentThird getPaymentThird(Long id);

	/**
	 * 获取全部彩种开奖号码最新一期期号
	 *
	 * @return
	 */
	Map<String, String> getGameLotteryOpenCodeLastedIssue();

	/**
	 * 获取彩种开奖号码最新一期期号
	 *
	 * @param lottery
	 * @return
	 */
	String getGameLotteryOpenCodeLastedIssue(String lottery);

	/**
	 * 更新彩种开奖号码最新一期期号
	 *
	 * @param lottery
	 * @param issue
	 * @return
	 */
	boolean setGameLotteryOpenCodeLastedIssue(String lottery, String issue);

	boolean delGameMonopolyConfigInfo(String agentNo, String lottery);

	GameMonopolyConfigInfo getGameMonopolyConfigInfo(String agentNo, String lottery);

	boolean delAgentFlyingThrowList();

	boolean delAgentFlyingThrow(String agentNo);
	
	boolean setRebateLevel(String agentNo, PlatformConfig entry);
}
