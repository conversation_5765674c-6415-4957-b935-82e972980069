package ph.yckj.common.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import myutil.*;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sy.hoodle.base.common.dto.PlayerAmount;
import sy.hoodle.base.common.dto.activity.*;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Component
public class ActivityUtils extends AbstractDao {

    @Autowired
    private PlatformAgentPlayerCommonService platformAgentPlayerCommonService;

    /**
     * 获取彩票团队量（充值量、投注量、亏损量、团队活跃数）
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public PlayerAmount getLotteryTeamAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate,
            Date eDate, boolean includeSelf) {
        PlayerAmount playerAmount = new PlayerAmount();

        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf);
        if(teamTotalInfo != null) {
            playerAmount.setRechargeAmount(BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0])));
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[2]));
            BigDecimal bonusAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[3]));
            BigDecimal rebateAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[4]));
            BigDecimal activityAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[5]));
            BigDecimal salaryAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[6]));
            BigDecimal divsAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[7]));
            // 亏损 = 派奖 + 返点 + 活动 + 工资 + 分红 - 投注
            BigDecimal lossAmount = bonusAmount.add(rebateAmount).add(activityAmount).add(salaryAmount).add(divsAmount)
                    .subtract(betAmount);

            playerAmount.setBetAmount(betAmount);
            playerAmount.setBonusAmount(bonusAmount);
            playerAmount.setRebateAmount(rebateAmount);
            playerAmount.setActivityAmount(activityAmount);
            playerAmount.setSalaryAmount(salaryAmount);
            playerAmount.setDivsAmount(divsAmount);
            playerAmount.setLossAmount(lossAmount);
        }
        // 活跃用户条件
        BigDecimal minRechargeAmount = BigDecimal.ONE;
        BigDecimal minBetAmount = BigDecimal.ONE;
        Integer standardDays = 1;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
                // 日
                AcitvitySalaryDailyConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryDailyConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
            }else if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
                // 周/月
                AcitvitySalaryPeriodConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryPeriodConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
                standardDays = conditions.getStandardDays();
            }
        }

        int activeUser = getPlatformAgentPlayerReportDao().getTeamActiveUser(player.getAgentNo(), player.getPlayerId(),
                sDate, eDate, minRechargeAmount, minBetAmount, standardDays, includeSelf);
        playerAmount.setActiveUser(activeUser);
        return playerAmount;
    }

    /**
     * 获取三方团队量（充值量、投注量、亏损量、团队活跃数）
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public PlayerAmount getThirdTeamAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate,
            Date eDate, boolean includeSelf) {
        PlayerAmount playerAmount = new PlayerAmount();

        // 充值得从彩票报表获取
        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf);
        if(teamTotalInfo != null) {
            playerAmount.setRechargeAmount(BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0])));
        }

        // 从三方报表获取投注等
        teamTotalInfo = getGameSimulationReportDao().getTeamTotalInfo(player.getPlayerId(), null, sDate, eDate,
                includeSelf);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));

            playerAmount.setBetAmount(betAmount);
            playerAmount.setLossAmount(lossAmount);
        }

        // 活跃用户条件
        BigDecimal minRechargeAmount = BigDecimal.ONE;
        BigDecimal minBetAmount = BigDecimal.ONE;
        Integer standardDays = 1;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
                // 日
                AcitvitySalaryDailyConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryDailyConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
            }else if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
                // 周/月
                AcitvitySalaryPeriodConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryPeriodConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
                standardDays = conditions.getStandardDays();
            }
        }

        int activeUser = getGameSimulationReportDao().getTeamActiveUser(player.getPlayerId(), null, sDate, eDate,
                minRechargeAmount, minBetAmount, standardDays, includeSelf);
        playerAmount.setActiveUser(activeUser);
        return playerAmount;
    }

    /**
     * 获取彩票自己的量（充值量、投注量、亏损量、团队活跃数）
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public PlayerAmount getLotterySelfAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate,
            Date eDate) {
        PlayerAmount playerAmount = new PlayerAmount();

        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getSelfTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate);
        if(teamTotalInfo != null) {
            playerAmount.setRechargeAmount(BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0])));
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[2]));
            BigDecimal bonusAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[3]));
            BigDecimal rebateAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[4]));
            BigDecimal activityAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[5]));
            BigDecimal salaryAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[6]));
            BigDecimal divsAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[7]));
            // 亏损 = 派奖 + 返点 + 活动 + 工资 + 分红 - 投注
            BigDecimal lossAmount = bonusAmount.add(rebateAmount).add(activityAmount).add(salaryAmount).add(divsAmount)
                    .subtract(betAmount);

            playerAmount.setBetAmount(betAmount);
            playerAmount.setBonusAmount(bonusAmount);
            playerAmount.setRebateAmount(rebateAmount);
            playerAmount.setActivityAmount(activityAmount);
            playerAmount.setSalaryAmount(salaryAmount);
            playerAmount.setDivsAmount(divsAmount);
            playerAmount.setLossAmount(lossAmount);
        }
        // 活跃用户条件
        BigDecimal minRechargeAmount = BigDecimal.ONE;
        BigDecimal minBetAmount = BigDecimal.ONE;
        Integer standardDays = 1;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
                // 日
                AcitvitySalaryDailyConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryDailyConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
            }else if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
                // 周/月
                AcitvitySalaryPeriodConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryPeriodConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
                standardDays = conditions.getStandardDays();
            }
        }

        int activeDays = getPlatformAgentPlayerReportDao().getSelfActiveDays(player.getAgentNo(), player.getPlayerId(),
                sDate, eDate, minRechargeAmount, minBetAmount);
        if(activeDays >= standardDays) {
            playerAmount.setActiveUser(1);
        }
        return playerAmount;
    }

    /**
     * 获取三方自己的量（充值量、投注量、亏损量、团队活跃数）
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public PlayerAmount getThirdSelfAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate,
            Date eDate) {
        PlayerAmount playerAmount = new PlayerAmount();

        // 充值得从彩票报表获取
        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getSelfTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate);
        if(teamTotalInfo != null) {
            playerAmount.setRechargeAmount(BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0])));
        }

        // 从三方报表获取投注等
        teamTotalInfo = getGameSimulationReportDao().getSelfTotalInfo(player.getPlayerId(), null, sDate, eDate);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));

            playerAmount.setBetAmount(betAmount);
            playerAmount.setLossAmount(lossAmount);
        }

        // 活跃用户条件
        BigDecimal minRechargeAmount = BigDecimal.ONE;
        BigDecimal minBetAmount = BigDecimal.ONE;
        Integer standardDays = 1;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
                // 日
                AcitvitySalaryDailyConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryDailyConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
            }else if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
                // 周/月
                AcitvitySalaryPeriodConditions conditions = JSON.parseObject(config.getActivityRules(),
                        AcitvitySalaryPeriodConditions.class);
                minRechargeAmount = conditions.getActiveMinRecharge();
                minBetAmount = conditions.getActiveMinConsume();
                standardDays = conditions.getStandardDays();
            }
        }

        int activeDays = getGameSimulationReportDao().getSelfActiveDays(player.getPlayerId(), null, sDate, eDate,
                minRechargeAmount, minBetAmount);
        if(activeDays >= standardDays) {
            playerAmount.setActiveUser(1);
        }
        return playerAmount;
    }

    /**
     * 获取彩票团队量（投注量、亏损量）
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public PlayerAmount getLotteryTeamBetAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate,
            Date eDate, boolean includeSelf) {
        PlayerAmount playerAmount = new PlayerAmount();

        Object[] teamTotalInfo = getAgentPlayerGameOrderDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf, false);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));

            playerAmount.setBetAmount(betAmount);
            playerAmount.setLossAmount(lossAmount);
        }
        return playerAmount;
    }

    /**
     * 获取彩票自己的量（投注量、亏损量）
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public PlayerAmount getLotterySelfBetAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate,
            Date eDate) {
        PlayerAmount playerAmount = new PlayerAmount();

        Object[] teamTotalInfo = getAgentPlayerGameOrderDao().getSelfTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));

            playerAmount.setBetAmount(betAmount);
            playerAmount.setLossAmount(lossAmount);
        }
        return playerAmount;
    }

    /**
     * 获取彩票团队量亏损量
     *
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public BigDecimal getLotteryTeamLossAmount(AgentPlayerInfo player, Date sDate, Date eDate, boolean includeSelf) {
        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[2]));
            BigDecimal bonusAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[3]));
            BigDecimal rebateAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[4]));
            BigDecimal activityAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[5]));
            BigDecimal salaryAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[6]));
            BigDecimal divsAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[7]));
            // 亏损 = 派奖 + 返点 + 活动 + 工资 + 分红 - 投注
            return bonusAmount.add(rebateAmount).add(activityAmount).add(salaryAmount).add(divsAmount).subtract(
                    betAmount);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取三方团队量亏损量
     *
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public BigDecimal getThirdTeamLossAmount(AgentPlayerInfo player, Date sDate, Date eDate, boolean includeSelf) {
        // 从三方报表获取投注等
        Object[] teamTotalInfo = getGameSimulationReportDao().getTeamTotalInfo(player.getPlayerId(), null, sDate, eDate,
                includeSelf);
        if(teamTotalInfo != null) {
            return BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取彩票自己的亏损量
     *
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public BigDecimal getLotterySelfLossAmount(AgentPlayerInfo player, Date sDate, Date eDate) {
        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getSelfTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[2]));
            BigDecimal bonusAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[3]));
            BigDecimal rebateAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[4]));
            BigDecimal activityAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[5]));
            BigDecimal salaryAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[6]));
            BigDecimal divsAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[7]));
            // 亏损 = 派奖 + 返点 + 活动 + 工资 + 分红 - 投注
            return bonusAmount.add(rebateAmount).add(activityAmount).add(salaryAmount).add(divsAmount).subtract(
                    betAmount);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取三方自己的亏损量
     *
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public BigDecimal getThirdSelfLossAmount(AgentPlayerInfo player, Date sDate, Date eDate) {
        // 从三方报表获取投注等
        Object[] teamTotalInfo = getGameSimulationReportDao().getSelfTotalInfo(player.getPlayerId(), null, sDate,
                eDate);
        if(teamTotalInfo != null) {
            return BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获得最后的登录ip
     *
     * @param playerId
     * @return
     */
    public String getAccountLoginIp(long playerId) {
        PlayerLoginLog loginLog = getPlayerLoginLogDao().getCurrLogin(playerId);
        if(loginLog != null) {
            return loginLog.getIp();
        }
        return "";
    }

    /**
     * 具有计算周期的活动周期区间段开始时间和结束时间计算
     *
     * @param config
     * @return
     */
    public Map<String, Date> getStartAndEndDate(PlatformActivityConfig config) {
        Map<String, Date> resultMap = new HashMap<>();
        Date sDate; // 开始日期
        Date eDate; // 结束日期
        int cycleType = config.getCycleScope();

        if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
            // 上周期：昨天的量
            sDate = new Moment().yesterday().startOf("day").toDate(); // 昨天
            eDate = new Moment().startOf("day").toDate(); // 今天
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_WEEK) {
            // 上周期：上周的量
            sDate = new Moment().startOf("week").subtract(1, "weeks").toDate();
            eDate = new Moment().startOf("week").toDate();
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH) {
            // 上周期：半月的量
            // 获取当月16日0点时间
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.DAY_OF_MONTH, 16);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            // 当前时间
            Date now = new Date();
            if(now.before(cal.getTime())) {
                // 当前时间在16日前，取上月下半月
                cal.add(Calendar.MONTH, -1); // 月份减1，就是上月16日0点时间
                sDate = cal.getTime();
                eDate = new Moment().startOf("month").toDate();
            }else {
                // 当前时间在16日后，取当月上半月
                sDate = new Moment().startOf("month").toDate();
                eDate = cal.getTime();
            }
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
            // 上周期：上月的量
            sDate = new Moment().startOf("month").subtract(1, "months").toDate();
            eDate = new Moment().startOf("month").toDate();
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_10) {
            // 当前整分钟时间
            Moment moment = new Moment().startOf("minute");
            int minute = moment.get("minute");
            if(minute % 10 != 0) {
                // 不是整10分钟，减去多余的分钟数
                minute = minute % 10;
                moment = moment.subtract(minute, "m");
            }
            eDate = moment.toDate();
            // 开始时间要再减去10分钟
            sDate = moment.subtract(10, "m").toDate();
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_20) {
            // 当前整分钟时间
            Moment moment = new Moment().startOf("minute");
            int minute = moment.get("minute");
            if(minute % 20 != 0) {
                // 不是整20分钟，减去多余的分钟数
                minute = minute % 20;
                moment = moment.subtract(minute, "m");
            }
            eDate = moment.toDate();
            // 开始时间要再减去20分钟
            sDate = moment.subtract(20, "m").toDate();
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_30) {
            // 当前整分钟时间
            Moment moment = new Moment().startOf("minute");
            int minute = moment.get("minute");
            if(minute % 30 != 0) {
                // 不是整30分钟，减去多余的分钟数
                minute = minute % 30;
                moment = moment.subtract(minute, "m");
            }
            eDate = moment.toDate();
            // 开始时间要再减去30分钟
            sDate = moment.subtract(30, "m").toDate();
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_HOUR_1) {
            // 当前整小时时间
            Moment moment = new Moment().startOf("hour");
            eDate = moment.toDate();
            // 开始时间减去1小时
            sDate = moment.subtract(1, "h").toDate();
        }else {
            throw new IllegalArgumentException();
        }

        resultMap.put("sDate", sDate);
        resultMap.put("eDate", eDate);
        return resultMap;
    }

    /**
     * 具有累计的活动，累计区间段开始时间
     *
     * @param config 活动配置
     * @param grandTotalType 累计类型
     * @param sDate 上周期开始时间，也就是累计计算的结束时间
     * @return
     */
    public Date getGrandTotalStartDate(PlatformActivityConfig config, int grandTotalType, Date sDate) {
        if(grandTotalType == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO ||
                grandTotalType == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
            // 不累计时，直接返回null
            // 统计上周期以前的亏损金额总和，开始时间就是null
            return null;
        }
        // 开始时间
        Calendar calStart = Calendar.getInstance();
        // 先设置为结束时间（即上周期开始时间），再计算开始时间
        calStart.setTime(sDate);
        int cycleType = config.getCycleScope();
        if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
            // 日，日期减N天数
            calStart.add(Calendar.DATE, -grandTotalType);
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_WEEK) {
            // 周，日期减N周天数
            calStart.add(Calendar.DATE, -grandTotalType * 7);
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH) {
            // 半月
            if(grandTotalType == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_1) {
                // 累计1周期，就是半个月
                int day = calStart.get(Calendar.DAY_OF_MONTH);
                if(day == 1) {
                    // 结束时间为当月1日时，开始时间就是上月16日
                    calStart.add(Calendar.MONTH, -1);
                    calStart.set(Calendar.DAY_OF_MONTH, 16);
                }else if(day == 16) {
                    // 结束时间为当月16日时，开始时间就是当月1日
                    calStart.set(Calendar.DAY_OF_MONTH, 1);
                }else {
                    throw new IllegalArgumentException("半月日期错误");
                }
            }else if(grandTotalType == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_2) {
                // 累计2周期，就是1个月
                calStart.add(Calendar.MONTH, -1);
            }else {
                throw new IllegalArgumentException("累计类型配置错误");
            }
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
            // 月，日期减N月数
            calStart.add(Calendar.MONTH, -grandTotalType);
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_10) {
            // 10分钟，日期减N个10分钟
            calStart.add(Calendar.MINUTE, -grandTotalType * 10);
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_20) {
            // 20分钟，日期减N个20分钟
            calStart.add(Calendar.MINUTE, -grandTotalType * 20);
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_30) {
            // 30分钟，日期减N个30分钟
            calStart.add(Calendar.MINUTE, -grandTotalType * 30);
        }else if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_HOUR_1) {
            // 1小时，日期减N小时
            calStart.add(Calendar.HOUR_OF_DAY, -grandTotalType);
        }else {
            throw new IllegalArgumentException();
        }

        return calStart.getTime();
    }

    /**
     * 验证是否在活动时间范围内
     *
     * @param date
     * @param config
     * @return
     */
    public boolean verifyBetweenActivityDay(Date date, PlatformActivityConfig config) {
        if(date == null) {
            return true;
        }
        String dateString = new Moment().fromDate(date).toSimpleDate();
        if(config.getStartTime() != null) {
            String startTime = new Moment().fromDate(config.getStartTime()).toSimpleDate();
            if(dateString.compareTo(startTime) < 0) {
                throw new IllegalArgumentException("120-06");
            }
        }
        if(config.getFinishTime() != null) {
            String endTime = new Moment().fromDate(config.getFinishTime()).toSimpleDate();
            if(dateString.compareTo(endTime) > 0) {
                throw new IllegalArgumentException("120-06");
            }
        }
        return true;
    }

    /**
     * 验证活动rules外围配置
     *
     * @param player
     * @param config
     * @param date
     * @return
     */
    public boolean verifyActivityConfig(AgentPlayerInfo player, PlatformActivityConfig config, Date date) {
        if(config == null) {
            throw new IllegalArgumentException("120-01");
        }
        if(player == null) {
            throw new IllegalArgumentException("120-12");
        }
        if(config.getActivityStatus() != PlatformActivityConfig.ACTIVITY_STATUS_NORMAL) {
            throw new IllegalArgumentException("120-02");
        }
        verifyBetweenActivityDay(date, config);

        // 验证用户类型
        String playerType = config.getPlayerType();
        if(!playerType.contains(player.getAccountType())) {
            // ERR:不是活动参与人
            throw new IllegalArgumentException("120-12");
        }
        return true;
    }

    /**
     * 获得彩票和三方工资/分红结果，返回团队充值、消费、亏损、活跃人数
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public PlayerAmount getPlayerAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate, Date eDate) {
        // 计算对象
        int targetType = config.getTargetType();
        // 计算周期
        int cycleType = config.getCycleScope();
        if(cycleType != PlatformActivityConfig.CYCLE_SCOPE_DAY &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_WEEK &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_MONTH &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_MIN_10 &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_MIN_20 &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_MIN_30 &&
                cycleType != PlatformActivityConfig.CYCLE_SCOPE_HOUR_1) {
            log.info("doDraw活动==={}==={}===计算周期错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }
        boolean includeSelf; // 团队包含本人
        boolean onlySelf = false; // 仅本人
        if(targetType == PlatformActivityConfig.TARGET_TYPE_TEAM_WITH_SELF) {
            includeSelf = true;
        }else if(targetType == PlatformActivityConfig.TARGET_TYPE_TEAM_WITHOUT_SELF) {
            includeSelf = false;
        }else if(targetType == PlatformActivityConfig.TARGET_TYPE_SELF) {
            includeSelf = true;
            onlySelf = true;
        }else {
            // 计算对象错误
            log.info("doDraw活动==={}==={}===计算对象错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }

        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND) {
            // 获得彩票周期量
            if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_10 ||
                            cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_20 ||
                            cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_30 ||
                            cycleType == PlatformActivityConfig.CYCLE_SCOPE_HOUR_1) {
                // 计算周期为10分钟/20分钟/30分钟/1小时
                if(onlySelf) {
                    return getLotterySelfBetAmount(config, player, sDate, eDate);
                }else {
                    return getLotteryTeamBetAmount(config, player, sDate, eDate, includeSelf);
                }
            }else {
                // 计算周期为日/周/半月/月
                if(onlySelf) {
                    return getLotterySelfAmount(config, player, sDate, eDate);
                }else {
                    return getLotteryTeamAmount(config, player, sDate, eDate, includeSelf);
                }
            }
        }else if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            // 获得三方周期量
            if(onlySelf) {
                return getThirdSelfAmount(config, player, sDate, eDate);
            }else {
                return getThirdTeamAmount(config, player, sDate, eDate, includeSelf);
            }
        }else {
            log.info("doDraw活动==={}==={}===契约类型错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }
    }

    /**
     * 获取累计亏损量
     *
     * @param config 活动配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public BigDecimal getLossAmount(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate, Date eDate) {
        // 计算对象
        int targetType = config.getTargetType();
        // 计算周期
        int cycleType = config.getCycleScope();
        boolean includeSelf; // 团队包含本人
        boolean onlySelf = false; // 仅本人
        if(targetType == PlatformActivityConfig.TARGET_TYPE_TEAM_WITH_SELF) {
            includeSelf = true;
        }else if(targetType == PlatformActivityConfig.TARGET_TYPE_TEAM_WITHOUT_SELF) {
            includeSelf = false;
        }else if(targetType == PlatformActivityConfig.TARGET_TYPE_SELF) {
            includeSelf = true;
            onlySelf = true;
        }else {
            // 计算对象错误
            log.info("doDraw活动==={}==={}===计算对象错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }

        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND) {
            // 获得彩票周期量
            if(cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_10 ||
                    cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_20 ||
                    cycleType == PlatformActivityConfig.CYCLE_SCOPE_MIN_30 ||
                    cycleType == PlatformActivityConfig.CYCLE_SCOPE_HOUR_1) {
                // 计算周期为10分钟/20分钟/30分钟/1小时
                if(onlySelf) {
                    return getLotterySelfBetAmount(config, player, sDate, eDate).getLossAmount();
                }else {
                    return getLotteryTeamBetAmount(config, player, sDate, eDate, includeSelf).getLossAmount();
                }
            }else {
                // 计算周期为日/周/半月/月
                if(onlySelf) {
                    return getLotterySelfLossAmount(player, sDate, eDate);
                }else {
                    return getLotteryTeamLossAmount(player, sDate, eDate, includeSelf);
                }
            }
        }else if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            // 获得三方周期量
            if(onlySelf) {
                return getThirdSelfLossAmount(player, sDate, eDate);
            }else {
                return getThirdTeamLossAmount(player, sDate, eDate, includeSelf);
            }
        }else {
            log.info("doDraw活动==={}==={}===契约类型错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }
    }

    /**
     * 获得彩票和三方工资/分红活动最优的奖励规则。计算周期：日
     *
     * @param config
     * @param playerAmount
     * @return
     */
    public AcitvitySalaryDailyRules getSalaryDailyPerfectRules(PlatformActivityConfig config,
            PlayerAmount playerAmount) {
        // 获取奖励规则
        AcitvitySalaryDailyConditions conditions = JSON.parseObject(config.getActivityRules(),
                AcitvitySalaryDailyConditions.class);
        if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
            throw new IllegalArgumentException("120-01");
        }
        // 奖励规则倒序排序
        reorderDaily(conditions.getRules());

        // 奖励规则，充值等单位是万，所以需要乘以万
        BigDecimal wan = BigDecimal.valueOf(10000);

        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        double betAmount = playerAmount.getBetAmount().doubleValue();
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        int activeUser = playerAmount.getActiveUser();

        for(AcitvitySalaryDailyRules rules : conditions.getRules()) {
            if(rules.getDailyRecharge().doubleValue() > 0) {
                if(rechargeAmount < rules.getDailyRecharge().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getDailyConsume().doubleValue() > 0) {
                if(betAmount < rules.getDailyConsume().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getDailyLoss().doubleValue() > 0) {
                if(lossAmount > 0 || Math.abs(lossAmount) < rules.getDailyLoss().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getActiveUser() > 0) {
                if(activeUser < rules.getActiveUser()) {
                    continue;
                }
            }
            return rules;
        }
        return null;
    }

    /**
     * 获得彩票和三方工资/分红活动最优的奖励规则。计算周期：周/半月/月/10分钟/20分钟/30分钟/1小时
     *
     * @param config
     * @param playerAmount
     * @return
     */
    public AcitvitySalaryPeriodRules getSalaryPeriodPerfectRules(PlatformActivityConfig config,
            PlayerAmount playerAmount) {
        // 获取奖励规则
        AcitvitySalaryPeriodConditions conditions = JSON.parseObject(config.getActivityRules(),
                AcitvitySalaryPeriodConditions.class);
        if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
            throw new IllegalArgumentException("120-01");
        }
        // 奖励规则倒序排序
        reorderPeriod(conditions.getRules());

        // 奖励规则，充值等单位是万，所以需要乘以万
        BigDecimal wan = BigDecimal.valueOf(10000);

        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        double betAmount = playerAmount.getBetAmount().doubleValue();
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        int activeUser = playerAmount.getActiveUser();

        for(AcitvitySalaryPeriodRules rules : conditions.getRules()) {
            if(rules.getTotalRecharge().doubleValue() > 0) {
                if(rechargeAmount < rules.getTotalRecharge().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getTotalConsume().doubleValue() > 0) {
                if(betAmount < rules.getTotalConsume().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getTotalLoss().doubleValue() > 0) {
                if(lossAmount > 0 || Math.abs(lossAmount) < rules.getTotalLoss().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getActiveUser() > 0) {
                if(activeUser < rules.getActiveUser()) {
                    continue;
                }
            }
            return rules;
        }
        return null;
    }

    /**
     * 将工资/分红规则按发放比例倒序排序
     *
     * @param rules 奖励规则
     */
    private void reorderDaily(List<AcitvitySalaryDailyRules> rules) {
        for(int i = 0; i < rules.size(); i++) {
            for(int j = i + 1; j < rules.size(); j++) {
                if(rules.get(j).getScalePoint() > rules.get(i).getScalePoint()) {
                    AcitvitySalaryDailyRules tmp = rules.get(i);
                    rules.set(i, rules.get(j));
                    rules.set(j, tmp);
                }
            }
        }
    }

    /**
     * 将工资/分红规则按发放比例倒序排序
     *
     * @param rules 奖励规则
     */
    private void reorderPeriod(List<AcitvitySalaryPeriodRules> rules) {
        for(int i = 0; i < rules.size(); i++) {
            for(int j = i + 1; j < rules.size(); j++) {
                if(rules.get(j).getScalePoint() > rules.get(i).getScalePoint()) {
                    AcitvitySalaryPeriodRules tmp = rules.get(i);
                    rules.set(i, rules.get(j));
                    rules.set(j, tmp);
                }
            }
        }
    }

    /**
     * 获得彩票和三方工资/分红活动奖励记录。计算周期：日
     *
     * @param rules
     * @param player
     * @param config
     * @param playerAmount
     * @return
     */
    public PlatformActivityRewardRecord getSalaryDailyRewardRecord(AcitvitySalaryDailyRules rules,
            AgentPlayerInfo player, PlatformActivityConfig config, PlayerAmount playerAmount, Date sDate, Date eDate) {
        // 奖金
        double money;
        // 充值
        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        // 投注
        double betAmount = playerAmount.getBetAmount().doubleValue();
        // 亏损
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        // 活跃用户
        int activeUser = playerAmount.getActiveUser();

        String scalePointTitle;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = rules.getScalePoint();
                scalePointTitle = "工资金额";
            }else {
                // 工资金额 = 投注 * 比例
                money = MathUtils.divide(MathUtils.multiply(betAmount, rules.getScalePoint()), 100, 5);
                scalePointTitle = "工资比例";
            }
        }else if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = rules.getScalePoint();
                scalePointTitle = "分红金额";
            }else {
                // 分红金额 = 亏损 * 比例
                money = MathUtils.divide(MathUtils.multiply(Math.abs(lossAmount), rules.getScalePoint()), 100, 5);
                scalePointTitle = "分红比例";
            }
            if(lossAmount >= 0) {
                // 用户未亏损，奖励改为0
                money = 0D;
            }
        }else {
            log.info("doDraw活动==={}==={}===活动类型错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }

        if(money > 0) {
            PlatformActivityRewardRecord record = new PlatformActivityRewardRecord();
            record.setAgentNo(player.getAgentNo());
            record.setAgentName(player.getAgentName());
            record.setPlayerId(player.getPlayerId());
            record.setPlayerName(player.getPlayerName());
            record.setActivityId(config.getId());
            record.setActivityType(config.getActivityType());
            record.setPid(player.getPid());
            record.setPids(player.getPids());
            record.setAmount(BigDecimal.valueOf(money));
            record.setDrawStatus(PlatformActivityRewardRecord.DRAW_STATUS_WAIT);
            record.setRewardTime(new Timestamp(System.currentTimeMillis()));
            record.setActivityDate(new Timestamp(sDate.getTime())); // 记录周期里的开始时间
            record.setDrawData("");
            String ip = getAccountLoginIp(player.getPlayerId());
            record.setPlayerIp(ip);
            String remarks = "团队充值：" + rechargeAmount + "，团队投注：" + betAmount;
            if(playerAmount.getGrandTotalType() > AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO) {
                remarks += "，团队上周期亏损：" + playerAmount.getLastLossAmount() + "，累计周期：";
                if(playerAmount.getGrandTotalType() < AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                    remarks += playerAmount.getGrandTotalType();
                }else if(playerAmount.getGrandTotalType() == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                    remarks += "永久";
                }
                remarks += "，累计亏损：" + playerAmount.getGrandTotalLossAmount() + "，团队最终亏损：" + lossAmount;
            }else {
                remarks += "，团队亏损：" + lossAmount;
            }
            remarks += "，活跃人数：" + activeUser + "，" + scalePointTitle + "：" + rules.getScalePoint();
            record.setRemarks(remarks);
            Map<String, Object> extraData = new HashMap<>(5);
            extraData.put("rechargeAmount", rechargeAmount);
            extraData.put("betAmount", betAmount);
            extraData.put("lossAmount", lossAmount);
            extraData.put("activeUser", activeUser);
            extraData.put("scalePoint", rules.getScalePoint());
            record.setExtraData(JSON.toJSONString(extraData));
            return record;
        }
        log.info("doDraw活动==={}==={}===奖励为0", config.getCode(), player.getPlayerName());
        return null;
    }

    /**
     * 获得彩票和三方工资/分红活动奖励记录。计算周期：周/月
     *
     * @param rules
     * @param player
     * @param config
     * @param playerAmount
     * @return
     */
    public PlatformActivityRewardRecord getSalaryPeriodRewardRecord(AcitvitySalaryPeriodRules rules,
            AgentPlayerInfo player, PlatformActivityConfig config, PlayerAmount playerAmount, Date sDate, Date eDate) {
        // 奖金
        double money;
        // 充值
        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        // 投注
        double betAmount = playerAmount.getBetAmount().doubleValue();
        // 亏损
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        // 活跃用户
        int activeUser = playerAmount.getActiveUser();

        String scalePointTitle;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = rules.getScalePoint();
                scalePointTitle = "工资金额";
            }else {
                // 工资金额 = 投注 * 比例
                money = MathUtils.divide(MathUtils.multiply(betAmount, rules.getScalePoint()), 100, 5);
                scalePointTitle = "工资比例";
            }
        }else if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = rules.getScalePoint();
                scalePointTitle = "分红金额";
            }else {
                // 分红金额 = 亏损 * 比例
                money = MathUtils.divide(MathUtils.multiply(Math.abs(lossAmount), rules.getScalePoint()), 100, 5);
                scalePointTitle = "分红比例";
            }
            if(lossAmount >= 0) {
                // 用户未亏损，奖励改为0
                money = 0D;
            }
        }else {
            throw new IllegalArgumentException();
        }

        if(money > 0) {
            PlatformActivityRewardRecord record = new PlatformActivityRewardRecord();
            record.setAgentNo(player.getAgentNo());
            record.setAgentName(player.getAgentName());
            record.setPlayerId(player.getPlayerId());
            record.setPlayerName(player.getPlayerName());
            record.setActivityId(config.getId());
            record.setActivityType(config.getActivityType());
            record.setPid(player.getPid());
            record.setPids(player.getPids());
            record.setAmount(BigDecimal.valueOf(money));
            record.setDrawStatus(PlatformActivityRewardRecord.DRAW_STATUS_WAIT);
            record.setRewardTime(new Timestamp(System.currentTimeMillis()));
            record.setActivityDate(new Timestamp(sDate.getTime())); // 记录周期里的开始时间
            record.setDrawData("");
            String ip = getAccountLoginIp(player.getPlayerId());
            record.setPlayerIp(ip);
            String remarks;
            if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
                // 周/半月/月
                remarks = "团队充值：" + rechargeAmount + "，团队投注：" + betAmount;
                if(playerAmount.getGrandTotalType() > AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO) {
                    remarks += "，团队上周期亏损：" + playerAmount.getLastLossAmount() + "，累计周期：";
                    if(playerAmount.getGrandTotalType() < AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += playerAmount.getGrandTotalType();
                    }else if(playerAmount.getGrandTotalType() == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += "永久";
                    }
                    remarks += "，累计亏损：" + playerAmount.getGrandTotalLossAmount() + "，团队最终亏损：" + lossAmount;
                }else {
                    remarks += "，团队亏损：" + lossAmount;
                }
                remarks += "，活跃人数：" + activeUser + "，" + scalePointTitle + "：" + rules.getScalePoint();
            }else {
                // 10分钟/20分钟/30分钟/1小时
                remarks = "团队投注：" + betAmount;
                if(playerAmount.getGrandTotalType() > AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO) {
                    remarks += "，团队上周期亏损：" + playerAmount.getLastLossAmount() + "，累计周期：";
                    if(playerAmount.getGrandTotalType() < AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += playerAmount.getGrandTotalType();
                    }else if(playerAmount.getGrandTotalType() == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += "永久";
                    }
                    remarks += "，累计亏损：" + playerAmount.getGrandTotalLossAmount() + "，团队最终亏损：" + lossAmount;
                }else {
                    remarks += "，团队亏损：" + lossAmount;
                }
                remarks += "，" + scalePointTitle + "：" + rules.getScalePoint();
            }
            record.setRemarks(remarks);
            Map<String, Object> extraData = new HashMap<>(5);
            extraData.put("rechargeAmount", rechargeAmount);
            extraData.put("betAmount", betAmount);
            extraData.put("lossAmount", lossAmount);
            extraData.put("activeUser", activeUser);
            extraData.put("scalePoint", rules.getScalePoint());
            record.setExtraData(JSON.toJSONString(extraData));
            return record;
        }
        return null;
    }

    /**
     * 获得上级彩票和三方工资/分红活动奖励记录。计算周期：日
     *
     * @param rules
     * @param player
     * @param config
     * @param playerAmount
     * @return
     */
    public PlatformActivityRewardRecord getUpSalaryDailyRewardRecord(AcitvitySalaryDailyRules rules,
            AgentPlayerInfo player, PlatformActivityConfig config, PlayerAmount playerAmount, Date sDate, Date eDate) {
        // 上级奖励
        double upScalePoint = rules.getUpScalePoint();
        if(upScalePoint <= 0D) {
            return null;
        }
        if(player.getPid() == null || player.getPid() == 0L) {
            return null;
        }
        // 上级用户
        AgentPlayerInfo upPlayer = getAgentPlayerInfoDao().getByPlayerId(player.getPid());
        if(upPlayer == null) {
            return null;
        }
        // 奖金
        double money;
        // 充值
        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        // 投注
        double betAmount = playerAmount.getBetAmount().doubleValue();
        // 亏损
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        // 活跃用户
        int activeUser = playerAmount.getActiveUser();

        String scalePointTitle;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = upScalePoint;
                scalePointTitle = "工资金额";
            }else {
                // 工资金额 = 投注 * 比例
                money = MathUtils.divide(MathUtils.multiply(betAmount, upScalePoint), 100, 5);
                scalePointTitle = "工资比例";
            }
        }else if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = upScalePoint;
                scalePointTitle = "分红金额";
            }else {
                // 分红金额 = 亏损 * 比例
                money = MathUtils.divide(MathUtils.multiply(Math.abs(lossAmount), upScalePoint), 100, 5);
                scalePointTitle = "分红比例";
            }
            if(lossAmount >= 0) {
                // 用户未亏损，奖励改为0
                money = 0D;
            }
        }else {
            log.info("doDraw活动==={}==={}===活动类型错误", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }

        if(money > 0) {
            PlatformActivityRewardRecord record = new PlatformActivityRewardRecord();
            record.setAgentNo(upPlayer.getAgentNo());
            record.setAgentName(upPlayer.getAgentName());
            record.setPlayerId(upPlayer.getPlayerId());
            record.setPlayerName(upPlayer.getPlayerName());
            record.setActivityId(config.getId());
            record.setActivityType(config.getActivityType());
            record.setPid(upPlayer.getPid());
            record.setPids(upPlayer.getPids());
            record.setAmount(BigDecimal.valueOf(money));
            record.setDrawStatus(PlatformActivityRewardRecord.DRAW_STATUS_WAIT);
            record.setRewardTime(new Timestamp(System.currentTimeMillis()));
            record.setActivityDate(new Timestamp(sDate.getTime())); // 记录周期里的开始时间
            record.setDrawData("");
            String ip = getAccountLoginIp(upPlayer.getPlayerId());
            record.setPlayerIp(ip);
            String remarks = "来自下级：" + player.getPlayerName() + "，团队充值：" + rechargeAmount + "，团队投注：" +
                    betAmount;
            if(playerAmount.getGrandTotalType() > AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO) {
                remarks += "，团队上周期亏损：" + playerAmount.getLastLossAmount() + "，累计周期：";
                if(playerAmount.getGrandTotalType() < AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                    remarks += playerAmount.getGrandTotalType();
                }else if(playerAmount.getGrandTotalType() == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                    remarks += "永久";
                }
                remarks += "，累计亏损：" + playerAmount.getGrandTotalLossAmount() + "，团队最终亏损：" + lossAmount;
            }else {
                remarks += "，团队亏损：" + lossAmount;
            }
            remarks += "，活跃人数：" + activeUser + "，" + scalePointTitle + "：" + upScalePoint;
            record.setRemarks(remarks);
            Map<String, Object> extraData = new HashMap<>(5);
            extraData.put("rechargeAmount", rechargeAmount);
            extraData.put("betAmount", betAmount);
            extraData.put("lossAmount", lossAmount);
            extraData.put("activeUser", activeUser);
            extraData.put("scalePoint", upScalePoint);
            record.setExtraData(JSON.toJSONString(extraData));
            return record;
        }
        log.info("doDraw活动==={}==={}===奖励为0", config.getCode(), upPlayer.getPlayerName());
        return null;
    }

    /**
     * 获得上级彩票和三方工资/分红活动奖励记录。计算周期：周/月
     *
     * @param rules
     * @param player
     * @param config
     * @param playerAmount
     * @return
     */
    public PlatformActivityRewardRecord getUpSalaryPeriodRewardRecord(AcitvitySalaryPeriodRules rules,
            AgentPlayerInfo player, PlatformActivityConfig config, PlayerAmount playerAmount, Date sDate, Date eDate) {
        // 上级奖励
        double upScalePoint = rules.getUpScalePoint();
        if(upScalePoint <= 0D) {
            return null;
        }
        if(player.getPid() == null || player.getPid() == 0L) {
            return null;
        }
        // 上级用户
        AgentPlayerInfo upPlayer = getAgentPlayerInfoDao().getByPlayerId(player.getPid());
        if(upPlayer == null) {
            return null;
        }
        // 奖金
        double money;
        // 充值
        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        // 投注
        double betAmount = playerAmount.getBetAmount().doubleValue();
        // 亏损
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        // 活跃用户
        int activeUser = playerAmount.getActiveUser();

        String scalePointTitle;
        if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = upScalePoint;
                scalePointTitle = "工资金额";
            }else {
                // 工资金额 = 投注 * 比例
                money = MathUtils.divide(MathUtils.multiply(betAmount, upScalePoint), 100, 5);
                scalePointTitle = "工资比例";
            }
        }else if(config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                config.getActivityType() == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
            if(rules.getRewardType() == AcitvitySalaryRules.REWARD_TYPE_FIXED) {
                // 固定金额
                money = upScalePoint;
                scalePointTitle = "分红金额";
            }else {
                // 分红金额 = 亏损 * 比例
                money = MathUtils.divide(MathUtils.multiply(Math.abs(lossAmount), upScalePoint), 100, 5);
                scalePointTitle = "分红比例";
            }
            if(lossAmount >= 0) {
                // 用户未亏损，奖励改为0
                money = 0D;
            }
        }else {
            throw new IllegalArgumentException();
        }

        if(money > 0) {
            PlatformActivityRewardRecord record = new PlatformActivityRewardRecord();
            record.setAgentNo(upPlayer.getAgentNo());
            record.setAgentName(upPlayer.getAgentName());
            record.setPlayerId(upPlayer.getPlayerId());
            record.setPlayerName(upPlayer.getPlayerName());
            record.setActivityId(config.getId());
            record.setActivityType(config.getActivityType());
            record.setPid(upPlayer.getPid());
            record.setPids(upPlayer.getPids());
            record.setAmount(BigDecimal.valueOf(money));
            record.setDrawStatus(PlatformActivityRewardRecord.DRAW_STATUS_WAIT);
            record.setRewardTime(new Timestamp(System.currentTimeMillis()));
            record.setActivityDate(new Timestamp(sDate.getTime())); // 记录周期里的开始时间
            record.setDrawData("");
            String ip = getAccountLoginIp(upPlayer.getPlayerId());
            record.setPlayerIp(ip);
            String remarks = "来自下级：" + player.getPlayerName();
            if(config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                    config.getCycleScope() == PlatformActivityConfig.CYCLE_SCOPE_MONTH) {
                // 周/半月/月
                remarks += "，团队充值：" + rechargeAmount + "，团队投注：" + betAmount;
                if(playerAmount.getGrandTotalType() > AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO) {
                    remarks += "，团队上周期亏损：" + playerAmount.getLastLossAmount() + "，累计周期：";
                    if(playerAmount.getGrandTotalType() < AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += playerAmount.getGrandTotalType();
                    }else if(playerAmount.getGrandTotalType() == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += "永久";
                    }
                    remarks += "，累计亏损：" + playerAmount.getGrandTotalLossAmount() + "，团队最终亏损：" + lossAmount;
                }else {
                    remarks += "，团队亏损：" + lossAmount;
                }
                remarks += "，活跃人数：" + activeUser + "，" + scalePointTitle + "：" + rules.getScalePoint();
            }else {
                // 10分钟/20分钟/30分钟/1小时
                remarks += "，团队投注：" + betAmount;
                if(playerAmount.getGrandTotalType() > AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO) {
                    remarks += "，团队上周期亏损：" + playerAmount.getLastLossAmount() + "，累计周期：";
                    if(playerAmount.getGrandTotalType() < AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += playerAmount.getGrandTotalType();
                    }else if(playerAmount.getGrandTotalType() == AcitvitySalaryConditions.GRAND_TOTAL_TYPE_FOREVER) {
                        remarks += "永久";
                    }
                    remarks += "，累计亏损：" + playerAmount.getGrandTotalLossAmount() + "，团队最终亏损：" + lossAmount;
                }else {
                    remarks += "，团队亏损：" + lossAmount;
                }
                remarks += "，" + scalePointTitle + "：" + upScalePoint;
            }
            record.setRemarks(remarks);
            Map<String, Object> extraData = new HashMap<>(5);
            extraData.put("rechargeAmount", rechargeAmount);
            extraData.put("betAmount", betAmount);
            extraData.put("lossAmount", lossAmount);
            extraData.put("activeUser", activeUser);
            extraData.put("scalePoint", upScalePoint);
            record.setExtraData(JSON.toJSONString(extraData));
            return record;
        }
        return null;
    }

    /**
     * 验证是否已领取过指定活动奖励，领取过返回true，未领取返回false
     *
     * @param config
     * @param playerId
     * @return
     */
    public boolean hasReceiveReward(PlatformActivityConfig config, long playerId, Date sDate, Date eDate) {
        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.eq("activityId", config.getId()));
        criterions.add(Restrictions.eq("playerId", playerId));
        int activityType = config.getActivityType();
        switch(activityType) {
            default:
                criterions.add(Restrictions.eq("activityDate", sDate));
                break;
        }
        int totalCount = getPlatformActivityRewardRecordDao().totalCount(criterions);
        return totalCount > 0;
    }

    /**
     * 派发活动奖励，并记录
     *
     * @param record
     * @param config
     */
    public void payMoneyAndRecord(PlatformActivityRewardRecord record, PlatformActivityConfig config) {
        record.setRewardTime(new Timestamp(System.currentTimeMillis()));
        record.setDrawStatus(PlatformActivityRewardRecord.DRAW_STATUS_ALREADY);

        // 系统自动
        boolean result = getPlatformActivityRewardRecordDao().save(record);
        if(result) {
            log.info("doDraw活动==={}==={}===发放奖励", config.getCode(), record.getPlayerName());
            addRewardToAccount(record, config);
        }
    }

    /**
     * 将奖励发放到活动账户
     *
     * @param rewardRecord 活动记录
     * @param config 活动配置
     */
    public void addRewardToAccount(PlatformActivityRewardRecord rewardRecord, PlatformActivityConfig config) {
        AgentPlayerInfo player = getAgentPlayerInfoDao().getById(rewardRecord.getPlayerId());

        String billno = RandomUtils.fromTime16();
        String remark = config.getActivityTitle() + " " + DateUtils.getDateString(rewardRecord.getActivityDate());
        // 给目标中心账户加钱
        boolean updateBalance = platformAgentPlayerCommonService.updateAvailableBalanceForGameIn(player.getPlayerId(),
                rewardRecord.getAmount(), rewardRecord.getAmount().negate(), AgentPlayerAccountBill.BILL_TYPE_ACTIVITY,
                billno, player.getPlayerName(), remark);
        if(!updateBalance) {
            log.info("doDraw活动==={}==={}===发放奖励失败", config.getCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }
    }
}
