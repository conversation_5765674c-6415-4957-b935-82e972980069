package ph.yckj.common.util;

public class RedisKey {
	private static final String SYSTEM_CACHE_KEY = "system-cache:%s";
	private static final String AGENT_PLAYER_INFO_KEY = "agent-player-info:pid:%s";
	private static final String GAME_RECORD_DETAIL_KEY = "game-record-detail:oid:%s";
	private static final String GIFT_INFO_LIST_KEY = "gift-info-list";
	private static final String GIFT_INFO_CATEGORY_LIST_KEY = "gift-info-category-list";
	private static final String GIFT_INFO_KEY = "gift-info:code:%s";
	private static final String AGENT_INFO_KEY = "agent-info:aNo:%s";
	private static final String AGENT_PLAYER_INFO_NAME_KEY = "agent-player-info:pName:%s";
	private static final String AGENT_PLAYER_INFO_UAK_KEY = "agent-player-info:pName:%s:urlAuthKey:%s";
	private static final String AGENT_PLAYER_TOTAL_METHOD_BET_AMOUNT_KEY = "agent-player-total-method-bet-amount:pid:%s:lottery:%s:issue:%s";
	private static final String GAME_OPEN_CODE_KEY = "game-open-code:lottery:%s";
	private static final String GAME_LOTTERY_TYPE_KEY = "game-lottery-type";
	private static final String GAME_LOTTERY_TYPE_GAMETYPECODE_KEY = "game-lottery-type:gameTypeCode:%s";
	private static final String GAME_LOTTERY_GROUP_KEY = "game-lottery-group";
	private static final String GAME_LOTTERY_GROUP_GAMETYPECODE_KEY = "game-lottery-group:gameGroupCode:%s";
	private static final String GAME_LOTTERY_INFO_KEY = "game-lottery-info";
	private static final String GAME_LOTTERY_INFO_LOTTERY_KEY = "game-lottery-info:lottery:%s";
	private static final String GAME_LOTTERY_METHOD_KEY = "game-lottery-method:gameTypeCode:%s";
	private static final String GAME_LOTTERY_METHOD_METHODCODE_KEY = "game-lottery-method:gameTypeCode:%s:methodCode:%s";
	private static final String GAME_LOTTERY_OPEN_TIME_KEY = "game-lottery-open-time:lottery:%s";
	private static final String GAME_MONOPOLY_CONFIG_INFO_KEY = "game-monopoly-config-info:%s:%s";
	private static final String GAME_LOTTERY_OPEN_NOTICE_KEY = "game-lottery-open-notice:pid:%s";
	private static final String SYSTEM_CONFIG_GROUP_KEY = "system-config:group:%s";
	private static final String SYSTEM_CONFIG_GROUP_KEY_KEY = "system-config:group:%s:key:%s";
	private static final String TELEGRAM_PUSH_BET_KEY = "telegram:push:bet";
	private static final String TELEGRAM_PUSH_BOUNS_KEY = "telegram:push:bouns";
	private static final String LOGIN_GUEST_IP_KEY = "login:guest:ip";
	private static final String LOGIN_GUEST_INFO_KEY = "login:guest:info";
	private static final String PAYMENT_BANK_KEY = "payment_bank";
	private static final String PAYMENT_BANK_ID_KEY = "payment_bank:id:%s";
	private static final String PAYMENT_THIRD_KEY = "payment_third";
	private static final String PAYMENT_THIRD_ID_KEY = "payment_third:id:%s";
	private static final String GAME_LOTTERY_OPEN_CODE_LASTED_ISSUE_KEY = "game-lottery-open-code:lasted-issue";
	private static final String AGENT_FLYING_THROW_KEY = "agent-flying-throw";
	private static final String AGENT_FLYING_THROW_AGENTNO_KEY = "agent-flying-throw:agentNo:%s";
	private static final String REBATE_LEVEL_KEY = "rebate-level:%s";

	public static String getUsdtAddressCacheKey(String usdtAddress) {
		return String.format("usdt-address:%s", usdtAddress);
	}

	public static String getPatchAccountRecharge(String billno) {
		return String.format("PatchAccountRecharge:%s", billno);
	}

	public static String getSystemCacheKey(String key) {
		return String.format(SYSTEM_CACHE_KEY, key);
	}

	public static String getAgentPlayerInfoKey(Long playerId) {
		return String.format(AGENT_PLAYER_INFO_KEY, playerId);
	}

	public static String getGameRecordDetailKey(Long orderId) {
		return String.format(GAME_RECORD_DETAIL_KEY, orderId);
	}

	public static String getGiftInfoListKey() {

		return String.format(GIFT_INFO_LIST_KEY);
	}

	public static String getGiftInfoCategoryListKey() {
		return String.format(GIFT_INFO_CATEGORY_LIST_KEY);
	}

	public static String getGiftInfoKey(String giftCode) {
		return String.format(GIFT_INFO_KEY, giftCode);
	}

	public static String getAgentInfoKey(String agentNo) {
		return String.format(AGENT_INFO_KEY, agentNo);
	}

	public static String getAgentPlayerInfoNameKey(String agentNo, String playerName) {
		return String.format(AGENT_PLAYER_INFO_NAME_KEY, agentNo + playerName);
	}

	public static String getAgentPlayerInfoUrlAuthKeyKey(String agentNo, String playerName, String urlAuthKey) {
		return String.format(AGENT_PLAYER_INFO_UAK_KEY, agentNo + playerName, urlAuthKey);
	}

	public static String getAgentPlayerTotalMethodBetAmountKey(Long playerId, String lottery, String issue) {
		return String.format(AGENT_PLAYER_TOTAL_METHOD_BET_AMOUNT_KEY, playerId, lottery, issue);
	}

	public static String getGameOpenCodeKey(String lottery) {
		return String.format(GAME_OPEN_CODE_KEY, lottery);
	}

	public static String getGameLotteryTypeKey() {
		return String.format(GAME_LOTTERY_TYPE_KEY);
	}

	public static String getGameLotteryTypeKey(String gameTypeCode) {
		return String.format(GAME_LOTTERY_TYPE_GAMETYPECODE_KEY, gameTypeCode);
	}

	public static String getGameLotteryGroupKey() {
		return String.format(GAME_LOTTERY_GROUP_KEY);
	}

	public static String getGameLotteryGroupKey(String gameGroupCode) {
		return String.format(GAME_LOTTERY_GROUP_GAMETYPECODE_KEY, gameGroupCode);
	}

	public static String getGameLotteryInfoKey() {
		return String.format(GAME_LOTTERY_INFO_KEY);
	}

	public static String getGameLotteryInfoKey(String lottery) {
		return String.format(GAME_LOTTERY_INFO_LOTTERY_KEY, lottery);
	}

	public static String getGameLotteryMethodKey(String gameTypeCode) {
		return String.format(GAME_LOTTERY_METHOD_KEY, gameTypeCode);
	}

	public static String getGameLotteryMethodKey(String gameTypeCode, String methodCode) {
		return String.format(GAME_LOTTERY_METHOD_METHODCODE_KEY, gameTypeCode, methodCode);
	}

	public static String getSystemConfigKey(String group) {
		return String.format(SYSTEM_CONFIG_GROUP_KEY, group);
	}

	public static String getSystemConfigKey(String group, String key) {
		return String.format(SYSTEM_CONFIG_GROUP_KEY_KEY, group, key);
	}

	public static String getGameLotteryOpenTimeKey(String lottery) {
		return String.format(GAME_LOTTERY_OPEN_TIME_KEY, lottery);
	}

	public static String getGameMonopolyConfigInfoKey(String agentNo, String lottery) {
		return String.format(GAME_MONOPOLY_CONFIG_INFO_KEY, agentNo, lottery);
	}

	public static String getGameLotteryOpenNoticeKey(Long playerId) {
		return String.format(GAME_LOTTERY_OPEN_NOTICE_KEY, playerId);
	}

	public static String getTelegramPushBetKey() {
		return String.format(TELEGRAM_PUSH_BET_KEY);
	}

	public static String getTelegramPushBounsKey() {
		return String.format(TELEGRAM_PUSH_BOUNS_KEY);
	}

	public static String getLoginGuestIpKey() {
		return LOGIN_GUEST_IP_KEY;
	}

	public static String getLoginGuestInfoKey() {
		return LOGIN_GUEST_INFO_KEY;
	}

	public static String getPaymentBanKey() {
		return PAYMENT_BANK_KEY;
	}

	public static String getPaymentBankKey(Long id) {
		return String.format(PAYMENT_BANK_ID_KEY, id);
	}

	public static String getPaymentThirdKey() {
		return PAYMENT_THIRD_KEY;
	}

	public static String getPaymentThirdKey(Long id) {
		return String.format(PAYMENT_THIRD_ID_KEY, id);
	}

	public static String getGameLotteryOpenCodeLastedIssueKey() {
		return String.format(GAME_LOTTERY_OPEN_CODE_LASTED_ISSUE_KEY);
	}

	public static String getAgentFlyingThrowKey() {
		return String.format(AGENT_FLYING_THROW_KEY);
	}

	public static String getAgentFlyingThrowKey(String agentNo) {
		return String.format(AGENT_FLYING_THROW_AGENTNO_KEY, agentNo);
	}

	public static String getRebateLevelKey(String agentNo) {
		return String.format(REBATE_LEVEL_KEY, agentNo);
	}
}