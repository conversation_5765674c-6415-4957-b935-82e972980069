package ph.yckj.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import myutil.JacksonUtils;
import myutil.MathUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cn.hutool.core.util.StrUtil;
import lottery.utils.open.OpenTime;
import lottery.utils.open.OpenTimeConfig;
import lottery.utils.open.impl.HighOpenTimeService;
import lottery.utils.prize.LotteryType;
import lottery.utils.prize.shuangmian.calculate.k3.K3BonusCalculate;
import lottery.utils.prize.shuangmian.calculate.pk8.Pk8BonusCalculate;
import lottery.utils.prize.shuangmian.calculate.lhc.LhcBonusCalculate;
import lottery.utils.prize.shuangmian.calculate.pc28.Pc28BonusCalculate;
import lottery.utils.prize.shuangmian.calculate.pk10.Pk10BonusCalculate;
import lottery.utils.prize.shuangmian.calculate.ssc.SscBonusCalculate;
import lottery.utils.prize.shuangmian.calculate.x511.X511BonusCalculate;
import myutil.Moment;
import ph.yckj.admin.dao.AdminRoleDao;
import ph.yckj.admin.entity.AdminRole;
import ph.yckj.admin.service.AgentPlayerService;
import ph.yckj.admin.vo.AgentUsdtExchangeVo;
import ph.yckj.admin.vo.agent.PointRangeVo;
import ph.yckj.admin.vo.game.GameLotteryMethodCustomizeVo;
import sy.hoodle.base.common.entity.*;
import ph.yckj.common.service.RedisService;

@Component
public class ServiceUtils extends AbstractDao {

	@Autowired
	private AdminRoleDao adminRoleDao;

	@Autowired
	private RedisService redisService;
	@Autowired
	private AgentPlayerService agentPlayerService;

	/**
	 * 验证开奖号码是否符合规则
	 *
	 * @param lottery
	 * @param result
	 * @return
	 */
	public boolean validateResult(String lottery, String result) {
		GameLotteryInfo gameLotteryInfo = redisService.getGameLotteryInfo(lottery);
		return validateResult(gameLotteryInfo, result);
	}

	/**
	 * 验证开奖号码是否符合规则
	 *
	 * @param gameLotteryInfo
	 * @param result
	 * @return
	 */
	public boolean validateResult(GameLotteryInfo gameLotteryInfo, String result) {
		LotteryType lotteryType = LotteryType.valueOf(gameLotteryInfo.getGameTypeCode());
		switch (lotteryType) {
			case k3:
				return K3BonusCalculate.validateResult(result);
			case ssc:
				return SscBonusCalculate.validateResult(result);
			case pk10:
				return Pk10BonusCalculate.validateResult(result);
			case lhc:
				return LhcBonusCalculate.validateResult(result);
			case kl8:
				return Pk8BonusCalculate.validateResult(result);
			case pc28:
			case dpc:
			case d3:
				return Pc28BonusCalculate.validateResult(result);
			case x511:
				return X511BonusCalculate.validateResult(result);
			default:
				break;
		}
		return false;
	}

	// 定义一个映射，存储每种 LotteryType 对应的最大限制值
	private static final Map<LotteryType, Integer> MAX_LIMIT_MAP = new HashMap<>();
	static {
		// 初始化映射关系
		MAX_LIMIT_MAP.put(LotteryType.k3, 705);
		MAX_LIMIT_MAP.put(LotteryType.ssc, 940);
		MAX_LIMIT_MAP.put(LotteryType.pk10, 282);
		MAX_LIMIT_MAP.put(LotteryType.lhc, 564);
		MAX_LIMIT_MAP.put(LotteryType.kl8, 705);
		MAX_LIMIT_MAP.put(LotteryType.pc28, 470);
	}

	public boolean validateGameIssue(String lottery, String gameIssue) {
		if (",hklhc,".contains(lottery) && gameIssue.length() == 8) {
			return true;
		}
		if (",fc3d,pl3,molhc,mo2lhc,".contains(lottery) && gameIssue.length() == 7) {
			return true;
		}

		GameLotteryInfo gameLotteryInfo = redisService.getGameLotteryInfo(lottery);
		OpenTime openTime = getOpenTimeByTime(gameLotteryInfo, new Moment().toSimpleTime());
		if (null == openTime) {
			return false;
		}

		// 判断是否大于当前一期期号
		String issue = openTime.getIssue().replace("-", "");
		if (Long.parseLong(gameIssue.replace("-", "")) > Long.parseLong(issue)) {
			return false;
		}

		// 判断期号-后值位数
		if(",bjpk10,xjssc,ah11x5,sh11x5,ln11x5,ahk3,jlk3,hbk3,jsk3,".contains(lottery)) {
			if (StrUtil.subAfter(gameIssue, "-", true).length() != 3) {
				return false;
			}
		}else if(",greece28,".contains(lottery)) {
			// 希腊PC28等彩种，只要期长度满足条件即可
			if(gameIssue.length() < 6) {
				return false;
			}
		}else if(",jndpc28,canada3d5fc,georgia3d5points,georgia5,greekAirship,".contains(lottery)) {
			// 加拿大PC28等彩种，只要期长度满足条件即可
			if(gameIssue.length() < 7) {
				return false;
			}
		}else if(",t6s300b,jspk10,".contains(lottery)) {
			// 澳洲PK10、极速赛车等彩种，只要期长度满足条件即可
			if(gameIssue.length() < 8) {
				return false;
			}
		}else if(",taiwan5fc,taiwanPK10,taiwan28,".contains(lottery)) {
			// 台湾5分彩等彩种，只要期长度满足条件即可
			if(gameIssue.length() < 9) {
				return false;
			}
		}else if(GameLotteryInfo.GAME_CLASS_DZ.equals(gameLotteryInfo.getGameClass())) {
			LotteryType lotteryType = LotteryType.valueOf(gameLotteryInfo.getGameTypeCode());
			int lastThreeDigits = extractLastThreeDigits(gameIssue);
			// 根据 LotteryType 获取对应的最大限制值
			Integer maxLimit = MAX_LIMIT_MAP.get(lotteryType);
			if(maxLimit != null) {
				// 判断后三位数字是否小于等于最大限制值
				return lastThreeDigits <= maxLimit;
			}
			return false;
		}else {
			// 获取开奖期数长度
			int issueLength = String.valueOf(gameLotteryInfo.getTimes()).length();
			// 开奖期数长度与期号后面部分长度是否一致
			if(StrUtil.subAfter(gameIssue, "-", true).length() != issueLength &&
					(gameIssue.length() < 8 || gameIssue.substring(8).length() != issueLength)) {
				return false;
			}
		}

		return true;
	}

	/*
	 * 可以查询多少天内的报表数据
	 */
	public int maxReportDays(String queryLimit, String reportName, int defaultMaxDays) {
		int maxDays = defaultMaxDays;
		if (null != queryLimit && !"".equals(queryLimit)) {
			if (null == reportName || "".equals(reportName)) {
				reportName = "DEFAULT";
			}
			Map<String, Integer> queryLimitMap = JacksonUtils.toMap(queryLimit, String.class, Integer.class);
			Integer maxDaysConf = queryLimitMap.get(reportName);
			if (null != maxDaysConf) {
				maxDays = maxDaysConf.intValue();
			} else {
				maxDaysConf = queryLimitMap.get("DEFAULT");
				if (null != maxDaysConf) {
					maxDays = maxDaysConf.intValue();
				}
			}
		}
		return maxDays;
	}

	// 提取字符串后三位并转换为整数的方法
	private int extractLastThreeDigits(String lottery) {
		return Integer.parseInt(lottery.substring(lottery.length() - 3));
	}

	public AgentUsdtExchangeVo getUsdtExchange(String agentNo) {
		AgentUsdtExchangeVo vo = new AgentUsdtExchangeVo(agentNo);
		List<PlatformConfig> configs = getPlatformConfigDao().listByGroup(agentNo, "USDT_EXCHANGE");
		if (CollectionUtils.isEmpty(configs)) {
			return null;
		}
		Map<String, PlatformConfig> configMap = configs.stream()
				.collect(Collectors.toMap(PlatformConfig::getKey, t -> t));
		for (PlatformConfig config : configs) {
			String key = config.getKey();
			String value = config.getValue();
			switch (key) {
			case "USDT_RECHARGE_RATE":
				vo.setUsdtRechargeRate(Double.parseDouble(value));
				break;
			case "USDT_WITHDRAW_RATE":
				vo.setUsdtWithdrawRate(Double.parseDouble(value));
				break;
			case "FOLLOW_RECHARGE_ADD_RATE":
				vo.setFollowRechargeAddRate(Double.parseDouble(value));
				break;
			case "IS_USDT_RECHARGE_SYNC":
				boolean isUsdtRechargeSync = Boolean.parseBoolean(value);
				if (isUsdtRechargeSync) {
					vo.setUsdtRechargeSync(isUsdtRechargeSync);
					vo.getConfigMap().put("USDT_RECHARGE_RATE", configMap.get("USDT_RECHARGE_RATE"));
				}
				break;
			case "IS_USDT_WITHDRAW_SYNC":
				boolean isUsdtWithdrawSync = Boolean.parseBoolean(value);
				if (isUsdtWithdrawSync) {
					vo.setUsdtWithdrawSync(isUsdtWithdrawSync);
					vo.getConfigMap().put("USDT_WITHDRAW_RATE", configMap.get("USDT_WITHDRAW_RATE"));
				}
				break;
			case "IS_USDT_RATE_FOLLOW_RECHARGE":
				vo.setUsdtRateFollowRecharge(Boolean.parseBoolean(value));
				break;
			default:
				break;
			}
		}
		return vo;
	}

	/**
	 * 转换开奖时间
	 *
	 * @param lotteryName
	 * @return
	 */
	protected List<OpenTimeConfig> listOpenTime(String lotteryName) {
		List<GameLotteryOpenTime> timeList = redisService.getGameLotteryOpenTimeList(lotteryName);
		List<OpenTimeConfig> list = new ArrayList<OpenTimeConfig>();
		for (GameLotteryOpenTime tmpBean : timeList) {
			list.add(new OpenTimeConfig(tmpBean.getIssue(), tmpBean.getStartTime(), tmpBean.getStopTime(),
					tmpBean.getOpenTime(), tmpBean.getIssueDay(), tmpBean.getStopDay(), tmpBean.getOpenDay()));
		}
		return list;
	}

	/**
	 * 获取开奖时间
	 *
	 * @param gameLotteryInfo
	 * @param time
	 * @return
	 */
	public OpenTime getOpenTimeByTime(GameLotteryInfo gameLotteryInfo, String time) {
		String lottery = gameLotteryInfo.getLottery();
		List<OpenTimeConfig> timeList = listOpenTime(lottery);
		int delay = 0;
		HighOpenTimeService highOpenTimeService = new HighOpenTimeService();
		OpenTime bean = highOpenTimeService.getByTime(time, timeList, delay);
		return bean;
	}

	/**
	 * 获取开奖时间
	 *
	 * @param gameLotteryInfo
	 * @param issue
	 * @return
	 */
	public OpenTime getOpenTimeByIssue(GameLotteryInfo gameLotteryInfo, String issue) {
		return null;
	}

	/**
	 * 更新用户锁定时间
	 *
	 * @param playerInfo
	 * @param hours
	 */
	public void updateAccountLockTime(AgentPlayerInfo playerInfo, int hours) {
		if (hours > 0) {
			Date oldLockTime = playerInfo.getLockTime();
			Date newStartTime = new Date();
			if (oldLockTime != null) {
				if (oldLockTime.compareTo(newStartTime) > 0) {
					newStartTime = oldLockTime;
				}
			}
			Date newLockTime = new Moment().fromDate(newStartTime).add(hours, "hours").toDate();
			boolean updateLockTime = getAgentPlayerInfoDao().updateLockTime(playerInfo.getPlayerId(), newLockTime);
			if (!updateLockTime) {
				throw new IllegalArgumentException();
			}
		}
	}

	public AdminRole getAgentLevelCode(Integer agentLevelCode) {
		int roleId = 3;
		if (agentLevelCode == 1) {
			roleId = 5;
		} else if (agentLevelCode == 2) {
			roleId = 6;
		}
		return adminRoleDao.getById(roleId);
	}

	public List<GameLotteryMethodCustomizeVo> getGameLotteryMethod(String agentNo, GameLotteryInfo gameLotteryInfo,
			Integer methodType, String methodName, Integer methodStatus) {
		// 玩法列表
		List<GameLotteryMethodCustomizeVo> dataList = new ArrayList<>();
		// 是否查询玩法名称
		boolean checkMethodName = !StringUtils.isEmpty(methodName);
		// 是否查询代理商
		boolean agentQuery = false;
		// 代理商名称
		String agentName = "";
		if(!StringUtils.isEmpty(agentNo)) {
			AgentInfo agent = redisService.getAgentInfo(agentNo);
			if(agent != null) {
				agentName = agent.getAgentName();
				agentQuery = true;
			}
		}

		List<GameLotteryInfo> gameLotteryInfos;
		if(gameLotteryInfo != null) {
			gameLotteryInfos = new ArrayList<>();
			gameLotteryInfos.add(gameLotteryInfo);
		}else {
			// 没有彩种，查询全部彩种
			gameLotteryInfos = redisService.getGameLotteryInfoList();
			if(gameLotteryInfos == null) {
				gameLotteryInfos = new ArrayList<>();
			}
		}

		for(GameLotteryInfo lotteryInfo : gameLotteryInfos) {
			// 游戏类型名称
			String gameTypeName = "";
			GameLotteryType gameLotteryType = redisService.getGameLotteryType(lotteryInfo.getGameTypeCode());
			if(gameLotteryType != null) {
				gameTypeName = gameLotteryType.getGameTypeName();
			}
			// 系统玩法列表
			List<GameLotteryMethod> methodList = getGameLotteryMethodDao().listByGameTypeCode(
					lotteryInfo.getGameTypeCode());
			// 代理商玩法列表
			List<GameLotteryMethodCustomize> methodCustomizeList = null;
			if(agentQuery) {
				methodCustomizeList = getGameLotteryMethodCustomizeDao().listAll(agentNo, lotteryInfo.getLottery());
				if(methodCustomizeList == null || methodCustomizeList.isEmpty()) {
					// 代理商没有自定义玩法时，当成没有查询代理商处理
					agentQuery = false;
				}
			}
			for(GameLotteryMethod method : methodList) {
				if(methodType != null) {
					if((methodType == GameLotteryMethod.METHOD_TYPE_STANDARD &&
							method.getMethodType() == GameLotteryMethod.METHOD_TYPE_SHUANGMIAN) ||
							(methodType == GameLotteryMethod.METHOD_TYPE_SHUANGMIAN &&
									method.getMethodType() == GameLotteryMethod.METHOD_TYPE_STANDARD)) {
						// 过滤玩法类型
						continue;
					}
				}
				if(checkMethodName && !method.getMethodName().contains(methodName)) {
					// 过滤玩法名称
					continue;
				}

				boolean agentHasMethod = false;
				if(agentQuery) {
					// 有查询代理商，判断此代理商有没有自定义该玩法，有就使用代理商的，没有则使用系统的
					for(GameLotteryMethodCustomize customize : methodCustomizeList) {
						if(customize.getMethodType().equals(method.getMethodType()) && customize.getMethodCode().equals(
								method.getMethodCode())) {
							if(methodStatus != null && !methodStatus.equals(customize.getMethodStatus())) {
								// 过滤玩法状态
								break;
							}
							agentHasMethod = true;
							GameLotteryMethodCustomizeVo bean = new GameLotteryMethodCustomizeVo(agentNo, agentName,
									gameTypeName, lotteryInfo.getGameName(), customize);
							dataList.add(bean);
							break;
						}
					}
				}
				if(!agentHasMethod) {
					if(methodStatus != null && !methodStatus.equals(method.getMethodStatus())) {
						// 过滤玩法状态
						continue;
					}
					// 没有查询代理商，或代理商没有自定义该玩法
					GameLotteryMethodCustomizeVo bean = new GameLotteryMethodCustomizeVo(agentNo, agentName,
							gameTypeName, lotteryInfo.getLottery(), lotteryInfo.getGameName(), method);
					dataList.add(bean);
				}
			}
		}

		return dataList;
	}

	/**
	 * 后台调整用户返点，返回用户可修改返点区间
	 *
	 * @param player
	 * @return
	 */
	public PointRangeVo getLotteryAdminFixedRange(AgentPlayerInfo player) {
		PointRangeVo rangeVO = new PointRangeVo();
		// 受限于下级用户
		double minPoint = getAgentPlayerInfoDao().getMaxDownPoint(player.getPlayerId());
		if(minPoint >= 0 && player.getEqualLevel() != AgentPlayerInfo.EQUAL_LEVEL_ON) {
			// 当前用户没有开启同级开号权限时，最小返点+0.1
			minPoint = MathUtils.add(minPoint, 0.1);
		}
		if(minPoint < 0) {
			minPoint = 0;
		}
		// 获取系统最高返点
		PlatformConfig sysPointConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(),
				"GAME_LOTTERY", "SYS_POINT");
		if(sysPointConfig == null) {
			throw new ServiceException("1", "系统未配置最高返点，请联系客服！");
		}
		double maxPoint = Double.parseDouble(sysPointConfig.getValue());
		if(player.getPid() != 0) {
			AgentPlayerInfo upPlayer = getAgentPlayerInfoDao().getById(player.getPid());
			if(upPlayer != null) {
				double maxFromUpPoint = upPlayer.getPoint();
				if(upPlayer.getEqualLevel() != AgentPlayerInfo.EQUAL_LEVEL_ON) {
					// 上级用户没有开启同级开号权限时，最小返点+0.1
					maxFromUpPoint = MathUtils.subtract(maxPoint, 0.1);
				}
				if(maxPoint > maxFromUpPoint) {
					maxPoint = maxFromUpPoint;
				}
			}
		}
		rangeVO.setMinPoint(minPoint);
		rangeVO.setMaxPoint(maxPoint);
		return rangeVO;
	}

	/**
	 * 可提现金额
	 *
	 * @param player
	 * @return
	 */
	public BigDecimal getAvailableWithdrawBalance(AgentPlayerInfo player) {
		BigDecimal withdrawLimitAmount = player.getPlayerAvailableBalance(); // 剩余消费量 = 充值金额 * 比例 - 当前消费量
		PlayerWithdrawLimit limit = getPlayerWithdrawLimitDao().get(player.getPlayerId());
		if(limit == null) {
			return withdrawLimitAmount;
		}
		BigDecimal lotteryBalance = limit.getLotteryBalance();
		BigDecimal lotteryConsumeBalance = limit.getLotteryConsumeBalance();
		BigDecimal baccaratConsumeBalance = limit.getBaccaratConsumeBalance();

		// 提款消费量限制金额小于等于0时，直接返回用户余额
		if(lotteryBalance.compareTo(BigDecimal.ZERO) <= 0) {
			return withdrawLimitAmount;
		}

		// 是否彩票与第三方游戏消费比列是否各自比例
		PlatformConfig config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(), "WITHDRAW",
				"WITHDRAW_SPLIT_SCALE_ENABLE");
		boolean withdrawSplitScaleEnable = false;
		if(null != config) {
			withdrawSplitScaleEnable = Boolean.parseBoolean(config.getValue());
		}
		if(withdrawSplitScaleEnable) {
			// 彩票和三方使用不一样的消费量比例
			// 彩票消费量比例
			BigDecimal lotteryScale = BigDecimal.ZERO;
			config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(), "WITHDRAW",
					"LOTTERY_SCALE");
			if(null != config) {
				lotteryScale = BigDecimal.valueOf(Double.parseDouble(config.getValue()));
			}
			// 三方消费量比例
			BigDecimal baccaratScale = BigDecimal.ZERO;
			config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(), "WITHDRAW",
					"BACCARAT_SCALE");
			if(null != config) {
				baccaratScale = BigDecimal.valueOf(Double.parseDouble(config.getValue()));
			}

			if(lotteryScale.compareTo(BigDecimal.ZERO) <= 0 && baccaratScale.compareTo(BigDecimal.ZERO) <= 0) {
				lotteryBalance = BigDecimal.ZERO;
			}else {
				if(lotteryScale.compareTo(BigDecimal.ZERO) > 0 && lotteryConsumeBalance.compareTo(BigDecimal.ZERO) >
						0) {
					// 彩票
					lotteryBalance = lotteryBalance.subtract(
							lotteryConsumeBalance.divide(lotteryScale, 2, RoundingMode.HALF_DOWN));
				}
				if(baccaratScale.compareTo(BigDecimal.ZERO) > 0 && baccaratConsumeBalance.compareTo(BigDecimal.ZERO) >
						0) {
					// 三方
					lotteryBalance = lotteryBalance.subtract(
							baccaratConsumeBalance.divide(baccaratScale, 2, RoundingMode.HALF_DOWN));
				}
			}
		}else {
			// 彩票和三方使用一样的消费量比例
			BigDecimal totalScale = BigDecimal.ZERO;
			config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(), "WITHDRAW",
					"TOTAL_SCALE");
			if(null != config) {
				totalScale = BigDecimal.valueOf(Double.parseDouble(config.getValue()));
			}

			if(totalScale.compareTo(BigDecimal.ZERO) <= 0) {
				lotteryBalance = BigDecimal.ZERO;
			}else {
				// 消费量
				BigDecimal consumeBalance = BigDecimal.ZERO;
				if(lotteryConsumeBalance.compareTo(BigDecimal.ZERO) > 0) {
					// 加上彩票消费量
					consumeBalance = consumeBalance.add(lotteryConsumeBalance);
				}
				if(baccaratConsumeBalance.compareTo(BigDecimal.ZERO) > 0) {
					// 加上百家乐消费量
					consumeBalance = consumeBalance.add(baccaratConsumeBalance);
				}
				lotteryBalance = lotteryBalance.subtract(consumeBalance.divide(totalScale, 2, RoundingMode.HALF_DOWN));
			}
		}
		// 减去不能提款的那部分钱
		if(lotteryBalance.compareTo(BigDecimal.ZERO) > 0) {
			withdrawLimitAmount = withdrawLimitAmount.subtract(lotteryBalance);
		}
		if(withdrawLimitAmount.compareTo(BigDecimal.ZERO) < 0) {
			withdrawLimitAmount = BigDecimal.ZERO;
		}
		return withdrawLimitAmount;
	}
}