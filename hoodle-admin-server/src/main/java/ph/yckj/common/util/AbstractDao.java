package ph.yckj.common.util;

import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import sy.hoodle.base.common.dao.*;

@Getter
public abstract class AbstractDao {

	@Autowired
	private AgentInfoDao agentInfoDao;
	@Autowired
	private AgentFlyingThrowDao agentFlyingThrowDao;
	@Autowired
	private GameLotteryTypeDao gameLotteryTypeDao;
	@Autowired
	private GameLotteryGroupDao gameLotteryGroupDao;
	@Autowired
	private GameLotteryInfoDao gameLotteryInfoDao;
	@Autowired
	private AgentGameLotteryInfoDao agentGameLotteryInfoDao;
	@Autowired
	private GameLotteryMethodDao gameLotteryMethodDao;
	@Autowired
	private GameLotteryMethodCustomizeDao gameLotteryMethodCustomizeDao;
	@Autowired
	private GameLotteryOpenCodeDao gameLotteryOpenCodeDao;
	@Autowired
	private GameLotteryOpenTimeDao gameLotteryOpenTimeDao;
	@Autowired
	private GameLotteryVideoDao gameLotteryVideoDao;
	@Autowired
	private AgentPlayerInfoDao agentPlayerInfoDao;
	@Autowired
	private AgentRechargeRecordDao agentRechargeRecordDao;
	@Autowired
	private AgentWithdrawRecordDao agentWithdrawRecordDao;
	@Autowired
	private AgentPlayerAccountBillDao agentPlayerAccountBillDao;
	@Autowired
	private AgentPlayerTransferRecordDao agentPlayerTransferRecordDao;
	@Autowired
	private AgentPlayerGameOrderDao agentPlayerGameOrderDao;
	@Autowired
	private AgentAccountBillDao agentAccountBillDao;
	@Autowired
	private GameMethodReportDao gameMethodReportDao;
	@Autowired
	private AgentPlayerGameReportDao agentPlayerGameReportDao;
	@Autowired
	private ThirdAgentPlayerReportDao thirdAgentPlayerReportDao;
	@Autowired
	private PlatformAgentPlayerReportDao platformAgentPlayerReportDao;
	@Autowired
	private GameMonopolyConfigInfoDao gameMonopolyConfigInfoDao;
	@Autowired
	private GameMonopolyBetRecordDao gameMonopolyBetRecordDao;
	@Autowired
	private GameMonopolyPlayerBetDetailDao gameMonopolyPlayerBetDetailDao;
	@Autowired
	private GameMonopolyPoolBillDao gameMonopolyPoolBillDao;
	@Autowired
	private GameOrderIssueRecordDao gameOrderIssueRecordDao;
	@Autowired
	private AgentPlayerOpinionDao agentPlayerOpinionDao;
	@Autowired
	private AgentPlayerRewardDao agentPlayerRewardDao;
	@Autowired
	private GameLotteryOpenNoticeDao gameLotteryOpenNoticeDao;
	@Autowired
	private GiftInfoDao giftInfoDao;
	@Autowired
	private SystemNoticeDao systemNoticeDao;
	@Autowired
	private SystemConfigDao systemConfigDao;
	@Autowired
	private SystemCacheDao systemCacheDao;
	@Autowired
	private SystemWhitelistDao systemWhitelistDao;
	@Autowired
	private PaymentBankDao paymentBankDao;
	@Autowired
	private PaymentTransferDao paymentTransferDao;
	@Autowired
	private PaymentThirdDao paymentThirdDao;
	@Autowired
	private PaymentThirdPayDao paymentThirdPayDao;
	@Autowired
	private PaymentThirdRemitDao paymentThirdRemitDao;
	@Autowired
	private PlatformLineDao platformLineDao;
	@Autowired
	private PlatformLiveLineDao platformLiveLineDao;
	@Autowired
	private PlayerRechargeDao playerRechargeDao;
	@Autowired
	private PlayerWithdrawDao playerWithdrawDao;
	@Autowired
	private PlayerCardBankDao playerCardBankDao;
	@Autowired
	private PlayerCardUstdDao playerCardUstdDao;
	@Autowired
	private PlayerCardMDao playerCardMDao;
	@Autowired
	private PlayerCardHh5Dao playerCardHh5Dao;
	@Autowired
	private PlayerCardOkgDao playerCardOkgDao;
	@Autowired
	private PlayerCardAlipayDao playerCardAlipayDao;
	@Autowired
	private PlatformConfigDao platformConfigDao;
	@Autowired
	private AgentPromoteConfigDao agentPromoteConfigDao;
	@Autowired
	private AgentPlayerRebateRecordDao agentPlayerRebateRecordDao;
	@Autowired
	private PlatformActivityConfigDao platformActivityConfigDao;
	@Autowired
	private PlatformActivityInviteRecordDao platformActivityInviteRecordDao;
	@Autowired
	private PlatformActivityRewardRecordDao platformActivityRewardRecordDao;
	@Autowired
	private AgentPlayerCommissionRecordDao agentPlayerCommissionRecordDao;
	@Autowired
	private AgentPlayerGiftBackpackDao agentPlayerGiftBackpackDao;
	@Autowired
	private AgentPlayerAccountTypeDao agentPlayerAccountTypeDao;
	@Autowired
	private PlatformThesaurusDao platformThesaurusDao;
	@Autowired
	private CountryDao countryDao;
	@Autowired
	private PlatformCountryDao platformCountryDao;
	@Autowired
	private PlayerLoginLogDao playerLoginLogDao;
	@Autowired
	private GameSimulationReportDao gameSimulationReportDao;
	@Autowired
	private ContractRecordDao contractRecordDao;
	@Autowired
	private PlayerWithdrawLimitDao playerWithdrawLimitDao;
	@Autowired
	private GameWarnConfigInfoDao gameWranConfigInfoDao;
	@Autowired
	private GameWarnRecordDao gameWarnRecordDao;
	@Autowired
	private GameWarnAutoDisableRecordDao gameWarnAutoDisableRecordDao;
    @Autowired
    private LockAccountRecordDao lockAccountRecordDao;
    @Autowired
    private LockOrderRecordDao lockOrderRecordDao;

	protected AbstractDao() {

	}

}