package ph.yckj.common.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import myutil.MathUtils;
import myutil.Moment;
import myutil.ObjectUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import sy.hoodle.base.common.dto.PlayerAmount;
import sy.hoodle.base.common.dto.contract.ContractDailyConditions;
import sy.hoodle.base.common.dto.contract.ContractDailyRules;
import sy.hoodle.base.common.dto.contract.ContractPeriodConditions;
import sy.hoodle.base.common.dto.contract.ContractPeriodRules;
import sy.hoodle.base.common.entity.*;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class ContractUtils extends AbstractDao {

    /**
     * 获取彩票团队量（充值量、投注量、亏损量、团队活跃数）
     *
     * @param config 平台契约配置
     * @param playerConfig 用户契约配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public PlayerAmount getLotteryTeamAmount(ContractPlatformConfig config, ContractPlayerConfig playerConfig,
            AgentPlayerInfo player, Date sDate, Date eDate, boolean includeSelf) {
        PlayerAmount playerAmount = new PlayerAmount();

        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf);
        if(teamTotalInfo != null) {
            playerAmount.setRechargeAmount(BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0])));
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[2]));
            BigDecimal bonusAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[3]));
            BigDecimal rebateAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[4]));
            BigDecimal activityAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[5]));
            BigDecimal salaryAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[6]));
            BigDecimal divsAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[7]));
            // 亏损 = 派奖 + 返点 + 活动 + 工资 + 分红 - 投注
            BigDecimal lossAmount = bonusAmount.add(rebateAmount).add(activityAmount).add(salaryAmount).add(divsAmount)
                    .subtract(betAmount);

            playerAmount.setBetAmount(betAmount);
            playerAmount.setBonusAmount(bonusAmount);
            playerAmount.setRebateAmount(rebateAmount);
            playerAmount.setActivityAmount(activityAmount);
            playerAmount.setSalaryAmount(salaryAmount);
            playerAmount.setDivsAmount(divsAmount);
            playerAmount.setLossAmount(lossAmount);
        }
        // 活跃用户条件
        BigDecimal minRechargeAmount = BigDecimal.ONE;
        BigDecimal minBetAmount = BigDecimal.ONE;
        Integer standardDays = 1;

        if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 日
            ContractDailyConditions conditions = JSON.parseObject(playerConfig.getExtraRules(),
                    ContractDailyConditions.class);
            minRechargeAmount = conditions.getActiveMinRecharge();
            minBetAmount = conditions.getActiveMinConsume();
        }else if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_WEEK ||
                config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_HALF_MONTH ||
                config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MONTH) {
            // 周/月
            ContractPeriodConditions conditions = JSON.parseObject(playerConfig.getExtraRules(),
                    ContractPeriodConditions.class);
            minRechargeAmount = conditions.getActiveMinRecharge();
            minBetAmount = conditions.getActiveMinConsume();
            standardDays = conditions.getStandardDays();
        }

        int activeUser = getPlatformAgentPlayerReportDao().getTeamActiveUser(player.getAgentNo(), player.getPlayerId(),
                sDate, eDate, minRechargeAmount, minBetAmount, standardDays, includeSelf);
        playerAmount.setActiveUser(activeUser);
        return playerAmount;
    }

    /**
     * 获取三方团队量（充值量、投注量、亏损量、团队活跃数）
     *
     * @param config 平台契约配置
     * @param playerConfig 用户契约配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public PlayerAmount getThirdTeamAmount(ContractPlatformConfig config, ContractPlayerConfig playerConfig,
            AgentPlayerInfo player, Date sDate, Date eDate, boolean includeSelf) {
        PlayerAmount playerAmount = new PlayerAmount();

        // 充值得从彩票报表获取
        Object[] teamTotalInfo = getPlatformAgentPlayerReportDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf);
        if(teamTotalInfo != null) {
            playerAmount.setRechargeAmount(BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0])));
        }

        // 从三方报表获取投注等
        teamTotalInfo = getGameSimulationReportDao().getTeamTotalInfo(player.getPlayerId(), null, sDate, eDate,
                includeSelf);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));

            playerAmount.setBetAmount(betAmount);
            playerAmount.setLossAmount(lossAmount);
        }

        // 活跃用户条件
        BigDecimal minRechargeAmount = BigDecimal.ONE;
        BigDecimal minBetAmount = BigDecimal.ONE;
        Integer standardDays = 1;

        if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 日
            ContractDailyConditions conditions = JSON.parseObject(playerConfig.getExtraRules(),
                    ContractDailyConditions.class);
            minRechargeAmount = conditions.getActiveMinRecharge();
            minBetAmount = conditions.getActiveMinConsume();
        }else if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_WEEK ||
                config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_HALF_MONTH ||
                config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MONTH) {
            // 周/月
            ContractPeriodConditions conditions = JSON.parseObject(playerConfig.getExtraRules(),
                    ContractPeriodConditions.class);
            minRechargeAmount = conditions.getActiveMinRecharge();
            minBetAmount = conditions.getActiveMinConsume();
            standardDays = conditions.getStandardDays();
        }

        int activeUser = getGameSimulationReportDao().getTeamActiveUser(player.getPlayerId(), null, sDate, eDate,
                minRechargeAmount, minBetAmount, standardDays, includeSelf);
        playerAmount.setActiveUser(activeUser);
        return playerAmount;
    }

    /**
     * 获取彩票团队量（投注量、亏损量）
     *
     * @param config 平台契约配置
     * @param playerConfig 用户契约配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @param includeSelf 是否包含本人
     * @return
     */
    public PlayerAmount getLotteryTeamBetAmount(ContractPlatformConfig config, ContractPlayerConfig playerConfig,
            AgentPlayerInfo player, Date sDate, Date eDate, boolean includeSelf) {
        PlayerAmount playerAmount = new PlayerAmount();

        // 仅计算未中奖订单
        boolean onlyLose = config.getComputeRange() == ContractPlatformConfig.COMPUTE_RANGE_LOSE;

        Object[] teamTotalInfo = getAgentPlayerGameOrderDao().getTeamTotalInfo(player.getAgentNo(),
                player.getPlayerId(), sDate, eDate, includeSelf, onlyLose);
        if(teamTotalInfo != null) {
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[1]));

            playerAmount.setBetAmount(betAmount);
            playerAmount.setLossAmount(lossAmount);
        }
        return playerAmount;
    }

    /**
     * 获得最后的登录ip
     *
     * @param playerId
     * @return
     */
    public String getAccountLoginIp(long playerId) {
        PlayerLoginLog loginLog = getPlayerLoginLogDao().getCurrLogin(playerId);
        if(loginLog != null) {
            return loginLog.getIp();
        }
        return "";
    }

    /**
     * 具有计算周期的契约周期区间段开始时间和结束时间计算
     *
     * @param config
     * @return
     */
    public Map<String, Date> getStartAndEndDate(ContractPlatformConfig config) {
        Map<String, Date> resultMap = new HashMap<>();
        Date sDate; // 开始日期
        Date eDate; // 结束日期
        int cycleType = config.getCycleType();

        if(cycleType == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 上周期：昨天的量
            sDate = new Moment().yesterday().startOf("day").toDate(); // 昨天
            eDate = new Moment().startOf("day").toDate(); // 今天
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_WEEK) {
            // 上周期：上周的量
            sDate = new Moment().startOf("week").subtract(1, "weeks").toDate();
            eDate = new Moment().startOf("week").toDate();
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_HALF_MONTH) {
            // 上周期：半月的量
            // 获取当月16日0点时间
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.DAY_OF_MONTH, 16);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            // 当前时间
            Date now = new Date();
            if(now.before(cal.getTime())) {
                // 当前时间在16日前，取上月下半月
                cal.add(Calendar.MONTH, -1); // 月份减1，就是上月16日0点时间
                sDate = cal.getTime();
                eDate = new Moment().startOf("month").toDate();
            }else {
                // 当前时间在16日后，取当月上半月
                sDate = new Moment().startOf("month").toDate();
                eDate = cal.getTime();
            }
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_MONTH) {
            // 上周期：上月的量
            sDate = new Moment().startOf("month").subtract(1, "months").toDate();
            eDate = new Moment().startOf("month").toDate();
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_MIN_10) {
            // 当前整分钟时间
            Moment moment = new Moment().startOf("minute");
            int minute = moment.get("minute");
            if(minute % 10 != 0) {
                // 不是整10分钟，减去多余的分钟数
                minute = minute % 10;
                moment = moment.subtract(minute, "m");
            }
            eDate = moment.toDate();
            // 开始时间要再减去10分钟
            sDate = moment.subtract(10, "m").toDate();
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_MIN_20) {
            // 当前整分钟时间
            Moment moment = new Moment().startOf("minute");
            int minute = moment.get("minute");
            if(minute % 20 != 0) {
                // 不是整20分钟，减去多余的分钟数
                minute = minute % 20;
                moment = moment.subtract(minute, "m");
            }
            eDate = moment.toDate();
            // 开始时间要再减去20分钟
            sDate = moment.subtract(20, "m").toDate();
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_MIN_30) {
            // 当前整分钟时间
            Moment moment = new Moment().startOf("minute");
            int minute = moment.get("minute");
            if(minute % 30 != 0) {
                // 不是整30分钟，减去多余的分钟数
                minute = minute % 30;
                moment = moment.subtract(minute, "m");
            }
            eDate = moment.toDate();
            // 开始时间要再减去30分钟
            sDate = moment.subtract(30, "m").toDate();
        }else if(cycleType == ContractPlatformConfig.CYCLE_TYPE_HOUR_1) {
            // 当前整小时时间
            Moment moment = new Moment().startOf("hour");
            eDate = moment.toDate();
            // 开始时间减去1小时
            sDate = moment.subtract(1, "h").toDate();
        }else {
            throw new IllegalArgumentException();
        }

        resultMap.put("sDate", sDate);
        resultMap.put("eDate", eDate);
        return resultMap;
    }

    /**
     * 获得彩票和三方工资/分红结果，返回团队充值、消费、亏损、活跃人数
     *
     * @param config 平台契约配置
     * @param playerConfig 用户契约配置
     * @param player 用户
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     */
    public PlayerAmount getPlayerAmount(ContractPlatformConfig config, ContractPlayerConfig playerConfig,
            AgentPlayerInfo player, Date sDate, Date eDate) {
        // 计算对象
        int targetType = config.getTargetType();
        // 契约周期
        int cycleType = config.getCycleType();
        boolean includeSelf;
        if(targetType == ContractPlatformConfig.TARGET_TYPE_INCLUDE) {
            includeSelf = true;
        }else if(targetType == ContractPlatformConfig.TARGET_TYPE_NOT_INCLUDE) {
            includeSelf = false;
        }else {
            // 计算对象错误
            log.info("doDraw契约==={}==={}===计算对象错误", config.getContractCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }
        // 获得计算周期
        if(cycleType != ContractPlatformConfig.CYCLE_TYPE_DAY && cycleType != ContractPlatformConfig.CYCLE_TYPE_WEEK &&
                cycleType != ContractPlatformConfig.CYCLE_TYPE_HALF_MONTH &&
                cycleType != ContractPlatformConfig.CYCLE_TYPE_MONTH &&
                cycleType != ContractPlatformConfig.CYCLE_TYPE_MIN_10 &&
                cycleType != ContractPlatformConfig.CYCLE_TYPE_MIN_20 &&
                cycleType != ContractPlatformConfig.CYCLE_TYPE_MIN_30 &&
                cycleType != ContractPlatformConfig.CYCLE_TYPE_HOUR_1) {
            log.info("doDraw契约==={}==={}===计算周期错误", config.getContractCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }

        if(config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY ||
                config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND) {
            // 获得彩票周期量
            if(cycleType == ContractPlatformConfig.CYCLE_TYPE_MIN_10 ||
                    cycleType == ContractPlatformConfig.CYCLE_TYPE_MIN_20 ||
                    cycleType == ContractPlatformConfig.CYCLE_TYPE_MIN_30 ||
                    cycleType == ContractPlatformConfig.CYCLE_TYPE_HOUR_1) {
                // 计算周期为10分钟/20分钟/30分钟/1小时
                return getLotteryTeamBetAmount(config, playerConfig, player, sDate, eDate, includeSelf);
            }else {
                // 获得彩票周期量
                return getLotteryTeamAmount(config, playerConfig, player, sDate, eDate, includeSelf);
            }
        }else if(config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY ||
                config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_DIVIDEND) {
            // 获得三方周期量
            return getThirdTeamAmount(config, playerConfig, player, sDate, eDate, includeSelf);
        }else {
            log.info("doDraw契约==={}==={}===契约类型错误", config.getContractCode(), player.getPlayerName());
            throw new IllegalArgumentException();
        }
    }

    /**
     * 获得彩票和三方工资/分红契约最优的奖励规则。计算周期：日
     *
     * @param rulesList
     * @param playerAmount
     * @return
     */
    public ContractDailyRules getDailyPerfectRules(List<ContractDailyRules> rulesList, PlayerAmount playerAmount) {
        // 奖励规则，充值等单位是万，所以需要乘以万
        BigDecimal wan = BigDecimal.valueOf(10000);

        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        double betAmount = playerAmount.getBetAmount().doubleValue();
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        int activeUser = playerAmount.getActiveUser();

        for(ContractDailyRules rules : rulesList) {
            if(rules.getDailyRecharge().doubleValue() > 0) {
                if(rechargeAmount < rules.getDailyRecharge().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getDailyConsume().doubleValue() > 0) {
                if(betAmount < rules.getDailyConsume().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getDailyLoss().doubleValue() > 0) {
                if(lossAmount > 0 || Math.abs(lossAmount) < rules.getDailyLoss().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getActiveUser() > 0) {
                if(activeUser < rules.getActiveUser()) {
                    continue;
                }
            }
            return rules;
        }
        return null;
    }

    /**
     * 获得彩票和三方工资/分红契约最优的奖励规则。计算周期：周/月
     *
     * @param rulesList
     * @param playerAmount
     * @return
     */
    public ContractPeriodRules getPeriodPerfectRules(ContractPlatformConfig config, List<ContractPeriodRules> rulesList,
            PlayerAmount playerAmount) {
        // 奖励规则，充值等单位是万，所以需要乘以万
        BigDecimal wan = BigDecimal.valueOf(10000);

        double rechargeAmount = playerAmount.getRechargeAmount().doubleValue();
        double betAmount = playerAmount.getBetAmount().doubleValue();
        double lossAmount = playerAmount.getLossAmount().doubleValue();
        int activeUser = playerAmount.getActiveUser();

        for(ContractPeriodRules rules : rulesList) {
            if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_WEEK ||
                    config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_HALF_MONTH ||
                    config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MONTH) {
                // 周/半月/月，才验证充值和活跃用户
                if(rules.getTotalRecharge().doubleValue() > 0) {
                    if(rechargeAmount < rules.getTotalRecharge().multiply(wan).doubleValue()) {
                        continue;
                    }
                }
                if(rules.getActiveUser() > 0) {
                    if(activeUser < rules.getActiveUser()) {
                        continue;
                    }
                }
            }
            if(rules.getTotalConsume().doubleValue() > 0) {
                if(betAmount < rules.getTotalConsume().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            if(rules.getTotalLoss().doubleValue() > 0) {
                if(lossAmount > 0 || Math.abs(lossAmount) < rules.getTotalLoss().multiply(wan).doubleValue()) {
                    continue;
                }
            }
            return rules;
        }
        return null;
    }

    /**
     * 将工资/分红规则按发放比例排序
     *
     * @param rules 奖励规则
     * @param isAsc 是否顺序
     */
    public void reorderDaily(List<ContractDailyRules> rules, boolean isAsc) {
        for(int i = 0; i < rules.size(); i++) {
            for(int j = i + 1; j < rules.size(); j++) {
                if(isAsc && rules.get(j).getScalePoint() < rules.get(i).getScalePoint()) {
                    // 顺序排序
                    ContractDailyRules tmp = rules.get(i);
                    rules.set(i, rules.get(j));
                    rules.set(j, tmp);
                }
                if(!isAsc && rules.get(j).getScalePoint() > rules.get(i).getScalePoint()) {
                    // 倒序排序
                    ContractDailyRules tmp = rules.get(i);
                    rules.set(i, rules.get(j));
                    rules.set(j, tmp);
                }
            }
        }
    }

    /**
     * 将工资/分红规则按发放比例排序
     *
     * @param rules 奖励规则
     * @param isAsc 是否顺序
     */
    public void reorderPeriod(List<ContractPeriodRules> rules, boolean isAsc) {
        for(int i = 0; i < rules.size(); i++) {
            for(int j = i + 1; j < rules.size(); j++) {
                if(isAsc && rules.get(j).getScalePoint() < rules.get(i).getScalePoint()) {
                    // 顺序排序
                    ContractPeriodRules tmp = rules.get(i);
                    rules.set(i, rules.get(j));
                    rules.set(j, tmp);
                }
                if(!isAsc && rules.get(j).getScalePoint() > rules.get(i).getScalePoint()) {
                    // 倒序排序
                    ContractPeriodRules tmp = rules.get(i);
                    rules.set(i, rules.get(j));
                    rules.set(j, tmp);
                }
            }
        }
    }

    /**
     * 获得彩票和三方工资/分红契约奖励记录。计算周期：日
     *
     * @param rules 契约规则
     * @param player 用户
     * @param config 用户契约
     * @param playerAmount 用户量
     * @param calculateCycle 计算周期
     * @param achieveStatus 达标状态
     * @return
     */
    public ContractRecord getDailyRewardRecord(ContractDailyRules rules, AgentPlayerInfo player,
            ContractPlayerConfig config, PlayerAmount playerAmount, String calculateCycle, Integer achieveStatus) {
        // 比例
        Double scalePoint = 0D;
        // 奖金
        double money = 0D;
        // 投注
        double betAmount = playerAmount.getBetAmount().doubleValue();
        // 亏损
        double lossAmount = playerAmount.getLossAmount().doubleValue();

        if(achieveStatus == ContractRecord.ACHIEVE_STATUS_YES) {
            // 已达标时，才计算金额
            scalePoint = rules.getScalePoint();
            if(config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY ||
                    config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY) {
                // 工资金额 = 投注 * 比例
                money = MathUtils.divide(MathUtils.multiply(betAmount, scalePoint), 100, 5);
            }else if(config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND ||
                    config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_DIVIDEND) {
                if(lossAmount > 0) {
                    // 用户未亏损，奖励为0
                    money = 0D;
                }else {
                    // 分红金额 = 亏损 * 比例
                    money = MathUtils.divide(MathUtils.multiply(Math.abs(lossAmount), scalePoint), 100, 5);
                }
            }else {
                log.info("doDraw契约==={}==={}===契约类型错误", config.getContractCode(), player.getPlayerName());
                throw new IllegalArgumentException();
            }
        }

        return new ContractRecord(player.getAgentNo(), player.getAgentName(), config.getContractCode(),
                config.getContractTitle(), config.getContractType(), config.getToPlayerId(), config.getToPlayerName(),
                config.getFromPlayerId(), config.getFromPlayerName(), playerAmount.getRechargeAmount(),
                playerAmount.getBetAmount(), playerAmount.getLossAmount(), playerAmount.getActiveUser(), scalePoint,
                BigDecimal.valueOf(money), achieveStatus, ContractRecord.DRAW_STATUS_WAITING, calculateCycle,
                new Date());
    }

    /**
     * 获得彩票和三方工资/分红契约奖励记录。计算周期：周/月
     *
     * @param rules 契约规则
     * @param player 用户
     * @param config 用户契约
     * @param playerAmount 用户量
     * @param calculateCycle 计算周期
     * @param achieveStatus 达标状态
     * @return
     */
    public ContractRecord getPeriodRewardRecord(ContractPeriodRules rules, AgentPlayerInfo player,
            ContractPlayerConfig config, PlayerAmount playerAmount, String calculateCycle, Integer achieveStatus) {
        // 比例
        Double scalePoint = 0D;
        // 奖金
        double money = 0D;
        // 投注
        double betAmount = playerAmount.getBetAmount().doubleValue();
        // 亏损
        double lossAmount = playerAmount.getLossAmount().doubleValue();

        if(achieveStatus == ContractRecord.ACHIEVE_STATUS_YES) {
            // 已达标时，才计算金额
            scalePoint = rules.getScalePoint();
            if(config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY ||
                    config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY) {
                // 工资金额 = 投注 * 比例
                money = MathUtils.divide(MathUtils.multiply(betAmount, scalePoint), 100, 5);
            }else if(config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND ||
                    config.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_DIVIDEND) {
                if(lossAmount > 0) {
                    // 用户未亏损，奖励为0
                    money = 0D;
                }else {
                    // 分红金额 = 亏损 * 比例
                    money = MathUtils.divide(MathUtils.multiply(Math.abs(lossAmount), scalePoint), 100, 5);
                }
            }else {
                log.info("doDraw契约==={}==={}===契约类型错误", config.getContractCode(), player.getPlayerName());
                throw new IllegalArgumentException();
            }
        }

        return new ContractRecord(player.getAgentNo(), player.getAgentName(), config.getContractCode(),
                config.getContractTitle(), config.getContractType(), config.getToPlayerId(), config.getToPlayerName(),
                config.getFromPlayerId(), config.getFromPlayerName(), playerAmount.getRechargeAmount(),
                playerAmount.getBetAmount(), playerAmount.getLossAmount(), playerAmount.getActiveUser(), scalePoint,
                BigDecimal.valueOf(money), achieveStatus, ContractRecord.DRAW_STATUS_WAITING, calculateCycle,
                new Date());
    }

    /**
     * 验证是否已领取过指定契约奖励，领取过返回true，未领取返回false
     *
     * @param config
     * @param playerId
     * @return
     */
    public boolean hasReceiveReward(ContractPlayerConfig config, long playerId, String calculateCycle) {
        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.eq("agentNo", config.getAgentNo()));
        criterions.add(Restrictions.eq("contractCode", config.getContractCode()));
        criterions.add(Restrictions.eq("toPlayerId", playerId));
        criterions.add(Restrictions.eq("calculateCycle", calculateCycle));
        int totalCount = getContractRecordDao().totalCount(criterions);
        return totalCount > 0;
    }
}
