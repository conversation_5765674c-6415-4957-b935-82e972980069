package ph.yckj.admin.activity;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 用户个人/团队的充值、投注等，用于活动、契约等计算
 */
@Setter
@Getter
public class ActivityPlayerAmount {

    /**
     * 充值
     */
    private BigDecimal rechargeAmount = BigDecimal.ZERO;
    /**
     * 提现
     */
    private BigDecimal withdrawAmount = BigDecimal.ZERO;
    /**
     * 投注
     */
    private BigDecimal betAmount = BigDecimal.ZERO;
    /**
     * 派奖
     */
    private BigDecimal bonusAmount = BigDecimal.ZERO;
    /**
     * 返点
     */
    private BigDecimal rebateAmount = BigDecimal.ZERO;
    /**
     * 活动
     */
    private BigDecimal activityAmount = BigDecimal.ZERO;
    /**
     * 工资
     */
    private BigDecimal salaryAmount = BigDecimal.ZERO;
    /**
     * 分红
     */
    private BigDecimal divsAmount = BigDecimal.ZERO;
    /**
     * 亏损/盈亏
     */
    private BigDecimal lossAmount = BigDecimal.ZERO;

    /**
     * 玩家id
     */
    private Long playerId;
    /**
     * 活跃用户(人)
     */
    private Integer activeUser = 0;

    @Override
    public String toString() {
        return "充值：" + rechargeAmount + "，提现：" + withdrawAmount + "，投注：" + betAmount + "，派奖：" + bonusAmount +
                "，返点：" + rebateAmount + "，活动：" + activityAmount + "，工资：" + salaryAmount + "，分红：" + divsAmount +
                "，亏损：" + lossAmount + "，活跃人数：" + activeUser + "，玩家id：" + playerId;
    }
}
