package ph.yckj.admin.activity.handler.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import ph.yckj.admin.activity.ActivityContext;
import ph.yckj.admin.activity.ActivityPlayerAmount;
import ph.yckj.admin.activity.ActivityRuleConfig;
import ph.yckj.admin.activity.handler.AbstractActivityHandler;
import sy.hoodle.base.common.dao.PlatformAgentPlayerReportDao;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.PlatformActivityConfig;
import sy.hoodle.base.common.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动计算基础处理器
 */
@Slf4j
public abstract class BaseActivityCalculationHandler<T extends ActivityRuleConfig> extends AbstractActivityHandler<T> {
    @Autowired
    private PlatformAgentPlayerReportDao platformAgentPlayerReportDao;

    @Override
    protected String getHandlerName() {
        return "活动计算";
    }

    @Override
    protected void doHandle(ActivityContext<T> context) {
        log.info("开始进行活动相关计算，活动编码: {}", context.getActivityConfig().getCode());
        List<ActivityPlayerAmount> playerAmounts = calculateAmount(context);
        if(playerAmounts == null) {
            return;
        }
        context.setPlayerAmounts(playerAmounts);
        log.info("活动计算完成");
    }

    /**
     * 计算活动金额 - 抽象方法，必须由子类实现
     * 因为不同活动的计算逻辑差异很大
     */
    protected abstract List<ActivityPlayerAmount> calculateAmount(ActivityContext<T> context);




    /**
     * 根据当前时间和结算周期类型计算结算周期的起始时间，并获取用户投注数据
     * @param config 活动配置
     * @param playerInfo 用户信息
     * @return 用户投注数据
     */
    protected ActivityPlayerAmount getPlayerAmountBySettlementCycle(PlatformActivityConfig config, AgentPlayerInfo playerInfo) {
        // 获取结算周期的起始时间和结束时间
        Map<String, Date> dateRange = getSettlementDateRange(config);
        if (dateRange == null) {
            log.warn("无法计算结算周期时间范围，cycleScope: {}", config.getCycleScope());
            return null;
        }

        Date startDate = dateRange.get("startDate");
        Date endDate = dateRange.get("endDate");

        log.info("结算周期: {} - {}", startDate, endDate);

        // 调用现有的getPlayerAmountByTargetType方法
        return getPlayerAmountByTargetType(config, playerInfo, startDate, endDate);
    }

    /**
     * 根据结算周期类型计算起始时间和结束时间
     * @param config 活动配置
     * @return 包含startDate和endDate的Map
     */
    protected Map<String, Date> getSettlementDateRange(PlatformActivityConfig config) {
        Integer cycleScope = config.getCycleScope();
        if (cycleScope == null) {
            return null;
        }

        LocalDate today = LocalDate.now();
        Date startDate;
        Date endDate;

        switch (cycleScope) {
            case 0: // 活动期间：活动开始至活动结束
                startDate = config.getStartTime();
                endDate = config.getFinishTime();
                if (startDate == null || endDate == null) {
                    log.warn("活动期间结算但活动开始或结束时间为空");
                    return null;
                }
                break;

            case 1: // 日：每日清算，前一日的0点至24点
                LocalDate yesterday = today.minusDays(1);
                startDate = Date.from(yesterday.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
                endDate = Date.from(yesterday.atTime(23, 59, 59).atZone(ZoneId.of("Asia/Shanghai")).toInstant());
                break;

            case 2: // 周：每周清算，前一周的周一至周日
                LocalDate weekStart = today.minusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                LocalDate weekEnd = today.minusWeeks(1).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                startDate = Date.from(weekStart.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
                endDate = Date.from(weekEnd.atTime(23, 59, 59).atZone(ZoneId.of("Asia/Shanghai")).toInstant());
                break;

            case 3: // 半月：每月1日和16日清算
                LocalDate halfMonthStart;
                LocalDate halfMonthEnd;
                if (today.getDayOfMonth() >= 16) {
                    // 当前是16号及以后，结算周期是本月1号到15号
                    halfMonthStart = today.withDayOfMonth(1);
                    halfMonthEnd = today.withDayOfMonth(15);
                } else {
                    // 当前是15号及以前，结算周期是上个月16号到上个月月底
                    LocalDate lastMonth = today.minusMonths(1);
                    halfMonthStart = lastMonth.withDayOfMonth(16);
                    halfMonthEnd = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
                }
                startDate = Date.from(halfMonthStart.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
                endDate = Date.from(halfMonthEnd.atTime(23, 59, 59).atZone(ZoneId.of("Asia/Shanghai")).toInstant());
                break;

            case 4: // 月：每月清算，上一月1日至月底
                LocalDate lastMonth = today.minusMonths(1);
                LocalDate monthStart = lastMonth.withDayOfMonth(1);
                LocalDate monthEnd = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
                startDate = Date.from(monthStart.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
                endDate = Date.from(monthEnd.atTime(23, 59, 59).atZone(ZoneId.of("Asia/Shanghai")).toInstant());
                break;

            default:
                log.warn("不支持的结算周期类型: {}", cycleScope);
                return null;
        }

        // 构建返回结果
        Map<String, Date> dateRange = new HashMap<>();
        dateRange.put("startDate", startDate);
        dateRange.put("endDate", endDate);

        return dateRange;
    }








    /**
    /**
    /**
     * 根据计算对象类型获取用户投注数据
     */
    protected ActivityPlayerAmount getPlayerAmountByTargetType(PlatformActivityConfig config,
                                                       AgentPlayerInfo playerInfo,
                                                       Date startDate, Date endDate) {
        Integer targetType = config.getTargetType();

        switch (targetType) {
            case 1: // 本人
                return getPlayerAmount(playerInfo, startDate, endDate, true, true);
            case 2: // 团队(包含本人)
                return getPlayerAmount(playerInfo, startDate, endDate, false, true);
            case 3: // 团队(不含本人)
                return getPlayerAmount(playerInfo, startDate, endDate, false, false);
            default:
                return null;
        }
    }

    /**
     * 获取用户投注数据
     */
    protected ActivityPlayerAmount getPlayerAmount(AgentPlayerInfo playerInfo, Date startDate, Date endDate,
                                   boolean selfOnly, boolean includeSelf) {
        try {
            // 1. 获取原始数据
            Object[] teamTotalInfo = getTeamTotalData(playerInfo, startDate, endDate, selfOnly, includeSelf);

            // 2. 解析并构建PlayerAmount对象
            return buildPlayerAmount(playerInfo, teamTotalInfo);

        } catch (Exception e) {
            log.error("获取用户 {} 投注数据时发生错误: {}", playerInfo.getPlayerName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取团队总数据
     */
    private Object[] getTeamTotalData(AgentPlayerInfo playerInfo, Date startDate, Date endDate,
                                boolean selfOnly, boolean includeSelf) {
        if (selfOnly) {
            // 只查询本人数据
            return platformAgentPlayerReportDao.getSelfTotalInfo(
                playerInfo.getAgentNo(), playerInfo.getPlayerId(), startDate, endDate);
        } else {
            // 查询团队数据
            return platformAgentPlayerReportDao.getTeamTotalInfo(
                playerInfo.getAgentNo(), playerInfo.getPlayerId(), startDate, endDate, includeSelf);
        }
    }

    /**
     * 构建ActivityPlayerAmount对象
     */
    private ActivityPlayerAmount buildPlayerAmount(AgentPlayerInfo playerInfo, Object[] teamTotalInfo) {
        ActivityPlayerAmount playerAmount = new ActivityPlayerAmount();

        if (teamTotalInfo != null) {
            // 解析数据
            BigDecimal rechargeAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[0]));
            BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[2]));
            BigDecimal bonusAmount = BigDecimal.valueOf(myutil.ObjectUtils.toDouble(teamTotalInfo[3]));
            BigDecimal rebateAmount = BigDecimal.valueOf(myutil.ObjectUtils.toDouble(teamTotalInfo[4]));
            BigDecimal activityAmount = BigDecimal.valueOf(myutil.ObjectUtils.toDouble(teamTotalInfo[5]));
            BigDecimal salaryAmount = BigDecimal.valueOf(myutil.ObjectUtils.toDouble(teamTotalInfo[6]));
            BigDecimal divsAmount = BigDecimal.valueOf(myutil.ObjectUtils.toDouble(teamTotalInfo[7]));
            BigDecimal lossAmount = BigDecimal.valueOf(ObjectUtils.toDouble(teamTotalInfo[8]));

            // 设置数据
            playerAmount.setRechargeAmount(rechargeAmount);
            playerAmount.setBetAmount(betAmount);
            playerAmount.setBonusAmount(bonusAmount);
            playerAmount.setRebateAmount(rebateAmount);
            playerAmount.setActivityAmount(activityAmount);
            playerAmount.setSalaryAmount(salaryAmount);
            playerAmount.setDivsAmount(divsAmount);
            playerAmount.setLossAmount(lossAmount);
            playerAmount.setPlayerId(playerInfo.getPlayerId());

            log.debug("用户 {} 数据: ", playerAmount);
        } else {
           return null;
        }

        return playerAmount;
    }
}