package ph.yckj.admin.activity.handler.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.admin.activity.ActivityContext;
import sy.hoodle.base.common.dao.PlatformActivityRewardRecordDao;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.PlatformActivityRewardRecord;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;
import myutil.DateUtils;
import myutil.RandomUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PayMoneyAndRecordService {

    @Autowired
    private PlatformActivityRewardRecordDao platformActivityRewardRecordDao;

    @Autowired
    private PlatformAgentPlayerCommonService platformAgentPlayerCommonService;

    /**
     * 用户活动金额放发（变更余额，账单写入）、活动记录写入、用户报表更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void payMoneyAndRecord(ActivityContext<?> context) {
        log.info("使用默认用户活动金额发放逻辑");
        List<AgentPlayerInfo> playerInfos = context.getAgentPlayerInfos();
        List<PlatformActivityRewardRecord> platformActivityRewardRecords = context.getPlatformActivityRewardRecords();
        Map<Long, PlatformActivityRewardRecord> map = new HashMap<>();
        for (PlatformActivityRewardRecord record : platformActivityRewardRecords) {
            map.put(record.getPlayerId(), record);
        }

        for (AgentPlayerInfo playerInfo : playerInfos) {
            PlatformActivityRewardRecord record = map.get(playerInfo.getPlayerId());
            //活动记录写入
            if (record != null) {
                platformActivityRewardRecordDao.save(record);
            } else {
                continue;
            }

            String billno = RandomUtils.fromTime16();
            String remark = context.getActivityConfig().getActivityTitle() + " " + DateUtils.getDateString(record.getActivityDate());
            // 给目标中心账户加钱
            boolean updateBalance = platformAgentPlayerCommonService.updateAvailableBalanceForGameIn(playerInfo.getPlayerId(),
                    record.getAmount(), record.getAmount().negate(), AgentPlayerAccountBill.BILL_TYPE_ACTIVITY,
                    billno, record.getPlayerName(), remark);
            if (!updateBalance) {
                log.info("doDraw活动==={}==={}===发放奖励失败", context.getActivityConfig().getCode(), playerInfo.getPlayerName());
                throw new IllegalArgumentException();
            }

        }
    }
}
