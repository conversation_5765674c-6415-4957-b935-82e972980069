package ph.yckj.admin.activity.handler.base;

import lombok.extern.slf4j.Slf4j;
import myutil.DateUtils;
import myutil.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.admin.activity.ActivityContext;
import ph.yckj.admin.activity.ActivityRuleConfig;
import ph.yckj.admin.activity.handler.AbstractActivityHandler;
import sy.hoodle.base.common.dao.PlatformActivityRewardRecordDao;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.PlatformActivityRewardRecord;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 金额发放基础处理器
 */
@Slf4j
@Service
@Scope("prototype")
public class BasePayMoneyAndRecordHandler<T extends ActivityRuleConfig> extends AbstractActivityHandler<T> {

    @Autowired
    private PayMoneyAndRecordService payMoneyAndRecordService;


    @Override
    protected String getHandlerName() {
        return "用户活动金额发放";
    }
    
    @Override
    protected void doHandle(ActivityContext<T> context) {
        log.info("开始发放用户活动金额，活动编码: {}",
                context.getActivityConfig().getCode());

        payMoneyAndRecordService.payMoneyAndRecord(context);
        
        log.info("用户活动金额发放完成");
    }
}