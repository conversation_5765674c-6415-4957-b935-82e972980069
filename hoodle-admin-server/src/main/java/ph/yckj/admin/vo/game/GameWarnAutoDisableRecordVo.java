package ph.yckj.admin.vo.game;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import sy.hoodle.base.common.entity.GameWarnAutoDisableRecord;
import sy.hoodle.base.common.entity.GameWarnRecord;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameWarnAutoDisableRecordVo {

    private Long id;
    private Long configId;
    private String disableLottery;      // 禁用彩种
    private String disableGameplay;     // 禁用玩法
    private BigDecimal autoDisableLossAmount;  // 自动禁用亏损量
    private Timestamp disableTime;
    private String methodName;
    private int isDelete;
    public GameWarnAutoDisableRecordVo(GameWarnAutoDisableRecord bean) {
        super();
        BeanUtils.copyProperties(bean, this);
    }

}
