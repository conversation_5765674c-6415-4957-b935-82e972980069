package ph.yckj.admin.vo.agent;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import ph.yckj.admin.service.ToolsService;
import sy.hoodle.base.common.entity.AgentPlayerInfo;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentPlayerInfoVo {

	private Map<String, Object> totalQueryInfo;

	private Long playerId;

	private String agentNo;

	private Integer agentType;

	private String agentName;

	private String playerName;

	private Integer playerType;

	private Integer lotteryCode;

	/**
	 * 用户类型
	 */
	private String accountType;
	/**
	 * 用户类型名称
	 */
	private String accountTypeName;

	private String playerNick;

	private String playerPassword;

	private String withdrawPassword;

	private BigDecimal withdrawLimitBalance;

	private String withdrawName;

	private Long playerTelegramId;

	private BigDecimal playerAvailableBalance;
	
	private BigDecimal playerAvailableWithdrawBalance;

	private BigDecimal playerBlockedBalance;

	private String device;

	private Integer markersStatus;

	private Integer playerStatus;

	private Integer onlineStatus;

	private Integer playerLevelId;

	private Integer playerWealth;

	private Integer languageId;

	private Integer skinId;

	private Integer musicId;

	private Timestamp lastLoginTime;

	private Integer isDelete;

	private String createBy;

	private Timestamp createTime;

	private Timestamp updateTime;

	private String sessionId;

	private String urlAuthKey;

	private Integer googleLogin;

	private Integer googleBind;

	private String googleKey;

	private Timestamp lockTime;

	private Integer withdrawLimit;

	private Long pid;
	/**
	 * 上级用户名
	 */
	private String upPlayerName;

	private String pids;
	/**
	 * 上级代理
	 */
	private String upPlayerNames;

	private String email;

	private String telephone;

	/**
	 * 返点
	 */
	private Double point;
	/**
	 * 同级开号，0-否，1-是
	 */
	private Integer equalLevel;
	/**
	 * 上下级转账，0-关闭，1-都开启，2-仅开启给下级转账，3-仅开启给上级转账
	 */
	private Integer allowTransfer;

	// 上级给下级下分
	private Boolean applyUpDedTransfer;

	private Integer loginFailedNum;

	private Integer paymentPasswordFailedNum;

	public AgentPlayerInfoVo(AgentPlayerInfo bean, ToolsService toolsService) {
		super();
		BeanUtils.copyProperties(bean, this);
		if (!StringUtils.isEmpty(email)) {
			email = toolsService.decrypt(email);
			if (!StringUtils.isEmpty(email)) {
				int maskLength = 3;
				String maskUsername = "";
				int atIndex = email.lastIndexOf("@");
				String domain = email.substring(atIndex);
				String username = email.substring(0, atIndex);
				if (username.length() <= maskLength * 2) {
					maskUsername = username.replaceAll(".", "*"); // 如果长度小于等于掩码长度，全部掩码
				} else {
					maskUsername = username.substring(0, maskLength)
							+ String.join("", Collections.nCopies(username.length() - 2 * maskLength, "*"))
							+ username.substring(username.length() - maskLength); // 掩码中间部分，保留首尾部分
				}
				this.email = maskUsername + domain;
			}
		}
		if (!StringUtils.isEmpty(telephone)) {
			telephone = toolsService.decrypt(telephone);
			if (!StringUtils.isEmpty(telephone)) {
				StringBuffer sb = new StringBuffer();
				sb.append(telephone.substring(0, 3));
				sb.append("****");
				sb.append(telephone.substring(telephone.length() - 4));
				this.telephone = sb.toString();
			}
		}
	}

}
