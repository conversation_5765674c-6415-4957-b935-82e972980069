package ph.yckj.admin.vo.game;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import sy.hoodle.base.common.entity.GameWarnConfigInfo;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameWarnConfigInfoVo {

    private Long id;

    private String agentNo;

    private String agentName;

    /** 预警亏损量 */
    private BigDecimal warnLossAmount;

    /** 自动禁用亏损量 */
    private BigDecimal warnDisableLossVolume;

    /** 计量方式:1,彩种,2,玩法 */
    private String measurementWay;

    /** 预警记录方式: 1,共有记录,2,独立记录 */
    private String warnWay;

    /** 自动禁用方式: 1,禁用彩种,2,禁用玩法 */
    private String automaticDisableWay;

    /** 预警可清除: 1,是,2,否 */
    private String warnClear;

    /**状态：1,启用, 2,禁用*/
    private String warnStatus;

    /** 备注 */
    private String warnRemark;

    private String isDelete;

    public GameWarnConfigInfoVo(GameWarnConfigInfo bean) {
        super();
        BeanUtils.copyProperties(bean, this);
    }

}
