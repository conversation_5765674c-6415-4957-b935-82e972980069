package ph.yckj.admin.vo.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import ph.yckj.admin.vo.GameSimulationTeamReportVO;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlayerReportVo {

    private String agentNo;

    private String agentName;

    private Long playerId;

    private String playerName;

    private BigDecimal totalTransferInAmount = BigDecimal.ZERO;

    private BigDecimal totalTransferOutAmount = BigDecimal.ZERO;

    private BigDecimal totalBetAmount = BigDecimal.ZERO;

    private BigDecimal totalBonusAmount = BigDecimal.ZERO;

    private BigDecimal totalProfitAmount = BigDecimal.ZERO;

    private BigDecimal totalRebateAmount = BigDecimal.ZERO;

    private BigDecimal totalPumpAmount = BigDecimal.ZERO;

    private BigDecimal totalActivityAmount = BigDecimal.ZERO;

    private BigDecimal totalPointAmount = BigDecimal.ZERO;  //返点金额

    private BigDecimal totalRewardAmount = BigDecimal.ZERO;  //打赏金额

    private BigDecimal playerAvailableBalance = BigDecimal.ZERO;  //玩家彩票余额
    private BigDecimal balance = BigDecimal.ZERO;  //玩家三方余额


    private BigDecimal upAndDownTransfer = BigDecimal.ZERO;  // 上下级转帐
    private BigDecimal totalDivsAmount = BigDecimal.ZERO;         // 分红

    private BigDecimal totalUpAndDownTransferIn = BigDecimal.ZERO;  // 上下级转帐转入
    private BigDecimal totalUpAndDownTransferOut = BigDecimal.ZERO;  // 上下级转帐转出
    private BigDecimal totalRechargeFeeAmount = BigDecimal.ZERO;    // 充值手续费
    private BigDecimal totalWithdrawFeeAmount = BigDecimal.ZERO;     // 取款手续费
    private BigDecimal totalHandlingFee = BigDecimal.ZERO;   // 手续费
    private BigDecimal totalSalaryAmount = BigDecimal.ZERO;   // 分红

    private BigDecimal teamAvailableBalance = BigDecimal.ZERO;   // 分红


    public PlayerReportVo(String agentNo, String agentName, Long playerId, String playerName,
                          BigDecimal totalTransferInAmount, BigDecimal totalTransferOutAmount,
                          BigDecimal totalBetAmount, BigDecimal totalBonusAmount,
                          BigDecimal totalProfitAmount, BigDecimal totalRebateAmount,
                          BigDecimal totalPumpAmount, BigDecimal totalActivityAmount,
                          BigDecimal totalPointAmount, BigDecimal totalRewardAmount,
                          BigDecimal totalDivsAmount, BigDecimal totalUpAndDownTransferIn,
                          BigDecimal totalUpAndDownTransferOut, BigDecimal totalRechargeFeeAmount, BigDecimal totalWithdrawFeeAmount) {
        this.agentNo = agentNo;
        this.agentName = agentName;
        this.playerId = playerId;
        this.playerName = playerName;
        this.totalTransferInAmount = totalTransferInAmount;
        this.totalTransferOutAmount = totalTransferOutAmount;
        this.totalBetAmount = totalBetAmount;
        this.totalBonusAmount = totalBonusAmount;
        this.totalProfitAmount = totalProfitAmount;
        this.totalRebateAmount = totalRebateAmount;
        this.totalPumpAmount = totalPumpAmount;
        this.totalActivityAmount = totalActivityAmount;
        this.totalPointAmount = totalPointAmount;
        this.totalRewardAmount = totalRewardAmount;
        this.totalDivsAmount = totalDivsAmount;
        this.totalUpAndDownTransferIn = totalUpAndDownTransferIn;
        this.totalUpAndDownTransferOut = totalUpAndDownTransferOut;
        this.totalRechargeFeeAmount = totalRechargeFeeAmount;
        this.totalWithdrawFeeAmount = totalWithdrawFeeAmount;
    }

    public PlayerReportVo(String agentNo, String agentName, Long playerId, String playerName,
                          BigDecimal totalTransferInAmount, BigDecimal totalTransferOutAmount,
                          BigDecimal totalBetAmount, BigDecimal totalBonusAmount,
                          BigDecimal totalProfitAmount, BigDecimal totalRebateAmount,
                          BigDecimal totalPumpAmount, BigDecimal totalActivityAmount,
                          BigDecimal totalPointAmount, BigDecimal totalRewardAmount) {
        this.agentNo = agentNo;
        this.agentName = agentName;
        this.playerId = playerId;
        this.playerName = playerName;
        this.totalTransferInAmount = totalTransferInAmount;
        this.totalTransferOutAmount = totalTransferOutAmount;
        this.totalBetAmount = totalBetAmount;
        this.totalBonusAmount = totalBonusAmount;
        this.totalProfitAmount = totalProfitAmount;
        this.totalRebateAmount = totalRebateAmount;
        this.totalPumpAmount = totalPumpAmount;
        this.totalActivityAmount = totalActivityAmount;
        this.totalPointAmount = totalPointAmount;
        this.totalRewardAmount = totalRewardAmount;
    }

}
