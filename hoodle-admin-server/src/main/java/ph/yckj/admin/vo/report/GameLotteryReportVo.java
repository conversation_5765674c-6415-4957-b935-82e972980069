package ph.yckj.admin.vo.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameLotteryReportVo {

    private String gameTypeCode;

    private String gameTypeName;

    private String lottery;

    private String gameName;

    private String agentNo;

    private String agentName;

    private BigDecimal totalBetAmount;

    private BigDecimal totalBonusAmount;

    private BigDecimal totalProfitAmount;

    private BigDecimal totalRebateAmount;

    private BigDecimal totalPumpAmount;

//    private BigDecimal totalActivityAmount;

    public GameLotteryReportVo(String gameTypeCode, String lottery, BigDecimal totalBetAmount, BigDecimal totalBonusAmount, BigDecimal totalRebateAmount, BigDecimal totalPumpAmount, BigDecimal totalProfitAmount) {
        this.gameTypeCode = gameTypeCode;
        this.lottery = lottery;
        this.totalBetAmount = totalBetAmount;   // 投注
        this.totalBonusAmount = totalBonusAmount;  // 派奖
        this.totalRebateAmount = totalRebateAmount;    // 返点
        this.totalPumpAmount = totalPumpAmount; // 抽水
        this.totalProfitAmount = totalProfitAmount;  // 盈亏
    }

    public GameLotteryReportVo(String agentNo, String agentName, String gameTypeCode, String lottery, BigDecimal totalBetAmount, BigDecimal totalBonusAmount, BigDecimal totalProfitAmount, BigDecimal totalRebateAmount, BigDecimal totalPumpAmount) {
        this.agentNo = agentNo;
        this.agentName = agentName;
        this.gameTypeCode = gameTypeCode;
        this.lottery = lottery;
        this.totalBetAmount = totalBetAmount;       // 投注
        this.totalBonusAmount = totalBonusAmount;       // 派奖
        this.totalProfitAmount = totalProfitAmount;     // 盈亏
        this.totalRebateAmount = totalRebateAmount;     // 返点
        this.totalPumpAmount = totalPumpAmount;     // 抽水
    }
}
