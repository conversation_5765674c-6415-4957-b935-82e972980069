package ph.yckj.admin.vo.game;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import sy.hoodle.base.common.dao.AgentGameLotteryInfoDao;
import sy.hoodle.base.common.entity.AgentGameLotteryInfo;
import sy.hoodle.base.common.entity.LockAccountRecord;

@Setter
@Getter
public class LockAccountRecordVo {
    private LockAccountRecord bean;
    /**
     * 彩种名称
     */
    private String lotteryName;
    /**
     * 用户名
     */
    private String playerName;
    /**
     * 期号
     */
    private String issue;

    public LockAccountRecordVo(LockAccountRecord entity, AgentGameLotteryInfoDao agentGameLotteryInfoDao) {
        this.bean = entity;
        String lotteryList = "全部";
        this.playerName = "全部";
        this.issue = "全部";
        if(StringUtils.isNotBlank(entity.getLottery())) {
            String[] lotteryLists = entity.getLottery().split(",");
            for(String lottery : lotteryLists) {
                // 名单上才显示
                AgentGameLotteryInfo gameLotteryInfo = agentGameLotteryInfoDao.getByAgentNoAndLottery(
                        entity.getAgentNo(), lottery);
                if(gameLotteryInfo != null) {
                    String gameName = gameLotteryInfo.getGameName();
                    if(StringUtils.isNotBlank(lotteryList) && !"全部".equals(lotteryList)) {
                        lotteryList += "," + gameName;
                    }else {
                        lotteryList = gameName;
                    }
                }
            }
        }
        if(StringUtils.isNotBlank(entity.getPlayerNameList())) {
            this.playerName = entity.getPlayerNameList();
        }
        if(StringUtils.isNotBlank(entity.getIssue())) {
            this.issue = entity.getIssue();
        }
        this.lotteryName = lotteryList;
    }
}
