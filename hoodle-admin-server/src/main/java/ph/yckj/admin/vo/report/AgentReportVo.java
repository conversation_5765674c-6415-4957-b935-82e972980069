package ph.yckj.admin.vo.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentReportVo {


    private String agentNo;

    private String agentName;

    private BigDecimal totalTransferInAmount;

    private BigDecimal totalTransferOutAmount;

    private BigDecimal totalBetAmount;

    private BigDecimal totalBonusAmount;

    private BigDecimal totalProfitAmount;

    private BigDecimal totalRebateAmount;

    private BigDecimal totalPumpAmount;

    private BigDecimal totalActivityAmount;

    private BigDecimal totalPointAmount;

    private BigDecimal totalRewardAmount;

    private BigDecimal agentAvailableBalance = BigDecimal.ZERO;  //平台代理彩票余额
    private BigDecimal agentBalance = BigDecimal.ZERO;  //平台代理三方余额

    private BigDecimal teamAvailableBalance = BigDecimal.ZERO;

    public AgentReportVo(String agentNo, String agentName, BigDecimal totalTransferInAmount,
                         BigDecimal totalTransferOutAmount, BigDecimal totalBetAmount,
                         BigDecimal totalBonusAmount, BigDecimal totalProfitAmount,
                         BigDecimal totalRebateAmount, BigDecimal totalPumpAmount,
                         BigDecimal totalActivityAmount, BigDecimal totalPointAmount,
                         BigDecimal totalRewardAmount) {
        this.agentNo = agentNo;
        this.agentName = agentName;
        this.totalTransferInAmount = totalTransferInAmount;
        this.totalTransferOutAmount = totalTransferOutAmount;
        this.totalBetAmount = totalBetAmount;
        this.totalBonusAmount = totalBonusAmount;
        this.totalProfitAmount = totalProfitAmount;
        this.totalRebateAmount = totalRebateAmount;
        this.totalPumpAmount = totalPumpAmount;
        this.totalActivityAmount = totalActivityAmount;
        this.totalPointAmount = totalPointAmount;
        this.totalRewardAmount = totalRewardAmount;
    }
}
