package ph.yckj.admin.vo.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlayerTeamReportVo {

    private String agentNo;

    private String agentName;

    private Long playerId;

    private String playerName;

    /**
     * 直接下级人数
     */
    private int downCount = 0;

    /**
     * 上级玩家链路
     */
    private List<String> pidsAndPlayerNames;

    /**
     * 上级用户名
     */
    private String upPlayerName;

    private BigDecimal totalTransferInAmount = BigDecimal.ZERO;

    private BigDecimal totalTransferOutAmount = BigDecimal.ZERO;

    private BigDecimal totalBetAmount = BigDecimal.ZERO;


    private BigDecimal totalBonusAmount = BigDecimal.ZERO;

    private BigDecimal totalProfitAmount = BigDecimal.ZERO;

    private BigDecimal totalRebateAmount = BigDecimal.ZERO;

    private BigDecimal totalPumpAmount = BigDecimal.ZERO;

    private BigDecimal totalActivityAmount = BigDecimal.ZERO;

    private BigDecimal totalRewardAmount = BigDecimal.ZERO;  //打赏金额

    private BigDecimal totalPointAmount = BigDecimal.ZERO;  //返点金额

    private BigDecimal teamAvailableBalance = BigDecimal.ZERO;  //平台团队彩票余额
    private BigDecimal teamBalance = BigDecimal.ZERO;  //平台团队三方余额
    private BigDecimal playerAvailableBalance = BigDecimal.ZERO;
    private Boolean queryNameFlag = false;
    private BigDecimal totalHandlingFee = BigDecimal.ZERO;
    private BigDecimal totalSalaryAmount =  BigDecimal.ZERO;
    private BigDecimal totalDivsAmount =  BigDecimal.ZERO;
    private BigDecimal totalPlayerTransInAmount =  BigDecimal.ZERO;
    private BigDecimal totalPlayerTransOutAmount =  BigDecimal.ZERO;

    public void addEntity(PlayerTeamReportVo entity) {
        this.totalTransferInAmount = this.totalTransferInAmount.add(entity.getTotalTransferInAmount());
        this.totalTransferOutAmount = this.totalTransferOutAmount.add(entity.getTotalTransferOutAmount());
        this.totalBetAmount = this.totalBetAmount.add(entity.getTotalBetAmount());
        this.totalBonusAmount = this.totalBonusAmount.add(entity.getTotalBonusAmount());
        this.totalProfitAmount = this.totalProfitAmount.add(entity.getTotalProfitAmount());
        this.totalRebateAmount = this.totalRebateAmount.add(entity.getTotalRebateAmount());
        this.totalPumpAmount = this.totalPumpAmount.add(entity.getTotalPumpAmount());
        this.totalActivityAmount = this.totalActivityAmount.add(entity.getTotalActivityAmount());
        this.totalRewardAmount = this.totalRewardAmount.add(entity.getTotalRewardAmount());
        this.totalPointAmount = this.totalPointAmount.add(entity.getTotalPointAmount());
    }
}
