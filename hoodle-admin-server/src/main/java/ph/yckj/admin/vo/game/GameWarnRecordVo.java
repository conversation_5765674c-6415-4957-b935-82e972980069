package ph.yckj.admin.vo.game;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import sy.hoodle.base.common.entity.GameWarnConfigInfo;
import sy.hoodle.base.common.entity.GameWarnRecord;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameWarnRecordVo {

    private Long id;
    private Long configId;
    private String lotteryGameplay;  // 彩种玩法
    private BigDecimal warnLossAmount;  // 预警亏损量
    private Timestamp lastWarnTime; // 最后预警时间
    private String status;
    private int isDelete;

    public GameWarnRecordVo(GameWarnRecord bean) {
        super();
        BeanUtils.copyProperties(bean, this);
    }


}
