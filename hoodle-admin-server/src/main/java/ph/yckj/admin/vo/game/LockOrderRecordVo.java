package ph.yckj.admin.vo.game;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;
import sy.hoodle.base.common.dao.AgentGameLotteryInfoDao;
import sy.hoodle.base.common.dao.GameLotteryMethodDao;
import sy.hoodle.base.common.entity.AgentGameLotteryInfo;
import sy.hoodle.base.common.entity.GameLotteryMethod;
import sy.hoodle.base.common.entity.LockOrderRecord;

@Setter
@Getter
public class LockOrderRecordVo {

    private LockOrderRecord bean;
    /**
     * 彩种名称
     */
    private String lotteryName;
    /**
     * 玩法名称
     */
    private String methodName;

    public LockOrderRecordVo(LockOrderRecord entity, AgentGameLotteryInfoDao agentGameLotteryInfoDao,
            GameLotteryMethodDao gameLotteryMethodDao) {
        this.bean = entity;
        if(!StringUtils.isEmpty(bean.getContent()) && bean.getContent().length() > 20) {
            bean.setContent(bean.getContent().substring(0, 17) + "...");
        }

        AgentGameLotteryInfo gameLotteryInfo = agentGameLotteryInfoDao.getByAgentNoAndLottery(entity.getAgentNo(),
                entity.getLottery());
        if(gameLotteryInfo != null) {
            this.lotteryName = gameLotteryInfo.getGameName();
            GameLotteryMethod gameLotteryMethod = gameLotteryMethodDao.getByGameTypeCodeAndMethodCode(
                    gameLotteryInfo.getGameTypeCode(), entity.getMethod());
            if(gameLotteryMethod != null) {
                this.methodName = gameLotteryMethod.getMethodName();
            }
        }else {
            this.lotteryName = entity.getLottery();
            this.methodName = entity.getMethod();
        }
    }
}
