package ph.yckj.admin.web.ctrl;

import myutil.CipherUtils;
import myutil.ErrorUtils;
import myutil.HttpUtils;
import myutil.Moment;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.util.StrUtils;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import sy.hoodle.base.common.entity.AgentInfo;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;
import sy.hoodle.base.common.dao.GameWarnRecordDao;
import org.springframework.beans.factory.annotation.Autowired;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 主入口处理类
 */
@RestController
@RequestMapping(Route.PATH)
public class MainController extends SuperController {

	private final static Log log = LogFactory.getLog(MainController.class);

	@Autowired
	private GameWarnRecordDao gameWarnRecordDao;

	public GameWarnRecordDao getGameWarnRecordDao() {
		return gameWarnRecordDao;
	}
	@GetMapping(value = "/status")
	public String success() {
		return "success";
	}

	@RequestMapping(Route.METRICS)
	public WebJson METRICS(HttpServletRequest request, HttpServletResponse response) {
		WebJson webJson = new WebJson();
		try {
			request.getRequestDispatcher(Route.METRICS).forward(request, response);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return webJson;
	}

	/**
	 * 用户登录
	 * 
	 * @param request
	 * @param username 用户名
	 * @param password 密码
	 * @return
	 */
	@RequestMapping(Route.LOGIN)
	@ResponseBody
	public WebJson login(HttpServletRequest request, @RequestParam(name = "username") String username,
			@RequestParam(name = "password") String password,
			@RequestParam(name = "googleKey", required = false) Integer googleKey,
			@RequestParam(name = "isBindStep", defaultValue = "false") boolean isBindStep) {
		WebJson webJson = new WebJson();
		try {
			if (StrUtils.isContainChinese(password)) {
				throw newException("104-03");
			}
			if (isLogin(request)) {
				throw newException("0-2");
			}
			String agentNo = "";
			AgentInfo agent = getAgent(request);
			if (null != agent) {
				agentNo = agent.getAgentNo();
			}
			AdminAccount adminAccount = getAdminAccountDao().getByUsername(agentNo, username);
			if (adminAccount == null) {
				throw newException("100-01");
			}
//			if(!StringUtils.isEmpty(adminAccount.getAgentNo())) {
//				throw newException("100-01");
//			}
			if (adminAccount.getStatus() != AdminAccount.STATUS_NORMAL) {
				throw newException("0-5");
			}
			// 密码验证
			boolean verifyPassword = CipherUtils.verify(adminAccount.getLoginPassword(), password);
			if (!verifyPassword) {
				throw newException("102-01");
			}
			// Google验证
			if (adminAccount.getGoogleLogin()) {
				// 如果没有绑定谷歌验证
				if (!adminAccount.getGoogleBind()) {
					// 如果不是绑定步骤
					if (!isBindStep) {
						String googleUrl = getAdminService().createGoogleAuth(adminAccount);
						Map<String, Object> data = new HashMap<String, Object>();
						data.put("googleUrl", googleUrl);
						data.put("notBindGoogleAuth", true);
						webJson.setData(data);
						setSuccess(webJson);
						return webJson;
					}
				}
				if (StringUtils.isEmpty(googleKey)) {
					Map<String, Object> data = new HashMap<String, Object>();
					data.put("notInputGoogleKey", true);
					webJson.setData(data);
					setSuccess(webJson);
					return webJson;
				}
			}
			String ip = HttpUtils.getRemoteAddr(request);
			String sessionId = HttpUtils.formatSessionId(request);
			String userAgent = request.getHeader("User-Agent");
			String client = HttpUtils.formatClient(userAgent);
			String url = request.getHeader("Referer");
			// 进行登录操作
			boolean result = getAdminService().login(adminAccount, password, googleKey, isBindStep, sessionId, ip,
					client, url);
			if (result) {
				// 保存用户到会话信息
				setSessionUser(request, adminAccount);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("LOGIN 异常", t);
		}
		return webJson;
	}

	/**
	 * 用户退出
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(Route.LOGOUT)
	@ResponseBody
	public WebJson logout(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		logOut(request);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ, skip = true)
	@RequestMapping(Route.TASK_STATUS)
	@ResponseBody
	public WebJson TASK_STATUS(HttpServletRequest request, HttpServletResponse response) {
		WebJson webJson = new WebJson();
		try {
			String agentNo = "";
			AgentInfo agent = getAgent(request);
			if (agent != null) {
				agentNo = agent.getAgentNo();
			}

			HashMap<String, Object> stringObjectHashMap = getAdminService().indexCount(agentNo);


			List<Criterion> criterions = new ArrayList<Criterion>();

			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));

			}

			criterions.add(Restrictions.eq("orderStatus", 0));

			int withdrawCount = getPlayerWithdrawDao().totalCount(criterions);
			int transferCount = 0;
			int thridCount = getPaymentThirdPayDao().getOverload();
			int activityCount = 0;
			int accountLotteryAttentionCount = 0;
			// 亏损预警
//			int lotteryWarningCount = 0;
//			lotteryWarningCount = getGameOrderIssueRecordDao().totalCount(getLotteryWarningCriterions());

			// 亏损预警 - 使用预警记录表
			int lotteryWarningCount = 0;
			try {
				List<Criterion> warnCriterions = new ArrayList<>();
				warnCriterions.add(Restrictions.eq("status", "1")); // 已预警状态
				warnCriterions.add(Restrictions.eq("isDelete", 0));
				lotteryWarningCount = getGameWarnRecordDao().totalCount(warnCriterions);
			} catch (Exception e) {
				log.error("获取预警记录数量失败", e);
				lotteryWarningCount = 0;
			}
			Map<String, Object> data = new HashMap<String, Object>(stringObjectHashMap);
			data.put("withdrawCount", withdrawCount);
			data.put("transferCount", transferCount);
			data.put("thridCount", thridCount);
			data.put("activityCount", activityCount);
			data.put("accountLotteryAttentionCount", accountLotteryAttentionCount);
			data.put("lotteryWarningCount", lotteryWarningCount);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("TASK_STATUS 异常", t);
		}
		return webJson;
	}

	private List<Criterion> getLotteryWarningCriterions() {
		List<Criterion> criterions = new ArrayList<Criterion>();
		Moment moment = new Moment();
		moment = moment.fromDate(moment.toSimpleDate());
		Date sDate = moment.toDate();
		Date eDate = moment.tomorrow().toDate();
		criterions.add(Restrictions.gt("totalProfitAmount", BigDecimal.ZERO));
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));

		return criterions;
	}
}