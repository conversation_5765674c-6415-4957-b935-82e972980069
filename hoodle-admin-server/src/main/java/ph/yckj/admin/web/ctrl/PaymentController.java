package ph.yckj.admin.web.ctrl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import myutil.ErrorUtils;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.enums.CommonStatus;
import ph.yckj.admin.enums.PayTypeEnum;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.payment.PaymentThirdPayVo;
import ph.yckj.admin.vo.payment.PaymentThirdRemitVo;
import ph.yckj.admin.vo.payment.PaymentThirdVo;
import ph.yckj.admin.vo.payment.PaymentTransferVo;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import sy.hoodle.base.common.entity.PaymentBank;
import sy.hoodle.base.common.entity.PaymentThird;
import sy.hoodle.base.common.entity.PaymentThirdPay;
import sy.hoodle.base.common.entity.PaymentThirdRemit;
import sy.hoodle.base.common.entity.PaymentTransfer;
import sy.hoodle.base.common.entity.PlatformConfig;
import sy.hoodle.base.common.entity.PlayerWithdraw;
import sy.hoodle.tool.redis.lock.RedisLock;
import sy.hoodle.tool.redis.utils.ProRedisLockUtil;
import sy.hoodle.tool.redis.utils.RedisLockResult;
import ph.yckj.common.util.RedisKey;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;

@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.Payment.PATH)
public class PaymentController extends SuperController {

	@Autowired
	private ProRedisLockUtil proRedisLockUtil;

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Payment.LIST_PAYMENT_BANK)
	public WebJson LIST_PAYMENT_BANK(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			List<PaymentBank> list = getPaymentBankDao().listAll();
			webJson.setData(list);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Payment.SEARCH_PAYMENT_THIRD)
	public WebJson SEARCH_PAYMENT_THIRD(HttpServletRequest request,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "1000") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();
			if (null != status) {
				criterions.add(Restrictions.eq("status", status));
			}
			List<PaymentThirdVo> list = new ArrayList<PaymentThirdVo>();
			int totalCount = getPaymentThirdDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.desc("id"));
				List<PaymentThird> resultList = getPaymentThirdDao().find(criterions, orders, firstResult, maxResults);
				for (PaymentThird tmpBean : resultList) {
					list.add(new PaymentThirdVo(tmpBean));
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "payUrl") String payUrl, @RequestParam(name = "payBackUrl") String payBackUrl,
			@RequestParam(name = "whiteIps") String whiteIps) {
		WebJson webJson = new WebJson();
		try {
			PaymentThird entity = getPaymentThirdDao().getById(id);
			if (null == entity) {
				throw newException("-1", "该三方支付渠道已不存在。");
			}
			boolean result = getPaymentService().updateThird(entity.getId(), payUrl, payBackUrl, whiteIps);
			if (!result) {
				setFail(webJson);
			} else {
				// 保存操作日志
				String content = "渠道id：" + id + "，支付地址：" + payUrl + "，返回地址：" + payBackUrl + "，通知IP白名单：" + whiteIps;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Payment.SEARCH_PAYMENT_TRANSFER)
	@ResponseBody
	public WebJson SEARCH_PAYMENT_TRANSFER(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
			if (null != status) {
				criterions.add(Restrictions.eq("status", status));
			}
			List<PaymentTransferVo> list = new ArrayList<PaymentTransferVo>();
			int totalCount = getPaymentTransferDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.desc("id"));
				List<PaymentTransfer> resultList = getPaymentTransferDao().find(criterions, orders, firstResult,
						maxResults);
				for (PaymentTransfer tmpBean : resultList) {
					list.add(new PaymentTransferVo(tmpBean));
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	/**
	 * 添加USDT地址
	 *
	 * @param request
	 * @param type
	 * @param agentNo
	 * @param agentName
	 * @param name
	 * @param payType
	 * @param bankName
	 * @param bankBranch
	 * @param bankCardName
	 * @param bankCardAddress
	 * @param base64Code
	 * @param minAmount
	 * @param maxAmount
	 * @param minUnitAmount
	 * @param maxUnitAmount
	 * @param feeRate
	 * @param totalCredits
	 * @param sort
	 * @return
	 */
	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.ADD_PAYMENT_TRANSFER)
	@ResponseBody
	public WebJson ADD_PAYMENT_TRANSFER(HttpServletRequest request, @RequestParam(name = "type") int type,
			@RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "name") String name, @RequestParam(name = "payType") String payType,
			@RequestParam(name = "bankName", defaultValue = "") String bankName,
			@RequestParam(name = "bankBranch", defaultValue = "") String bankBranch,
			@RequestParam(name = "bankCardName", defaultValue = "") String bankCardName,
			@RequestParam(name = "bankCardAddress", required = false) String bankCardAddress,
			@RequestParam(name = "base64Code", defaultValue = "") String base64Code,
			@RequestParam(name = "minAmount") double minAmount, @RequestParam(name = "maxAmount") double maxAmount,
			@RequestParam(name = "minUnitAmount") double minUnitAmount,
			@RequestParam(name = "maxUnitAmount") double maxUnitAmount, @RequestParam(name = "feeRate") double feeRate,
			@RequestParam(name = "totalCredits") double totalCredits, @RequestParam(name = "sort") int sort) {
		WebJson webJson = new WebJson();
		// 加锁
		String usdtAddressCacheKey = RedisKey.getUsdtAddressCacheKey(bankCardAddress);
		RedisLockResult needUnlockTryLock = proRedisLockUtil.needUnlockTryLock(usdtAddressCacheKey, 1_000, 15_000);
		try {
			if (needUnlockTryLock.isTryLock()) {
				// 加锁成功
				List<PaymentTransfer> paymentTransferList = getPaymentTransferDao()
						.getByBankCardAddress(bankCardAddress);
				if (CollectionUtil.isNotEmpty(paymentTransferList)) {
					setFail(webJson);
					webJson.setMessage("地址重复");
					return webJson;
				}
				AdminAccount adminAccount = getSessionUser(request);
				String defaultAgentNo = adminAccount.getAgentNo();
				if (!StringUtils.isEmpty(defaultAgentNo)) {
					agentNo = defaultAgentNo;
				}
				if (!StringUtils.isEmpty(agentNo)) {
					agentName = getRedisService().getAgentInfo(agentNo).getAgentName();
				}
				boolean result = getPaymentService().addTransfer(agentNo, agentName, type, name, payType, bankName,
						bankBranch, bankCardName, bankCardAddress, base64Code, minAmount, maxAmount, minUnitAmount,
						maxUnitAmount, feeRate, totalCredits, sort);
				if (result) {
					// 保存操作日志
					String content = "平台代理商户号：" + agentNo + "，平台代理商名称：" + agentName + "，代收名称：" + name + "，充值类型：USDT充值"
							+ "，支付类型：" + payType + "，收款地址：" + bankCardAddress + "，总额度" + totalCredits + "，会员充值范围："
							+ minAmount + "~" + maxAmount + "，通道充值范围：" + minUnitAmount + "~" + maxUnitAmount
							+ "，手续费（百分比）：" + feeRate + "，排序：" + sort;
					// 设置 ThreadLocal 变量
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				} else {
					setFail(webJson);
				}
			} else {
				// 加锁失败
				setFail(webJson);
				webJson.setMessage("加锁失败");
				return webJson;
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		} finally {
			if (needUnlockTryLock.isTryLock()) {
				proRedisLockUtil.unlock(needUnlockTryLock.getLockKey(), needUnlockTryLock.getLockVal());
			}
		}
		return webJson;
	}

	/**
	 * 更新USDT地址信息
	 *
	 * @param request
	 * @param id
	 * @param type
	 * @param name
	 * @param payType
	 * @param bankName
	 * @param bankBranch
	 * @param bankCardName
	 * @param bankCardAddress
	 * @param base64Code
	 * @param minAmount
	 * @param maxAmount
	 * @param minUnitAmount
	 * @param maxUnitAmount
	 * @param feeRate
	 * @param totalCredits
	 * @param sort
	 * @return
	 */
	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_TRANSFER)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_TRANSFER(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "type") int type, @RequestParam(name = "name") String name,
			@RequestParam(name = "payType") String payType,
			@RequestParam(name = "bankName", defaultValue = "") String bankName,
			@RequestParam(name = "bankBranch", defaultValue = "") String bankBranch,
			@RequestParam(name = "bankCardName", defaultValue = "") String bankCardName,
			@RequestParam(name = "bankCardAddress", required = false) String bankCardAddress,
			@RequestParam(name = "base64Code", defaultValue = "") String base64Code,
			@RequestParam(name = "minAmount") double minAmount, @RequestParam(name = "maxAmount") double maxAmount,
			@RequestParam(name = "minUnitAmount") double minUnitAmount,
			@RequestParam(name = "maxUnitAmount") double maxUnitAmount, @RequestParam(name = "feeRate") double feeRate,
			@RequestParam(name = "totalCredits") double totalCredits, @RequestParam(name = "sort") int sort) {
		WebJson webJson = new WebJson();
		log.info(bankName);
		// 加锁
		String usdtAddressCacheKey = RedisKey.getUsdtAddressCacheKey(bankCardAddress);
		RedisLockResult needUnlockTryLock = proRedisLockUtil.needUnlockTryLock(usdtAddressCacheKey, 1_000, 15_000);
		try {
			if (needUnlockTryLock.isTryLock()) {
				// 加锁成功
				List<PaymentTransfer> paymentTransferList = getPaymentTransferDao()
						.getByBankCardAddress(bankCardAddress);
				if (CollectionUtil.isNotEmpty(paymentTransferList)) {
					if (paymentTransferList.size() == 1) {
						if (!paymentTransferList.get(0).getId().equals(id)) {
							// 更新的地址,和其他地址存在重复
							setFail(webJson);
							webJson.setMessage("地址重复");
							return webJson;
						}
					} else {
						// 存在多个地址,返沪错误
						setFail(webJson);
						webJson.setMessage("地址重复");
						return webJson;
					}
				}
				boolean result = getPaymentService().updateTransfer(id, type, name, payType, bankName, bankBranch,
						bankCardName, bankCardAddress, base64Code, minAmount, maxAmount, minUnitAmount, maxUnitAmount,
						feeRate, totalCredits, sort);
				if (result) {
					// 保存操作日志
					String content = "通道id：" + id + "，代收名称：" + name + "，充值类型：USDT充值" + "，支付类型：" + payType + "，收款地址："
							+ bankCardAddress + "，会员充值范围：" + minAmount + "~" + maxAmount + "，通道充值范围：" + minUnitAmount
							+ "~" + maxUnitAmount + "，排序：" + sort;
					// 设置 ThreadLocal 变量
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				} else {
					setFail(webJson);
				}
			} else {
				// 加锁失败
				setFail(webJson);
				webJson.setMessage("加锁失败");
				return webJson;
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		} finally {
			if (needUnlockTryLock.isTryLock()) {
				proRedisLockUtil.unlock(needUnlockTryLock.getLockKey(), needUnlockTryLock.getLockVal());
			}
		}
		return webJson;
	}


	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.DELETE_PAYMENT_TRANSFER)
	@ResponseBody
	public WebJson DELETE_PAYMENT_TRANSFER(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		try {
			PaymentTransfer entity = getPaymentTransferDao().getById(id);
			boolean result = getPaymentService().deleteTransfer(entity.getId());
			if (result) {
				// 保存操作日志
				String content = "删除" + entity.getName() + "，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.CLEAR_PAYMENT_TRANSFER)
	@ResponseBody
	public WebJson CLEAR_PAYMENT_TRANSFER(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		try {
			PaymentTransfer entity = getPaymentTransferDao().getById(id);
			boolean result = getPaymentService().clearTransferCredits(entity.getId());
			if (result) {
				// 保存操作日志
				String content = "清空" + entity.getName() + "已用额度，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_TRANSFER_STATUS)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_TRANSFER_STATUS(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		try {
			PaymentTransfer entity = getPaymentTransferDao().getById(id);
			boolean result = getPaymentService().updateTransferStatus(entity.getId(), status);
			if (result) {
				// 保存操作日志
				String button = (0 == status) ? "启用" : "禁用";
				String content = button + entity.getName() + "，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Payment.SEARCH_PAYMENT_THIRD_PAY)
	@ResponseBody
	public WebJson SEARCH_PAYMENT_THIRD_PAY(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "thirdId", required = false) Long thirdId,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
			if (null != status) {
				criterions.add(Restrictions.eq("status", status));
			}
			if (null != thirdId && thirdId > 0) {
				criterions.add(Restrictions.eq("thirdId", thirdId));
			}
			List<PaymentThirdPayVo> list = new ArrayList<PaymentThirdPayVo>();
			int totalCount = getPaymentThirdPayDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.desc("id"));
				List<PaymentThirdPay> resultList = getPaymentThirdPayDao().find(criterions, orders, firstResult,
						maxResults);
				for (PaymentThirdPay tmpBean : resultList) {
					list.add(new PaymentThirdPayVo(tmpBean, getRedisService()));
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.ADD_PAYMENT_THIRD_PAY)
	@ResponseBody
	public WebJson ADD_PAYMENT_THIRD_PAY(HttpServletRequest request, @RequestParam(name = "name") String name,
			@RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "merId") String merId, @RequestParam(name = "merSecretKey") String merSecretKey,
			@RequestParam(name = "thirdId") Long thirdId, @RequestParam(name = "totalCredits") double totalCredits,
			@RequestParam(name = "minAmount") double minAmount, @RequestParam(name = "maxAmount") double maxAmount,
			@RequestParam(name = "minUnitAmount") double minUnitAmount,
			@RequestParam(name = "maxUnitAmount") double maxUnitAmount, @RequestParam(name = "feeRate") double feeRate,
			@RequestParam(name = "payWayCode") String payWayCode, @RequestParam(name = "payType") String payType,
			@RequestParam(name = "paySpecific", defaultValue = "") String paySpecific,
			@RequestParam(name = "sort") int sort) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			if (!StringUtils.isEmpty(agentNo)) {
				agentName = getRedisService().getAgentInfo(agentNo).getAgentName();
			}
			boolean result = getPaymentService().addThirdPay(agentNo, agentName, name, thirdId, merId, merSecretKey,
					totalCredits, minAmount, maxAmount, minUnitAmount, maxUnitAmount, feeRate, payType, payWayCode,
					paySpecific, sort);
			if (result) {
				// 保存操作日志
				String content = "平台代理商户号：" + agentNo + "，平台代理商名称：" + agentName + "，名称：" + name + "，渠道id：" + thirdId
						+ "，商户号：" + merId + "，商户号秘钥：" + merSecretKey + "，通道代码：" + payWayCode + "，支付类型：" + payType
						+ "，定额支付金额：" + paySpecific + "，总额度：" + totalCredits + "，会员充值范围：" + minAmount + "~" + maxAmount
						+ "，通道充值范围：" + minUnitAmount + "~" + maxUnitAmount + "，手续费（百分比）：" + feeRate + "，排序：" + sort;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD_PAY)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD_PAY(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "name") String name, @RequestParam(name = "thirdId") Long thirdId,
			@RequestParam(name = "totalCredits") double totalCredits,
			@RequestParam(name = "minAmount") double minAmount, @RequestParam(name = "maxAmount") double maxAmount,
			@RequestParam(name = "minUnitAmount") double minUnitAmount,
			@RequestParam(name = "maxUnitAmount") double maxUnitAmount, @RequestParam(name = "feeRate") double feeRate,
			@RequestParam(name = "payWayCode") String payWayCode, @RequestParam(name = "payType") String payType,
			@RequestParam(name = "paySpecific") String paySpecific, @RequestParam(name = "sort") int sort) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getPaymentService().updateThirdPay(id, name, thirdId, totalCredits, minAmount, maxAmount,
					minUnitAmount, maxUnitAmount, feeRate, payType, payWayCode, paySpecific, sort);
			if (result) {
				// 保存操作日志
				String content = "通道id：" + id + "，通道名称：" + name + "，渠道id：" + thirdId + "，通道代码：" + payWayCode + "，支付类型："
						+ payType + "，定额支付金额：" + paySpecific + "，总额度：" + totalCredits + "，会员充值范围：" + minAmount + "~"
						+ maxAmount + "，通道充值范围：" + minUnitAmount + "~" + maxUnitAmount + "，手续费（百分比）：" + feeRate + "，排序："
						+ sort;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD_PAY_KEY)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD_PAY_KEY(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "merId") String merId, @RequestParam(name = "merSecretKey") String merSecretKey) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getPaymentService().updateThirdPayKey(id, merId, merSecretKey);
			if (result) {
				// 保存操作日志
				String content = "通道id：" + id + "，商户号：" + merId + "，商户号秘钥：" + merSecretKey;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.DELETE_PAYMENT_THIRD_PAY)
	@ResponseBody
	public WebJson DELETE_PAYMENT_THIRD_PAY(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		try {
			PaymentThirdPay entity = getPaymentThirdPayDao().getById(id);
			boolean result = getPaymentService().deleteThirdPay(entity.getId());
			if (result) {
				// 保存操作日志
				String content = "删除" + entity.getName() + "，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.CLEAR_PAYMENT_THIRD_PAY)
	@ResponseBody
	public WebJson CLEAR_PAYMENT_THIRD_PAY(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		try {
			PaymentThirdPay entity = getPaymentThirdPayDao().getById(id);
			boolean result = getPaymentService().clearThirdPayCredits(entity.getId());
			if (result) {
				// 保存操作日志
				String content = "清空" + entity.getName() + "已用额度，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD_PAY_STATUS)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD_PAY_STATUS(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		try {
			PaymentThirdPay entity = getPaymentThirdPayDao().getById(id);
			boolean result = getPaymentService().updateThirdPayStatus(entity.getId(), status);
			if (result) {
				// 保存操作日志
				String button = (0 == status) ? "启用" : "禁用";
				String content = button + entity.getName() + "，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Payment.SEARCH_PAYMENT_THIRD_REMIT)
	@ResponseBody
	public WebJson SEARCH_PAYMENT_THIRD_REMIT(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "thirdId", required = false) Long thirdId,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
			if (null != status) {
				criterions.add(Restrictions.eq("status", status));
			}
			if (null != thirdId && thirdId > 0) {
				criterions.add(Restrictions.eq("thirdId", thirdId));
			}
			List<PaymentThirdRemitVo> list = new ArrayList<PaymentThirdRemitVo>();
			int totalCount = getPaymentThirdRemitDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.desc("id"));
				List<PaymentThirdRemit> resultList = getPaymentThirdRemitDao().find(criterions, orders, firstResult,
						maxResults);
				for (PaymentThirdRemit tmpBean : resultList) {
					list.add(new PaymentThirdRemitVo(tmpBean, getRedisService()));
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.ADD_PAYMENT_THIRD_REMIT)
	@ResponseBody
	public WebJson ADD_PAYMENT_THIRD_REMIT(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "name") String name, @RequestParam(name = "thirdId") Long thirdId,
			@RequestParam(name = "merId") String merId, @RequestParam(name = "merSecretKey") String merSecretKey,
			@RequestParam(name = "remitType") String remitType, @RequestParam(name = "feeRate") double feeRate,
			@RequestParam(name = "minUnitAmount") double minUnitAmount,
			@RequestParam(name = "maxUnitAmount") double maxUnitAmount) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			if (!StringUtils.isEmpty(agentNo)) {
				agentName = getRedisService().getAgentInfo(agentNo).getAgentName();
			}
			boolean result = getPaymentService().addThirdRemit(agentNo, agentName, name, thirdId, merId, merSecretKey,
					remitType, feeRate, minUnitAmount, maxUnitAmount);
			if (result) {
				// 保存操作日志
				String content = "平台代理商户号：" + agentNo + "，平台代理商名称：" + agentName + "，名称：" + name + "，渠道id：" + thirdId
						+ "，商户号：" + merId + "，商户号秘钥：" + merSecretKey + "，付款类型：" + remitType + "，通道充值范围：" + minUnitAmount
						+ "~" + maxUnitAmount + "，手续费（百分比）：" + feeRate;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD_REMIT)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD_REMIT(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "name") String name, @RequestParam(name = "thirdId") Long thirdId,
			@RequestParam(name = "remitType") String remitType, @RequestParam(name = "feeRate") double feeRate,
			@RequestParam(name = "minUnitAmount") double minUnitAmount,
			@RequestParam(name = "maxUnitAmount") double maxUnitAmount) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getPaymentService().updateThirdRemit(id, name, thirdId, remitType, feeRate, minUnitAmount,
					maxUnitAmount);
			if (result) {
				// 保存操作日志
				String content = "通道id：" + id + "，名称：" + name + "，渠道id：" + thirdId + "，付款类型：" + remitType + "，通道充值范围："
						+ minUnitAmount + "~" + maxUnitAmount + "，手续费（百分比）：" + feeRate;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD_REMIT_KEY)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD_REMIT_KEY(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "merId") String merId, @RequestParam(name = "merSecretKey") String merSecretKey) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getPaymentService().updateThirdRemitKey(id, merId, merSecretKey);
			if (result) {
				// 保存操作日志
				String content = "通道id：" + id + "，商户号：" + merId + "，商户秘钥：" + merSecretKey;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_THIRD_REMIT_STATUS)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_THIRD_REMIT_STATUS(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		try {
			PaymentThirdRemit entity = getPaymentThirdRemitDao().getById(id);
			boolean result = getPaymentService().updateThirdRemitStatus(entity.getId(), status);
			if (result) {
				// 保存操作日志
				String desc = CommonStatus.getByCode(status).getDesc();
				String content = desc + entity.getName() + "，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Payment.DELETE_PAYMENT_THIRD_REMIT)
	@ResponseBody
	public WebJson DELETE_PAYMENT_THIRD_REMIT(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		try {
			PaymentThirdRemit entity = getPaymentThirdRemitDao().getById(id);
			boolean result = getPaymentService().deleteThirdRemit(entity.getId());
			if (result) {
				// 保存操作日志
				String content = "删除" + entity.getName() + "，通道id：" + id;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Payment.LIST_PAYMENT_USDT_HUILV)
	@ResponseBody
	public WebJson LIST_PAYMENT_USDT_HUILV(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo) {
		WebJson webJson = new WebJson();
		try {
			double usdtRechargeRate = 0;
			double usdtWithdrawRate = 0;
			double followRechargeAddRate = 0;
			boolean isUsdtRechargeSync = false;
			boolean isUsdtWithdrawSync = false;
			boolean isUsdtRateFollowRecharge = false;
			List<PlatformConfig> list = getPlatformConfigDao().listByGroup(agentNo, "USDT_EXCHANGE");
			for (PlatformConfig config : list) {
				String key = config.getKey();
				String value = config.getValue();
				if ("IS_USDT_RECHARGE_SYNC".equals(key)) {
					isUsdtRechargeSync = Boolean.parseBoolean(value);
				} else if ("IS_USDT_WITHDRAW_SYNC".equals(key)) {
					isUsdtWithdrawSync = Boolean.parseBoolean(value);
				} else if ("IS_USDT_RATE_FOLLOW_RECHARGE".equals(key)) {
					isUsdtRateFollowRecharge = Boolean.parseBoolean(value);
				} else if ("USDT_RECHARGE_RATE".equals(key)) {
					usdtRechargeRate = Double.parseDouble(value);
				} else if ("USDT_WITHDRAW_RATE".equals(key)) {
					usdtWithdrawRate = Double.parseDouble(value);
				} else if ("FOLLOW_RECHARGE_ADD_RATE".equals(key)) {
					followRechargeAddRate = Double.parseDouble(value);
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("usdtRechargeRate", usdtRechargeRate);
			data.put("usdtWithdrawRate", usdtWithdrawRate);
			data.put("followRechargeAddRate", followRechargeAddRate);
			data.put("isUsdtRechargeSync", isUsdtRechargeSync);
			data.put("isUsdtWithdrawSync", isUsdtWithdrawSync);
			data.put("isUsdtRateFollowRecharge", isUsdtRateFollowRecharge);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			log.error("异常", e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("失败", t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Payment.UPDATE_PAYMENT_USDT_HUILV)
	@ResponseBody
	public WebJson UPDATE_PAYMENT_USDT_HUILV(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "usdtRechargeRate", required = true) String usdtRechargeRate,
			@RequestParam(name = "usdtWithdrawRate", required = true) String usdtWithdrawRate,
			@RequestParam(name = "followRechargeAddRate", required = true) String followRechargeAddRate,
			@RequestParam(name = "isUsdtRechargeSync", required = true) boolean isUsdtRechargeSync,
			@RequestParam(name = "isUsdtWithdrawSync", required = true) boolean isUsdtWithdrawSync,
			@RequestParam(name = "isUsdtRateFollowRecharge", required = true) boolean isUsdtRateFollowRecharge) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
			boolean admin = getAdminUtils().isAdmin(sessionUser);
			if (admin) {
				if (StringUtils.isEmpty(agentNo)) {
					throw new ServiceException("1", "超管账户必须指定代理商");
				}
			} else {
				if (StringUtils.isEmpty(sessionUser.getAgentNo())) {
					throw new ServiceException("1", "平台管理员必须绑定代理商");
				} else {
					agentNo = sessionUser.getAgentNo();
				}
			}
			Map<String, String> data = new HashMap<String, String>();
			data.put("USDT_RECHARGE_RATE", usdtRechargeRate);
			data.put("USDT_WITHDRAW_RATE", usdtWithdrawRate);
			data.put("FOLLOW_RECHARGE_ADD_RATE", followRechargeAddRate);
			data.put("IS_USDT_RECHARGE_SYNC", String.valueOf(isUsdtRechargeSync));
			data.put("IS_USDT_WITHDRAW_SYNC", String.valueOf(isUsdtWithdrawSync));
			data.put("IS_USDT_RATE_FOLLOW_RECHARGE", String.valueOf(isUsdtRateFollowRecharge));
			boolean result = getPlatformService().updateConfig(agentNo, "USDT_EXCHANGE", data);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			log.error("异常", e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("失败", t);
		}
		return webJson;
	}

	@RequestMapping(Route.Payment.UPDATE_WITHDRAW_REMIT_ID)
	@ResponseBody
	public WebJson UPDATE_WITHDRAW_REMIT_ID(HttpServletRequest request, @RequestParam Long id,
			@RequestParam int remitId) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			boolean result = getPaymentService().updateWithdrawRemitId(adminAccount, id, remitId);
			if (!result) {
				setFail(webJson);
			} else {
				setSuccess(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@RequestMapping(Route.Payment.LIST_PAYMENT_THRID_REMIT_NORMAL)
	@ResponseBody
	public WebJson LIST_PAYMENT_THRID_REMIT_NORMAL(@RequestParam Long id) {
		WebJson webJson = new WebJson();
		try {
			List<PaymentThirdRemitVo> dataList = new ArrayList<PaymentThirdRemitVo>();
			PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
			if (null != entity) {
				String agentNo = entity.getAgentNo();
				double actualAmount = entity.getActualAmount().doubleValue();
				String remitType = PayTypeEnum.getByCode(entity.getRemitType()).getPayTypeCode();
				List<PaymentThirdRemit> remitList = getPaymentThirdRemitDao().listAvailableInRangeByRemitType(agentNo,
						actualAmount, remitType);
				if (!CollectionUtils.isEmpty(remitList)) {
					remitList.stream().forEach(t -> {
						dataList.add(new PaymentThirdRemitVo(t, getRedisService()));
					});
				}
			}
			webJson.setData(dataList);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@RequestMapping(Route.Payment.LOCK_PLAYER_WITHDRAW)
	@ResponseBody
	public WebJson LOCK_PLAYER_WITHDRAW(HttpServletRequest request, @RequestParam Long id) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			boolean result = getPaymentService().lockWithdraw(adminAccount, id);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@RequestMapping(Route.Payment.UNLOCK_PLAYER_WITHDRAW)
	@ResponseBody
	public WebJson UNLOCK_PLAYER_WITHDRAW(HttpServletRequest request, @RequestParam Long id) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			boolean result = getPaymentService().unlockWithdraw(adminAccount, id);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@RequestMapping(Route.Payment.COMPLETED_PLAYER_WITHDRAW)
	@ResponseBody
	public WebJson COMPLETED_PLAYER_WITHDRAW(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "payBillno", defaultValue = "") String payBillno,
			@RequestParam(name = "remarks", defaultValue = "") String remarks) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			boolean result = getPaymentService().completedWithdraw(adminAccount, id, payBillno, remarks);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@RequestMapping(Route.Payment.REFUSE_PLAYER_WITHDRAW)
	@ResponseBody
	public WebJson REFUSE_PLAYER_WITHDRAW(HttpServletRequest request, @RequestParam(name = "id") Long id,
			@RequestParam(name = "reason") String reason) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			boolean result = getPaymentService().refuseWithdraw(adminAccount, id, reason);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

}
