package ph.yckj.admin.web.helper;

public class Route {

	public static final String PATH = "/api";
	// 用户登录
	public static final String LOGIN = "/login";

	public static final String METRICS = "/metrics";

	// 用户退出
	public static final String LOGOUT = "/logout";
	// 谷歌验证
	public static final String GOOGLE_AUTH = "/google-auth";

	// 任务状态
	public static final String TASK_STATUS = "/task-status";

	// 即时统计
	public static final String PLATFORM_RTS = "/platform-rts";


	// 系统模块
	public class System {
		// 系统模块根路径
		public static final String PATH = "/system";
		// 列出系统白名单
		public static final String LIST_SYSTEM_WHITELIST = "/list-system-whitelist";
		// 添加系统白名单
		public static final String ADD_SYSTEM_WHITELIST = "/add-system-whitelist";
		// 启用系统白名单
		public static final String ENABLE_SYSTEM_WHITELIST = "/enable-system-whitelist";
		// 禁用系统白名单
		public static final String DISABLED_SYSTEM_WHITELIST = "/disbaled-system-whitelist";
		// 删除系统白名单
		public static final String DELETE_SYSTEM_WHITELIST = "/delete-system-whitelist";

		// 列出系统公告
		public static final String LIST_SYSTEM_NOTICE = "/list-system-notice";
		// 添加系统公告
		public static final String ADD_SYSTEM_NOTICE = "/add-system-notice";
		// 更新系统公告状态
		public static final String UPDATE_SYSTEM_NOTICE_STATUS = "/update-system-notice-status";
		// 更新系统公告置顶
		public static final String UPDATE_SYSTEM_NOTICE_STICK = "/update-system-notice-stick";
		// 编辑系统公告
		public static final String EDIT_SYSTEM_NOTICE = "/edit-system-notice";
		// 删除系统公告
		public static final String DELETE_SYSTEM_NOTICE = "/delete-system-notice";

		// 列出商务合作配置
		public static final String LIST_BUSINESS_COOPERATE = "/list-business-cooperate";
		// 更新商务合作配置
		public static final String UPDATE_BUSINESS_COOPERATE = "/update-business-cooperate";

		// 列出在线服务配置
		public static final String LIST_ONLINE_SERVICE = "/list-online-service";
		// 更新在线服务配置
		public static final String UPDATE_ONLINE_SERVICE = "/update-online-service";

		// 列出彩票配置
		public static final String LIST_LOTTERY_CONFIG = "/list-lottery-config";
		// 更新彩票配置
		public static final String UPDATE_LOTTERY_CONFIG = "/update-lottery-config";

		// 列出用户配置
		public static final String LIST_PLAYER_SERVICE_CONFIG = "/list-player-service-config";
		// 更新用户配置
		public static final String UPDATE_PLAYER_SYSTEM_CONFIG = "/update-player-system-config";

		// 列出聊天室配置
		public static final String LIST_CHAT_SERVICE_CONFIG = "/list-chat-service-config";
		// 更新聊天室配置
		public static final String UPDATE_CHAT_SYSTEM_CONFIG = "/update-chat-system-config";

		// 列出用户试玩配置
		public static final String LIST_TRY_SERVICE_CONFIG = "/list-try-service-config";
		// 更新用户试玩配置
		public static final String UPDATE_TRY_SYSTEM_CONFIG = "/update-try-system-config";

		// 列出游戏服务配置
		public static final String LIST_GAME_SERVICE_CONFIG = "/list-game-service-config";
		// 更新游戏服务配置
		public static final String UPDATE_GAME_SYSTEM_CONFIG = "/update-game-system-config";

		// 列出直播线路
		public static final String LIST_LIVE_LINE = "/list-live-line";

		// 新增直播线路
		public static final String ADD_LIVE_LINE = "/add-live-line";

		// 编辑直播线路
		public static final String EDIT_LIVE_LINE = "/edit-live-line";

		// 启禁用直播线路
		public static final String ENABLE_LIVE_LINE = "/enable-live-line";

		// 删除直播线路
		public static final String DELETE_LIVE_LINE = "/delete-live-line";

		// 排序直播线路
		public static final String SORT_LIVE_LINE = "/sort-live-line";

		// 列出下载配置
		public static final String LIST_DOWNLOAD_CONFIG = "/list-download-config";
		// 更新下载配置
		public static final String UPDATE_DOWNLOAD_CONFIG = "/update-download-config";

		// 列出提现配置
		public static final String LIST_WITHDRAW_CONFIG = "/list-withdraw-config";
		// 更新提现配置
		public static final String UPDATE_WITHDRAW_CONFIG = "/update-withdraw-config";

		public static final String SYSTEM_LINE = "/system-line";
		// 用户类型列表
		public static final String LIST_PLAYER_ACCOUNT_TYPE = "/list-player-account-type";
		// 添加用户类型
		public static final String ADD_PLAYER_ACCOUNT_TYPE = "/add-player-account-type";
		// 修改用户类型
		public static final String UPDATE_PLAYER_ACCOUNT_TYPE = "/update-player-account-type";
		// 删除用户类型
		public static final String DELETE_PLAYER_ACCOUNT_TYPE = "/delete-player-account-type";

		// 平台服务国家列表查询
		public static final String PLATFORM_COUNTRY_LIST = "/platform-country-list";
		// 平台服务国家启禁用
		public static final String PLATFORM_COUNTRY_ENABLE = "/platform-country-enable";
		// 平台服务国家排序
		public static final String PLATFORM_COUNTRY_SORT = "/platform-country-sort";
		// 平台发言词库新增
		public static final String PLATFORM_THESAURUS_ADD = "/platform-thesaurus-add";
		// 平台发言词库删除
		public static final String PLATFORM_THESAURUS_DELETE = "/platform-thesaurus-delete";
		// 平台发言词库修改
		public static final String PLATFORM_THESAURUS_UPDATE = "/platform-thesaurus-update";
		// 平台发言词库列表查询
		public static final String PLATFORM_THESAURUS_LIST = "/platform-thesaurus-list";
		// 平台发言词库启禁用
		public static final String PLATFORM_THESAURUS_ENABLE = "/platform-thesaurus-enable";
		// 平台发言词库排序
		public static final String PLATFORM_THESAURUS_SORT = "/platform-thesaurus-sort";

		// USDT 汇率
		public static final String USDT_EXCHANGE_RATE = "/usdt-exchange-rate";

	}

	// 管理员模块
	public class Admin {

		// 管理员模块根路径
		public static final String LOGIN_INFO = "/login-info";

		// 管理员模块根路径
		public static final String PATH = "/admin";
		// 搜索后台账号
		public static final String SEARCH_ACCOUNT = "/search-account";
		// 列出后台账号
		public static final String LIST_ACCOUNT = "/list-account";
		// 添加后台账号
		public static final String ADD_ACCOUNT = "/add-account";
		// 更新后台账号状态
		public static final String UPDATE_ACCOUNT_STATUS = "/update-account-status";
		// 修改后台账号登录密码
		public static final String MODIFY_LOGIN_PASSWORD = "/modify-login-password";
		// 修改后台账号操作密码
		public static final String MODIFY_OPERATION_PASSWORD = "/modify-operation-password";
		// 修改当前后台账号登录密码
		public static final String MODIFY_MY_LOGIN_PASSWORD = "/modify-my-login-password";
		// 修改当前后台账号操作密码
		public static final String MODIFY_MY_OPERATION_PASSWORD = "/modify-my-operation-password";
		// 更新后台账号谷歌登录
		public static final String UPDATE_GOOGLE_LOGIN = "/update-google-login";
		// 重置后台账号谷歌绑定
		public static final String RESET_GOOGLE_BIND = "/reset-google-bind";
		// 列出角色
		public static final String LIST_ROLE = "/list-role";
		// 添加角色
		public static final String ADD_ROLE = "/add-role";
		// 更新角色
		public static final String UPDATE_ROLE = "/update-role";
		// 删除角色
		public static final String DELETE_ROLE = "/delete-role";
		// 列出菜单
		public static final String LIST_MENU = "/list-menu";
		// 添加菜单
		public static final String ADD_MENU = "/add-menu";
		// 更新菜单
		public static final String UPDATE_MENU = "/update-menu";
		// 列出权限
		public static final String LIST_ACCESS = "/list-access";
		// 新增权限
		public static final String ADD_ACCESS = "/add-access";
		// 更新权限
		public static final String UPDATE_ACCESS = "/update-access";
		// 列出后台账号菜单
		public static final String LIST_ACCOUNT_MENU = "/list-account-menu";
		// 列出后台账号权限
		public static final String LIST_ACCOUNT_ACCESS = "/list-account-access";
		// 添加后台账号权限
		public static final String ADD_ACCOUNT_ACCESS = "/add-account-access";
		// 删除后台账号权限
		public static final String DELETE_ACCOUNT_ACCESS = "/delete-account-access";
		// 重置后台账号权限
		public static final String RESET_ACCOUNT_ACCESS = "/reset-account-access";
		// 列出角色权限
		public static final String LIST_ROLE_ACCESS = "/list-role-access";
		// 添加角色权限
		public static final String ADD_ROLE_ACCESS = "/add-role-access";
		// 删除角色权限
		public static final String DELETE_ROLE_ACCESS = "/delete-role-access";
		// 查询管理员操作日志
		public static final String SEARCH_ADMIN_LOG = "/search-admin-log";
		// 查询管理员登录日志
		public static final String SEARCH_ADMIN_LOGIN_LOG = "/search-admin-login-log";
		// 修改查询报表的限制天数
		public static final String MODIFY_QUERY_LIMIT = "/modify-query-limit";
		// 下载管理员日志
		public static final String DOWNLOAD_ADMIN_LOG = "/download-admin-log";
		// 给后台账号添加全部权限
		public static final String ADD_ACCOUNT_ACCESS_ALL = "/add-account-access-all";
		// 给后台账号 删除全部权限
		public static final String DELETE_ACCOUNT_ACCESS_ALL = "/delete-account-access-all";
		// 添加角色的全部权限
		public static final String ADD_ROLE_ACCESS_ALL = "/add-role-access-all";
		// 删除角色的全部权限
		public static final String DELETE_ROLE_ACCESS_ALL = "/delete-role-access-all";

	}

	// 代理商模块
	public class Agent {
		// 代理商模块根路径
		public static final String PATH = "/agent";

		// 列出全部代理商
		public static final String LIST_AGENT = "/list-agent";

		// 清楚代理商数据
		public static final String CLEAN_AGENT_DATA = "/clean-agent-data";

		// 列出三方代理商
		public static final String LIST_THIRD_AGENT = "/list-third-agent";
		// 列出平台代理商
		public static final String LIST_PLATFORM_AGENT = "/list-platform-agent";

		// 添加代理商
		public static final String ADD_AGENT = "/add-agent";
		// 添加代理商uid
		public static final String BINDING_UID = "/binding-uid";
		// 变更玩家父级
		public static final String UPDATE_PID = "/update-pid";
		// 代理商同步百家乐平台
		public static final String AGENT_SYNC_THIRD_PLATFORM = "/agent-sync-third-platform";
		// 代理商补全配置
		public static final String AGENT_SUPPLEMENT_PLATFORM_CONFIG = "/agent-supplement-platform-config";
		// 代理游戏绑定信息
		public static final String LIST_AGENT_GAME_LOTTERY_INFO = "/list-agent-game-lottery-info";

		// 启用禁用游戏直播间
		public static final String UPDATE_BAN_STATUS = "/update-ban-status";
		// 游戏直播间推荐与否
		public static final String UPDATE_RECOMMEND = "/update-recommend";
		// 添加代理商
		public static final String UPDATE_AGENT = "/update-agent";
		// 搜索代理商
		public static final String SEARCH_AGENT = "/search-agent";
		// 启用/禁用代理商
		public static final String UPDATE_AGENT_STATUS = "/update-agent-status";
		// 代理商手动上分
		public static final String MANUAL_AGENT_RECHARGE = "/manual-agent-recharge";
		public static final String MANUAL_AGENT_ADD_BALANCE = "/manual-agent-add-balance";
		// 代理商提现
		public static final String MANUAL_AGENT_WITHDRAW = "/manual-agent-withdraw";
		// 查看代理商秘钥
		public static final String GET_AGENT_SECRETKEY = "/get-agent-secretkey";
		// 更新代理商访问后台ip白名单
		public static final String UPDATE_AGENT_WHITELIST = "/update-agent-whitelist";

		// 列出平台代理商的平台线路列表
		public static final String LIST_PLATFORM_LINE = "/list-platform-line";

		// 新增平台代理商的平台线路
		public static final String SAVE_PLATFORM_LINE = "/save-platform-line";

		// 删除平台代理商的平台线路
		public static final String DELETE_PLATFORM_LINE = "/delete-platform-line";

		// 添加代理商后台账号
		public static final String ADD_AGENT_ACCOUNT = "/add-agent-account";
		// 搜索代理商后台账号
		public static final String SEARCH_AGENT_ACCOUNT = "/search-agent-account";
		// 搜索代理商账单
		public static final String SEARCH_AGENT_BILL = "/search-agent-bill";

		// 搜索代理商充值记录
		public static final String SEARCH_AGENT_RECHARGE_RECORD = "/search-agent-recharge-record";

		// 搜索代理商提现记录
		public static final String SEARCH_AGENT_WITHDRAW_RECORD = "/search-agent-withdraw-record";

		// 添加玩家账号
		public static final String ADD_PLAYER_ACCOUNT = "/add-player-account";
		// 搜索玩家列表
		public static final String SEARCH_PLAYER = "/search-player";
		// 获取玩家详情
		public static final String GET_PLAYER_DETAILS = "/get-player-details";


		//清除登录错误次数
		public static final String RESET_ACCOUNT_LOGIN_FAILED_NUM = "/update-account-login-fail-num";

		//清除资金密码错误次数
		public static final String RESET_PAYMENT_PASSWORD_FAILED_NUM = "/update-payment-password-fail-num";

		// 手动给玩家充值
		public static final String MANUAL_PLAYER_RECHARGE = "/manual-player-recharge";
		// 手动给玩家提现
		public static final String MANUAL_PLAYER_WITHDRAW = "/manual-player-withdraw";

		// 更新玩家状态
		public static final String UPDATE_PLAYER_STATUS = "/update-player-status";
		// 更新玩家标记状态
		public static final String UPDATE_PLAYER_MARKER_STATUS = "/update-player-marker-status";

		// 获取默认玩家账户密码
		public static final String GET_DEFAULT_ACCOUNT_PASSWORD = "/get-default-account-password";

		// 修改玩家登录密码
		public static final String MODIFY_LOGIN_PASSWORD = "/modify-login-password";
		// 修改玩家提现密码
		public static final String MODIFY_WITHDRAW_PASSWORD = "/modify-withdraw-password";
		// 修改玩家提现人
		public static final String MODIFY_WITHDRAW_NAME = "/modify-withdraw-name";
		// 充值玩家锁定时间
		public static final String RESET_ACCOUNT_LOCK_TIME = "/reset-account-lock-time";
		// 清空玩家提现限制
		public static final String CLEAR_ACCOUNT_WITHDRAW_LIMIT = "/clear-account-withdraw-limit";
		// 修改玩家谷歌登录开关
		public static final String MODIFY_GOOGLE_LOGIN = "/modify-google-login";
		// 重置玩家谷歌绑定
		public static final String RESET_GOOGLE_BIND = "/reset-google-bind";
		// 充值玩家密码错误次数
		public static final String RESET_ACCOUNT_PASSWORD_ERROR_COUNT = "/reset-account-password-error-count";
		// 准备修改返点
		public static final String PREPARE_MODIFY_POINT = "/prepare-modify-point";
		// 修改返点
		public static final String MODIFY_POINT = "/modify-point";
		// 统一降点
		public static final String MODIFY_LINE_POINT = "/modify-line-point";
		// 统一升点
		public static final String INCREASE_LINE_POINT = "/increase-line-point";
		// 修改用户类型
		public static final String MODIFY_PLAYER_TYPE = "/modify-player-type";
		// 同级开号权限
		public static final String MODIFY_EQUAL_LEVEL = "/modify-equal-level";
		// 上下级转账权限
		public static final String MODIFY_ALLOW_TRANSFER = "/modify-allow-transfer";

		// 上级下分下级权限
		public static final String MODIFY_APPLY_UP_DED_TRANSFER = "/modify-apply-up-ded-transfer";

		// 搜索玩家账单
		public static final String SEARCH_PLAYER_BILL = "/search-player-bill";

		// 搜索玩家游戏记录
		public static final String SEARCH_PLAYER_GAME_RECORD = "/search-player-game-record";

		// 玩家游戏记录撤单
		public static final String CANCEL_PLAYER_GAME_RECORD = "/cancel-player-game-record";

		// 搜索玩家提现银行卡列表
		public static final String SEARCH_PLAYER_CARD_BANK = "/search-player-card-bank";

		// 增加玩家提现银行卡
		public static final String ADD_PLAYER_CARD_BANK = "/add-player-card-bank";

		// 启禁用玩家提现银行卡
		public static final String ENABLE_PLAYER_CARD_BANK = "/enable-player-card-bank";

		// 编辑玩家提现银行卡
		public static final String EDIT_PLAYER_CARD_BANK = "/edit-player-card-bank";

		// 解锁玩家提现银行卡
		public static final String UNLOCK_PLAYER_CARD_BANK = "/unlock-player-card-bank";

		// 锁定玩家提现银行卡
		public static final String LOCK_PLAYER_CARD_BANK = "/lock-player-card-bank";

		// 删除玩家提现银行卡
		public static final String DELETE_PLAYER_CARD_BANK = "/delete-player-card-bank";

		// 搜索玩家提现USTD列表
		public static final String SEARCH_PLAYER_CARD_USDT = "/search-player-card-usdt";

		// 增加玩家提现USTD
		public static final String ADD_PLAYER_CARD_USDT = "/add-player-card-usdt";

		// 启禁用玩家提现USTD
		public static final String ENABLE_PLAYER_CARD_USDT = "/enable-player-card-usdt";

		// 编辑玩家提现USTD
		public static final String EDIT_PLAYER_CARD_USDT = "/edit-player-card-usdt";

		// 解锁玩家提现USTD
		public static final String UNLOCK_PLAYER_CARD_USDT = "/unlock-player-card-usdt";

		// 锁定玩家提现USTD
		public static final String LOCK_PLAYER_CARD_USDT = "/lock-player-card-usdt";

		// 删除玩家提现USTD
		public static final String DELETE_PLAYER_CARD_USDT = "/delete-player-card-usdt";

		// 搜索玩家提现支付宝列表
		public static final String SEARCH_PLAYER_CARD_ALIPAY = "/search-player-card-alipay";

		// 增加玩家提现支付宝
		public static final String ADD_PLAYER_CARD_ALIPAY = "/add-player-card-alipay";

		// 启禁用玩家提现支付宝
		public static final String ENABLE_PLAYER_CARD_ALIPAY = "/enable-player-card-alipay";

		// 编辑玩家提现支付宝
		public static final String EDIT_PLAYER_CARD_ALIPAY = "/edit-player-card-alipay";

		// 解锁玩家提现支付宝
		public static final String UNLOCK_PLAYER_CARD_ALIPAY = "/unlock-player-card-alipay";

		// 锁定玩家提现支付宝
		public static final String LOCK_PLAYER_CARD_ALIPAY = "/lock-player-card-alipay";

		// 删除玩家提现银行卡
		public static final String DELETE_PLAYER_CARD_ALIPAY = "/delete-player-card-alipay";

		// 搜索玩家体现地址
		public static final String SEARCH_PLAYER_RECHARGE_RECORD = "/search-player-recharge-record";

		// 取消玩家充值
		public static final String CALCEL_PLAYER_RECHARGE = "/calcel-player-recharge";

		// 玩家充值补单
		public static final String PATCH_ACCOUNT_RECHARGE = "/patch-account-recharge";

		// 获取玩家充值记录详情
		public static final String GET_PLAYER_RECHARGE_RECORD_DETAIL = "/get-player-recharge-record-detail";

		// 搜索玩家提现记录
		public static final String SEARCH_PLAYER_WITHDRAW_RECORD = "/search-player-withdraw-record";

		// 获取玩家提现记录详情
		public static final String GET_PLAYER_WITHDRAW_RECORD_DETAIL = "/get-player-withdraw-record-detail";

		// 更新玩家提现记录状态
		public static final String UPDATE_PLAYER_WITHDRAW_RECORD_STATUS = "/update-player-withdraw-record-status";

		// 人工下分
		public static final String MANUAL_WITHDRAW_DEDUCTION = "/manual-withdraw-deduction";

		// 搜索玩家打赏记录
		public static final String SEARCH_PLAYER_REWARD = "/search-player-reward";

		// 获取玩家游戏详情
		public static final String GET_PLAYER_GAME_RECORD_DETAILS = "/get-player-game-details";

		// 搜索玩家转账记录
		public static final String SEARCH_PLAYER_TRANSFER_RECORD = "/search-player-transfer-record";

		// 搜索玩家游戏报表
		public static final String SEARCH_PLAYER_GAME_REPORT = "/search-player-game-report";

		// 搜索玩家投诉建议
		public static final String SEARCH_PLAYER_OPINION = "/search-player-opinion";

		// 处理玩家投诉建议
		public static final String PROCESS_PLAYER_OPINION = "/process-player-opinion";

		// 搜索代理商的登录日志
		public static final String SEARCH_AGENT_LOGIN_LOG = "/search-agent-login-log";

		// 搜索代理商的操作日志
		public static final String SEARCH_ACCOUNT_ACTION_LOG = "/search-account-action-log";

		// 修复用户报表
		public static final String FIX_PLAYER_REPORT = "/fix-player-report";

		public static final String SEARCH_AGENT_THIRD_LEVEL = "/search-agent-third-level";

		// 搜索玩家提现M列表
        public static final String SEARCH_PLAYER_CARD_M = "/search-player-card-m";
        // 增加玩家提现M
        public static final String ADD_PLAYER_CARD_M = "/add-player-card-m";
        // 启禁用玩家提现M
        public static final String ENABLE_PLAYER_CARD_M = "/enable-player-card-m";
        // 编辑玩家提现M
        public static final String EDIT_PLAYER_CARD_M = "/edit-player-card-m";
        // 解锁玩家提现M
        public static final String UNLOCK_PLAYER_CARD_M = "/unlock-player-card-m";
        // 锁定玩家提现M
        public static final String LOCK_PLAYER_CARD_M = "/lock-player-card-m";
        // 删除玩家提现M
        public static final String DELETE_PLAYER_CARD_M = "/delete-player-card-m";

        // 搜索玩家提现HH5列表
        public static final String SEARCH_PLAYER_CARD_HH5 = "/search-player-card-hh5";
        // 增加玩家提现HH5
        public static final String ADD_PLAYER_CARD_HH5 = "/add-player-card-hh5";
        // 启禁用玩家提现HH5
        public static final String ENABLE_PLAYER_CARD_HH5 = "/enable-player-card-hh5";
        // 编辑玩家提现HH5
        public static final String EDIT_PLAYER_CARD_HH5 = "/edit-player-card-hh5";
        // 解锁玩家提现HH5
        public static final String UNLOCK_PLAYER_CARD_HH5 = "/unlock-player-card-hh5";
        // 锁定玩家提现HH5
        public static final String LOCK_PLAYER_CARD_HH5 = "/lock-player-card-hh5";
        // 删除玩家提现HH5
        public static final String DELETE_PLAYER_CARD_HH5 = "/delete-player-card-hh5";

        // 搜索玩家提现OKG列表
        public static final String SEARCH_PLAYER_CARD_OKG = "/search-player-card-okg";
        // 增加玩家提现OKG
        public static final String ADD_PLAYER_CARD_OKG = "/add-player-card-okg";
        // 启禁用玩家提现OKG
        public static final String ENABLE_PLAYER_CARD_OKG = "/enable-player-card-okg";
        // 编辑玩家提现OKG
        public static final String EDIT_PLAYER_CARD_OKG = "/edit-player-card-okg";
        // 解锁玩家提现OKG
        public static final String UNLOCK_PLAYER_CARD_OKG = "/unlock-player-card-okg";
        // 锁定玩家提现OKG
        public static final String LOCK_PLAYER_CARD_OKG = "/lock-player-card-okg";
        // 删除玩家提现OKG
        public static final String DELETE_PLAYER_CARD_OKG = "/delete-player-card-okg";
	}

	// 代理商账单模块
	public class AgentBill {

		public static final String PATH = "/agent-bill-record";

		// 账单信息列表
		public static final String BILL_LIST_INFO = "/list-agent-bill-record";
		// 支付确认接口
		public static final String BILL_PAYMENT_CONFIRM = "/bill-payment-confirm";

	}

	// 游戏模块
	public class GameLottery {
		// 游戏模块根路径
		public static final String PATH = "/game-lottery";

		// 游戏类型列表
		public static final String STATIS_LIST_TYPE = "/static-list-type";
		// 游戏信息列表
		public static final String STATIC_LIST_INFO = "/static-list-info";

		// 游戏概率统计分析
		public static final String PROBABILITY_ANALYSIS = "/probability-analysis";

		// 游戏类型列表
		public static final String LIST_LOTTERY_TYPE = "/list-lottery-type";
		// 更新游戏类型状态
		public static final String UPDATE_LOTTERY_TYPE_STATUS = "/update-lottery-type-status";
		// 排序上一位
		public static final String UPDATE_PRE_STEP_LOTTERY_TYPE = "/update-pre-step-lottery-type";
		// 排序下一位
		public static final String UPDATE_NEXT_STEP_LOTTERY_TYPE = "/update-next-step-lottery-type";

		// 游戏信息列表
		public static final String LIST_LOTTERY_INFO = "/list-lottery-info";
		// 编辑游戏信息
		public static final String EDIT_LOTTERY_INFO = "/edit-lottery-info";
		// 更新游戏信息直播状态
		public static final String UPDATE_LOTTERY_INFO_STATUS = "/update-lottery-info-status";

		// 更新游戏信息直播状态
		public static final String UPDATE_LOTTERY_INFO_MAINTAIN_STATUS = "/update-lottery-info-maintain-status";
		// 更新游戏信息杀率状态
		public static final String UPDATE_LOTTERY_INFO_KILL_STATUS = "/update-lottery-info-kill-status";
		// 游戏信息排序上一位
		public static final String UPDATE_PRE_STEP_LOTTERY_INFO = "/update-pre-step-lottery-info";
		// 游戏信息排序下一位
		public static final String UPDATE_NEXT_STEP_LOTTERY_INFO = "/update-next-step-lottery-info";
		// 设置游戏杀率
		public static final String EDIT_KILLRATE_SETTING = "/edit-killrate-setting";
		// 请求杀率设置
		public static final String REQUEST_KILLRATE_SETTING = "/request-killrate-setting";
		// 设置杀率
		public static final String SET_KILL_PROPORTION = "/set-kill-proportion";

		// 绑定直播间列表
		public static final String LIST_LOTTERY_AGENT = "/list-lottery-agent";
		// 绑定直播间列表（按游戏类型分组）
		public static final String LIST_LOTTERY_AGENT_BIND = "/list-lottery-agent-bind";
		// 绑定代理商和游戏直播间
		public static final String BIND_AGENT_GAME_LOTTERY_INFO = "/bind-agent-game-lottery-info";

		// 游戏分组列表
		public static final String LIST_GAME_LOTTERY_GROUP = "/list-game-lottery-group";
		// 添加游戏分组
		public static final String ADD_GAME_LOTTERY_GROUP = "/add-game-lottery-group";
		// 编辑游戏分组
		public static final String EDIT_GAME_LOTTERY_GROUP = "/edit-game-lottery-group";
		// 删除游戏分组
		public static final String DELETE_GAME_LOTTERY_GROUP = "/delete-game-lottery-group";
		// 游戏列表
		public static final String LIST_GAME_LOTTERY_INFO_LIST = "/list-game-lottery-info-list";
		// 游戏状态
		public static final String STATUS_GAME_LOTTERY_INFO_LIST = "/status-game-lottery-info-list";
		// 编辑游戏
		public static final String EDIT_GAME_LOTTERY_INFO_LIST = "/edit-game-lottery-info-list";

		// 查询游戏直播记录
		public static final String SEARCH_GAME_LIVE_RECORD = "/search-game-live-record";

		// 列出彩票玩法
		public static final String LIST_LOTTERY_METHOD = "/list-lottery-method";
		// 玩法编辑
		public static final String UPDATE_LOTTERY_METHOD = "/update-lottery-method";
		// 玩法奖金编辑
		public static final String UPDATE_LOTTERY_METHOD_BONUS = "/update-lottery-method-bonus";
		// 启用禁用玩法
		public static final String UPDATE_LOTTERY_METHOD_STATUS = "/update-lottery-method-status";

		// 列出游戏开奖时间
		public static final String LIST_LOTTERY_OPEN_TIME = "/list-lottery-open-time";
		// 列出游戏开奖号码
		public static final String LIST_LOTTERY_OPEN_CODE = "/list-lottery-open-code";
		// 确认游戏开奖号码
		public static final String SUBMIT_LOTTERY_OPEN_CODE = "/submit-lottery-open-code";
		// 添加开奖号码
		public static final String ADD_LOTTERY_OPEN_CODE = "/add-lottery-open-code";
		// 修改开奖号码
		public static final String UPDATE_LOTTERY_OPEN_CODE = "/update-lottery-open-code";
		// 反结算
		public static final String REVISE_LOTTERY_OPEN_CODE = "/revise-lottery-open-code";
		// 撤销开奖结果
		public static final String CANCEL_LOTTERY_OPEN_CODE = "/cancel-lottery-open-code";
		// 游戏开奖视频列表
		public static final String SEARCH_GAME_VIDEO = "/search-game-video";

		// 导入游戏开奖视频
		public static final String IMPORT_GAME_VIDEO = "/import-game-video";

		// 播放游戏开奖视频
		public static final String PLAY_GAME_VIDEO = "/play-game-video";

		// 推送游戏开奖视频
		public static final String PUSH_GAME_VIDEO = "/push-game-video";

		// 修改游戏开奖视频
		public static final String EDIT_GAME_VIDEO = "/edit-game-video";

		// 禁用游戏开奖视频
		public static final String BAN_GAME_VIDEO = "/ban-game-video";

		// 删除游戏开奖视频
		public static final String DELETE_GAME_VIDEO = "/delete-game-video";

		// 游戏礼物列表
		public static final String SEARCH_GIFT = "/search-gift";

		// 新增礼物
		public static final String ADD_GIFT = "/add-gift";

		// 修改礼物
		public static final String EDIT_GIFT = "/edit-gift";

		// 修改礼物排序
		public static final String UPDATE_GIFT_SORT = "/update-gift-sort";

		// 禁用新增礼物
		public static final String BAN_GIFT = "/ban-gift";

		// 删除新增礼物
		public static final String DELETE_GIFT = "/delete-gift";

		// 游戏每期汇总记录
		public static final String SEARCH_GAME_ORDER_ISSUE_RECORD = "/search-game-order-issue-record";

		// 盈利排行榜
		public static final String PROFIT_RANKING = "/profit-ranking";

		public static final String ADD_PROFIT_RANKING = "/add-profit-ranking";
		public static final String UPDATE_PROFIT_RANKING = "/update-profit-ranking";
		public static final String DELETE_PROFIT_RANKING = "/delete-profit-ranking";
		// 亏损量预警配置
		public static final String SEARCH_EARLY_WARN_CONFIG = "/search-early-warn-config";
		public static final String ADD_EARLY_WARN_CONFIG = "/add-early-warn-config";
		public static final String UPDATE_EARLY_WARN_CONFIG = "/update-early-warn-config";
		public static final String UPDATE_EARLY_WARN_CONFIG_STATUS = "/update-early-warn-config-status";
		public static final String GET_EARLY_WARN_CONFIG = "/get-early-warn-config";
		public static final String SET_EARLY_WARN_CONFIG_LOTTERY = "/set-early-warn-config-lottery";

		// 亏损量预警记录
		public static final String SEARCH_EARLY_WARN_RECORD = "/search-early-warn-record";
		public static final String CLEAR_EARLY_WARN = "/clear-early-warn";
		public static final String SEARCH_EARLY_WARN_AUTO_DISABLE_RECORD = "/search-early-warn-auto-disable-record";

		public static final String CLEAR_EARLY_WARN_RECORD_COUNT = "/search-early-warn-record-count";
        // 查询卡单任务
        public static final String SEARCH_LOCK_TASK = "/search-lock-task";
        // 添加卡单任务
        public static final String ADD_LOCK_TASK = "/add-lock-task";
        // 启用卡单任务
        public static final String START_LOCK_TASK = "/start-lock-task";
        // 删除卡单任务
        public static final String DELETE_LOCK_TASK = "/delete-lock-task";
        // 查询卡单记录
        public static final String SEARCH_LOCK_ORDER_RECORD = "/search-lock-order_record";
	}

    // 抽水配置
    public class GamePumpConfig {
        public static final String PATH = "/game-pump-config";
        // 列出抽水配置
        public static final String LIST = "/list";
        // 获取抽水配置
        public static final String GET = "/get";
        // 保存抽水配置
        public static final String SAVE = "/save";
        // 更新抽水配置
        public static final String UPDATE = "/update";
        // 删除抽水配置
        public static final String DELETE = "/delete";
        // 更新抽水配置状态
        public static final String UPDATE_STATUS = "/updateStatus";
    }

	// 报表模块
	public class Report {
		public static final String PATH = "/report";

		// 玩家报表
		public static final String SEARCH_PLAYER_REPORT = "/search-player-report";

		// 三方玩家报表
		public static final String SEARCH_THIRD_PLAYER_REPORT = "/search-third-player-report";

		// 平台玩家报表
		public static final String SEARCH_PLATFORM_PLAYER_REPORT = "/search-platform-player-report";

		// 代理商报表
		public static final String SEARCH_AGENT_REPORT = "/search-agent-report";

		// 三方代理商报表
		public static final String SEARCH_THIRD_AGENT_REPORT = "/search-third-agent-report";

		// 平台代理商报表
		public static final String SEARCH_PLATFORM_AGENT_REPORT = "/search-platform-agent-report";

		// 游戏彩种报表
		public static final String SEARCH_GAME_LOTTERY_REPORT = "/search-game-lottery-report";

		// 游戏玩法报表
		public static final String SEARCH_GAME_METHOD_REPORT = "/search-game-method-report";

		// 游戏总报表
		public static final String SEARCH_GAME_TYPE_REPORT = "/search-game-type-report";

		public static final String SEARCH_PLATFORM_AGENT_PLAYER_TEAM_REPORT = "/search-platform-agent-player-team-report";

		public static final String SEARCH_PLATFORM_AGENT_PLAYER_PERSON_REPORT = "/search-platform-agent-player-person-report";

		public static final String SEARCH_PLATFORM_AGENT_PLAYER_TEAM_TOTAL_REPORT = "/search-platform-agent-player-team-total-report";

		public static final String MANUAL_REPORT = "/manual-report";
	}

	// 大富翁模块
	public class Monopoly {
		public static final String PATH = "/monopoly";

		// 获取配置信息
		public static final String GET_CONFIG_INFO = "/get-config-info";

		public static final String ADD_CONFIG_INFO = "/add-config-info";
		// 获取配置信息
		public static final String EDIT_CONFIG_INFO = "/edit-config-info";

		// 奖金池管理员加
		public static final String EDIT_POOL_PLUS = "/edit-pool-plus";

		// 奖金池管理员减
		public static final String EDIT_POOL_SUB = "/edit-pool-sub";

		// 奖池上浮金额增减
		public static final String EDIT_RISE_AMOUNT = "/edit-rise-amount";

		// 查询投注汇总列表
		public static final String SEARCH_BET_RECORD = "/search-bet-record";

		// 查询玩家投注详情列表
		public static final String SEARCH_PLAYER_BET_DETAIL = "/search-player-bet-detail";

		// 查询奖池账单
		public static final String SEARCH_POOL_BILL = "/search-pool-bill";

	}

	// 运营模块
	public class Operations {
		public static final String PATH = "/operations";

		// 查询盈亏走势
		public static final String SEARCH_PROFIT_TABLE = "/search-profit-table";

		// 查询注册玩家走势
		public static final String SEARCH_REGISTER_PLAYER_TABLE = "/search-register-player-table";

		// 查询游戏玩家走势
		public static final String SEARCH_ACTIVITY_PLAYER_TABLE = "/search-activity-player-table";

	}

	// 回调模块
	public class Callack {
		public static final String PATH = "/callback";

		// 获取配置信息
		public static final String PUSH_STREAM_EVENT = "/pushStreamEvent";

		// 获取配置信息
		public static final String SRS_PUSH_STREAM_EVENT = "/srsPushStreamEvent";

		// 获取配置信息
		public static final String SRS_STOP_STREAM_EVENT = "/srsStopStreamEvent";

		// 获取配置信息
		public static final String MANUAL_STOP_LIVE = "/manualStopLive";

		// 获取配置信息
		public static final String MANUAL_START_LIVE = "/manualStartLive";

	}

	// 活动模块
	public class Activity {
		public static final String PATH = "/activity";

		public static final String LIST_ACTIVITY = "list-activity";
		// 代理商下的活动列表
		public static final String AGENT_LIST_ACTIVITY = "agent-list-activity";
		// 查询活动列表
		public static final String SEARCH_ACTIVITY_CONFIG = "/search-activity-config";
		// 活动活动配置
		public static final String GET_ACTIVITY_CONFIG = "/get-activity-config";
		// 添加活动配置
		public static final String ADD_ACTIVITY_CONFIG = "/add-activity-config";
		public static final String UPLOAD_ACTIVITY_IMAGE = "/upload-activity-image";
		// 更新活动配置
		public static final String UPDATE_ACTIVITY_CONFIG = "/update-activity-config";
		// 更新活动配置规则
		public static final String UPDATE_ACTIVITY_CONFIG_RULES = "/update-activity-config-rules";
		// 更新活动状态
		public static final String UPDATE_ACTIVITY_CONFIG_STATUS = "/update-activity-config-status";
		// 删除活动
		public static final String DELETE_ACTIVITY_CONFIG_STATUS = "/delete-activity-config-status";

		public static final String SEARCH_ACTIVITY_REWAED_RECORD = "/search-activity-reward-record";
		// 互斥活动列表
		public static final String GET_MUTUAL_EXCLUSION = "/get-mutual-exclusion";
		//更新互斥活动
		public static final String UPDATE_MUTUAL_EXCLUSION = "/update-mutual-exclusion";
	}

	// 契约
	public static class Contract {
		public static final String PATH = "/contract";
		// 平台契约列表
		public static final String LIST_PLATFORM_CONTRACT = "/list-platform-contract";
		// 添加平台契约
		public static final String ADD_PLATFORM_CONTRACT = "/add-platform-contract";
		// 编辑平台契约
		public static final String EDIT_PLATFORM_CONTRACT = "/edit-platform-contract";
		// 平台契约状态
		public static final String PLATFORM_CONTRACT_STATUS = "/platform-contract-status";
		// 刷新平台契约。查询指定某平台契约，查询该契约用户类型的用户，如果某用户没有此用户契约，则复制此平台契约到该用户契约
		public static final String REFRESH_PLATFORM_CONTRACT = "/refresh-platform-contract";
		// 删除平台契约
		public static final String DELETE_PLATFORM_CONTRACT = "/delete-platform-contract";
		// 用户契约列表
		public static final String LIST_PLAYER_CONTRACT = "/list-player-contract";
		// 编辑用户契约
		public static final String EDIT_PLAYER_CONTRACT = "/edit-player-contract";
		// 删除用户契约
		public static final String DELETE_PLAYER_CONTRACT = "/delete-player-contract";
		// 契约发放记录
		public static final String LIST_CONTRACT_RECORD = "/list-contract-record";
		// 发放契约
		public static final String DRAW_CONTRACT_RECORD = "/draw-contract-record";
	}

	// 支付模块
	public class Payment {
		public static final String PATH = "/payment";

		// 列出银行列表
		public static final String LIST_PAYMENT_BANK = "/list-payment-bank";

		// 搜索三方支付
		public static final String SEARCH_PAYMENT_THIRD = "/search-payment-third";

		// 更新三方支付
		public static final String UPDATE_PAYMENT_THIRD = "/update-payment-third";

		// 列出USDT支付汇率
		public static final String LIST_PAYMENT_USDT_HUILV = "/list-payment-usdt-huilv";

		// 更新USDT支付汇率
		public static final String UPDATE_PAYMENT_USDT_HUILV = "/update-payment-usdt-huilv";

		// 找出符合条件的USDT交易记录
		public static final String LIST_PAYMENT_USDT_TX_HASH = "/list-payment-usdt-tx-hash";

		// 搜索支付渠道
		public static final String SEARCH_PAYMENT_TRANSFER = "/search-payment-transfer";
		// 添加支付渠道
		public static final String ADD_PAYMENT_TRANSFER = "/add-payment-transfer";

		// 清空支付渠道
		public static final String CLEAR_PAYMENT_TRANSFER = "/clear-payment-transfer";
		public static final String UPDATE_PAYMENT_TRANSFER = "/update-payment-transfer";
		public static final String DELETE_PAYMENT_TRANSFER = "/delete-payment-transfer";
		public static final String UPDATE_PAYMENT_TRANSFER_STATUS = "/update-payment-transfer-status";

		public static final String SEARCH_PAYMENT_THIRD_PAY = "/search-payment-third-pay";
		public static final String ADD_PAYMENT_THIRD_PAY = "/add-payment-third-pay";
		public static final String CLEAR_PAYMENT_THIRD_PAY = "/clear-payment-third-pay";
		public static final String UPDATE_PAYMENT_THIRD_PAY = "/update-payment-third-pay";
		public static final String UPDATE_PAYMENT_THIRD_PAY_KEY = "/update-payment-third-pay-key";
		public static final String DELETE_PAYMENT_THIRD_PAY = "/delete-payment-third-pay";
		public static final String UPDATE_PAYMENT_THIRD_PAY_STATUS = "/update-payment-third-pay-status";

		public static final String SEARCH_PAYMENT_THIRD_REMIT = "/search-payment-third-remit";
		public static final String ADD_PAYMENT_THIRD_REMIT = "/add-payment-third-remit";
		public static final String UPDATE_PAYMENT_THIRD_REMIT = "/update-payment-third-remit";
		public static final String UPDATE_PAYMENT_THIRD_REMIT_KEY = "/update-payment-third-remit-key";
		public static final String DELETE_PAYMENT_THIRD_REMIT = "/delete-payment-third-remit";
		public static final String UPDATE_PAYMENT_THIRD_REMIT_STATUS = "/update-payment-third-remit-status";

		public static final String UPDATE_WITHDRAW_REMIT_ID = "/update-withdraw-remit-id";
		public static final String LIST_PAYMENT_THRID_REMIT_NORMAL = "/list-payment-thrid-remit-normal";
		// 锁定用户提现
		public static final String LOCK_PLAYER_WITHDRAW = "/lock-player-withdraw";
		// 解锁用户提现
		public static final String UNLOCK_PLAYER_WITHDRAW = "/unlock-player-withdraw";
		public static final String REFUSE_PLAYER_WITHDRAW = "/refuse-player-withdraw";
		public static final String COMPLETED_PLAYER_WITHDRAW = "/completed-player-withdraw";
	}

	// 推广模块
	public class Promote {
		// 推广模块根路径
		public static final String PATH = "/promote";
		// 查询推广配置
		public static final String SEARCH_PROMOTE_CONFIG = "/search-promote-config";

		// 新增推广配置
		public static final String ADD_PROMOTE_CONFIG = "/add-promote-config";
		// 编辑推广配置
		public static final String EDIT_PROMOTE_CONFIG = "/edit-promote-config";

		// 查询佣金记录
		public static final String SEARCH_COMMISSION_RECORD = "/search-commission-record";
		// 查询返水记录
		public static final String SEARCH_REBATE_RECORD = "/search-rebate-record";

	}

	// 直播监控
	public class Live {
		public static final String PATH = "/live";
		public static final String SEARCH_LIVE_INFO = "/search-live-info";
		public static final String INIT = "/inti";

	}

	public class Flyingthrow {
		public static final String PATH = "/flyingthrow";

		public static final String SEARCH_FLYINGTHROW = "/search-flyingthrow";
		public static final String ADD_FLYINGTHROW = "/add-flyingthrow";
		public static final String UPDATE_FLYINGTHROW = "/update-flyingthrow";
		public static final String UPDATE_FLYINGTHROW_STATUS = "/update-flyingthrow-status";
	}

	// 真人游戏
	public class GameSimulation {

		public static final String PATH = "/game-simulation";

		// 游戏账号
		public static final String SEARCH_GAME_ACCOUNT = "/search-game-account";

		// 开通游戏
		public static final String SEARCH_GAME_TRANSFER = "/search-game-transfer";

		// 修改账号状态
		public static final String MODIFY_GAME_ACCOUNT_STATUS = "/modify-game-account-status";

		// 列出游戏
		public static final String SEARCH_SIMULATION_GAME = "/search-simulation-game";

		// 修改游戏状态
		public static final String MODIFY_GAME_STATUS = "/modify-game-status";

		// 列出所有游戏平台
		public static final String STATIC_LIST_PLATFORM = "/static-list-platform";

		// 列出所有游戏平台
		public static final String STATIC_LIST_PLATFORM_ACTIVE = "/static-list-platform-active";

		// 新增游戏
		public static final String ADD_GAME_SIMULATION = "/add-game-simulation";

		// 修改游戏
		public static final String EDIT_GAME_SIMULATION = "/edit-game-simulation";

		// 修改密钥
		public static final String EDIT_GAME_SIMULATION_KEY = "/edit-game-simulation-key";

		// 列出游戏记录
		public static final String SEARCH_GAME_PLAYRECORD = "/search-game-playrecord";

		// 列出游戏个人报表
		public static final String SEARCH_GAME_REPORT = "/search-game-report";

		// 列出游戏种类报表
		public static final String SEARCH_GAME_INFO_REPORT = "/search-game-info-report";

		// 列出全部游戏game_simulation_platform_type
		public static final String LIST_ALL_GAMES = "/list-all-games";

		// 列出全部游戏game_simulation
		public static final String LIST_ALL_SIMULATION_GAMES = "/list-all-game-simulations";

		// 游戏即时统计
		public static final String FINANCIAL_STATISTIC = "/financial-statistic";

		// 查询修正游戏报表请求
		public static final String SEARCH_GAME_REPORT_RECRAWL = "/search-game-report-recrawl";

		// 新增修正游戏报表请求
		public static final String ADD_GAME_REPORT_RECRAWL = "/add-game-report-recrawl";

		// 查询Flash Tech订单
		public static final String SEARCH_FLASH_TECH_REPORT = "/search-flash-tech-report";

		// 查询游戏平台详细订单
		public static final String SEARCH_GAME_PLATFORM_DETAIL_ORDER = "/search-game-platform-detail-order";

		// 强制转出第三方游戏余额
		public static final String MANUAL_TRANSFER_GAME_TO_LOTTERY = "/manual-transfer-game-to-lottery";

		// 强制转出第三方游戏余额
		public static final String MANUAL_TRANSFER_GAME_TO_LOTTERY2 = "/manual-transfer-game-to-lottery2";

		// 列出游戏团队报表
		public static final String SEARCH_GAME_TEAM_REPORT = "/search-game-team-report";
		// 查询平台场馆列表
		public static final String PLAT_PLATFORM_LIST = "/plat-platform-list";
		// 更改平台场馆状态
		public static final String PLAT_PLATFORM_UPDATE_STATUS = "/plat-platform-update-status";
		// 更改平台场馆推荐
		public static final String PLAT_PLATFORM_UPDATE_RECOMMEND = "/plat-platform-update-recommend";

		public static final String SEARCH_GAME_TEAM_REPORT_NEW = "/search-game-team-report-new";
		public static final String SEARCH_GAME_TEAM_TOTAL_REPORT = "/search-game-team-total-report";
	}

	public class PlatformAgentPlayer{
		public static final String PATH = "/platform-agent-player";
		public static final String SEARCH_PLATFORM_AGENT_PLAYER_REPORT = "/search-platform-agent-player-report";
	}

}
