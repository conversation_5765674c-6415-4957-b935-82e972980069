package ph.yckj.admin.web.ctrl;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.contract.ContractPlatformConfigVo;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;
import sy.hoodle.base.common.entity.AgentPlayerAccountType;
import sy.hoodle.base.common.entity.ContractPlatformConfig;
import sy.hoodle.base.common.entity.ContractPlayerConfig;
import sy.hoodle.base.common.entity.ContractRecord;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.Contract.PATH)
public class ContractController extends SuperController {

    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.Contract.LIST_PLATFORM_CONTRACT)
    public WebJson LIST_PLATFORM_CONTRACT(HttpServletRequest request,
            @RequestParam(name = "agentNo", required = false) String agentNo,
            @RequestParam(name = "agentName", required = false) String agentName,
            @RequestParam(name = "contractTitle", required = false) String contractTitle,
            @RequestParam(name = "contractType", required = false) Integer contractType,
            @RequestParam(name = "contractStatus", required = false) Integer contractStatus,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size) {
        WebJson webJson = new WebJson();
        try {
            int firstResult = page * size;

            List<Criterion> criterions = new ArrayList<>();
            if(!StringUtils.isEmpty(agentNo)) {
                criterions.add(Restrictions.eq("agentNo", agentNo));
            }
            if(!StringUtils.isEmpty(agentName)) {
                criterions.add(Restrictions.like("agentName", agentName, MatchMode.ANYWHERE));
            }
            if(!StringUtils.isEmpty(contractTitle)) {
                criterions.add(Restrictions.like("contractTitle", contractTitle, MatchMode.ANYWHERE));
            }
            if(contractType != null) {
                criterions.add(Restrictions.eq("contractType", contractType));
            }
            if(contractStatus != null) {
                criterions.add(Restrictions.eq("contractStatus", contractStatus));
            }
            List<Order> orders = new ArrayList<>();
            orders.add(Order.asc("id"));

            List<ContractPlatformConfigVo> list = new ArrayList<>();

            int totalCount = getContractPlatformConfigDao().totalCount(criterions);
            List<ContractPlatformConfig> configList = getContractPlatformConfigDao().find(criterions, orders,
                    firstResult, size);
            if(configList != null && !configList.isEmpty()) {
                for(ContractPlatformConfig c : configList) {
                    AgentPlayerAccountType accountType = getAgentPlayerAccountTypeDao().getByCode(c.getAgentNo(),
                            c.getAccountType());
                    ContractPlatformConfigVo vo = new ContractPlatformConfigVo(c);
                    if(accountType == null) {
                        vo.setAccountTypeName(vo.getAccountType());
                    }else {
                        vo.setAccountTypeName(accountType.getName());
                    }
                    list.add(vo);
                }
            }

            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", totalCount);
            data.put("list", list);

            webJson.setData(data);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }
        return webJson;
    }

    @CheckLogin
    @RequestMapping(Route.Contract.ADD_PLATFORM_CONTRACT)
    public WebJson ADD_PLATFORM_CONTRACT(HttpServletRequest request, ContractPlatformConfigVo vo) {
        WebJson webJson = new WebJson();
        try {
            if(vo.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MIN_10 &&
                    vo.getContractType() != ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY &&
                    vo.getContractType() != ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND) {
                throw newException("500-132");
            }
            boolean result = getContractService().saveContractPlatformConfig(vo);
            if(result) {
                // 保存操作日志
                String content = "添加平台契约，agentNo：" + vo.getAgentNo() + "，契约编号：" + vo.getContractCode() +
                        "，契约标题：" + vo.getContractTitle() + "，契约类型：" + vo.getContractType() + "，计算对象：" +
                        vo.getTargetType() + "，计算周期：" + vo.getCycleType() + "，计算范围：" + vo.getComputeRange() +
                        "，发放方式：" + vo.getDrawType() + "，执行时间：" + vo.getCronSchedule() + "，契约规则：" +
                        vo.getContractRules() + "，下级契约规则：" + vo.getDownRules();
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);
                // 同步到用户契约
                getContractService().refreshPlatformContract(vo.getAgentNo(), vo.getContractCode());
                setSuccess(webJson);
            }else {
                setFail(webJson);
            }
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }
        return webJson;
    }

    @CheckLogin
    @RequestMapping(Route.Contract.EDIT_PLATFORM_CONTRACT)
    public WebJson EDIT_PLATFORM_CONTRACT(HttpServletRequest request, ContractPlatformConfigVo vo) {
        WebJson webJson = new WebJson();
        try {
            if(vo.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MIN_10 &&
                    vo.getContractType() != ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY &&
                    vo.getContractType() != ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND) {
                throw newException("500-132");
            }
            getContractService().updateContractPlatformConfig(vo);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }
        return webJson;
    }

    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.Contract.PLATFORM_CONTRACT_STATUS)
    public WebJson PLATFORM_UPDATE_STATUS(@RequestParam(name = "agentNo") String agentNo,
            @RequestParam(name = "contractCode") String contractCode, @RequestParam(name = "status") int status) {
        WebJson webJson = new WebJson();
        try {
            boolean result = getContractPlatformConfigDao().updateContractStatus(agentNo, contractCode, status);

            if(result) {
                // 保存操作日志
                String content = "修改平台契约状态，agentNo：" + agentNo + "，契约编号：" + contractCode + "，状态：" +
                        status;
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);
                setSuccess(webJson);
            }
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }

        return webJson;
    }

    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.Contract.REFRESH_PLATFORM_CONTRACT)
    public WebJson REFRESH_PLATFORM_CONTRACT(@RequestParam(name = "agentNo") String agentNo,
            @RequestParam(name = "contractCode") String contractCode) {
        WebJson webJson = new WebJson();
        try {
            boolean result = getContractService().refreshPlatformContract(agentNo, contractCode);

            if(result) {
                // 保存操作日志
                String content = "同步平台契约，agentNo：" + agentNo + "，契约编号：" + contractCode;
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);
                setSuccess(webJson);
            }
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }

        return webJson;
    }

    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.Contract.DELETE_PLATFORM_CONTRACT)
    public WebJson DELETE_PLATFORM_CONTRACT(@RequestParam(name = "agentNo") String agentNo,
            @RequestParam(name = "contractCode") String contractCode) {
        WebJson webJson = new WebJson();
        try {
            boolean result = getContractService().deletePlatformContract(agentNo, contractCode);

            if(result) {
                // 保存操作日志
                String content = "删除平台契约，agentNo：" + agentNo + "，契约编号：" + contractCode;
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);
                setSuccess(webJson);
            }
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }

        return webJson;
    }

    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.Contract.LIST_PLAYER_CONTRACT)
    public WebJson LIST_PLAYER_CONTRACT(HttpServletRequest request,
            @RequestParam(name = "agentNo", required = false) String agentNo,
            @RequestParam(name = "agentName", required = false) String agentName,
            @RequestParam(name = "contractTitle", required = false) String contractTitle,
            @RequestParam(name = "toPlayerName", required = false) String toPlayerName,
            @RequestParam(name = "fromPlayerName", required = false) String fromPlayerName,
            @RequestParam(name = "contractType", required = false) Integer contractType,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size) {
        WebJson webJson = new WebJson();
        try {
            int firstResult = page * size;

            List<Criterion> criterions = new ArrayList<>();
            if(!StringUtils.isEmpty(agentNo)) {
                criterions.add(Restrictions.eq("agentNo", agentNo));
            }
            if(!StringUtils.isEmpty(agentName)) {
                criterions.add(Restrictions.like("agentName", agentName, MatchMode.ANYWHERE));
            }
            if(!StringUtils.isEmpty(contractTitle)) {
                criterions.add(Restrictions.like("contractTitle", contractTitle, MatchMode.ANYWHERE));
            }
            if(!StringUtils.isEmpty(toPlayerName)) {
                criterions.add(Restrictions.eq("toPlayerName", toPlayerName));
            }
            if(!StringUtils.isEmpty(fromPlayerName)) {
                if("sys".equals(fromPlayerName)) {
                    criterions.add(Restrictions.eq("fromPlayerId", 0L));
                }else {
                    criterions.add(Restrictions.eq("fromPlayerName", fromPlayerName));
                }
            }
            if(contractType != null) {
                criterions.add(Restrictions.eq("contractType", contractType));
            }
            List<Order> orders = new ArrayList<>();
            orders.add(Order.desc("id"));

            int totalCount = getContractPlayerConfigDao().totalCount(criterions);
            List<ContractPlayerConfig> list = getContractPlayerConfigDao().find(criterions, orders, firstResult, size);

            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", totalCount);
            data.put("list", list);

            webJson.setData(data);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }
        return webJson;
    }

    @CheckLogin
    @RequestMapping(Route.Contract.EDIT_PLAYER_CONTRACT)
    public WebJson EDIT_PLAYER_CONTRACT(HttpServletRequest request, ContractPlayerConfig config) {
        WebJson webJson = new WebJson();
        try {
            getContractService().updateContractPlayerConfig(config);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }
        return webJson;
    }

    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.Contract.DELETE_PLAYER_CONTRACT)
    public WebJson DELETE_PLAYER_CONTRACT(@RequestParam(name = "id") Integer id) {
        WebJson webJson = new WebJson();
        try {
            // 查询用户契约
            ContractPlayerConfig config = getContractPlayerConfigDao().getById(id);
            // 删除用户及其下级该编号契约
            boolean result = getContractService().deleteContractPlayerConfig(config, true);

            if(result) {
                // 保存操作日志
                String content = "删除用户契约，agentNo：" + config.getAgentNo() + "，契约编号：" +
                        config.getContractCode() + "，契约标题：" + config.getContractTitle() + "，契约类型：" +
                        config.getContractType();
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);

                setSuccess(webJson);
            }
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }

        return webJson;
    }

    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.Contract.LIST_CONTRACT_RECORD)
    public WebJson LIST_CONTRACT_RECORD(HttpServletRequest request, @RequestParam(required = false) String agentNo,
            @RequestParam(required = false) String agentName, @RequestParam(required = false) String contractTitle,
            @RequestParam(required = false) String toPlayerName, @RequestParam(required = false) String fromPlayerName,
            @RequestParam(required = false) Integer contractType,
            @RequestParam(required = false) BigDecimal totalBetMin,
            @RequestParam(required = false) String startCalculateCycleDate,
            @RequestParam(required = false) String endCalculateCycleDate,
            @RequestParam(required = false) Integer drawStatus, @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        WebJson webJson = new WebJson();
        try {
            int firstResult = page * size;

            List<Criterion> criterions = new ArrayList<>();
            if(!StringUtils.isEmpty(startCalculateCycleDate)) {
                criterions.add(Restrictions.ge("calculateCycle", startCalculateCycleDate));
            }
            if(!StringUtils.isEmpty(endCalculateCycleDate)) {
                criterions.add(Restrictions.lt("calculateCycle", endCalculateCycleDate));
            }
            if(!StringUtils.isEmpty(agentNo)) {
                criterions.add(Restrictions.eq("agentNo", agentNo));
            }
            if(!StringUtils.isEmpty(agentName)) {
                criterions.add(Restrictions.like("agentName", agentName, MatchMode.ANYWHERE));
            }
            if(!StringUtils.isEmpty(contractTitle)) {
                criterions.add(Restrictions.like("contractTitle", contractTitle, MatchMode.ANYWHERE));
            }
            if(!StringUtils.isEmpty(toPlayerName)) {
                criterions.add(Restrictions.eq("toPlayerName", toPlayerName));
            }
            if(!StringUtils.isEmpty(fromPlayerName)) {
                if("sys".equals(fromPlayerName)) {
                    criterions.add(Restrictions.eq("fromPlayerId", 0L));
                }else {
                    criterions.add(Restrictions.eq("fromPlayerName", fromPlayerName));
                }
            }
            if(contractType != null) {
                criterions.add(Restrictions.eq("contractType", contractType));
            }
            if(totalBetMin != null) {
                criterions.add(Restrictions.gt("totalBet", totalBetMin));
            }
            if(drawStatus != null) {
                criterions.add(Restrictions.eq("drawStatus", drawStatus));
            }
            List<Order> orders = new ArrayList<>();
            orders.add(Order.desc("id"));

            int totalCount = getContractRecordDao().totalCount(criterions);
            List<ContractRecord> list = getContractRecordDao().find(criterions, orders, firstResult, size);

            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", totalCount);
            data.put("list", list);

            webJson.setData(data);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }
        return webJson;
    }

    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.Contract.DRAW_CONTRACT_RECORD)
    public WebJson DRAW_CONTRACT_RECORD(@RequestParam(name = "id") Integer id) {
        WebJson webJson = new WebJson();
        try {
            ContractRecord record = getContractRecordDao().getById(id);
            if(record == null) {
                throw newException("500-081");
            }
            boolean result = getContractService().drawContractRecord(record);

            if(result) {
                // 保存操作日志
                String content = "发放契约，agentNo：" + record.getAgentNo() + "，契约编号：" + record.getContractCode() +
                        "，契约标题：" + record.getContractTitle() + "，契约类型：" + record.getContractType() +
                        "，发放金额：" + record.getDrawAmount();
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);

                setSuccess(webJson);
            }
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            setFail(webJson);
        }

        return webJson;
    }
}
