package ph.yckj.admin.web.ctrl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import myutil.HttpUtils;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.util.StrUtils;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.report.*;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.util.ServiceException;
import sy.hoodle.base.common.dao.AgentPlayerInfoDao;
import sy.hoodle.base.common.dao.PageResult;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;
import sy.hoodle.base.common.util.ProBaseStrUtil;
import sy.hoodle.base.common.util.ProBaseTimeUtil;
import ph.yckj.common.util.WebJson;

@RestController
@RequestMapping(Route.PATH + Route.Report.PATH)
public class ReportController extends SuperController {

	@Autowired
	private TotalReportService totalReportService;

	private final static Log log = LogFactory.getLog(ReportController.class);

	@SuppressWarnings("unchecked")
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_PLAYER_REPORT)
	public WebJson SEARCH_PLAYER_REPORT(@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "playerName", required = false) String playerName,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		Integer totalCount = getAgentPlayerGameReportDao().countPlayerReport(agentNo, agentName, playerName, sDate,
				eDate);
		List<PlayerReportVo> list = new ArrayList<PlayerReportVo>();
		if (totalCount > 0) {
			list = (List<PlayerReportVo>) getAgentPlayerGameReportDao().searchPlayerReport(agentNo, agentName,
					playerName, sDate, eDate, firstResult, maxResults);
			if (!CollectionUtils.isEmpty(list)) {
				list.forEach(l -> {
					l.setTotalProfitAmount(
							l.getTotalBonusAmount().add(l.getTotalRebateAmount()).add(l.getTotalPumpAmount())
									.add(l.getTotalActivityAmount()).subtract(l.getTotalBetAmount()));
				});
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@SuppressWarnings("unchecked")
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_AGENT_REPORT)
	public WebJson SEARCH_AGENT_REPORT(@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		Integer totalCount = getAgentPlayerGameReportDao().countAgentReport(agentNo, agentName, sDate, eDate);

		List<AgentReportVo> list = new ArrayList<AgentReportVo>();
		if (totalCount > 0) {
			list = (List<AgentReportVo>) getAgentPlayerGameReportDao().searchAgentReport(agentNo, agentName, sDate,
					eDate, firstResult, maxResults);
			if (!CollectionUtils.isEmpty(list)) {
				list.forEach(l -> {
					l.setTotalProfitAmount(
							l.getTotalBonusAmount().add(l.getTotalRebateAmount()).add(l.getTotalPumpAmount())
									.add(l.getTotalActivityAmount()).subtract(l.getTotalBetAmount()));
					AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(l.getAgentNo());
					if (agentInfo != null) {
						l.setAgentName(agentInfo.getAgentName());
					}
				});
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}



	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_PLATFORM_AGENT_PLAYER_TEAM_REPORT)
	public WebJson SEARCH_PLATFORM_AGENT_PLAYER_REPORT(HttpServletRequest request,
													   @RequestParam(name = "agentNo", required = false) String agentNo,
													   @RequestParam(name = "playerName", required = false) String playerName,
													   @RequestParam(name = "playerId", required = false) Long playerId,
													   @RequestParam(name = "sDate", required = false) String sDate,
													   @RequestParam(name = "eDate", required = false) String eDate,
													   @RequestParam(name = "orderField", defaultValue = "") String orderField,
													   @RequestParam(name = "orderWay", defaultValue = "") String orderWay,
													   @RequestParam(name = "page", defaultValue = "0") int page,
													   @RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		List<PlayerTeamReportVo> dataList = new ArrayList<>();
		try {
			Date _sDate = HttpUtils.prepareQueryDate(sDate, 0).toDate();
			Date _eDate = HttpUtils.prepareQueryDate(eDate, 1).toDate();
			PageResult<PlatformAgentPlayerReport> pageReports = getPlatformAgentPlayerReportPageResult(agentNo, playerName, playerId, orderField, orderWay, page, size, _sDate, _eDate, dataList);

			for(PlayerTeamReportVo temp : dataList){
				// 自身数据
				PlatformAgentPlayerReport personReport = getPlatformAgentPlayerReportDao().platformAgentPlayerReportData(agentNo, temp.getPlayerId(), _sDate, _eDate);
				temp.setTotalTransferInAmount(personReport.getTransferInAmount().add(temp.getTotalTransferInAmount()));
				temp.setTotalTransferOutAmount(personReport.getTransferOutAmount().add(temp.getTotalTransferOutAmount()));
				temp.setTotalHandlingFee(personReport.getTotalHandlingFee().add(temp.getTotalHandlingFee()));
				temp.setTotalBetAmount(personReport.getBetAmount().add(temp.getTotalBetAmount()));
				temp.setTotalBonusAmount(personReport.getBonusAmount().add(temp.getTotalBonusAmount()));
				temp.setTotalRebateAmount(personReport.getRebateAmount().add(temp.getTotalRebateAmount()));
				temp.setTotalPumpAmount(personReport.getPumpAmount().add(temp.getTotalPumpAmount()));
				temp.setTotalRewardAmount(personReport.getRewardAmount().add(temp.getTotalRewardAmount()));
				temp.setTotalActivityAmount(personReport.getActivityAmount().add(temp.getTotalActivityAmount()));
				temp.setTotalSalaryAmount(personReport.getSalaryAmount().add(temp.getTotalSalaryAmount()));
				temp.setTotalDivsAmount(personReport.getDivsAmount().add(temp.getTotalDivsAmount()));
				temp.setTotalPlayerTransInAmount(personReport.getPlayerTransInAmount().add(temp.getTotalPlayerTransInAmount()));
				temp.setTotalPlayerTransOutAmount(personReport.getPlayerTransOutAmount().add(temp.getTotalPlayerTransOutAmount()));


				BigDecimal bonusTotal = getSafeBigDecimal(personReport.getBonusAmount());
				BigDecimal rebateTotal = getSafeBigDecimal(personReport.getRebateAmount());
				BigDecimal activityTotal = getSafeBigDecimal(personReport.getActivityAmount());
				BigDecimal salaryTotal = getSafeBigDecimal(personReport.getSalaryAmount());
				BigDecimal divsTotal = getSafeBigDecimal(personReport.getDivsAmount());
				BigDecimal betTotal = getSafeBigDecimal(personReport.getBetAmount());

				// 派奖+返点+活动+工资+分红-投注
				BigDecimal totalProfitTotal = bonusTotal.add(rebateTotal).add(activityTotal).add(salaryTotal).add(divsTotal).subtract(betTotal);

				PlatformAgentPlayerReport totalProfitAmount = getPlatformAgentPlayerReportDao().getTotalProfitAmount(agentNo, temp.getPlayerId(), _sDate, _eDate);

				log.info(" ReportController - 184 playerName :" + playerName);
				// 盈亏
				BigDecimal bonus = getSafeBigDecimal(totalProfitAmount.getBonusAmount());
				BigDecimal rebate = getSafeBigDecimal(totalProfitAmount.getRebateAmount());
				BigDecimal activity = getSafeBigDecimal(totalProfitAmount.getActivityAmount());
				BigDecimal salary = getSafeBigDecimal(totalProfitAmount.getSalaryAmount());
				BigDecimal divs = getSafeBigDecimal(totalProfitAmount.getDivsAmount());
				BigDecimal bet = getSafeBigDecimal(totalProfitAmount.getBetAmount());
				// 派奖+返点+活动+工资+分红-投注
				BigDecimal totalProfit = bonus.add(rebate).add(activity).add(salary).add(divs).subtract(bet);
				temp.setTotalProfitAmount(totalProfit.add(totalProfitTotal)); // 盈亏

			}

			Map<String, Object> data = new HashMap<String, Object>();
			if(!StringUtils.isEmpty(playerName)){
				data.put("queryNameFlag", true);
			} else {
				data.put("queryNameFlag", false);
			}
			data.put("totalCount", pageReports.getTotalCount());
			data.put("list", dataList);
			webJson.setData(data);
			setSuccess(webJson);
			return webJson;
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	private PageResult<PlatformAgentPlayerReport> getPlatformAgentPlayerReportPageResult(String agentNo, String playerName, Long playerId, String orderField, String orderWay, int page, int size, Date _sDate, Date _eDate, List<PlayerTeamReportVo> dataList) {
		PageResult<PlatformAgentPlayerReport> pageReports = getPlatformAgentPlayerReportDao().getTeamReportList(
				agentNo, playerName, playerId, _sDate, _eDate, orderField, orderWay, page, size);
		List<PlatformAgentPlayerReport> reports = pageReports.getData();
		if (!CollectionUtils.isEmpty(reports)) {
			for(PlatformAgentPlayerReport report : reports) {
				PlayerTeamReportVo temp = new PlayerTeamReportVo();
				temp.setAgentName(report.getAgentName());
				temp.setAgentNo(report.getAgentNo());
				temp.setPlayerId(report.getPlayerId());
				temp.setPlayerName(report.getPlayerName());
				temp.setDownCount(report.getDownCount());
				temp.setTotalTransferInAmount(report.getTransferInAmount());
				temp.setTotalTransferOutAmount(report.getTransferOutAmount());
				temp.setTotalHandlingFee(report.getTotalHandlingFee());
				temp.setTotalBetAmount(report.getBetAmount());
				temp.setTotalBonusAmount(report.getBonusAmount());
				temp.setTotalRebateAmount(report.getRebateAmount());
				temp.setTotalPumpAmount(report.getPumpAmount());
				temp.setTotalRewardAmount(report.getRewardAmount());
				temp.setTotalActivityAmount(report.getActivityAmount());
				temp.setTotalSalaryAmount(report.getSalaryAmount());
				temp.setTotalDivsAmount(report.getDivsAmount());
				temp.setTotalPlayerTransInAmount(report.getPlayerTransInAmount());
				temp.setTotalPlayerTransOutAmount(report.getPlayerTransOutAmount());

				// 明细报表中含自己的余额
				List<AgentPlayerInfo> agentPlayerInfoList = getAgentPlayerInfoDao().findByPlayerIdIncludingPidsInMyBalance(report.getPlayerId(), agentNo);
				if (!CollectionUtils.isEmpty(agentPlayerInfoList)) {
					for (AgentPlayerInfo api : agentPlayerInfoList) {
						temp.setTeamAvailableBalance(temp.getTeamAvailableBalance().add(api.getPlayerAvailableBalance()));
					}
				}

				PlatformAgentPlayerReport totalProfitAmount = getPlatformAgentPlayerReportDao().getTotalProfitAmount(agentNo, report.getPlayerId(), _sDate, _eDate);

				log.info(" ReportController - 184 playerName :" + playerName);
				// 盈亏
				BigDecimal bonus = getSafeBigDecimal(totalProfitAmount.getBonusAmount());
				BigDecimal rebate = getSafeBigDecimal(totalProfitAmount.getRebateAmount());
				BigDecimal activity = getSafeBigDecimal(totalProfitAmount.getActivityAmount());
				BigDecimal salary = getSafeBigDecimal(totalProfitAmount.getSalaryAmount());
				BigDecimal divs = getSafeBigDecimal(totalProfitAmount.getDivsAmount());
				BigDecimal bet = getSafeBigDecimal(totalProfitAmount.getBetAmount());
				// 派奖+返点+活动+工资+分红-投注
				BigDecimal totalProfit = bonus.add(rebate).add(activity).add(salary).add(divs).subtract(bet);
				temp.setTotalProfitAmount(totalProfit); // 盈亏

				if(!StringUtils.isEmpty(playerName)){
					temp.setQueryNameFlag(true);
				}
				dataList.add(temp);
			}
		}
		return pageReports;
	}


	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_PLATFORM_AGENT_PLAYER_PERSON_REPORT)
	public WebJson SEARCH_PLATFORM_AGENT_PLAYER_PERSON_REPORT(HttpServletRequest request,
															  @RequestParam(name = "agentNo", required = false) String agentNo,
															  @RequestParam(name = "playerName", required = false) String playerName,
															  @RequestParam(name = "sDate", required = false) String sDate,
															  @RequestParam(name = "eDate", required = false) String eDate) {
		WebJson webJson = new WebJson();
		try {
			Date _sDate = HttpUtils.prepareQueryDate(sDate, 0).toDate();
			Date _eDate = HttpUtils.prepareQueryDate(eDate, 1).toDate();
			PlayerTeamReportVo temp = getPlayerTeamReportVo(agentNo, playerName, _sDate, _eDate);
			if (temp == null) return null;
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("data", temp);
			webJson.setData(data);
			setSuccess(webJson);
			return webJson;
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	private PlayerTeamReportVo getPlayerTeamReportVo(String agentNo, String playerName, Date _sDate, Date _eDate) {
		PlatformAgentPlayerReport report = getPlatformAgentPlayerReportDao().getPersonReport(agentNo, playerName, _sDate, _eDate);
		if(null == report){
			return null;
		}
		PlayerTeamReportVo temp = new PlayerTeamReportVo();
		temp.setAgentName(report.getAgentName());
		temp.setAgentNo(report.getAgentNo());
		temp.setPlayerId(report.getPlayerId());
		temp.setPlayerName(report.getPlayerName());
		temp.setDownCount(report.getDownCount());
		temp.setTotalTransferInAmount(report.getTransferInAmount());
		temp.setTotalTransferOutAmount(report.getTransferOutAmount());
		temp.setTotalHandlingFee(report.getTotalHandlingFee());
		temp.setTotalBetAmount(report.getBetAmount());
		temp.setTotalBonusAmount(report.getBonusAmount());
		temp.setTotalRebateAmount(report.getRebateAmount());
		temp.setTotalPumpAmount(report.getPumpAmount());
		temp.setTotalRewardAmount(report.getRewardAmount());
		temp.setTotalActivityAmount(report.getActivityAmount());
		temp.setTotalSalaryAmount(report.getSalaryAmount());
		temp.setTotalDivsAmount(report.getDivsAmount());
		temp.setTotalPlayerTransInAmount(report.getPlayerTransInAmount());
		temp.setTotalPlayerTransOutAmount(report.getPlayerTransOutAmount());
		temp.setTotalProfitAmount(
				report.getBonusAmount()
						.add(report.getRebateAmount())
						.add(report.getActivityAmount())
						.add(report.getSalaryAmount())
						.add(report.getDivsAmount())
						.subtract(report.getBetAmount()));  // 盈亏
		temp.setTeamAvailableBalance(report.getPlayerAvailableBalance());
		temp.setTeamBalance(report.getTeamBalance());
		return temp;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_PLATFORM_AGENT_PLAYER_TEAM_TOTAL_REPORT)
	public WebJson SEARCH_PLATFORM_AGENT_PLAYER_TEAM_TOTAL_REPORT(HttpServletRequest request,
																  @RequestParam(name = "agentNo", required = false) String agentNo,
																  @RequestParam(name = "playerName", required = false) String playerName,
																  @RequestParam(name = "playerId", required = false) Long playerId,
																  @RequestParam(name = "sDate", required = false) String sDate,
																  @RequestParam(name = "eDate", required = false) String eDate) {
		WebJson webJson = new WebJson();

		PlayerTeamReportVo temp = new PlayerTeamReportVo();
		try {
			Date _sDate = HttpUtils.prepareQueryDate(sDate, 0).toDate();
			Date _eDate = HttpUtils.prepareQueryDate(eDate, 1).toDate();
			PlatformAgentPlayerReport report = getPlatformAgentPlayerReportDao().getTeamTotalReport(
					agentNo, playerName,  playerId, _sDate, _eDate);

			Long tempPlayerId = null;
			// 前端查出来的
			if(!StringUtils.isEmpty(playerName)){
				AgentPlayerInfo byPlayerName = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if(null == byPlayerName){
					return null;
				}
				tempPlayerId = byPlayerName.getPlayerId();
			}

			if (report != null) {
				List<AgentPlayerInfo> upPlayers = getAgentPlayerInfoDao().listAllParent(report.getPids());
				List<String> pidsAndPlayerNames = new ArrayList<>();
				if (upPlayers != null && !upPlayers.isEmpty()) {
					for(AgentPlayerInfo upPlayer : upPlayers) {
						pidsAndPlayerNames.add(upPlayer.getPlayerId() + "|" + upPlayer.getPlayerName());
					}
				}
				temp.setAgentName(report.getAgentName());
				temp.setPidsAndPlayerNames(pidsAndPlayerNames);
				temp.setPlayerId(report.getPlayerId());
				temp.setPlayerName(report.getPlayerName());
				temp.setTotalTransferInAmount(report.getTransferInAmount());
				temp.setTotalTransferOutAmount(report.getTransferOutAmount());
				temp.setTotalHandlingFee(report.getTotalHandlingFee());
				temp.setTotalBetAmount(report.getBetAmount());
				temp.setTotalBonusAmount(report.getBonusAmount());
				temp.setTotalRebateAmount(report.getRebateAmount());
				temp.setTotalPumpAmount(report.getPumpAmount());
				temp.setTotalRewardAmount(report.getRewardAmount());
				temp.setTotalActivityAmount(report.getActivityAmount());
				temp.setTotalSalaryAmount(report.getSalaryAmount());
				temp.setTotalDivsAmount(report.getDivsAmount());
				temp.setTotalPlayerTransInAmount(report.getPlayerTransInAmount());
				temp.setTotalPlayerTransOutAmount(report.getPlayerTransOutAmount());

				// 明细报表中含自己的余额
				List<AgentPlayerInfo> agentPlayerInfoList = getAgentPlayerInfoDao().findByPlayerIdIncludingPidsInMyBalance(tempPlayerId, agentNo);
				if (!CollectionUtils.isEmpty(agentPlayerInfoList)) {
					for (AgentPlayerInfo api : agentPlayerInfoList) {
						temp.setTeamAvailableBalance(temp.getTeamAvailableBalance().add(api.getPlayerAvailableBalance()));
					}
				}

				log.info(" ReportController - 347 playerName :" + playerName);

//				List<PlayerTeamReportVo> dataList = new ArrayList<>();
				// 盈亏
//				PageResult<PlatformAgentPlayerReport> pageReports = getPlatformAgentPlayerReportPageResult(agentNo, playerName, playerId, null, null, 0, 20, _sDate, _eDate, dataList);
//				PlayerTeamReportVo playerTeamReportVo = getPlayerTeamReportVo(agentNo, playerName, _sDate, _eDate);
//				if(null != playerTeamReportVo){
//					dataList.add(playerTeamReportVo);
//				}
//				BigDecimal totalProfit = BigDecimal.ZERO;
//				for(PlayerTeamReportVo totalProfitAmount : dataList){
//					totalProfit = totalProfit.add(totalProfitAmount.getTotalProfitAmount());
//				}

				PlatformAgentPlayerReport platformAgentPlayerReport = getPlatformAgentPlayerReportDao().getTotalProfitAmount(agentNo, tempPlayerId, playerName, _sDate, _eDate);


				BigDecimal totalProfitAmount =
						report.getBonusAmount()
								.add(platformAgentPlayerReport.getRebateAmount())
								.add(platformAgentPlayerReport.getActivityAmount())
								.add(platformAgentPlayerReport.getSalaryAmount())
								.add(platformAgentPlayerReport.getDivsAmount())
								.subtract(platformAgentPlayerReport.getBetAmount());

				temp.setTotalProfitAmount(totalProfitAmount);
			}
			webJson.setData(temp);
			setSuccess(webJson);
			return webJson;
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}


	// 辅助方法
	private BigDecimal getSafeBigDecimal(BigDecimal value) {
		return value != null ? value : BigDecimal.ZERO;
	}



	/**
	 * 查询游戏彩种报表
	 *
	 * @param gameTypeCode 游戏类型code
	 * @param lottery      游戏简称
	 * @param sTime        开始时间
	 * @param eTime        结束时间
	 * @param page         当前页码
	 * @param size         每页条数
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_GAME_LOTTERY_REPORT)
	public WebJson SEARCH_GAME_LOTTERY_REPORT(
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "gameTypeCode", required = false) String gameTypeCode,
			@RequestParam(name = "lottery", required = false) String lottery,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "orderField", defaultValue = "") String orderField,
			@RequestParam(name = "orderWay", defaultValue = "") String orderWay,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		Integer totalCount = getGameMethodReportDao().countGameLotteryReportByAgentNo(agentNo, agentName, gameTypeCode, lottery, sDate, eDate);
		List<GameLotteryReportVo> list = new ArrayList<GameLotteryReportVo>();
		if (totalCount > 0) {
			list = (List<GameLotteryReportVo>) getGameMethodReportDao().searchGameLotteryReportByAgentNo(agentNo, agentName, gameTypeCode, lottery,
					sDate, eDate, orderField, orderWay, firstResult, maxResults);
			list.forEach(l -> {
				l.setTotalProfitAmount(l.getTotalBonusAmount().subtract(l.getTotalBetAmount()));

				GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(l.getGameTypeCode());
				if (gameLotteryType != null) {
					l.setGameTypeName(gameLotteryType.getGameTypeName());
				}
				GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(l.getLottery());
				if (gameLotteryInfo != null) {
					l.setGameName(gameLotteryInfo.getGameName());
				}

			});
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	/**
	 * 查询游戏玩法报表
	 *
	 * @param gameTypeCode 游戏类型code
	 * @param lottery      游戏简称
	 * @param methodCode   玩法code
	 * @param sTime        开始时间
	 * @param eTime        结束时间
	 * @param page         当前页码
	 * @param size         每页条数
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_GAME_METHOD_REPORT)
	public WebJson SEARCH_GAME_METHOD_REPORT(
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "gameTypeCode", required = false) String gameTypeCode,
			@RequestParam(name = "lottery", required = false) String lottery,
			@RequestParam(name = "methodType", required = false) Integer methodType,
			@RequestParam(name = "methodCode", required = false) String methodCode,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "orderField", defaultValue = "") String orderField,
			@RequestParam(name = "orderWay", defaultValue = "") String orderWay,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		List<String> methodCodeList = null;
		if(!StringUtils.isEmpty(methodCode)) {
			methodCodeList = Arrays.asList(methodCode.split(","));
		}
		Integer totalCount = getGameMethodReportDao().countGameMethodReport(agentNo, agentName,gameTypeCode, lottery, methodType,
				methodCodeList, sDate, eDate);
		List<GameMethodReportVo> list = new ArrayList<>();
		if (totalCount > 0) {
			list = (List<GameMethodReportVo>) getGameMethodReportDao().searchGameMethodReport(agentNo, agentName, gameTypeCode, lottery,
					methodType, methodCodeList, sDate, eDate, orderField, orderWay, firstResult, maxResults);
			list.forEach(l -> {
				l.setTotalProfitAmount(l.getTotalBonusAmount().subtract(l.getTotalBetAmount()));
				GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(l.getGameTypeCode());
				if (gameLotteryType != null) {
					l.setGameTypeName(gameLotteryType.getGameTypeName());
				}
				GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(l.getLottery());
				if (gameLotteryInfo != null) {
					l.setGameName(gameLotteryInfo.getGameName());
				}
				GameLotteryMethod gameLotteryMethod = getRedisService().getGameLotteryMethod(l.getGameTypeCode(),
						l.getMethodCode());
				if (gameLotteryMethod != null) {
					l.setMethodName(gameLotteryMethod.getMethodName());
				}
			});
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}


	/**
	 * 查询游戏总报表
	 *
	 * @param sTime 开始时间
	 * @param eTime 结束时间
	 * @param page  当前页码
	 * @param size  每页条数
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_GAME_TYPE_REPORT)
	public WebJson SEARCH_GAME_TYPE_REPORT(@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		Integer totalCount = getGameMethodReportDao().countGameTypeReport(sDate, eDate);
		List<GameTypeReportVo> list = new ArrayList<>();
		if (totalCount > 0) {
			list = (List<GameTypeReportVo>) getGameMethodReportDao().searchGameTypeReport(sDate, eDate, firstResult,
					maxResults);
			list.forEach(l -> {
				l.setTotalProfitAmount(l.getTotalBonusAmount().subtract(l.getTotalBetAmount()));
			});
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	// 新增报表接口

	/**
	 * 查询三方游戏玩家报表
	 *
	 * @param agentNo    代理编号
	 * @param agentName  代理名称
	 * @param playerName 玩家名称
	 * @param sTime      开始时间
	 * @param eTime      结束时间
	 * @param page       当前页码
	 * @param size       每页条数
	 * @return
	 */
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_THIRD_PLAYER_REPORT)
	public WebJson SEARCH_THIRD_PLAYER_REPORT(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "playerName", required = false) String playerName,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		String agentNumber = "";
		AgentInfo agent = getAgent(request);
		if (null == agent) {
			log.info("AgentInfo is null");
		} else {
			log.info("AgentInfo: " + agent.toString());
		}

		if (null != agent) {
			agentNumber = agent.getAgentNo();
			log.info("agentNumber from AgentInfo: " + agentNumber);
		}

		if (StringUtils.isEmpty(agentNumber)) {
			agentNumber = agentNo;
			log.info("agentNumber from incoming agentNo: " + agentNumber);
		}

		Integer totalCount = getThirdAgentPlayerReportDao().countPlayerReport(agentNumber, agentName, playerName, sDate,
				eDate);
		List<PlayerReportVo> list = new ArrayList<PlayerReportVo>();
		if (totalCount > 0) {
			list = (List<PlayerReportVo>) getThirdAgentPlayerReportDao().searchPlayerReport(agentNumber, agentName,
					playerName, sDate, eDate, firstResult, maxResults);
			if (!CollectionUtils.isEmpty(list)) {
				list.forEach(l -> {
					l.setTotalProfitAmount(
							l.getTotalBonusAmount().add(l.getTotalRebateAmount()).add(l.getTotalPumpAmount())
									.add(l.getTotalActivityAmount()).subtract(l.getTotalBetAmount()));
				});
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	/**
	 * 查询平台玩家报表
	 *
	 * @param agentNo    代理编号
	 * @param agentName  代理名称
	 * @param playerName 玩家名称
	 * @param sTime      开始时间
	 * @param eTime      结束时间
	 * @param page       当前页码
	 * @param size       每页条数
	 * @return
	 */
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_PLATFORM_PLAYER_REPORT)
	public WebJson SEARCH_PLATFORM_PLAYER_REPORT(HttpServletRequest request,
												 @RequestParam(name = "agentNo", required = false) String agentNo,
												 @RequestParam(name = "agentName", required = false) String agentName,
												 @RequestParam(name = "playerName", required = false) String playerName,
												 @RequestParam(name = "sTime", required = false) String sTime,
												 @RequestParam(name = "eTime", required = false) String eTime,
												 @RequestParam(name = "orderField", defaultValue = "") String orderField,
												 @RequestParam(name = "orderWay", defaultValue = "") String orderWay,
												 @RequestParam(name = "page", defaultValue = "0") int page,
												 @RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		String agentNumber = "";
		AgentInfo agent = getAgent(request);
		if (null == agent) {
			log.info("AgentInfo is null");
		} else {
			log.info("AgentInfo: " + agent.toString());
		}

		if (null != agent) {
			agentNumber = agent.getAgentNo();
			log.info("agentNumber from AgentInfo: " + agentNumber);
		}

		if (StringUtils.isEmpty(agentNumber)) {
			agentNumber = agentNo;
			log.info("agentNumber from incoming agentNo: " + agentNumber);
		}

		Integer totalCount = getPlatformAgentPlayerReportDao().countPlayerReport(agentNumber, agentName, playerName,
				sDate, eDate);
		List<PlayerReportVo> list = new ArrayList<PlayerReportVo>();
		List<PlatformAgentPlayerReport> reports = new ArrayList<>();
		if (totalCount > 0) {
			reports = (List<PlatformAgentPlayerReport>) getPlatformAgentPlayerReportDao().searchPlayerReportForAdmin(agentNumber,
					agentName, null, playerName, sDate, eDate, orderField, orderWay, firstResult, maxResults);
			if (!CollectionUtils.isEmpty(reports)) {
				for(PlatformAgentPlayerReport reportVo : reports) {
					PlayerReportVo temp = new PlayerReportVo();
					temp.setAgentNo(reportVo.getAgentNo());
					temp.setAgentName(reportVo.getAgentName());
					temp.setPlayerId(reportVo.getPlayerId());
					temp.setPlayerName(reportVo.getPlayerName());
					temp.setPlayerAvailableBalance(reportVo.getPlayerAvailableBalance()); // 玩家余额
					temp.setTotalTransferInAmount(reportVo.getTransferInAmount());		  // 充值
					temp.setTotalTransferOutAmount(reportVo.getTransferOutAmount());	  // 提现
					temp.setTotalHandlingFee(reportVo.getTotalHandlingFee()); 	// 手续费
					temp.setTotalBetAmount(reportVo.getBetAmount());					  // 投注
					temp.setTotalBonusAmount(reportVo.getBonusAmount()); 				  // 派奖
					temp.setTotalRebateAmount(reportVo.getRebateAmount());	// 返点
					temp.setTotalPumpAmount(reportVo.getPumpAmount());					  // 抽水
					temp.setTotalRewardAmount(reportVo.getRewardAmount());				  // 打赏
					temp.setTotalActivityAmount(reportVo.getActivityAmount());	// 活动
					temp.setTotalSalaryAmount(reportVo.getSalaryAmount());	// 工资
					temp.setTotalDivsAmount(reportVo.getDivsAmount());	// 分红
					temp.setTotalUpAndDownTransferIn(reportVo.getPlayerTransInAmount());  //  上下级转入
					temp.setTotalUpAndDownTransferOut(reportVo.getPlayerTransOutAmount());  // 上下级转出
					temp.setTotalProfitAmount(
							reportVo.getBonusAmount()
									.add(reportVo.getRebateAmount())
									.add(reportVo.getActivityAmount())
									.add(reportVo.getSalaryAmount())
									.add(reportVo.getDivsAmount())
									.subtract(reportVo.getBetAmount()));  // 盈亏
					list.add(temp);
				}
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	/**
	 * 查询三方游戏代理商报表
	 *
	 * @param agentNo   代理编号
	 * @param agentName 代理名称
	 * @param sTime     开始时间
	 * @param eTime     结束时间
	 * @param page      当前页码
	 * @param size      每页条数
	 * @return
	 */
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_THIRD_AGENT_REPORT)
	public WebJson SEARCH_THIRD_AGENT_REPORT(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		String agentNumber = "";
		AgentInfo agent = getAgent(request);
		if (null == agent) {
			log.info("AgentInfo is null");
		} else {
			log.info("AgentInfo: " + agent.toString());
		}

		if (null != agent) {
			agentNumber = agent.getAgentNo();
			log.info("agentNumber from AgentInfo: " + agentNumber);
		}

		if (StringUtils.isEmpty(agentNumber)) {
			agentNumber = agentNo;
			log.info("agentNumber from incoming agentNo: " + agentNumber);
		}

		Integer totalCount = getThirdAgentPlayerReportDao().countAgentReport(agentNumber, agentName, sDate, eDate);

		List<AgentReportVo> list = new ArrayList<AgentReportVo>();
		if (totalCount > 0) {
			list = (List<AgentReportVo>) getThirdAgentPlayerReportDao().searchAgentReport(agentNumber, agentName, sDate,
					eDate, firstResult, maxResults);

			if (!CollectionUtils.isEmpty(list)) {
				list.forEach(l -> {
					l.setTotalProfitAmount(
							l.getTotalBonusAmount().add(l.getTotalRebateAmount()).add(l.getTotalPumpAmount())
									.add(l.getTotalActivityAmount()).subtract(l.getTotalBetAmount()));
					AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(l.getAgentNo());
					if (agentInfo != null) {
						l.setAgentName(agentInfo.getAgentName());
					}
				});
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	/**
	 * 查询平台代理商报表
	 *
	 * @param agentNo   代理编号
	 * @param agentName 代理名称
	 * @param sTime     开始时间
	 * @param eTime     结束时间
	 * @param page      当前页码
	 * @param size      每页条数
	 * @return
	 */
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Report.SEARCH_PLATFORM_AGENT_REPORT)
	public WebJson SEARCH_PLATFORM_AGENT_REPORT(HttpServletRequest request,
												@RequestParam(name = "agentNo", required = false) String agentNo,
												@RequestParam(name = "agentName", required = false) String agentName,
												@RequestParam(name = "sTime", required = false) String sTime,
												@RequestParam(name = "eTime", required = false) String eTime,
												@RequestParam(name = "orderField", defaultValue = "") String orderField,
												@RequestParam(name = "orderWay", defaultValue = "") String orderWay,
												@RequestParam(name = "page", defaultValue = "0") int page,
												@RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		String agentNumber = "";
		AgentInfo agent = getAgent(request);

		if (null == agent) {
			log.info("AgentInfo is null");
		} else {
			log.info("AgentInfo: " + agent.toString());
		}

		if (null != agent) {
			agentNumber = agent.getAgentNo();
			log.info("agentNumber from AgentInfo: " + agentNumber);
		}

		if (StringUtils.isEmpty(agentNumber)) {
			agentNumber = agentNo;
			log.info("agentNumber from incoming agentNo: " + agentNumber);
		}

		Integer totalCount = getPlatformAgentPlayerReportDao().countAgentReport(agentNumber, agentName, sDate, eDate);
		List<PlatformAgentPlayerReport> reports = new ArrayList<>();
		List<PlayerTeamReportVo> list = new ArrayList<PlayerTeamReportVo>();
		if (totalCount > 0) {
			reports = (List<PlatformAgentPlayerReport>) getPlatformAgentPlayerReportDao().searchAgentReport(agentNumber, agentName,
					sDate, eDate, orderField, orderWay, firstResult, maxResults);
			for(PlatformAgentPlayerReport report : reports) {
				PlayerTeamReportVo temp = new PlayerTeamReportVo();
				temp.setAgentName(report.getAgentName());
				temp.setAgentNo(report.getAgentNo());
				temp.setTotalTransferInAmount(report.getTransferInAmount());
				temp.setTotalTransferOutAmount(report.getTransferOutAmount());
				temp.setTotalHandlingFee(report.getTotalHandlingFee());
				temp.setTotalBetAmount(report.getBetAmount());
				temp.setTotalBonusAmount(report.getBonusAmount());
				temp.setTotalRebateAmount(report.getRebateAmount());
				temp.setTotalPumpAmount(report.getPumpAmount());
				temp.setTotalRewardAmount(report.getRewardAmount());
				temp.setTotalActivityAmount(report.getActivityAmount());
				temp.setTotalSalaryAmount(report.getSalaryAmount());
				temp.setTotalDivsAmount(report.getDivsAmount());
				temp.setTotalPlayerTransInAmount(report.getPlayerTransInAmount());
				temp.setTotalPlayerTransOutAmount(report.getPlayerTransOutAmount());
				temp.setTeamAvailableBalance(report.getPlayerAvailableBalance());
				temp.setTotalProfitAmount(
						report.getBonusAmount()
								.add(report.getRebateAmount())
								.add(report.getActivityAmount())
								.add(report.getSalaryAmount())
								.add(report.getDivsAmount())
								.subtract(report.getBetAmount()));
				list.add(temp);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}



	@CheckLogin
	@RequestMapping(Route.Report.MANUAL_REPORT)
	public WebJson MANUAL_REPORT(HttpServletRequest request,
								 @RequestParam(name = "agentNo") String agentNo,
								 @RequestParam(name = "playerName") String playerName,
								 @RequestParam(name = "sDate") String sDate,
								 @RequestParam(name = "eDate") String eDate) {
		WebJson webJson = new WebJson();
		Date _sDate = HttpUtils.prepareQueryDate(sDate, 0).toDate();
		Date _eDate = HttpUtils.prepareQueryDate(eDate, 1).toDate();
		// 处理日期,因为报表 2-15 到 2-16 左闭右边开 不包含 2-16, 所以 2-16 修改为 2-15 23:59:59
		if (_sDate.after(_eDate) || sDate.equals(eDate)) {
			setFail(webJson);
			webJson.setMessage("结束日期必须在开始日期之后");
			return webJson;

		}
		List<AgentPlayerInfo> agentPlayerInfoList;
		try{
			if (ProBaseStrUtil.isBlank(playerName)) {
				// 当没有传入玩家用户名时,查询出代理所有用户进行报表修复
				agentPlayerInfoList = getAgentPlayerInfoDao().getByAgentNo(agentNo);
			} else {
				AgentPlayerInfo agentPlayerInfo = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == agentPlayerInfo) {
					throw newException("100-01");
				}
				agentPlayerInfoList = Arrays.asList(agentPlayerInfo);
			}

		}catch (ServiceException e){
			setError(webJson,e);
			return webJson;
		}
		for (AgentPlayerInfo agentPlayerInfo : agentPlayerInfoList) {
			try {
				totalReportService.begin(agentPlayerInfo, ReportTargetDataType.ALL, _sDate, _eDate);
			} catch (Exception e) {
				log.error("报表生成失败", e);
				setFail(webJson);
				return webJson;
			}
		}
		setSuccess(webJson);
		return webJson;
	}
}
