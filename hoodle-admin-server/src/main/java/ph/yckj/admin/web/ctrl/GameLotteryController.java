package ph.yckj.admin.web.ctrl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import lottery.utils.prize.LotteryType;
import myutil.*;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.entity.AdminLog;
import ph.yckj.admin.entity.PushStreamEvent;
import ph.yckj.admin.enums.CommonStatus;
import ph.yckj.admin.job.AdminLogJob;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.game.*;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.enums.LotteryMaintainStatus;
import ph.yckj.common.service.S3VideoUploadService;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;
import sy.hoodle.base.common.entity.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.GameLottery.PATH)
public class GameLotteryController extends SuperController {

	@Autowired
	private S3VideoUploadService s3VideoUploadService;

	@RequestMapping(Route.GameLottery.STATIS_LIST_TYPE)
	public List<GameLotteryTypeVo> STATIS_LIST_TYPE() {
		List<GameLotteryType> list = getRedisService().getGameLotteryTypeList();
		return ObjectUtils.copyProperties(list, GameLotteryTypeVo.class);
	}

	@RequestMapping(Route.GameLottery.STATIC_LIST_INFO)
	public Object STATIC_LIST_INFO(@RequestParam(name = "gameName", required = false) String gameName,
								   @RequestParam(name = "gameTypeCode", required = false) String gameTypeCode,
								   @RequestParam(name = "gameClass", required = false) String gameClass) {
		if (!StringUtils.isEmpty(gameName)) {
			return getRedisService().getGameLotteryInfo(gameName);
		} else {
			List<GameLotteryInfo> list = getRedisService().getGameLotteryInfoList();
			if(!StringUtils.isEmpty(gameTypeCode)) {
				list = list.stream().filter(obj -> obj.getGameTypeCode().equals(gameTypeCode)).collect(
						Collectors.toList());
			}
			if(!StringUtils.isEmpty(gameClass)) {
				list = list.stream().filter(obj -> obj.getGameClass().equals(gameClass)).collect(Collectors.toList());
			}
			return list;
		}
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.PROBABILITY_ANALYSIS)
	public WebJson PROBABILITY_ANALYSIS(@RequestParam(name = "lottery", required = false) String lottery,
										@RequestParam(name = "methodCode", required = false) String methodCode,
										@RequestParam(name = "issues", required = false) Integer issues,
										@RequestParam(name = "orderField", defaultValue = "") String orderField,
										@RequestParam(name = "orderWay", defaultValue = "") String orderWay) {
		WebJson webJson = new WebJson();
		setSuccess(webJson);
		if(StringUtils.isEmpty(lottery)) {
			return webJson;
		}
		if(issues == null || issues < 100) {
			issues = 100;
		}else if(issues > 10000) {
			issues = 10000;
		}
		List<ProbabilityAnalysisVo> list = getGameLotteryProbabilityAnalysisService().probabilityAnalysis(lottery,
				methodCode, issues, orderField, orderWay);
		webJson.setData(list);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.LIST_LOTTERY_TYPE)
	public WebJson LIST_LOTTERY_TYPE() {
		WebJson webJson = new WebJson();
		List<GameLotteryType> list = getGameLotteryTypeDao().listAll();
		webJson.setData(list);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_TYPE_STATUS)
	public WebJson UPDATE_LOTTERY_TYPE_STATUS(@RequestParam(name = "id") Long id,
											  @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		GameLotteryType entity = getGameLotteryTypeDao().getById(id);
		if (null == entity) {
			throw newException("-1", "该数据不存在！");
		}
		boolean result = getGameLotteryService().updateLotteryTypeStatus(id, status);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_TYPE);
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
			// 保存操作日志
			String button = (0 == status) ? "启用" : "禁用";
			String content = button + entity.getGameTypeName() + "，游戏大厅id：" + id;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_PRE_STEP_LOTTERY_TYPE)
	public WebJson UPDATE_PRE_STEP_LOTTERY_TYPE(@RequestParam(name = "sort") Integer sort) {
		WebJson webJson = new WebJson();
		if (sort == null || sort <= 0) {
			throw newException("无效的升序操作");
		}
		boolean result = getGameLotteryService().preStepSortLotteryType(sort);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_TYPE);
			// 保存操作日志
			String content = "游戏大厅，排序值：" + sort + "，上移";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_NEXT_STEP_LOTTERY_TYPE)
	public WebJson UPDATE_NEXT_STEP_LOTTERY_TYPE(@RequestParam(name = "sort") Integer sort) {
		WebJson webJson = new WebJson();
		if (sort == null || sort <= 0) {
			throw newException("无效的降序操作");
		}
		boolean result = getGameLotteryService().nextStepSortLotteryType(sort);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_TYPE);
			// 保存操作日志
			String content = "游戏大厅，排序值：" + sort + "，下移";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.LIST_LOTTERY_INFO)
	public WebJson LIST_LOTTERY_INFO(@RequestParam(name = "gameTypeCode", required = false) String gameTypeCode) {
		WebJson webJson = new WebJson();
		List<Criterion> criterions = new ArrayList<>();
		// 游戏直播页面，仅返回弹珠彩种
		criterions.add(Restrictions.eq("gameClass", GameLotteryInfo.GAME_CLASS_DZ));
		if (!StringUtils.isEmpty(gameTypeCode)) {
			criterions.add(Restrictions.eq("gameTypeCode", gameTypeCode));
		}
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<>();
		orders.add(Order.asc("sort"));
		int totalCount = getGameLotteryInfoDao().totalCount(criterions);
		List<GameLotteryInfoVo> list = new ArrayList<>();
		if (totalCount > 0) {
			List<GameLotteryInfo> resultList = getGameLotteryInfoDao().find(criterions, orders, -1, -1);
			for (GameLotteryInfo entity : resultList) {
				GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(entity.getGameTypeCode());
				GameLotteryGroup gameLotteryGroup = getRedisService().getGameLotteryGroup(entity.getGameGroupCode());
				list.add(new GameLotteryInfoVo(entity, gameLotteryType, gameLotteryGroup));
			}
		}
		webJson.setData(list);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.EDIT_LOTTERY_INFO)
	public WebJson EDIT_LOTTERY_INFO(@RequestParam int gameId, @RequestParam String gameRoomName,
									 @RequestParam String gameCompereName, @RequestParam String telegramAccount, @RequestParam String gameNotice,
									 @RequestParam Integer stopDelay, @RequestParam Integer downCode, @RequestParam Integer fenDownCode,
									 @RequestParam Integer liDownCode, @RequestParam Integer floatBonus, @RequestParam BigDecimal maxBonus) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getGameLotteryService().editLotteryInfo(gameId, gameRoomName, gameCompereName,
					telegramAccount, gameNotice, stopDelay, downCode, fenDownCode, liDownCode, floatBonus, maxBonus);
			if (result) {
				// 更新代理商直播间数据
				getGameLotteryService().editAgentGameLotteryInfo(gameId, gameRoomName, gameCompereName, telegramAccount,
						gameNotice);
				getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
				// 保存操作日志
				String content = "直播间id：" + gameId + "，直播间名称：" + gameRoomName + "，主持人名称：" +
						gameCompereName + "，直播间联系Telegram账号：" + telegramAccount + "，直播间公告：" + gameNotice +
						"，提前截止时间：" + stopDelay + "，彩票奖级：" + downCode + "，分模式降点：" + fenDownCode +
						"，厘模式降点：" + liDownCode + "，浮动奖级：" + floatBonus + "，最大奖金：" + maxBonus;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_INFO_STATUS)
	public WebJson UPDATE_LOTTERY_INFO_STATUS(@RequestParam(name = "id") Long id,
											  @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		try {
			GameLotteryInfo gameLotteryInfo = getGameLotteryInfoDao().getById(id);
			if (!(GameLotteryInfo.GAME_STATUS_NORMAL == status || GameLotteryInfo.GAME_STATUS_FORBIDDEN == status)) {
				throw newException("未知的直播间状态！");
			}
			boolean result = getGameLotteryService().updateLotteryGameStatus(gameLotteryInfo.getGameId(), status);
			if (result) {
				if(GameLotteryInfo.GAME_STATUS_FORBIDDEN == status) {
					// 禁用时，将全部代理商该彩种都禁用
					getAgentGameLotteryInfoDao().updateAllAgentLotteryStatus(gameLotteryInfo.getLottery(),
							AgentGameLotteryInfo.BAN_STATUS_DISABLED);
				}
				// 清除缓存
				getRedisService().delGameLotteryInfo(gameLotteryInfo.getLottery());
				getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
				// 保存操作日志
				String str = (status == 0) ? "启用" : "禁用";
				String lottery = gameLotteryInfo.getLottery();
				String gameName = gameLotteryInfo.getGameName();
				String content = "直播间代码：" + lottery + "，直播间名称：" + gameName + "，直播状态：" + status + "（" + str + "）";
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_INFO_MAINTAIN_STATUS)
	public WebJson UPDATE_LOTTERY_INFO_STATUS(@RequestParam(name = "id") Long id,
											  @RequestParam(name = "status") int status,
											  @RequestParam(name = "maintainEndTime", required = false) Long maintainEndTime) {
		WebJson webJson = new WebJson();
		try {

			LotteryMaintainStatus byCode = LotteryMaintainStatus.getByCode(status);
			if (byCode == null) {
				throw newException("未知的直播间维护状态！");
			}
			if (byCode.equals(LotteryMaintainStatus.MAINTAIN) && maintainEndTime == null) {
				throw newException("开始维护必须输入结束时间！");
			}

			GameLotteryInfo gameLotteryInfo = getGameLotteryInfoDao().getById(id);

			if (gameLotteryInfo == null) {
				throw newException("未找到直播间！");
			}
			Timestamp ts = null;
			if (maintainEndTime != null) {
				ts = new Timestamp(maintainEndTime);
			}

			boolean result = getGameLotteryService().updateLotteryGameMaintainStatus(gameLotteryInfo.getGameId(),
					status, ts);
			if (result) {
				getGameLotteryService().updateAgentGameLotteryMaintainStatus(gameLotteryInfo.getLottery(), status, ts);

				boolean b = getRedisService().delGameLotteryInfo(gameLotteryInfo.getLottery());
				if (b) {
					log.warn("delGameLotteryInfo 失败, lottery:{}", gameLotteryInfo.getLottery());
				}
				b = getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
				if (b) {
					log.warn("updateSystemCacheVersion 失败, lottery:{}", gameLotteryInfo.getLottery());
				}
				// 保存操作日志

				String str = "";
				if (status == 0) {
					str = "结束维护";
				} else if (status == -1) {
					str = "开始维护" + "维护结束时间：" + ts.toLocalDateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
				} else if (status == -2) {
					str = "开始建设";
				}
				String lottery = gameLotteryInfo.getLottery();
				String gameName = gameLotteryInfo.getGameName();
				String content = "直播间代码：" + lottery + "，直播间名称：" + gameName + "，维护状态：" + status + "（" + str + "）";
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_INFO_KILL_STATUS)
	public WebJson UPDATE_LOTTERY_INFO_KILL_STATUS(@RequestParam(name = "id") Long id,
												   @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		GameLotteryInfo gameLotteryInfo = getGameLotteryInfoDao().getById(id);
		boolean result = getGameLotteryService().updateLotteryGameKillStatus(gameLotteryInfo.getGameId(), status);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
			// 保存操作日志
			String str = (status == 0) ? "启用" : "禁用";
			String lottery = gameLotteryInfo.getLottery();
			String gameName = gameLotteryInfo.getGameName();
			String content = "直播间代码：" + lottery + "，直播间名称：" + gameName + "，杀率状态：" + status + "（" + str + "）";
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_PRE_STEP_LOTTERY_INFO)
	public WebJson UPDATE_PRE_STEP_LOTTERY_INFO(@RequestParam(name = "sort") Integer sort) {
		WebJson webJson = new WebJson();
		if (sort == null || sort <= 0) {
			throw newException("无效的升序操作");
		}
		boolean result = getGameLotteryService().preStepSortLotteryInfo(sort);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
			// 保存操作日志
			String content = "直播间，排序值：" + sort + "，上移";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_NEXT_STEP_LOTTERY_INFO)
	public WebJson UPDATE_NEXT_STEP_LOTTERY_INFO(@RequestParam(name = "sort") Integer sort) {
		WebJson webJson = new WebJson();
		if (sort == null || sort <= 0) {
			throw newException("无效的降序操作");
		}
		boolean result = getGameLotteryService().nextStepSortLotteryInfo(sort);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
			// 保存操作日志
			String content = "直播间，排序值：" + sort + "，下移";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.EDIT_KILLRATE_SETTING)
	public WebJson EDIT_KILLRATE_SETTING(@RequestParam(name = "id") Long id,
										 @RequestParam(name = "issue", required = false) String issue,
										 @RequestParam(name = "lotteryOrderSize") int lotteryOrderSize,
										 @RequestParam(name = "lotteryWinProbability") int lotteryWinProbability,
										 @RequestParam(name = "lotteryTotalBetAmount") BigDecimal lotteryTotalBetAmount) {
		WebJson webJson = new WebJson();
		issue = issue.replace("，", ",");
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("issue", Arrays.asList(issue.split(",")));
		dataMap.put("lotteryOrderSize", lotteryOrderSize);
		dataMap.put("lotteryTotalBetAmount", lotteryTotalBetAmount);
		dataMap.put("lotteryWinProbability", lotteryWinProbability);
		String killConfig = JSON.toJSONString(dataMap);
		boolean result = getGameLotteryService().updateKillConfigLotteryInfo(id, killConfig);
		if (result) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
			// 保存操作日志
			String content = "游戏ID：" + id + "，期号：" + issue + "，总投注订单数：" + lotteryOrderSize + "，中奖总额百分比："
					+ lotteryWinProbability + "，总投注金额：" + lotteryTotalBetAmount;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@RequestMapping(Route.GameLottery.LIST_LOTTERY_AGENT)
	public WebJson LIST_LOTTERY_AGENT(@RequestParam(name = "agentNo", required = false) String agentNo) {
		WebJson webJson = new WebJson();
		try {
			if(StringUtils.isEmpty(agentNo)) {
				throw newException("500-092");
			}

			List<GameLotteryInfoBindVo> list = new ArrayList<>();
			// 查询平台代理商已绑定的游戏
			List<AgentGameLotteryInfo> agentResultList = getAgentGameLotteryInfoDao().listByAgentNo(agentNo);
			if(agentResultList == null) {
				agentResultList = new ArrayList<>();
			}
			for(AgentGameLotteryInfo gameLotteryInfo : agentResultList) {
				list.add(new GameLotteryInfoBindVo(gameLotteryInfo.getLottery(), gameLotteryInfo.getGameName(), 1));
			}

			webJson.setData(list);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			log.error("查询绑定直播间列表异常", e);
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.LIST_LOTTERY_AGENT_BIND)
	public WebJson LIST_LOTTERY_AGENT_BIND(@RequestParam(name = "agentNo", required = false) String agentNo) {
		WebJson webJson = new WebJson();
		try {
			if(StringUtils.isEmpty(agentNo)) {
				throw newException("500-092");
			}

			// 游戏列表
			List<GameLotteryInfo> resultList = getGameLotteryInfoDao().listAllOrderByTypeSort();
			// 查询平台代理商已绑定的游戏
			List<AgentGameLotteryInfo> agentResultList = getAgentGameLotteryInfoDao().listByAgentNo(agentNo);
			if(agentResultList == null) {
				agentResultList = new ArrayList<>();
			}

			// 按游戏类型分类
			Map<String, List<GameLotteryInfo>> map = resultList.stream().collect(
					Collectors.groupingBy(GameLotteryInfo::getGameTypeCode,
							Collectors.collectingAndThen(Collectors.toList(), lotteryList -> lotteryList)));

			List<GameLotteryTypeBindVo> list = new ArrayList<>();
			for(Map.Entry<String, List<GameLotteryInfo>> entry : map.entrySet()) {
				GameLotteryType lotteryType = getGameLotteryTypeDao().getByGameTypeCode(entry.getKey());
				if(lotteryType == null) {
					continue;
				}

				List<GameLotteryInfoBindVo> bindVoList = new ArrayList<>();
				for(GameLotteryInfo lottery : entry.getValue()) {
					int bindStatus = 0;
					for(AgentGameLotteryInfo agentGameLotteryInfo : agentResultList) {
						if(agentGameLotteryInfo.getLottery().equals(lottery.getLottery())) {
							// 改为已绑定
							bindStatus = 1;
							break;
						}
					}
					bindVoList.add(new GameLotteryInfoBindVo(lottery.getLottery(), lottery.getGameName(), bindStatus));
				}
				list.add(new GameLotteryTypeBindVo(lotteryType.getGameTypeCode(), lotteryType.getGameTypeName(),
						bindVoList));
			}

			webJson.setData(list);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			log.error("查询绑定直播间列表异常", e);
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.BIND_AGENT_GAME_LOTTERY_INFO)
	public WebJson BIND_AGENT_GAME_LOTTERY_INFO(@RequestParam String agentNo,
												@RequestParam(required = false) String lotterys) {
		WebJson webJson = new WebJson();
		try {
			if(StringUtils.isEmpty(agentNo)) {
				throw newException("500-092");
			}
			AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(agentNo);
			if(agentInfo == null) {
				throw newException("500-093");
			}
			List<String> lotteryList;
			if(StringUtils.isEmpty(lotterys)) {
				lotteryList = new ArrayList<>();
			}else {
				lotteryList = Arrays.stream(lotterys.split(",")).collect(Collectors.toList());
			}

			// 游戏列表
			List<GameLotteryInfo> resultList = getGameLotteryInfoDao().listAllOrderByTypeSort();
			// 查询平台代理商已绑定的游戏
			List<AgentGameLotteryInfo> agentResultList = getAgentGameLotteryInfoDao().listByAgentNo(agentNo);

			if(agentResultList != null) {
				for(AgentGameLotteryInfo agentGameLotteryInfo : agentResultList) {
					// 存在时，将lotteryList中的删除，防止重复添加
					lotteryList.remove(agentGameLotteryInfo.getLottery());
				}
			}

			// 绑定
			for(GameLotteryInfo lotteryInfo : resultList) {
				if(lotteryList.contains(lotteryInfo.getLottery())) {
					AgentGameLotteryInfo agentGameLotteryInfo = new AgentGameLotteryInfo(lotteryInfo, agentInfo);
					agentGameLotteryInfo.setRecommend(0);
					getAgentGameLotteryInfoDao().save(agentGameLotteryInfo);
				}
			}

			// 更新游戏缓存版本
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
			// 保存操作日志
			String content = "绑定代理商: " + agentInfo.getAgentName() + "和游戏直播间: " + lotterys;
			log.info(content);
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			log.error("绑定直播间异常", e);
			setFail(webJson);
		}

		return webJson;
	}

	@RequestMapping(Route.GameLottery.LIST_GAME_LOTTERY_GROUP)
	public WebJson LIST_GAME_LOTTERY_GROUP() {
		WebJson webJson = new WebJson();
		List<GameLotteryGroup> list = getGameLotteryGroupDao().listAll();
		webJson.setData(list);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.ADD_GAME_LOTTERY_GROUP)
	@ResponseBody
	public WebJson ADD_GAME_LOTTERY_GROUP(HttpServletRequest request, @RequestParam String gameGroupCode,
										  @RequestParam String gameGroupName, @RequestParam Integer sort) {
		WebJson webJson = new WebJson();

		try {
			AdminAccount admin = getSessionUser(request);

			gameGroupCode = gameGroupCode.trim();
			if(StringUtils.isEmpty(gameGroupCode)) {
				throw newException("500-122");
			}
			gameGroupName = gameGroupName.trim();
			if(StringUtils.isEmpty(gameGroupName)) {
				throw newException("500-123");
			}
			// 检查记录是否重复
			GameLotteryGroup gameLotteryGroup = getGameLotteryGroupDao().getByGameGroupCode(gameGroupCode);
			if(gameLotteryGroup != null) {
				throw newException("500-124");
			}
			gameLotteryGroup = getGameLotteryGroupDao().getByGameGroupName(gameGroupName);
			if(gameLotteryGroup != null) {
				throw newException("500-125");
			}

			GameLotteryGroup lotteryGroup = new GameLotteryGroup();
			lotteryGroup.setGameGroupCode(gameGroupCode);
			lotteryGroup.setGameGroupName(gameGroupName);
			lotteryGroup.setSort(sort);
			lotteryGroup.setGameGroupStatus(GameLotteryGroup.GAME_GROUP_STATUS_NORMAL);
			lotteryGroup.setIsDelete(GameLotteryGroup.DELETE_STATUS_NORMAL);
			lotteryGroup.setCreateBy(admin.getUsername());
			Timestamp now = new Timestamp(System.currentTimeMillis());
			lotteryGroup.setCreateTime(now);
			lotteryGroup.setUpdateTime(now);

			boolean b = getGameLotteryGroupDao().save(lotteryGroup);
			if(b) {
				// 保存操作日志
				String content = "添加游戏分组，分组代码：" + gameGroupCode + "，名称：" + gameGroupName + "，排序：" + sort;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("添加游戏分组失败", e);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.EDIT_GAME_LOTTERY_GROUP)
	@ResponseBody
	public WebJson EDIT_GAME_LOTTERY_GROUP(HttpServletRequest request, @RequestParam Integer groupId,
										   @RequestParam String gameGroupCode, @RequestParam String gameGroupName, @RequestParam Integer sort) {
		WebJson webJson = new WebJson();

		try {
			gameGroupCode = gameGroupCode.trim();
			if(StringUtils.isEmpty(gameGroupCode)) {
				throw newException("500-122");
			}
			gameGroupName = gameGroupName.trim();
			if(StringUtils.isEmpty(gameGroupName)) {
				throw newException("500-123");
			}
			// 检查记录是否存在
			GameLotteryGroup lotteryGroup = getGameLotteryGroupDao().getById(groupId);
			if(lotteryGroup == null) {
				throw newException("500-126");
			}
			lotteryGroup.setGameGroupCode(gameGroupCode);
			lotteryGroup.setGameGroupName(gameGroupName);
			lotteryGroup.setSort(sort);
			lotteryGroup.setGameGroupStatus(GameLotteryGroup.GAME_GROUP_STATUS_NORMAL);
			lotteryGroup.setIsDelete(GameLotteryGroup.DELETE_STATUS_NORMAL);

			boolean b = getGameLotteryGroupDao().update(lotteryGroup);
			if(b) {
				// 保存操作日志
				String content = "编辑游戏分组，分组代码：" + gameGroupCode + "，名称：" + gameGroupName + "，排序：" + sort;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("编辑游戏分组失败", e);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.DELETE_GAME_LOTTERY_GROUP)
	@ResponseBody
	public WebJson DELETE_GAME_LOTTERY_GROUP(HttpServletRequest request, @RequestParam Integer groupId) {
		WebJson webJson = new WebJson();

		try {
			// 检查记录是否存在
			GameLotteryGroup lotteryGroup = getGameLotteryGroupDao().getById(groupId);
			if(lotteryGroup == null) {
				throw newException("500-126");
			}

			// 检查该分组下是否还有游戏
			List<Criterion> criterions = new ArrayList<>();
			criterions.add(Restrictions.eq("gameGroupCode", lotteryGroup.getGameGroupCode()));
			int totalCount = getGameLotteryInfoDao().totalCount(criterions);
			if(totalCount > 0) {
				throw newException("500-127");
			}

			boolean b = getGameLotteryGroupDao().deleteById(lotteryGroup.getGroupId());
			if(b) {
				// 保存操作日志
				String content = "删除游戏分组，分组代码：" + lotteryGroup.getGameGroupCode() + "，名称：" +
						lotteryGroup.getGameGroupName() + "，排序：" + lotteryGroup.getSort();
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("删除游戏分组失败", e);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.LIST_GAME_LOTTERY_INFO_LIST)
	public WebJson LIST_GAME_LOTTERY_INFO_LIST(@RequestParam(required = false) String agentNo,
			@RequestParam(required = false) String gameGroupCode, @RequestParam(required = false) Integer gameStatus) {
		WebJson webJson = new WebJson();
		List<AgentGameLotteryInfoVo> list;
		if(StringUtils.isEmpty(agentNo)) {
			// 查询总数据
			list = getGameLotteryInfoList(gameGroupCode, gameStatus);
		}else {
			// 查询代理商数据
			list = getAgentGameLotteryInfoList(agentNo, gameGroupCode, gameStatus);
		}
		webJson.setData(list);
		setSuccess(webJson);
		return webJson;
	}

	private List<AgentGameLotteryInfoVo> getGameLotteryInfoList(String gameGroupCode, Integer gameStatus) {
		List<Criterion> criterions = new ArrayList<>();
		if(!StringUtils.isEmpty(gameGroupCode)) {
			criterions.add(Restrictions.eq("gameGroupCode", gameGroupCode));
		}
		if(gameStatus != null) {
			if(gameStatus == GameLotteryInfo.GAME_STATUS_NORMAL) {
				criterions.add(Restrictions.eq("gameStatus", gameStatus));
			}else {
				criterions.add(Restrictions.ne("gameStatus", GameLotteryInfo.GAME_STATUS_NORMAL));
			}
		}
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<>();
		orders.add(Order.asc("sort"));
		int totalCount = getGameLotteryInfoDao().totalCount(criterions);
		List<AgentGameLotteryInfoVo> list = new ArrayList<>();
		if(totalCount > 0) {
			List<GameLotteryInfo> resultList = getGameLotteryInfoDao().find(criterions, orders, -1, -1);
			for(GameLotteryInfo entity : resultList) {
				GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(entity.getGameTypeCode());
				GameLotteryGroup gameLotteryGroup = getRedisService().getGameLotteryGroup(entity.getGameGroupCode());
				list.add(new AgentGameLotteryInfoVo(entity, gameLotteryType, gameLotteryGroup));
			}
		}
		return list;
	}

	private List<AgentGameLotteryInfoVo> getAgentGameLotteryInfoList(String agentNo, String gameGroupCode,
			Integer banStatus) {
		List<Criterion> criterions = new ArrayList<>();
		criterions.add(Restrictions.eq("agentNo", agentNo));
		if(!StringUtils.isEmpty(gameGroupCode)) {
			criterions.add(Restrictions.eq("gameGroupCode", gameGroupCode));
		}
		if(banStatus != null) {
			if(banStatus == AgentGameLotteryInfo.BAN_STATUS_ENABLED) {
				criterions.add(Restrictions.eq("banStatus", banStatus));
			}else {
				criterions.add(Restrictions.ne("banStatus", AgentGameLotteryInfo.BAN_STATUS_ENABLED));
			}
		}
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<>();
		orders.add(Order.asc("sort"));
		int totalCount = getAgentGameLotteryInfoDao().totalCount(criterions);
		List<AgentGameLotteryInfoVo> list = new ArrayList<>();
		if(totalCount > 0) {
			List<AgentGameLotteryInfo> resultList = getAgentGameLotteryInfoDao().find(criterions, orders, -1, -1);
			for(AgentGameLotteryInfo entity : resultList) {
				GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(entity.getGameTypeCode());
				GameLotteryGroup lotteryGroup = getRedisService().getGameLotteryGroup(entity.getGameGroupCode());
				list.add(new AgentGameLotteryInfoVo(entity, gameLotteryType, lotteryGroup));
			}
		}
		return list;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.STATUS_GAME_LOTTERY_INFO_LIST)
	public WebJson STATUS_GAME_LOTTERY_INFO_LIST(@RequestParam(required = false) String agentNo,
			@RequestParam String lottery, @RequestParam int status) {
		WebJson webJson = new WebJson();
		try {
			if(!(GameLotteryInfo.GAME_STATUS_NORMAL == status || GameLotteryInfo.GAME_STATUS_FORBIDDEN == status)) {
				throw newException("未知的游戏状态！");
			}
			GameLotteryInfo gameLotteryInfo = getGameLotteryInfoDao().getByLottery(lottery);
			if(gameLotteryInfo == null) {
				throw newException("500-128");
			}

			// 修改预警记录,如果没有传代理商，则按照彩种删除全部预警记录
			// status = 1 禁用， 0 启用
			Date date = new Date();
			if(0 == status) {
				getGameWarnRecordDao().deleteAgentOrAll(agentNo, gameLotteryInfo.getLottery(), date);
				getGameWarnAutoDisableRecordDao().deleteAgentOrAll(agentNo, gameLotteryInfo.getLottery(), date);
			}

			if(StringUtils.isEmpty(agentNo)) {
				// 总彩票数据状态
				boolean result = getGameLotteryService().updateLotteryGameStatus(gameLotteryInfo.getGameId(), status);
				if(result) {
					if(GameLotteryInfo.GAME_STATUS_FORBIDDEN == status) {
						// 禁用时，将全部代理商该彩种都禁用
						getAgentGameLotteryInfoDao().updateAllAgentLotteryStatus(gameLotteryInfo.getLottery(),
								AgentGameLotteryInfo.BAN_STATUS_DISABLED);
					}
					// 清除缓存
					getRedisService().delGameLotteryInfo(gameLotteryInfo.getLottery());
					getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
					// 保存操作日志
					String str = (status == GameLotteryInfo.GAME_STATUS_NORMAL) ? "启用" : "禁用";
					String gameName = gameLotteryInfo.getGameName();
					String content = "游戏代码：" + lottery + "，游戏名称：" + gameName + "，游戏状态：" + status + "（" +
							str + "）";
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				}else {
					setFail(webJson);
				}
			}else {
				// 代理商彩票状态
				AgentGameLotteryInfo agentGameLotteryInfo = getAgentGameLotteryInfoDao().getByAgentNoAndLottery(agentNo,
						lottery);
				if(agentGameLotteryInfo == null) {
					throw newException("500-128");
				}

				if(AgentGameLotteryInfo.BAN_STATUS_ENABLED == status) {
					// 代理商启用彩种时，判断总台彩种是否处理启用状态
					if(GameLotteryInfo.GAME_STATUS_NORMAL != gameLotteryInfo.getGameStatus()) {
						throw newException("500-129");
					}
				}
				boolean result = getAgentGameLotteryInfoDao().updateStatus(agentGameLotteryInfo.getGameId(), status);
				if(result) {
					// 清除缓存
					getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
					// 保存操作日志
					String str = (status == GameLotteryInfo.GAME_STATUS_NORMAL) ? "启用" : "禁用";
					String gameName = agentGameLotteryInfo.getGameName();
					String agentName = agentGameLotteryInfo.getAgentName();
					String content = "代理商：" + agentName + "，游戏代码：" + lottery + "，游戏名称：" + gameName +
							"，游戏状态：" + status + "（" + str + "）";
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				}else {
					setFail(webJson);
				}
			}
		}catch(ServiceException e) {
			setError(webJson, e);
			return webJson;
		}catch(Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.EDIT_GAME_LOTTERY_INFO_LIST)
	public WebJson EDIT_GAME_LOTTERY_INFO_LIST(@RequestParam(required = false) String agentNo,
			@RequestParam String lottery, @RequestParam String gameGroupCode, @RequestParam Integer stopDelay,
			@RequestParam Integer downCode, @RequestParam Integer fenDownCode, @RequestParam Integer liDownCode,
			@RequestParam Integer floatBonus, @RequestParam BigDecimal maxBonus, @RequestParam BigDecimal gameMaxLimit,
			@RequestParam Integer sort) {
		WebJson webJson = new WebJson();
		try {
			boolean result;
			String content = "";
			if(StringUtils.isEmpty(agentNo)) {
				// 总彩票数据
				GameLotteryInfo gameLotteryInfo = getGameLotteryInfoDao().getByLottery(lottery);
				if(gameLotteryInfo == null) {
					throw newException("500-128");
				}
				result = getGameLotteryService().editLotteryInfo(gameLotteryInfo.getGameId(), gameGroupCode, stopDelay,
						downCode, fenDownCode, liDownCode, floatBonus, maxBonus, gameMaxLimit, sort);
				content += "游戏代码：" + lottery + "，游戏名称：" + gameLotteryInfo.getGameName();
			}else {
				// 代理商彩票数据
				AgentGameLotteryInfo gameLotteryInfo = getAgentGameLotteryInfoDao().getByAgentNoAndLottery(agentNo,
						lottery);
				if(gameLotteryInfo == null) {
					throw newException("500-128");
				}
				result = getGameLotteryService().editAgentGameLotteryInfo(gameLotteryInfo.getGameId(), gameGroupCode,
						stopDelay, downCode, fenDownCode, liDownCode, floatBonus, maxBonus, gameMaxLimit, sort);
				content += "代理商：" + gameLotteryInfo.getAgentName() + "，游戏代码：" + lottery + "，游戏名称：" +
						gameLotteryInfo.getGameName();

				if(!gameLotteryInfo.getFloatBonus().equals(floatBonus)) {
					// 浮动奖级变化，清除玩法缓存
					getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_METHOD);
				}
			}
			if(result) {
				getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
				// 保存操作日志
				content += "，游戏分组：" + gameGroupCode + "，提前截止时间：" + stopDelay + "，彩票奖级：" + downCode +
						"，分模式降点：" + fenDownCode + "，厘模式降点：" + liDownCode + "，浮动奖级：" + floatBonus +
						"，最大奖金：" + maxBonus + "，双面盘限红：" + gameMaxLimit + "，排序：" + sort;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}catch(Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}

		return webJson;
	}

	@RequestMapping(Route.GameLottery.LIST_LOTTERY_METHOD)
	public WebJson LIST_LOTTERY_METHOD(@RequestParam String lottery, @RequestParam(defaultValue = "") String agentNo,
			@RequestParam(required = false) Integer methodType, @RequestParam(required = false) String methodName,
			@RequestParam(required = false) Integer methodStatus) {
		WebJson webJson = new WebJson();
		try {
			GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(lottery);
			List<GameLotteryMethodCustomizeVo> list = getServiceUtils().getGameLotteryMethod(agentNo, gameLotteryInfo,
					methodType, methodName, methodStatus);
			webJson.setData(list);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_METHOD)
	public WebJson UPDATE_LOTTERY_METHOD(@RequestParam(required = false) String agentNo,
			@RequestParam(required = false) String lottery, @RequestParam String gameTypeCode,
			@RequestParam String methodName, @RequestParam String methodCode, @RequestParam BigDecimal betMinLimit,
			@RequestParam BigDecimal betMaxLimit, @RequestParam BigDecimal bonusMaxLimit,
			@RequestParam Integer minRecord, @RequestParam Integer maxRecord, @RequestParam Integer totalRecord,
			@RequestParam Integer oooNums, @RequestParam BigDecimal oooBonus, @RequestParam Integer floatBonus) {
		WebJson webJson = new WebJson();
		boolean b = getGameLotteryService().editLotteryMethod(agentNo, lottery, gameTypeCode, methodCode, betMinLimit,
				betMaxLimit, bonusMaxLimit, minRecord, maxRecord, totalRecord, oooNums, oooBonus, floatBonus);
		if (b) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_METHOD);
			// 保存操作日志
			String content = "";
			if(!StringUtils.isEmpty(agentNo) && !StringUtils.isEmpty(lottery)) {
				content += "代理商：" + agentNo + "游戏：" + lottery + "，";
			}
			content += "游戏类型：" + gameTypeCode + "，玩法代码：" + methodCode + "，玩法名称：" + methodName +
					"，玩法Code：" + methodCode + "，最小投注金额：" + betMinLimit + "，最大投注金额：" + betMaxLimit +
					"，最大奖金配置：" + bonusMaxLimit + "，最小注数：" + minRecord + "，最大注数：" + maxRecord +
					"，总注数：" + totalRecord + "，单挑注数：" + oooNums + "，单挑奖金：" + oooBonus + "，浮动奖级：" +
					floatBonus;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_METHOD_BONUS)
	public WebJson UPDATE_LOTTERY_METHOD_BONUS(@RequestParam(required = false) String agentNo,
			@RequestParam(required = false) String lottery, @RequestParam String gameTypeCode,
			@RequestParam String methodCode, @RequestParam String bonus) {
		WebJson webJson = new WebJson();
		boolean b = getGameLotteryService().setLotteryMethodBonus(agentNo, lottery, gameTypeCode, methodCode, bonus);
		if (b) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_METHOD);
			// 保存操作日志
			String content = "";
			if(!StringUtils.isEmpty(agentNo) && !StringUtils.isEmpty(lottery)) {
				content += "代理商：" + agentNo + "游戏：" + lottery + "，";
			}
			content += "游戏类型：" + gameTypeCode + "，玩法代码：" + methodCode + "，奖金配置：" + bonus;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_METHOD_STATUS)
	public WebJson UPDATE_LOTTERY_METHOD_STATUS(@RequestParam(required = false) String agentNo,
			@RequestParam(required = false) String lottery, @RequestParam String gameTypeCode,
			@RequestParam String methodCode, @RequestParam int status) {
		WebJson webJson = new WebJson();
		boolean b = getGameLotteryService().enableOrDisableLotteryMethod(agentNo, lottery, gameTypeCode, methodCode,
				status);
		// 玩法
		if(0 == status){
			getGameWarnRecordDao().delete(agentNo, lottery, methodCode, new Date());
			getGameWarnAutoDisableRecordDao().delete(agentNo, lottery, methodCode, new Date());
		}

		if (b) {
			getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_METHOD);
			// 保存操作日志
			String content = "";
			if(!StringUtils.isEmpty(agentNo) && !StringUtils.isEmpty(lottery)) {
				content += "代理商：" + agentNo + "游戏：" + lottery + "，";
			}
			content += "游戏类型：" + gameTypeCode + "，玩法代码：" + methodCode + "，禁用状态：" + status;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.LIST_LOTTERY_OPEN_TIME)
	public WebJson LIST_LOTTERY_OPEN_TIME(@RequestParam(name = "lottery") String lottery,
			@RequestParam(name = "orderField", defaultValue = "") String orderField,
			@RequestParam(name = "orderWay", defaultValue = "") String orderWay) {
		WebJson webJson = new WebJson();
		List<Criterion> criterions = new ArrayList<>();
		if (!StringUtils.isEmpty(lottery)) {
			criterions.add(Restrictions.eq("lottery", lottery));
		}
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<>();
		if(!"issue".equals(orderField)) {
			orderField = "id";
		}
		if("desc".equals(orderWay)) {
			orders.add(Order.desc(orderField));
		}else {
			orders.add(Order.asc(orderField));
		}
		int totalCount = getGameLotteryOpenTimeDao().totalCount(criterions);
		List<GameLotteryOpenTimeVo> list = new ArrayList<>();
		if (totalCount > 0) {
			List<GameLotteryOpenTime> resultList = getGameLotteryOpenTimeDao().find(criterions, orders, -1, -1);
			for (GameLotteryOpenTime entity : resultList) {
				GameLotteryOpenTimeVo vo = new GameLotteryOpenTimeVo(entity);
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<>();
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.LIST_LOTTERY_OPEN_CODE)
	public WebJson LIST_LOTTERY_OPEN_CODE(@RequestParam(name = "lottery") String lottery,
										  @RequestParam(name = "issue", required = false) String issue,
										  @RequestParam(name = "page", defaultValue = "0") int page,
										  @RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(lottery)) {
			criterions.add(Restrictions.eq("lottery", lottery));
		}
		if (!StringUtils.isEmpty(issue)) {
			criterions.add(Restrictions.eq("gameIssue", issue));
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getGameLotteryOpenCodeDao().totalCount(criterions);
		List<GameLotteryOpenCodeVo> list = new ArrayList<GameLotteryOpenCodeVo>();
		if (totalCount > 0) {
			String gameName = "";
			GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(lottery);
			if (gameLotteryInfo != null) {
				gameName = gameLotteryInfo.getGameName();
			}
			List<GameLotteryOpenCode> resultList = getGameLotteryOpenCodeDao().find(criterions, orders, firstResult,
					maxResults);
			for (GameLotteryOpenCode entity : resultList) {
				GameLotteryOpenCodeVo vo = new GameLotteryOpenCodeVo(entity);
				vo.setGameName(gameName);
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SEARCH_GAME_VIDEO)
	public WebJson SEARCH_GAME_VIDEO(@RequestParam(name = "lottery", required = false) String lottery,
									 @RequestParam(name = "openCode", required = false) String openCode,
									 @RequestParam(name = "banStatus", required = false) Integer banStatus,
									 @RequestParam(name = "useStatus", required = false) Integer useStatus,
									 @RequestParam(name = "videoFormat", required = false) Integer videoFormat,
									 @RequestParam(name = "sTime", required = false) String sTime,
									 @RequestParam(name = "eTime", required = false) String eTime,
									 @RequestParam(name = "page", defaultValue = "0") int page,
									 @RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		int firstResult = page * size, maxResults = size;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(lottery)) {
			criterions.add(Restrictions.eq("lottery", lottery));
		}
		if (!StringUtils.isEmpty(openCode)) {
			criterions.add(Restrictions.eq("openCode", openCode));
		}
		if (banStatus != null) {
			criterions.add(Restrictions.eq("banStatus", banStatus));
		}
		if (useStatus != null) {
			criterions.add(Restrictions.eq("useStatus", useStatus));
		}
		if (videoFormat != null) {
			criterions.add(Restrictions.eq("videoFormat", videoFormat));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		criterions.add(Restrictions.eq("isDelete", 0));
		int totalCount = getGameLotteryVideoDao().totalCount(criterions);
		List<GameLotteryVideoVo> list = new ArrayList<GameLotteryVideoVo>();
		if (totalCount > 0) {
			List<GameLotteryVideo> resultList = getGameLotteryVideoDao().find(criterions, orders, firstResult,
					maxResults);
			for (GameLotteryVideo entity : resultList) {
				GameLotteryVideoVo vo = new GameLotteryVideoVo(entity);
				GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(entity.getLottery());
				if (gameLotteryInfo != null) {
					vo.setGameName(gameLotteryInfo.getGameName());
				}
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.IMPORT_GAME_VIDEO)
	public WebJson IMPORT_GAME_VIDEO(HttpServletRequest request, @RequestParam(value = "lottery") String lottery,
									 @RequestParam(value = "openCode") String openCode, @RequestParam(value = "video") MultipartFile video,
									 @RequestParam(value = "remark") String remark) {
		WebJson webJson = new WebJson();
		boolean b = getGameLotteryService().importGameLotteryVideo(lottery, openCode, video, remark);
		if (b) {
			// 保存操作日志
			String content = "直播间名称：" + lottery + "，开奖号码：" + openCode + "，备注：" + remark;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.PLAY_GAME_VIDEO)
	public WebJson PLAY_GAME_VIDEO(HttpServletRequest request, @RequestParam Long id) {

		WebJson webJson = new WebJson();

		GameLotteryVideo byId = getGameLotteryVideoDao().getById(id);
		if (byId != null) {
			URL url = s3VideoUploadService.generatePresignedVideoUrl(byId.getVideoYunStoreLink());
			if (url != null) {
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("url", url.toString());
				webJson.setData(data);

			}
		}
		setSuccess(webJson);
		return webJson;

	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.PUSH_GAME_VIDEO)
	public WebJson PUSH_GAME_VIDEO(@RequestParam(value = "id") Long id) {
		WebJson webJson = new WebJson();

		boolean b = getGameLotteryService().pushGameLotteryVideo(id);
		if (b) {
			// 保存操作日志
			String content = "视频ID：" + id;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.EDIT_GAME_VIDEO)
	public WebJson EDIT_GAME_VIDEO(@RequestParam(value = "id") Long id,
								   @RequestParam(value = "openCode") String openCode, @RequestParam(value = "remark") String remark) {
		WebJson webJson = new WebJson();

		boolean b = getGameLotteryService().editGameLotteryVideo(id, openCode, remark);
		if (b) {
			// 保存操作日志
			String content = "视频ID：" + id + "，开奖号码：" + openCode + "，备注：" + remark;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.BAN_GAME_VIDEO)
	public WebJson BAN_GAME_VIDEO(@RequestParam(value = "id") Long id,
								  @RequestParam(value = "banStatus") int banStatus) {
		WebJson webJson = new WebJson();

		boolean b = getGameLotteryService().banOrNotBnaGameLotteryVideo(id, banStatus);
		if (b) {
			// 保存操作日志
			String content = "视频ID：" + id + "，禁用状态：" + banStatus;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.DELETE_GAME_VIDEO)
	public WebJson DELETE_GAME_VIDEO(@RequestParam(value = "id") Long id) {
		WebJson webJson = new WebJson();

		boolean b = getGameLotteryService().deleteGameLotteryVideo(id);
		if (b) {
			// 保存操作日志
			String content = "视频ID：" + id;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SEARCH_GIFT)
	public WebJson SEARCH_GIFT(@RequestParam(name = "giftCode", required = false) String giftCode,
							   @RequestParam(name = "giftName", required = false) String giftName,
							   @RequestParam(name = "giftCategory", required = false) Integer giftCategory,
							   @RequestParam(name = "giftSeLevel", required = false) Integer giftSeLevel,
							   @RequestParam(name = "banStatus", required = false) Integer banStatus,
							   @RequestParam(name = "page", defaultValue = "0") int page,
							   @RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();

		int firstResult = page * size, maxResults = size;
		List<Criterion> criterions = new ArrayList<Criterion>();

		if (!StringUtils.isEmpty(giftCode)) {
			criterions.add(Restrictions.eq("giftCode", giftCode));
		}

		if (giftCategory != null) {
			criterions.add(Restrictions.eq("giftCategory", giftCategory));
		}

		if (giftSeLevel != null) {
			criterions.add(Restrictions.eq("giftSeLevel", giftSeLevel));
		}

		if (banStatus != null) {
			criterions.add(Restrictions.eq("banStatus", banStatus));
		}

		if (!StringUtils.isEmpty(giftName)) {
			criterions.add(Restrictions.like("giftName", giftName, MatchMode.ANYWHERE));
		}

		criterions.add(Restrictions.ne("giftCode", "send_money"));
		criterions.add(Restrictions.ne("giftCode", "send_money"));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.asc("sort"));
		criterions.add(Restrictions.eq("isDelete", 0));
		int totalCount = getGiftInfoDao().totalCount(criterions);
		List<GiftInfoVo> list = new ArrayList<GiftInfoVo>();
		if (totalCount > 0) {
			List<GiftInfo> resultList = getGiftInfoDao().find(criterions, orders, firstResult, maxResults);
			for (GiftInfo entity : resultList) {
				GiftInfoVo vo = new GiftInfoVo(entity);
				if (!StringUtils.isEmpty(entity.getGiftBackground())) {
					URL backgroundUrl = s3VideoUploadService.generatePresignedGiftUrlGlobal(entity.getGiftBackground());
					if (backgroundUrl != null) {
						vo.setGiftBackground(backgroundUrl.toString());
					}
				}
				if (!StringUtils.isEmpty(entity.getGiftSe())) {
					URL seUrl = s3VideoUploadService.generatePresignedGiftUrl(entity.getGiftSe());
					if (seUrl != null) {
						vo.setGiftSe(seUrl.toString());
					}
					URL seUrlGlobal = s3VideoUploadService.generatePresignedGiftUrlGlobal(entity.getGiftSe());
					if (seUrlGlobal != null) {
						vo.setGiftSeGol(seUrlGlobal.toString());
					}
				}
				URL iconUrl = s3VideoUploadService.generatePresignedGiftUrlGlobal(entity.getGiftIcon());
				if (iconUrl != null) {
					vo.setGiftIcon(iconUrl.toString());
				}
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.ADD_GIFT)
	public WebJson ADD_GIFT(HttpServletRequest request, @RequestParam(value = "giftCode") String giftCode,
							@RequestParam(value = "giftName") String giftName, @RequestParam(value = "giftSeLevel") Integer giftSeLevel,
							@RequestParam(value = "giftBackground", required = false) MultipartFile giftBackground,
							@RequestParam(value = "giftSe", required = false) MultipartFile giftSe,
							@RequestParam(value = "giftAmount", defaultValue = "0") BigDecimal giftAmount,
							@RequestParam(value = "giftCategory", defaultValue = "1") Integer giftCategory,
							@RequestParam(value = "giftNeedLevel", defaultValue = "0") Integer giftNeedLevel,
							@RequestParam(value = "giftIcon", required = false) MultipartFile giftIcon) {
		WebJson webJson = new WebJson();

		boolean b = getGiftService().addGift(giftCode, giftName, giftSeLevel, giftCategory, giftNeedLevel,
				giftBackground, giftSe, giftIcon, giftAmount);
		if (b) {
			// 保存操作日志
			String content = "礼物Code：" + giftCode + "，礼物名称：" + giftName + "，礼物金额：" + giftAmount + "，礼物特效等级："
					+ giftSeLevel + "，礼物类别：" + giftCategory + "，礼物需要的等级：" + giftNeedLevel;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.EDIT_GIFT)
	public WebJson EDIT_GIFT(HttpServletRequest request, @RequestParam(value = "giftId") Long giftId,
							 @RequestParam(value = "giftName", required = false) String giftName,
							 @RequestParam(value = "giftSe", required = false) MultipartFile giftSe,
							 @RequestParam(value = "giftBackground", required = false) MultipartFile giftBackground,
							 @RequestParam(value = "giftIcon", required = false) MultipartFile giftIcon) {

		WebJson webJson = new WebJson();

		boolean b = getGiftService().editGift(giftId, giftName, giftSe, giftBackground, giftIcon);
		if (b) {
			// 保存操作日志
			String content = "礼物ID：" + giftId + "，礼物名称：" + giftName;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;

	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.UPDATE_GIFT_SORT)
	public WebJson UPDATE_GIFT_SORT(HttpServletRequest request, @RequestParam(value = "giftId") Long giftId,
									@RequestParam(value = "sort", required = false) Integer sort) {

		WebJson webJson = new WebJson();

		if (sort < 0) {
			throw new ServiceException("1", "排序值不能小于0");
		}

		boolean b = getGiftService().updateGiftSort(giftId, sort);
		if (b) {
			// 保存操作日志
			String content = "礼物ID：" + giftId + "，排序值：" + sort;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;

	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.BAN_GIFT)
	public WebJson BAN_GIFT(@RequestParam(value = "giftId") Long giftId,
							@RequestParam(value = "banStatus") int banStatus) {
		WebJson webJson = new WebJson();

		boolean b = getGiftService().updateGiftBanStatus(giftId, banStatus);
		if (b) {
			// 保存操作日志
			String desc = CommonStatus.getByCode(banStatus).getDesc();
			String content = "礼物ID：" + giftId + "，禁用状态：" + desc;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.DELETE_GIFT)
	public WebJson DELETE_GIFT(@RequestParam(value = "giftId") Long giftId) {
		WebJson webJson = new WebJson();

		boolean b = getGiftService().deleteGift(giftId);
		if (b) {
			// 保存操作日志
			String content = "礼物ID：" + giftId;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SEARCH_GAME_ORDER_ISSUE_RECORD)
	public WebJson SEARCH_GAME_ORDER_ISSUE_RECORD(@RequestParam(name = "lottery", required = false) String lottery,
												  @RequestParam(name = "gameIssue", required = false) String gameIssue,
												  @RequestParam(name = "totalProfitStatus", required = false) Integer totalProfitStatus,
												  @RequestParam(name = "sTime", required = false) String sTime,
												  @RequestParam(name = "eTime", required = false) String eTime,
												  @RequestParam(name = "page", defaultValue = "0") int page,
												  @RequestParam(name = "size", defaultValue = "100") int size) {
		WebJson webJson = new WebJson();
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		int firstResult = page * size, maxResults = size;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(lottery)) {
			criterions.add(Restrictions.eq("lottery", lottery));
		}
		if (!StringUtils.isEmpty(gameIssue)) {
			criterions.add(Restrictions.eq("gameIssue", gameIssue));
		}
		if (totalProfitStatus != null) {
			if (totalProfitStatus == 0) {
				criterions.add(Restrictions.eq("totalProfitAmount", BigDecimal.ZERO));

			} else if (totalProfitStatus == 1) {
				criterions.add(Restrictions.gt("totalProfitAmount", BigDecimal.ZERO));
			} else if (totalProfitStatus == -1) {
				criterions.add(Restrictions.lt("totalProfitAmount", BigDecimal.ZERO));
			}
		}

		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		criterions.add(Restrictions.eq("isDelete", 0));
		int totalCount = getGameOrderIssueRecordDao().totalCount(criterions);
		List<GameOrderIssueRecordVo> list = new ArrayList<GameOrderIssueRecordVo>();
		if (totalCount > 0) {
			List<GameOrderIssueRecord> resultList = getGameOrderIssueRecordDao().find(criterions, orders, firstResult,
					maxResults);
			for (GameOrderIssueRecord entity : resultList) {
				GameOrderIssueRecordVo vo = new GameOrderIssueRecordVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SEARCH_GAME_LIVE_RECORD)
	public WebJson SEARCH_ADMIN_LOG(@RequestParam(name = "lottery", required = false) String lottery,
									@RequestParam(name = "event_type", required = false) Integer event_type,
									@RequestParam(name = "sTime", required = false) String sTime,
									@RequestParam(name = "eTime", required = false) String eTime,
									@RequestParam(name = "page", defaultValue = "0") int page,
									@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();

		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

		int firstResult = page * size, maxResults = size;
		Criteria criteria = new Criteria();

		if (!StringUtils.isEmpty(lottery)) {
			criteria.and("stream_id").is(lottery);
		}
		if (event_type != null) {
			criteria.and("event_type").is(event_type);
		}

		criteria.and("event_time").gte(sDate.getTime() / 1000).lt(eDate.getTime() / 1000);

		Sort sort = Sort.by(Sort.Direction.DESC, "event_time");
		int totalCount = getPushStreamEventDao().totalCount(criteria);
		List<GameLiveRecordVo> list = new ArrayList<GameLiveRecordVo>();

		if (totalCount > 0) {
			List<PushStreamEvent> resultList = getPushStreamEventDao().find(criteria, sort, firstResult, maxResults);
			for (PushStreamEvent tmpBean : resultList) {
				GameLiveRecordVo gameLiveRecordVo = new GameLiveRecordVo(tmpBean);
				GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(tmpBean.stream_id);
				if (gameLotteryInfo != null) {
					gameLiveRecordVo.setLottery(gameLotteryInfo.getLottery());
					gameLiveRecordVo.setGameName(gameLotteryInfo.getGameName());
				}
				list.add(gameLiveRecordVo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SUBMIT_LOTTERY_OPEN_CODE)
	public WebJson SUBMIT_LOTTERY_OPEN_CODE(@RequestParam(name = "lottery") String lottery,
											@RequestParam(name = "gameIssue") String gameIssue) {
		WebJson webJson = new WebJson();
		boolean bool = getGameLotteryOpenCodeDao().updateSettleStatus(lottery, gameIssue, 0);
		if (!bool) {
			setFail(webJson);
		} else {
			setSuccess(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.REVISE_LOTTERY_OPEN_CODE)
	@ResponseBody
	public WebJson REVISE_LOTTERY_OPEN_CODE(HttpServletRequest request, @RequestParam(name = "lottery") String lottery,
											@RequestParam(name = "issue") String issue) {
		WebJson webJson = new WebJson();
		try {
			RestTemplate template = new RestTemplate();
			SystemConfig config = getSystemConfigDao().getSystemConfigByGroupAndKey("OTHER", "PRIZE_SERVER");
			if (null == config) {
				setFail(webJson);
				return webJson;
			}
			String prizeServer = config.getValue();
			String apiUrl = prizeServer + "/api/revise-order?lottery=" + lottery + "&issue=" + issue;
			String response = template.getForObject(apiUrl, String.class);
			if ("success".equals(response)) {
				// 保存操作日志
				String content = "彩种：" + lottery + "，第" + issue + "期开奖号码反结算";
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			log.error("反结算异常。", e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("反结算失败。", t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.CANCEL_LOTTERY_OPEN_CODE)
	public WebJson CANCEL_LOTTERY_OPEN_CODE(HttpServletRequest request, @RequestParam String lottery,
			@RequestParam String issue, @RequestParam String reason) {
		WebJson webJson = new WebJson();
		try {
			boolean bool = getGameLotteryService().cancelLotteryOpenCode(lottery,issue,reason);
			if (!bool) {
				setFail(webJson);
			} else {
				String content = "开奖号码作废: lottery=" + lottery + ", issue=" + issue + ", reason=" + reason;
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			log.error("反结算异常。", e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("反结算失败。", t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.ADD_LOTTERY_OPEN_CODE)
	public WebJson ADD_LOTTERY_OPEN_CODE(HttpServletRequest request, @RequestParam String lottery,
			@RequestParam String gameIssue, @RequestParam String openCode, @RequestParam String hasOpenCode) {
		WebJson webJson = new WebJson();

		try {
			// 检查期号是否已存在
			GameLotteryOpenCode existingCode = getGameLotteryOpenCodeDao().getByLotteryAndIssue(lottery, gameIssue);
			if (existingCode != null) {
				throw newException("-1", "该期号已存在开奖记录");
			}
			if ("0".equals(hasOpenCode)) {
				// 自动生成符合开奖号码规则的开奖号（用？来代替具体的数字）
				openCode = generateOpenCode(lottery);

				// 设置开奖状态为已开奖
				int status = 1;

				boolean b = getGameLotteryService().addLotteryOpenCode(lottery, gameIssue, openCode, status);
				if (b) {
					// 保存操作日志
					String content = "游戏简称：" + lottery + "，期号：" + gameIssue + "，自动生成开奖号码：" + openCode;
					// 设置 ThreadLocal 变量
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				} else {
					setFail(webJson);
				}
			} else {
				openCode = openCode.replaceAll("，", ",").replaceAll("；", ",").replaceAll(";", ","); // 中英文逗号校验
				openCode = openCode.replaceAll(" ", "").trim(); // 空格校验
				boolean checkCode = getServiceUtils().validateResult(lottery, openCode);
				if (!checkCode) {
					throw newException("-1", "开奖号码不符合该直播间开奖规则");
				}
				boolean checkGameIssue = getServiceUtils().validateGameIssue(lottery, gameIssue);
				if (!checkGameIssue) {
					throw newException("-1", "开奖期号不符合该直播间期号规则");
				}
				boolean b = getGameLotteryService().addLotteryOpenCode(lottery, gameIssue, openCode);
				if (b) {
					// 保存操作日志
					String content = "游戏简称：" + lottery + "，期号：" + gameIssue + "，开奖号码：" + openCode;
					// 设置 ThreadLocal 变量
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				} else {
					setFail(webJson);
				}
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Exception e) {
			setFail(webJson);
			log.error("添加开奖号码失败", e);
		}

		return webJson;
	}

	private String generateOpenCode(String lottery) {
		GameLotteryInfo lotteryInfo = getRedisService().getGameLotteryInfo(lottery);
		if (lotteryInfo == null) {
			throw newException("-1", "未找到彩种信息");
		}
		int numberCount = 0;
		LotteryType lotteryType = LotteryType.valueOf(lotteryInfo.getGameTypeCode());
		switch (lotteryType) {
			case k3:
				numberCount = 3;
				break;
			case ssc:
				numberCount = 5;
				break;
			case pk10:
				numberCount = 10;
				break;
			case lhc:
				numberCount = 7;
				break;
			case kl8:
				numberCount = 8;
				break;
			case pc28:
				numberCount = 3;
				break;
			default:
				throw new IllegalArgumentException("Unknown lottery type: " + lotteryType);
		}

		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < numberCount; i++) {
			if (i > 0) {
				sb.append(",");
			}
			sb.append("?");
		}
		return sb.toString();
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_LOTTERY_OPEN_CODE)
	@ResponseBody
	public WebJson UPDATE_LOTTERY_OPEN_CODE(HttpServletRequest request, @RequestParam(name = "id") Long id,
											@RequestParam(name = "code") String code) {
		WebJson webJson = new WebJson();
		try {
			code = code.replaceAll("，", ",").replaceAll("；", ",").replaceAll(";", ","); // 中英文逗号校验
			code = code.replaceAll(" ", ""); // 空格校验
			GameLotteryOpenCode gameLotteryOpenCode = getGameLotteryOpenCodeDao().getById(id);
			boolean checkCode = getServiceUtils().validateResult(gameLotteryOpenCode.getLottery(), code);
			if (!checkCode) {
				throw newException("-1", "开奖号码不符合该直播间开奖规则");
			}
			boolean result = getGameLotteryService().updateLotteryOpenCode(gameLotteryOpenCode.getId(), code);
			if (result) {
				String oldOpenCode = gameLotteryOpenCode.getOpenCode();
				String shortName = gameLotteryOpenCode.getLottery();
				GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(shortName);
				String lotteryShowName = gameLotteryInfo.getGameName();
				String issue = gameLotteryOpenCode.getGameIssue();
				// 保存操作日志
				String content = "编辑直播间开奖号码，开奖记录ID：" + id + "，彩种：" + lotteryShowName + "，期号：" + issue + "，原开奖号码为："
						+ oldOpenCode + "，变更后开奖号码为：" + code;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@RequestMapping(Route.GameLottery.PROFIT_RANKING)
	@ResponseBody
	public WebJson getProfitRanking(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
									@RequestParam(name = "rankDay", required = false) int rankDay,
									@RequestParam(name = "playerName", required = false) String playerName,
									@RequestParam(name = "page", defaultValue = "0") int page,
									@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size;
		List<GameLotteryProfitRanking> list = new ArrayList<>();
		// 查询MongoDB
		Criteria criteria = new Criteria();
		if (!StringUtils.isEmpty(agentNo)) {
			criteria.and("agentNo").is(agentNo);
		}
		if (!StringUtils.isEmpty(playerName)) {
			criteria.and("playerName").is(playerName);
		}
		if (!StringUtils.isEmpty(rankDay)) {
			criteria.and("rankDay").is(rankDay);
		}
		Sort sort = Sort.by(Sort.Direction.DESC, "profitCountAmount");
		int count = getGameLotteryProfitRankingDao().totalCount(criteria);
		if (count > 0) {
			list = getGameLotteryProfitRankingDao().find(criteria, sort, firstResult, size);
		}
		// 使用 Stream API 进行排序
		list = list.stream().sorted(Comparator.comparing(GameLotteryProfitRanking::getProfitCountAmount).reversed())
				.collect(Collectors.toList());
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", count);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.ADD_PROFIT_RANKING)
	@ResponseBody
	public WebJson ADD_GATE_LOTTERY_PROFITRANKING(HttpServletRequest request,
												  @RequestParam(name = "agentNo") String agentNo, @RequestParam(name = "rankDay") int rankDay,
												  @RequestParam(name = "playerName") String playerName,
												  @RequestParam(name = "profitAmount") BigDecimal profitAmount) {
		WebJson webJson = new WebJson();
		// 判断名字是否重复
		Criteria criteria = new Criteria();
		criteria.and("agentNo").regex(agentNo);
		criteria.and("playerName").regex(playerName);
		Sort sort = Sort.by(Sort.Direction.ASC, "id");
		// 查询MongoDB表数据
		List<GameLotteryProfitRanking> gameLotteryProfitRankings = getGameLotteryProfitRankingDao().find(criteria, sort,
				0, 10);
		if (gameLotteryProfitRankings.size() >= 1) {
			throw newException("-1", "用户名重复！");
		}
		// 查询Agent_player_info
		AgentPlayerInfo byPlayerName = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (byPlayerName != null) {
			throw newException("-1", "用户名重复！");
		}
		AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(agentNo);
		GameLotteryProfitRanking gameLotteryProfitRanking = new GameLotteryProfitRanking();
		gameLotteryProfitRanking.setAgentNo(agentNo);
		gameLotteryProfitRanking.setProfitCountAmount(profitAmount);
		gameLotteryProfitRanking.setRankDay(rankDay);
		gameLotteryProfitRanking.setPlayerName(playerName);
		gameLotteryProfitRanking.setId(UUID.randomUUID().toString());
		gameLotteryProfitRanking.setAgentName(agentInfo.getAgentName());
		getGameLotteryProfitRankingDao().save(gameLotteryProfitRanking);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.UPDATE_PROFIT_RANKING)
	@ResponseBody
	public WebJson UPDATE_GATE_LOTTERY_PROFITRANKING(HttpServletRequest request, @RequestParam(name = "id") String id,
													 @RequestParam(name = "profitAmount") BigDecimal profitAmount) {
		WebJson webJson = new WebJson();
		getGameLotteryProfitRankingDao().update(id, profitAmount);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.GameLottery.DELETE_PROFIT_RANKING)
	public WebJson DELETE_GATE_LOTTERY_PROFITRANKING(HttpServletRequest request, @RequestParam(name = "id") String id) {
		WebJson webJson = new WebJson();
		getGameLotteryProfitRankingDao().delete(id);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SEARCH_LOCK_TASK)
	public WebJson SEARCH_LOCK_TASK(HttpServletRequest request, @RequestParam(required = false) String agentNo,
			@RequestParam(required = false) String agentName, @RequestParam(required = false) String playerName,
			@RequestParam(required = false) String lottery, @RequestParam(required = false) String issue,
			@RequestParam(required = false) Integer lockType, @RequestParam(required = false) Integer lockStatus,
			@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			List<LockAccountRecordVo> list = new ArrayList<>();
			int firstResult = page * size;
			int totalCount = getLockAccountRecordService().totalRecord(agentNo, agentName, playerName, lottery, issue,
					lockType, lockStatus);
			if(totalCount > 0) {
				List<LockAccountRecord> tmpList = getLockAccountRecordService().find(agentNo, agentName, playerName,
						lottery, issue, lockType, lockStatus, firstResult, size);
				for(LockAccountRecord tmpBean : tmpList) {
					list.add(new LockAccountRecordVo(tmpBean, getAgentGameLotteryInfoDao()));
				}
			}
			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);

		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.ADD_LOCK_TASK)
	public WebJson ADD_LOCK_TASK(HttpServletRequest request, @RequestParam String agentNo,
			@RequestParam(defaultValue = "") String playerNameList, @RequestParam Integer lockType,
			@RequestParam(defaultValue = "") String lottery, @RequestParam(defaultValue = "") String issue,
			@RequestParam Integer lockNumber, @RequestParam(defaultValue = "") String lockRemark,
			@RequestParam BigDecimal amountMix, @RequestParam BigDecimal amountMax) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount admin = getSessionUser(request);
			if(!StringUtils.isEmpty(playerNameList)) {
				playerNameList = playerNameList.trim();
				playerNameList = playerNameList.replace("，", ",");
			}
			if(LockAccountRecord.LOCK_TYPE_USER == lockType && StringUtils.isEmpty(playerNameList)) {
				throw newException("700-01");
			}
			AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(agentNo);
			if(agentInfo == null) {
				throw newException("500-093");
			}

			boolean result = getLockAccountRecordService().save(agentNo, agentInfo.getAgentName(), lockType,
					playerNameList, lottery, issue, lockNumber, lockRemark, amountMix, amountMax, admin.getUsername());
			if(result) {
				// 保存操作日志
				String content = "添加卡单，代理商：" + agentInfo.getAgentName() + "，卡单类型：" + lockType + "，游戏：" +
						lottery + "，用户：" + playerNameList + "，卡单策略：" + lockNumber + "，投注金额：" + amountMix +
						"~" + amountMax + "，备注：" + lockRemark;
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}

		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.START_LOCK_TASK)
	public WebJson START_LOCK_TASK(HttpServletRequest request, @RequestParam(name = "id") int id) {
		WebJson webJson = new WebJson();
		try {
			String content = "";
			LockAccountRecord entity = getLockAccountRecordService().getById(id);
			if(null != entity) {
				int lockStatus = 1;
				if(1 == entity.getLockStatus()) {
					content = "暂停卡单";
					lockStatus = 0;
				}else {
					content = "启用卡单";
				}
				content += "，ID：" + entity.getId() + "，代理商：" + entity.getAgentName() + "，卡单类型：" +
						entity.getLockType() + "，游戏：" + entity.getLottery() + "，用户：" + entity.getPlayerNameList();
				entity.setLockStatus(lockStatus);
				getLockAccountRecordService().update(entity);
			}

			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.GameLottery.DELETE_LOCK_TASK)
	public WebJson DELETE_LOCK_TASK(HttpServletRequest request, @RequestParam int id) {
		WebJson webJson = new WebJson();
		try {
			LockAccountRecord entity = getLockAccountRecordService().getById(id);
			if(entity == null) {
				throw newException("0-12");
			}
			getLockAccountRecordService().delete(entity.getId());
			// 保存操作日志
			String content = "删除卡单，ID：" + entity.getId() + "，代理商：" + entity.getAgentName() + "，卡单类型：" +
					entity.getLockType() + "，游戏：" + entity.getLottery() + "，用户：" + entity.getPlayerNameList() +
					"，卡单策略：" + entity.getLockNumber() + "，投注金额：" + entity.getAmountMix() + "~" +
					entity.getAmountMax() + "，备注：" + entity.getLockRemark();
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.GameLottery.SEARCH_LOCK_ORDER_RECORD)
	public WebJson SEARCH_LOCK_ORDER_RECORD(HttpServletRequest request, @RequestParam(required = false) String agentNo,
			@RequestParam(required = false) String agentName, @RequestParam(required = false) String playerName,
			@RequestParam(required = false) Integer lockId, @RequestParam(required = false) String lottery,
			@RequestParam(required = false) String billno, @RequestParam(required = false) String method,
			@RequestParam(required = false) String issue, @RequestParam(required = false) Integer lockType,
			@RequestParam(required = false) String sLockTime, @RequestParam(required = false) String eLockTime,
			@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			List<LockOrderRecordVo> list = new ArrayList<>();
			int firstResult = page * size;
			List<Order> orders = new ArrayList<>();
			orders.add(Order.desc("id"));
			List<Criterion> criterions = new ArrayList<>();
			if(!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if(!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
			if(lockId != null) {
				criterions.add(Restrictions.eq("lockId", lockId));
			}
			if(!StringUtils.isEmpty(playerName)) {
				criterions.add(Restrictions.eq("playerName", playerName));
			}
			if(!StringUtils.isEmpty(billno)) {
				criterions.add(Restrictions.eq("billno", billno));
			}
			if(!StringUtils.isEmpty(lottery)) {
				criterions.add(Restrictions.eq("lottery", lottery));
			}
			if(!StringUtils.isEmpty(issue)) {
				criterions.add(Restrictions.eq("issue", issue));
			}
			if(!StringUtils.isEmpty(method)) {
				criterions.add(Restrictions.eq("method", method));
			}
			if(null != lockType) {
				criterions.add(Restrictions.eq("lockType", lockType));
			}
			if(!StringUtils.isEmpty(sLockTime)) {
				Date thisDate = new Moment().fromDate(sLockTime).toDate();
				criterions.add(Restrictions.ge("lockTime", thisDate));
			}
			if(!StringUtils.isEmpty(eLockTime)) {
				Date thisDate = new Moment().fromDate(eLockTime).toDate();
				criterions.add(Restrictions.lt("lockTime", thisDate));
			}

			int totalCount = getLockOrderRecordDao().totalCount(criterions);
			if(totalCount > 0) {
				List<LockOrderRecord> resultList = getLockOrderRecordDao().find(criterions, orders, firstResult, size);
				for(LockOrderRecord tmpBean : resultList) {
					list.add(new LockOrderRecordVo(tmpBean, getAgentGameLotteryInfoDao(), getGameLotteryMethodDao()));
				}
			}
			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);

		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}
}