package ph.yckj.admin.web.ctrl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;

import myutil.ErrorUtils;
import myutil.JacksonUtils;
import myutil.MathUtils;
import myutil.Moment;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.PlatformLineVo;
import ph.yckj.admin.vo.system.*;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.util.UsdtRateUtil;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;

@RestController
@RequestMapping(Route.PATH + Route.System.PATH)
public class SystemController extends SuperController {

	private final static Log log = LogFactory.getLog(SystemController.class);

	@Autowired
	private UsdtRateUtil usdtRateUtil;

	@ResponseBody
	@RequestMapping(Route.System.USDT_EXCHANGE_RATE)
	public WebJson LIST_SYSTEM_WHITELIST(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo) {
		WebJson webJson = new WebJson();
		webJson.setData(usdtRateUtil.getAgentRate(agentNo));
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_SYSTEM_WHITELIST)
	@ResponseBody
	public WebJson LIST_SYSTEM_WHITELIST(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "whiteIp", required = false) String whiteIp,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
			if (!StringUtils.isEmpty(status)) {
				criterions.add(Restrictions.eq("status", status));
			}
			if (!StringUtils.isEmpty(whiteIp)) {
				criterions.add(Restrictions.eq("whiteIp", whiteIp));
			}
			List<Order> orders = new ArrayList<Order>();
			orders.add(Order.desc("id"));
			List<SystemWhitelistVO> list = new ArrayList<SystemWhitelistVO>();
			int totalCount = getSystemWhitelistDao().totalCount(criterions);
			if (totalCount > 0) {
				List<SystemWhitelist> resultList = getSystemWhitelistDao().find(criterions, orders, firstResult,
						maxResults);
				for (SystemWhitelist tmpBean : resultList) {
					SystemWhitelistVO vo = new SystemWhitelistVO(tmpBean);
					vo.setUsername(getAdminAccountDao().getById(tmpBean.getCreateBy()).getUsername());
					list.add(vo);
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("LIST_SYSTEM_WHITELIST 异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.ADD_SYSTEM_WHITELIST)
	@ResponseBody
	public WebJson ADD_SYSTEM_WHITELIST(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "whiteIp") String whiteIp, @RequestParam(name = "expiresType") int expiresType) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			Moment moment = new Moment();
			String username = adminAccount.getUsername();
			SystemWhitelist entity = new SystemWhitelist();
			if (StringUtils.isEmpty(agentNo)) {
				entity.setAgentNo("");
				entity.setAgentName("");
                entity.setAgentType(0);
			} else {
				entity.setAgentNo(agentNo);
				AgentInfo agent = getRedisService().getAgentInfo(agentNo);
				entity.setAgentName(agent.getAgentName());
                entity.setAgentType(agent.getAgentType());
			}
			entity.setUsername(username);
			entity.setCreateBy(adminAccount.getId());
			entity.setWhiteIp(whiteIp);
			Date createTime = moment.toDate();
			entity.setCreateTime(createTime);
			Date expiresTime = new Date();
			switch (expiresType) {
			case 1:// 一天
				expiresTime = moment.add(1, "days").toDate();
				break;
			case 2:// 三天
				expiresTime = moment.add(3, "days").toDate();
				break;
			default:// 一百年
				expiresTime = moment.add(100, "years").toDate();
				break;
			}
			entity.setStatus(SystemWhitelist.STATUS_NORMAL);
			entity.setExpiresTime(expiresTime);
			boolean result = getSystemWhitelistDao().save(entity);
			if (result) {
				// 保存操作日志
				String adminLogContent = "操作用户:" + username + "，白名单ip：" + whiteIp + "，到期时间："
						+ new Moment().fromDate(expiresTime).toSimpleTime() + "，状态：启用" + "，操作时间："
						+ new Moment().fromDate(createTime).toSimpleTime();
				ThreadLocalUtil.setAdminLogContent(adminLogContent);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("ADD_SYSTEM_WHITELIST 异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.ENABLE_SYSTEM_WHITELIST)
	@ResponseBody
	public WebJson ENABLE_SYSTEM_WHITELIST(HttpServletRequest request,
			@RequestParam(name = "id", required = false) int id) {
		WebJson webJson = new WebJson();
		try {
			String accessId = "8c74491f-584f-9d91-2b22-b89821e91b33";
			AdminAccount adminAccount = getSessionUser(request);
			hasAccess(adminAccount, accessId); // 查看是否有权限
			boolean result = getSystemWhitelistDao().enable(id);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("ENABLE_SYSTEM_WHITELIST 异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.DISABLED_SYSTEM_WHITELIST)
	@ResponseBody
	public WebJson DISABLED_SYSTEM_WHITELIST(HttpServletRequest request,
			@RequestParam(name = "id", required = false) int id) {
		WebJson webJson = new WebJson();
		try {
			String accessId = "8c74491f-584f-9d91-2b22-b89821e91b33";
			AdminAccount adminAccount = getSessionUser(request);
			hasAccess(adminAccount, accessId); // 查看是否有权限
			boolean result = getSystemWhitelistDao().disable(id);
			if (result) {
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("DISABLED_SYSTEM_WHITELIST 异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.DELETE_SYSTEM_WHITELIST)
	@ResponseBody
	public WebJson DELETE_SYSTEM_WHITELIST(HttpServletRequest request, @RequestParam(name = "id") int id) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String username = adminAccount.getUsername();
			SystemWhitelist systemWhitelist = getSystemWhitelistDao().getById(id);
			boolean result = getSystemWhitelistDao().delete(id);
			if (result) {
				StringBuilder contentDetail = new StringBuilder();
				contentDetail.append("[");
				if (null != systemWhitelist) {
					String whiteIp = systemWhitelist.getWhiteIp();
					String createTime = new Moment().fromDate(systemWhitelist.getCreateTime()).toSimpleTime();
					String expiresTime = new Moment().fromDate(systemWhitelist.getExpiresTime()).toSimpleTime();
					contentDetail.append("操作用户名：" + username);
					contentDetail.append(",白名单IP：");
					if (null != whiteIp) {
						contentDetail.append(whiteIp);
					} else {
						contentDetail.append("");
					}
					contentDetail.append(",到期时间：");
					if (null != expiresTime) {
						contentDetail.append(expiresTime);
					} else {
						contentDetail.append("");
					}
					contentDetail.append(",创建时间：");
					if (null != createTime) {
						contentDetail.append(createTime);
					} else {
						contentDetail.append("");
					}
				}
				contentDetail.append("]");
				// 保存操作日志
				String adminLogContent = "ID：" + id + "，记录：" + contentDetail.toString();
				ThreadLocalUtil.setAdminLogContent(adminLogContent);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("DELETE_SYSTEM_WHITELIST 异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_SYSTEM_NOTICE)
	public WebJson LIST_SYSTEM_NOTICE(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "noticeType", required = false) Integer noticeType,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		int firstResult = page * size, maxResults = size;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (null != noticeType) {
			criterions.add(Restrictions.eq("noticeType", noticeType));
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("isStick"));
		orders.add(Order.desc("showDate"));
		List<SystemNotice> resultList = getSystemNoticeDao().find(criterions, orders, firstResult, maxResults);
		int totalCount = getSystemNoticeDao().totalCount(criterions);
		List<SystemNoticeVo> list = new ArrayList<>();
		for (SystemNotice tmpBean : resultList) {
			list.add(new SystemNoticeVo(tmpBean, getRedisService()));
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.ADD_SYSTEM_NOTICE)
	public WebJson ADD_SYSTEM_NOTICE(HttpServletRequest request,
			@RequestParam(name = "agentNo", defaultValue = "") String agentNo,
			@RequestParam(name = "agentName", defaultValue = "") String agentName,
			@RequestParam(name = "noticeType", defaultValue = "1") int noticeType,
			@RequestParam(name = "title") String title, @RequestParam(name = "content") String content,
			@RequestParam(name = "status") int status, @RequestParam(name = "showDate") String showDate,
			@RequestParam(name = "isStick") boolean isStick) {
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			agentName = getRedisService().getAgentInfo(agentNo).getAgentName();
		}
		if (0 == noticeType) {
			agentNo = "";
			agentName = "";
		}
		WebJson webJson = new WebJson();
		Date formatDate = new Moment().fromDate(showDate).toDate();
		boolean result = getSystemService().addNotice(agentNo, agentName, title, noticeType, content,
				ThreadLocalUtil.getSessionUser().getUsername(), isStick, formatDate, status);
		if (result) {
			// 保存操作日志
			String adminLogContent = "标题：" + title + "，内容：" + content;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.EDIT_SYSTEM_NOTICE)
	public WebJson EDIT_SYSTEM_NOTICE(@RequestParam(name = "id") long id, @RequestParam(name = "title") String title,
			@RequestParam(name = "content") String content, @RequestParam(name = "isStick") boolean isStick,
			@RequestParam(name = "showDate") String showDate, @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		Date formatDate = new Moment().fromDate(showDate).toDate();
		boolean result = getSystemService().editNotice(id, title, content, isStick, formatDate, status);
		if (result) {
			// 保存操作日志
			String adminLogContent = "标题：" + title + "，内容：" + content;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_SYSTEM_NOTICE_STATUS)
	public WebJson UPDATE_SYSTEM_NOTICE_STATUS(@RequestParam(name = "id") long id,
			@RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		boolean result = getSystemService().updateNoticeStatus(id, status);
		if (result) {
			// 保存操作日志
			String adminLogContent = "ID：" + id + "，是否隐藏：" + status;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_SYSTEM_NOTICE_STICK)
	public WebJson UPDATE_SYSTEM_NOTICE_STICK(@RequestParam(name = "id") long id,
			@RequestParam(name = "isStick") boolean isStick) {
		WebJson webJson = new WebJson();

		boolean result = getSystemService().updateNoticeStick(id, isStick);
		if (result) {
			// 保存操作日志
			String adminLogContent = "ID：" + id + "，是否置顶：" + isStick;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.DELETE_SYSTEM_NOTICE)
	public WebJson DELETE_SYSTEM_NOTICE(HttpServletRequest request, @RequestParam(name = "id") long id) {
		WebJson webJson = new WebJson();
		getSessionUser(request);
		boolean result = getSystemService().deleteNotice(id);
		if (result) {
			// 保存操作日志
			String adminLogContent = "ID：" + id;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_BUSINESS_COOPERATE)
	public WebJson LIST_BUSINESS_COOPERATE() {
		WebJson webJson = new WebJson();
		Map<String, Object> data = new HashMap<String, Object>();
		List<SystemConfig> list = getRedisService().getSystemConfigList("CALL_CENTER");
		for (SystemConfig systemConfig : list) {
			String key = systemConfig.getKey();
			String value = systemConfig.getValue();
			data.put(key, value);
		}
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_BUSINESS_COOPERATE)
	public WebJson UPDATE_BUSINESS_COOPERATE(HttpServletRequest request, @RequestParam(name = "d") String d) {
		WebJson webJson = new WebJson();
		JSONObject object = JSONObject.parseObject(d);
		String qq = object.getString("qq");
		String email = object.getString("email");
		String whatsapp = object.getString("whatsapp");
		String telegram = object.getString("telegram");
		Map<String, String> data = new HashMap<String, String>();
		data.put("QQ", qq);
		data.put("EMAIL", email);
		data.put("WHATSAPP", whatsapp);
		data.put("TELEGRAM", telegram);
		boolean result = getSystemService().updateConfig("CALL_CENTER", data);
		if (result) {
			// 保存操作日志
			String adminLogContent = "编辑客服配置>>QQ:" + qq + ",EMAIL:" + email + ",TELEGRAM:" + telegram + ",WHATSAPP:"
					+ whatsapp;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_ONLINE_SERVICE)
	public WebJson LIST_ONLINE_SERVICE(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String group = "PLATFORM", key = "ONLINE_SERVICE", value = "";
		Map<String, Object> data = new HashMap<String, Object>();
		PlatformConfig config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, group, key);
		if (null != config) {
			value = config.getValue();
		}
		data.put(key, value);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_ONLINE_SERVICE)
	public WebJson UPDATE_ONLINE_SERVICE(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "onlineService") String onlineService) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String group = "PLATFORM", key = "ONLINE_SERVICE", value = onlineService;
		Map<String, String> data = new HashMap<String, String>();
		data.put("ONLINE_SERVICE", onlineService);
		PlatformConfig config = new PlatformConfig(agentNo, group, key, value, "平台在线客服地址");
		boolean result = getPlatformConfigDao().upsert(config);
		if (result) {
			// 保存操作日志
			String adminLogContent = "在线客服地址变更为：" + value;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_PLAYER_SERVICE_CONFIG)
	public WebJson LIST_PLAYER_SERVICE_CONFIG(HttpServletRequest request, @RequestParam("agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if(!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String lotteryGroup = "GAME_LOTTERY";
		String accountGroup = "ACCOUNT";
		List<PlatformConfig> list = getPlatformConfigDao().listByGroup(agentNo, lotteryGroup);
		if(list == null) {
			list = new ArrayList<>();
		}
		List<PlatformConfig> list1 = getPlatformConfigDao().listByGroup(agentNo, accountGroup);
		if(list1 != null) {
			list.addAll(list1);
		}
		Map<String, String> data = new HashMap<>();
		for(PlatformConfig config : list) {
			data.put(config.getKey(), config.getValue());
		}

		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_PLAYER_SYSTEM_CONFIG)
	public WebJson UPDATE_PLAYER_SYSTEM_CONFIG(HttpServletRequest request, @RequestParam("agentNo") String agentNo,
			@RequestParam("d") String d) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if(!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String lotteryGroup = "GAME_LOTTERY";
		String accountGroup = "ACCOUNT";

		JsonNode jsonNode = JacksonUtils.toJsonNode(d);
		int sysCode = jsonNode.path("sysCode").asInt();
		double sysPoint = jsonNode.path("sysPoint").asDouble();
		int sysCodeMax = jsonNode.path("sysCodeMax").asInt();
		int sysCodeMin = jsonNode.path("sysCodeMin").asInt();
		int sysUnitMoney = jsonNode.path("sysUnitMoney").asInt();
		boolean methodTypeStandard = jsonNode.path("methodTypeStandard").asBoolean();
		boolean lockAccount = jsonNode.path("lockAccount").asBoolean();
		boolean lockAccountIp = jsonNode.path("lockAccountIp").asBoolean();
		String autoEqualCode = jsonNode.path("autoEqualCode").asText();
		String allowTransferToDown = jsonNode.path("allowTransferToDown").asText();
		String allowTransferToUp = jsonNode.path("allowTransferToUp").asText();
		String applyUpDedTransfer = jsonNode.path("applyUpDedTransfer").asText();
		int loginFailedNum = jsonNode.path("loginFailedNum").asInt();
		int paymentPasswordFailedNum = jsonNode.path("paymentPasswordFailedNum").asInt();


		Map<String, String> dataLottery = new HashMap<>();
		dataLottery.put("SYS_CODE", String.valueOf(sysCode));
		dataLottery.put("SYS_POINT", String.valueOf(sysPoint));
		dataLottery.put("SYS_CODE_MAX", String.valueOf(sysCodeMax));
		dataLottery.put("SYS_CODE_MIN", String.valueOf(sysCodeMin));
		dataLottery.put("SYS_UNIT_MONEY", String.valueOf(sysUnitMoney));
		dataLottery.put("METHOD_TYPE_STANDARD", String.valueOf(methodTypeStandard));
		dataLottery.put("LOCK_ACCOUNT", String.valueOf(lockAccount));
		dataLottery.put("LOCK_ACCOUNT_IP", String.valueOf(lockAccountIp));

		Map<String, String> dataAccount = new HashMap<>();
		dataAccount.put("AUTO_EQUAL_CODE", autoEqualCode);
		dataAccount.put("ALLOW_TRANSFER_TO_DOWN", allowTransferToDown);
		dataAccount.put("ALLOW_TRANSFER_TO_UP", allowTransferToUp);
		dataAccount.put("APPLY_UP_DED_TRANSFER", applyUpDedTransfer);
		dataAccount.put("LOGIN_FAILED_NUM", String.valueOf(loginFailedNum));
		dataAccount.put("PAYMENT_PASSWORD_FAILED_NUM", String.valueOf(paymentPasswordFailedNum));

		getSystemService().updateConfig(agentNo, lotteryGroup, dataLottery);
		boolean result = getSystemService().updateConfig(agentNo, accountGroup, dataAccount);
		if(result) {
			// 保存操作日志
			String content = "修改用户配置，代理商商户号：" + agentNo + "，最高奖级：" + sysCode + "，最高返点：" + sysPoint +
					"，最高可投注奖级：" + sysCodeMax + "，最低可投注奖级：" + sysCodeMin + "，单注投注金额：" +
					sysUnitMoney + "，标准盘玩法总开关：" + methodTypeStandard + "，卡单总开关：" + lockAccount +
					"，IP卡单开关：" + lockAccountIp + "，自动开启平级账号：" + autoEqualCode + "，给下级转账：" +
					allowTransferToDown + "，给上级转账：" + allowTransferToUp;
			ThreadLocalUtil.setAdminLogContent(content);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_CHAT_SERVICE_CONFIG)
	public WebJson LIST_CHAT_SERVICE_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
        WebJson webJson = new WebJson();
        AdminAccount adminAccount = getSessionUser(request);
        String defaultAgentNo = adminAccount.getAgentNo();
        if (!StringUtils.isEmpty(defaultAgentNo)) {
            agentNo = defaultAgentNo;
        }
        String group = "CHAT_ROOM",
            orderNoticeKey = "ORDER_NOTICE", orderNotice = "",
            prizeNoticeKey = "PRIZE_NOTICE", prizeNotice = "";
        Map<String, Object> data = new HashMap<String, Object>();
        PlatformConfig orderNoticeConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, group, orderNoticeKey);
        if (null != orderNoticeConfig) {
            orderNotice = orderNoticeConfig.getValue();
        }

        PlatformConfig prizeNoticeConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, group, prizeNoticeKey);
        if (null != prizeNoticeConfig) {
            prizeNotice = prizeNoticeConfig.getValue();
        }
        data.put("orderNotice", orderNotice);
        data.put("prizeNotice", prizeNotice);
        webJson.setData(data);
        setSuccess(webJson);
        return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_CHAT_SYSTEM_CONFIG)
	public WebJson UPDATE_CHAT_SYSTEM_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo", required = false) String agentNo,@RequestParam(name = "orderNotice", required = false) String orderNotice,
			@RequestParam(name = "prizeNotice", required = false) String prizeNotice) {
		WebJson webJson = new WebJson();
        AgentInfo agent = getAgent(request);
        if (agent != null) {
            String defaultAgentNo = agent.getAgentNo();
            if (!StringUtils.isEmpty(defaultAgentNo)) {
                agentNo = defaultAgentNo;
            }
        }
		if (!StringUtils.isEmpty(orderNotice)) {
            PlatformConfig entity = new PlatformConfig(agentNo, "CHAT_ROOM", "ORDER_NOTICE", orderNotice, "");
            getPlatformConfigDao().upsert(entity);
		}

		if (!StringUtils.isEmpty(prizeNotice)) {
            PlatformConfig entity = new PlatformConfig(agentNo, "CHAT_ROOM", "PRIZE_NOTICE", prizeNotice, "");
            getPlatformConfigDao().upsert(entity);

        }
		// 保存操作日志
		String adminLogContent = "编辑聊天系统配置>>代理商号:"+ agentNo + "下单通知:" + orderNotice + ",中奖通知:" + prizeNotice;
		ThreadLocalUtil.setAdminLogContent(adminLogContent);

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_TRY_SERVICE_CONFIG)
	public WebJson LIST_TRY_SERVICE_CONFIG() {
		WebJson webJson = new WebJson();
		String canTry = getRedisService().getSystemConfig("USER_TRY", "CAN_TRY").getValue();
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("canTry", canTry);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_TRY_SYSTEM_CONFIG)
	public WebJson UPDATE_TRY_SYSTEM_CONFIG(@RequestParam(name = "canTry", required = false) String canTry) {
		WebJson webJson = new WebJson();

		if (!StringUtils.isEmpty(canTry)) {
			getSystemService().editSystemConfig("USER_TRY", "CAN_TRY", canTry);
		}

		// 保存操作日志
		String adminLogContent = "编辑试玩系统配置>>可以试玩:" + canTry;
		ThreadLocalUtil.setAdminLogContent(adminLogContent);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_GAME_SERVICE_CONFIG)
	public WebJson LIST_GAME_SERVICE_CONFIG() {
		WebJson webJson = new WebJson();
		String liveUrlDefault = getRedisService().getSystemConfig("GAME", "LIVE_URL_DEFAULT").getValue();
		String useOpenCodeVideo = getRedisService().getSystemConfig("GAME", "USE_OPEN_CODE_VIDEO").getValue();
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("liveUrlDefault", liveUrlDefault);
		data.put("useOpenCodeVideo", useOpenCodeVideo);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.UPDATE_GAME_SYSTEM_CONFIG)
	public WebJson UPDATE_GAME_SYSTEM_CONFIG(
			@RequestParam(name = "liveUrlDefault", required = false) String liveUrlDefault,
			@RequestParam(name = "useOpenCodeVideo", required = false) String useOpenCodeVideo) {
		WebJson webJson = new WebJson();

		if (!StringUtils.isEmpty(liveUrlDefault)) {
			getSystemService().editSystemConfig("GAME", "LIVE_URL_DEFAULT", liveUrlDefault);
		}

		if (!StringUtils.isEmpty(useOpenCodeVideo)) {
			getSystemService().editSystemConfig("GAME", "USE_OPEN_CODE_VIDEO", useOpenCodeVideo);
		}
		// 保存操作日志
		String adminLogContent = "编辑游戏系统配置>>是否直播默认地址:" + liveUrlDefault + "是否使用开奖视频：" + useOpenCodeVideo;
		ThreadLocalUtil.setAdminLogContent(adminLogContent);

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_DOWNLOAD_CONFIG)
	public WebJson LIST_DOWNLOAD_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String group = "PLATFORM", key = "DOWNLOAD_LINK", value = "";
		Map<String, Object> data = new HashMap<String, Object>();
		PlatformConfig config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, group, key);
		if (null != config) {
			value = config.getValue();
		}

		String forceUpdate = "";
		PlatformConfig forceUpdateConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "PLATFORM", "FORCED_UPDATE");
		if (null != forceUpdateConfig) {
			forceUpdate = forceUpdateConfig.getValue();
		}

		String latestAppVersion = "";
		PlatformConfig latestAppVersionConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "PLATFORM", "LATEST_APP_VERSION");
		if (null != latestAppVersionConfig) {
			latestAppVersion = latestAppVersionConfig.getValue();
		}

		String updateContent = "";
		PlatformConfig updateContentConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "PLATFORM", "UPDATE_CONTENT");
		if (null != updateContentConfig) {
			updateContent = updateContentConfig.getValue();
		}
		data.put(key, value);
		data.put("forceUpdate", forceUpdate);
		data.put("latestAppVersion", latestAppVersion);
		data.put("updateContent", updateContent);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_LIVE_LINE)
	public WebJson LIST_LIVE_LINE(HttpServletRequest request,
			@RequestParam(name = "noticeType", required = false) Integer noticeType,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		Map<String, Object> data = new HashMap<String, Object>();

		List<Criterion> criterions = new ArrayList<Criterion>();

		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.asc("sort"));
		int totalCount = getPlatformLiveLineDao().totalCount(criterions);
		List<PlatformLiveLineVo> list = new ArrayList<>();

		if (totalCount > 0) {
			List<PlatformLiveLine> resultList = getPlatformLiveLineDao().find(criterions, orders, 0, 1000);

			for (PlatformLiveLine tmpBean : resultList) {
				list.add(new PlatformLiveLineVo(tmpBean));
			}
		}

		webJson.setData(list);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.ADD_LIVE_LINE)
	public WebJson ADD_LIVE_LINE(HttpServletRequest request, @RequestParam(name = "liveLineCode") String liveLineCode,
			@RequestParam(name = "liveLineName") String liveLineName,
			@RequestParam(name = "liveLineDomain") String liveLineDomain) {
		AdminAccount adminAccount = getSessionUser(request);

		WebJson webJson = new WebJson();
		int i = getPlatformLiveLineDao().totalCount(new ArrayList<>());

		PlatformLiveLine platformLiveLine = new PlatformLiveLine();
		platformLiveLine.setLiveLineCode(liveLineCode);
		platformLiveLine.setLiveLineName(liveLineName);
		platformLiveLine.setLiveLineDomain(liveLineDomain);
		platformLiveLine.setStatus(0);
		platformLiveLine.setSort(i + 1);
		boolean result = getPlatformLiveLineDao().save(platformLiveLine);

		if (result) {
			// 保存操作日志
			String adminLogContent = "直播线路Code：" + liveLineCode + "，直播线路名称：" + liveLineName + "，直播线路域名："
					+ liveLineDomain;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.EDIT_LIVE_LINE)
	public WebJson EDIT_LIVE_LINE(@RequestParam(name = "id") long id,
			@RequestParam(name = "liveLineName", required = false) String liveLineName,
			@RequestParam(name = "liveLineDomain", required = false) String liveLineDomain) {
		WebJson webJson = new WebJson();
		PlatformLiveLine platformLiveLine = getPlatformLiveLineDao().getById(id);
		if (platformLiveLine != null) {
			platformLiveLine.setLiveLineName(liveLineName);
			platformLiveLine.setLiveLineDomain(liveLineDomain);
			getPlatformLiveLineDao().update(platformLiveLine);
			// 保存操作日志
			String adminLogContent = "id：" + id + "，直播线路名称：" + liveLineName + "，直播线路域名：" + liveLineDomain;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		}

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.SORT_LIVE_LINE)
	public WebJson SORT_LIVE_LINE(HttpServletRequest request, @RequestParam(value = "id") Long id,
			@RequestParam(value = "sort", required = false) Integer sort) {

		WebJson webJson = new WebJson();

		if (sort <= 0) {
			throw new ServiceException("1", "排序值不能小于等于0");
		}

		boolean b = getSystemService().updateLiveLineSort(id, sort);
		if (b) {
			// 保存操作日志
			String content = "直播线路ID：" + id + "，排序值：" + sort;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;

	}

	@CheckLogin
	@RequestMapping(Route.System.ENABLE_LIVE_LINE)
	public WebJson ENABLE_LIVE_LINE(@RequestParam(name = "id") long id, @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		PlatformLiveLine platformLiveLine = getPlatformLiveLineDao().getById(id);
		if (platformLiveLine != null) {
			platformLiveLine.setStatus(status);
			getPlatformLiveLineDao().update(platformLiveLine);
			// 保存操作日志
			String adminLogContent = "id：" + id + "，启用状态：" + status;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.UPDATE_DOWNLOAD_CONFIG)
	public WebJson UPDATE_DOWNLOAD_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "downloadLink") String downloadLink,
										  @RequestParam(name = "forceUpdate", required = false) String forceUpdate,
										  @RequestParam(name = "latestAppVersion", required = false) String latestAppVersion,
										  @RequestParam(name = "updateContent", required = false) String updateContent) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String group = "PLATFORM", key = "DOWNLOAD_LINK", value = downloadLink;
		Map<String, String> data = new HashMap<String, String>();
		data.put("DOWNLOAD_LINK", downloadLink);
		PlatformConfig config = new PlatformConfig(agentNo, group, key, value, "APP下载地址");
		boolean result = getPlatformConfigDao().upsert(config);
        if (result) {
            log.error("更新失败, agentNo=" + agentNo + ", agentName=" + agentName + ", downloadLink=" + downloadLink);
        }

		PlatformConfig latestAppVersionConfig = new PlatformConfig(agentNo, group, "LATEST_APP_VERSION", latestAppVersion, "APP最新版本号");
		result = getPlatformConfigDao().upsert(latestAppVersionConfig);
		if (result) {
			log.error("更新失败, agentNo=" + agentNo + ", agentName=" + agentName + ", latestAppVersion=" + latestAppVersion);
		}
		PlatformConfig updateContentConfig = new PlatformConfig(agentNo, group, "UPDATE_CONTENT", updateContent, "APP更新内容");
		result = getPlatformConfigDao().upsert(updateContentConfig);
		if (result) {
			log.error("更新失败, agentNo=" + agentNo + ", agentName=" + agentName + ", updateContent=" + updateContent);
		}
		PlatformConfig forceUpdateConfig = new PlatformConfig(agentNo, group, "FORCED_UPDATE", forceUpdate , "APP是否强制更新");
		result = getPlatformConfigDao().upsert(forceUpdateConfig);
		if (result) {
			// 保存操作日志
			String adminLogContent = "APP下载地址：" + value + "APP是否强制更新" + defaultAgentNo + "APP最新版本号" + latestAppVersion + "APP更新内容" + updateContent;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_WITHDRAW_CONFIG)
	public WebJson LIST_WITHDRAW_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		Map<String, Object> data = new HashMap<String, Object>();
		List<PlatformConfig> configs = getPlatformConfigDao().listByGroup(agentNo, "WITHDRAW");
		if (!CollectionUtils.isEmpty(configs)) {
			for (PlatformConfig config : configs) {
				String key = config.getKey();
				String value = config.getValue();
				data.put(key, value);
			}
		}
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.UPDATE_WITHDRAW_CONFIG)
	public WebJson UPDATE_WITHDRAW_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName, @RequestParam(name = "d") String d) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		JsonNode jsonNode = JacksonUtils.toJsonNode(d);
		int firstWithdrawWaitHour = jsonNode.path("firstWithdrawWaitHour").asInt();
		if(firstWithdrawWaitHour < 0){
			throw new ServiceException("1","新用户首次提现等待时间应大于等于0小时");
		}

		if(firstWithdrawWaitHour > 24){
			throw new ServiceException("1","新用户首次提现等待时间应小于于等于24小时");
		}

		double minUnitAmount = jsonNode.path("minUnitAmount").asDouble();
		double maxUnitAmount = jsonNode.path("maxUnitAmount").asDouble();
		int freeDailyCount = jsonNode.path("freeDailyCount").asInt();
		double feeRate = jsonNode.path("feeRate").asDouble();
		double maxFee = jsonNode.path("maxFee").asDouble();
		int maxDailyCount = jsonNode.path("maxDailyCount").asInt();
		double maxDailyAmount = jsonNode.path("maxDailyAmount").asDouble();

		boolean withdrawSplitScaleEnable = jsonNode.path("withdrawSplitScaleEnable").asBoolean();
		double lotteryScale = jsonNode.path("lotteryScale").asDouble();
		double baccaratScale = jsonNode.path("baccaratScale").asDouble();
		double totalScale = jsonNode.path("totalScale").asDouble();
		int maxBindCard = jsonNode.path("maxBindCard").asInt();

		int usdtFreeDailyCount = jsonNode.path("usdtFreeDailyCount").asInt();
		double usdtFeeRate = jsonNode.path("usdtFeeRate").asDouble();
		double usdtMaxFee = jsonNode.path("usdtMaxFee").asDouble();
		int usdtMaxBindCard = jsonNode.path("usdtMaxBindCard").asInt();
		double usdtMinUnitAmount = jsonNode.path("usdtMinUnitAmount").asDouble();
		double usdtMaxUnitAmount = jsonNode.path("usdtMaxUnitAmount").asDouble();

		int bankFreeDailyCount = jsonNode.path("bankFreeDailyCount").asInt();
		double bankFeeRate = jsonNode.path("bankFeeRate").asDouble();
		double bankMaxFee = jsonNode.path("bankMaxFee").asDouble();
		int bankMaxBindCard = jsonNode.path("bankMaxBindCard").asInt();
		double bankMinUnitAmount = jsonNode.path("bankMinUnitAmount").asDouble();
		double bankMaxUnitAmount = jsonNode.path("bankMaxUnitAmount").asDouble();

		int alipayFreeDailyCount = jsonNode.path("alipayFreeDailyCount").asInt();
		double alipayFeeRate = jsonNode.path("alipayFeeRate").asDouble();
		double alipayMaxFee = jsonNode.path("alipayMaxFee").asDouble();
		int alipayMaxBindCard = jsonNode.path("alipayMaxBindCard").asInt();
		double alipayMinUnitAmount = jsonNode.path("alipayMinUnitAmount").asDouble();
		double alipayMaxUnitAmount = jsonNode.path("alipayMaxUnitAmount").asDouble();

		int mFreeDailyCount = jsonNode.path("mFreeDailyCount").asInt();
		double mFeeRate = jsonNode.path("mFeeRate").asDouble();
		double mMaxFee = jsonNode.path("mMaxFee").asDouble();
		int mMaxBindCard = jsonNode.path("mMaxBindCard").asInt();
		double mMinUnitAmount = jsonNode.path("mMinUnitAmount").asDouble();
		double mMaxUnitAmount = jsonNode.path("mMaxUnitAmount").asDouble();


		int hh5FreeDailyCount = jsonNode.path("hh5FreeDailyCount").asInt();
		double hh5FeeRate = jsonNode.path("hh5FeeRate").asDouble();
		double hh5MaxFee = jsonNode.path("hh5MaxFee").asDouble();
		int hh5MaxBindCard = jsonNode.path("hh5MaxBindCard").asInt();
		double hh5MinUnitAmount = jsonNode.path("hh5MinUnitAmount").asDouble();
		double hh5MaxUnitAmount = jsonNode.path("hh5MaxUnitAmount").asDouble();


		int okgFreeDailyCount = jsonNode.path("okgFreeDailyCount").asInt();
		double okgFeeRate = jsonNode.path("okgFeeRate").asDouble();
		double okgMaxFee = jsonNode.path("okgMaxFee").asDouble();
		int okgMaxBindCard = jsonNode.path("okgMaxBindCard").asInt();
		double okgMinUnitAmount = jsonNode.path("okgMinUnitAmount").asDouble();
		double okgMaxUnitAmount = jsonNode.path("okgMaxUnitAmount").asDouble();

		Map<String, String> data = new HashMap<String, String>();
		data.put("FIRST_WITHDRAW_WAIT_HOUR", String.valueOf(firstWithdrawWaitHour));
		data.put("MIN_UNIT_AMOUNT", String.valueOf(minUnitAmount));
		data.put("MAX_UNIT_AMOUNT", String.valueOf(maxUnitAmount));
		data.put("FREE_DAILY_COUNT", String.valueOf(freeDailyCount));
		data.put("FEE_RATE", String.valueOf(feeRate));
		data.put("MAX_FEE", String.valueOf(maxFee));
		data.put("MAX_DAILY_COUNT", String.valueOf(maxDailyCount));
		data.put("MAX_DAILY_AMOUNT", String.valueOf(maxDailyAmount));

		data.put("WITHDRAW_SPLIT_SCALE_ENABLE", String.valueOf(withdrawSplitScaleEnable));
		data.put("LOTTERY_SCALE", String.valueOf(lotteryScale));
		data.put("BACCARAT_SCALE", String.valueOf(baccaratScale));
		data.put("TOTAL_SCALE", String.valueOf(totalScale));
		data.put("MAX_BIND_CARD", String.valueOf(maxBindCard));

		data.put("USDT_FEE_RATE", String.valueOf(usdtFeeRate));
		data.put("USDT_MAX_FEE", String.valueOf(usdtMaxFee));
		data.put("USDT_MAX_BIND_CARD", String.valueOf(usdtMaxBindCard));
		data.put("USDT_MIN_UNIT_AMOUNT", String.valueOf(usdtMinUnitAmount));
		data.put("USDT_MAX_UNIT_AMOUNT", String.valueOf(usdtMaxUnitAmount));
		data.put("USDT_FREE_DAILY_COUNT", String.valueOf(usdtFreeDailyCount));

		data.put("BANK_FEE_RATE", String.valueOf(bankFeeRate));
		data.put("BANK_MAX_FEE", String.valueOf(bankMaxFee));
		data.put("BANK_MAX_BIND_CARD", String.valueOf(bankMaxBindCard));
		data.put("BANK_MIN_UNIT_AMOUNT", String.valueOf(bankMinUnitAmount));
		data.put("BANK_MAX_UNIT_AMOUNT", String.valueOf(bankMaxUnitAmount));
		data.put("BANK_FREE_DAILY_COUNT", String.valueOf(bankFreeDailyCount));

		data.put("ALIPAY_FEE_RATE", String.valueOf(alipayFeeRate));
		data.put("ALIPAY_MAX_FEE", String.valueOf(alipayMaxFee));
		data.put("ALIPAY_MAX_BIND_CARD", String.valueOf(alipayMaxBindCard));
		data.put("ALIPAY_MIN_UNIT_AMOUNT", String.valueOf(alipayMinUnitAmount));
		data.put("ALIPAY_MAX_UNIT_AMOUNT", String.valueOf(alipayMaxUnitAmount));
		data.put("ALIPAY_FREE_DAILY_COUNT", String.valueOf(alipayFreeDailyCount));

		data.put("M_FEE_RATE", String.valueOf(mFeeRate));
		data.put("M_MAX_FEE", String.valueOf(mMaxFee));
		data.put("M_MAX_BIND_CARD", String.valueOf(mMaxBindCard));
		data.put("M_MIN_UNIT_AMOUNT", String.valueOf(mMinUnitAmount));
		data.put("M_MAX_UNIT_AMOUNT", String.valueOf(mMaxUnitAmount));
		data.put("M_FREE_DAILY_COUNT", String.valueOf(mFreeDailyCount));

		data.put("HH5_FEE_RATE", String.valueOf(hh5FeeRate));
		data.put("HH5_MAX_FEE", String.valueOf(hh5MaxFee));
		data.put("HH5_MAX_BIND_CARD", String.valueOf(hh5MaxBindCard));
		data.put("HH5_MIN_UNIT_AMOUNT", String.valueOf(hh5MinUnitAmount));
		data.put("HH5_MAX_UNIT_AMOUNT", String.valueOf(hh5MaxUnitAmount));
		data.put("HH5_FREE_DAILY_COUNT", String.valueOf(hh5FreeDailyCount));

		data.put("OKG_FEE_RATE", String.valueOf(okgFeeRate));
		data.put("OKG_MAX_FEE", String.valueOf(okgMaxFee));
		data.put("OKG_MAX_BIND_CARD", String.valueOf(okgMaxBindCard));
		data.put("OKG_MIN_UNIT_AMOUNT", String.valueOf(okgMinUnitAmount));
		data.put("OKG_MAX_UNIT_AMOUNT", String.valueOf(okgMaxUnitAmount));
		data.put("OKG_FREE_DAILY_COUNT", String.valueOf(okgFreeDailyCount));

		boolean result = getSystemService().updateConfig(agentNo, "WITHDRAW", data);
		if (result) {
			// 保存操作日志
			String content = "修改提现配置" + "代理商商户号：" + agentNo + "新用户首次提现等待时间：" + firstWithdrawWaitHour
					+ "，单次提现金额：" + minUnitAmount + " ~ " +
					maxUnitAmount + "，每日免手续费次数：" + freeDailyCount + "，提现手续费率：" + feeRate +
					"，单次最高手续费：" + maxFee + "，每日累计金额：" + maxDailyAmount + "，每日累计次数：" + maxDailyCount +
					"，彩票与第三方游戏消费比列是否各自比例：" + withdrawSplitScaleEnable + "，彩票消费比例：" +
					lotteryScale + "，第三方游戏消费比例：" + baccaratScale + "，总消费(有效投注)比例：" + totalScale +
					"，提现银行卡数量：" + maxBindCard + "，USDT提现每日免手续费次数：" + usdtFreeDailyCount +
					"，USDT提现手续费率：" + usdtFeeRate + "，USDT提现单次最高手续费：" + usdtMaxFee +
					"，USDT提现地址数量：" + usdtMaxBindCard + "，USDT单次提现金额：" + usdtMinUnitAmount + " ~ " +
					usdtMaxUnitAmount + "，银行卡提现每日免手续费次数：" + bankFreeDailyCount + "，银行卡提现手续费率：" +
					bankFeeRate + "，银行卡提现单次最高手续费：" + bankMaxFee + "，银行卡提现地址数量：" + bankMaxBindCard +
					"，银行卡单次提现金额：" + bankMinUnitAmount + " ~ " + bankMaxUnitAmount +

					"，支付宝提现每日免手续费次数：" + alipayFreeDailyCount + "，支付宝提现手续费率：" + alipayFeeRate +
					"，支付宝提现单次最高手续费：" + alipayMaxFee + "，支付宝提现地址数量：" + alipayMaxBindCard +
					"，支付宝单次提现金额：" + alipayMinUnitAmount + " ~ " + alipayMaxUnitAmount +

					"，M提现每日免手续费次数：" + mFreeDailyCount + "，M提现手续费率：" + mFeeRate +
					"，M提现单次最高手续费：" + mMaxFee + "，M提现地址数量：" + mMaxBindCard +
					"，M单次提现金额：" + mMinUnitAmount + " ~ " + mMaxUnitAmount +

					"，HH5提现每日免手续费次数：" + hh5FreeDailyCount + "，HH5提现手续费率：" + hh5FeeRate +
					"，HH5提现单次最高手续费：" + hh5MaxFee + "，HH5提现地址数量：" + hh5MaxBindCard +
					"，HH5单次提现金额：" + hh5MinUnitAmount + " ~ " + hh5MaxUnitAmount +

					"，OKG提现每日免手续费次数：" + okgFreeDailyCount + "，OKG提现手续费率：" + okgFeeRate +
					"，OKG提现单次最高手续费：" + okgMaxFee + "，OKG提现地址数量：" + okgMaxBindCard +
					"，OKG单次提现金额：" + okgMinUnitAmount + " ~ " + okgMaxUnitAmount
					;
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@RequestMapping(Route.System.SYSTEM_LINE)
	public WebJson SYSTEM_LINE(HttpServletRequest request,
			@RequestParam(value = "agentNo", required = false) String agentNo,
			@RequestParam(value = "agentName", required = false) String agentName,
			@RequestParam(value = "agentType", required = false) Integer agentType,
			@RequestParam(value = "status", required = false) Integer status,
			@RequestParam(value = "lineType", required = false) Integer lineType,
			@RequestParam(value = "lineDomainLink", required = false) String lineDomainLink,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
			if (null != agentType) {
				criterions.add(Restrictions.eq("agentType", agentType));
			}
			if (null != status) {
				criterions.add(Restrictions.eq("status", status));
			}
			if (null != lineType) {
				criterions.add(Restrictions.eq("lineType", lineType));
			}
			if (!StringUtils.isEmpty(lineDomainLink)) {
				criterions.add(Restrictions.eq("lineDomainLink", lineDomainLink));
			}
			List<Order> orders = new ArrayList<Order>();
			orders.add(Order.desc("id"));
			List<PlatformLineVo> list = new ArrayList<PlatformLineVo>();
			int totalCount = getPlatformLineDao().totalCount(criterions);
			if (totalCount > 0) {
				List<PlatformLine> resultList = getPlatformLineDao().find(criterions, orders, firstResult, maxResults);
				for (PlatformLine tmpBean : resultList) {
					list.add(new PlatformLineVo(tmpBean));
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.PLATFORM_COUNTRY_LIST)
	public WebJson PLATFORM_COUNTRY_LIST(HttpServletRequest request,
			@RequestParam(value = "agentNo", required = false) String agentNo,
			@RequestParam(value = "agentName", required = false) String agentName,
			@RequestParam(value = "enable", required = false) Integer enable,
			@RequestParam(value = "countryName", required = false) String countryName,
			@RequestParam(value = "countryCode", required = false) String countryCode,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			List<Criterion> criterionList = new ArrayList<>();
			if(!StringUtils.isEmpty(agentNo)) {
				criterionList.add(Restrictions.eq("agentNo", agentNo));
			}
			if(!StringUtils.isEmpty(agentName)) {
				criterionList.add(Restrictions.eq("agentName", agentName));
			}
			if(!StringUtils.isEmpty(countryName)) {
				criterionList.add(Restrictions.like("countryName", countryName, MatchMode.ANYWHERE));
			}
			if(!StringUtils.isEmpty(countryCode)) {
				criterionList.add(Restrictions.eq("countryCode", countryCode));
			}
			if(enable != null) {
				criterionList.add(Restrictions.eq("enable", enable));
			}
			int firstResult = page * size;
			List<Order> orderList = new ArrayList<>();
			orderList.add(Order.desc("agentNo"));
			orderList.add(Order.asc("sort"));

			List<PlatformCountryVo> dataList = new ArrayList<>();

			int totalCount = getPlatformCountryDao().totalCount(criterionList);

			List<PlatformCountry> platformCountries =
					getPlatformCountryDao().find(criterionList, orderList, firstResult, size);

			platformCountries.forEach(platformCountry -> {
				PlatformCountryVo platformCountryVo = new PlatformCountryVo(platformCountry);
				dataList.add(platformCountryVo);
			});

			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", dataList);
			webJson.setData(data);

			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
			return webJson;
		}catch(Exception e) {
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_COUNTRY_ENABLE)
	public WebJson PLATFORM_COUNTRY_ENABLE(@RequestParam(name = "id") int id, @RequestParam(name = "enable") int enable) {
		WebJson webJson = new WebJson();
		PlatformCountry platformCountry = getPlatformCountryDao().getById(id);
		if (platformCountry != null) {
			platformCountry.setEnable(enable);
			getPlatformCountryDao().update(platformCountry);
			// 保存操作日志
			String adminLogContent = "id：" + id + "，启用状态：" + enable;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_COUNTRY_SORT)
	public WebJson PLATFORM_COUNTRY_SORT(@RequestParam(name = "id") int id, @RequestParam(name = "sort") int sort) {
		WebJson webJson = new WebJson();
		PlatformCountry platformCountry = getPlatformCountryDao().getById(id);
		if (platformCountry != null) {
			boolean b = getAgentService().sortCountry(platformCountry, sort);
            if (b) {
				// 保存操作日志
				String adminLogContent = "id：" + id + "，排序修改为：" + sort;
				ThreadLocalUtil.setAdminLogContent(adminLogContent);
				setSuccess(webJson);
            }else {
				setFail(webJson);
			}
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_THESAURUS_ADD)
	public WebJson PLATFORM_THESAURUS_ADD(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
			@RequestParam(name = "content") String content) {
		WebJson webJson = new WebJson();

		if(StringUtils.isEmpty(agentNo)) {
			throw new ServiceException("1", "必须指定代理商");
		}

		AgentInfo byAgentNo = getAgentInfoDao().getByAgentNo(agentNo);

		if(byAgentNo == null) {
			throw new ServiceException("1", "指定的代理商不存在");
		}

		List<Criterion> criterionList = new ArrayList<>();
		criterionList.add(Restrictions.eq("agentNo", agentNo));

		int i = getPlatformThesaurusDao().totalCount(criterionList);

		PlatformThesaurus entity = new PlatformThesaurus();
		entity.setAgentNo(byAgentNo.getAgentNo());
		entity.setAgentName(byAgentNo.getAgentName());
		entity.setContent(content);
		entity.setEnable(0);
		entity.setSort(i + 1);
		boolean save = getPlatformThesaurusDao().save(entity);
		if(save) {
			// 保存操作日志;
			String adminLogContent = "新增词库:" + content;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_THESAURUS_DELETE)
	public WebJson PLATFORM_THESAURUS_DELETE(@RequestParam(name = "id") int id) {
		WebJson webJson = new WebJson();
		PlatformThesaurus entity = getPlatformThesaurusDao().getById(id);
		if(entity != null) {
			boolean delete = getPlatformThesaurusDao().delete(entity);
			if(delete) {
				// 保存操作日志
				String adminLogContent = "id：" + id + "，已删除";
				ThreadLocalUtil.setAdminLogContent(adminLogContent);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_THESAURUS_UPDATE)
	public WebJson PLATFORM_THESAURUS_UPDATE(@RequestParam(name = "id") int id,
			@RequestParam(name = "content") String content) {
		WebJson webJson = new WebJson();
		PlatformThesaurus platformThesaurus = getPlatformThesaurusDao().getById(id);
		if(platformThesaurus != null) {
			platformThesaurus.setContent(content);
			getPlatformThesaurusDao().update(platformThesaurus);
			// 保存操作日志
			String adminLogContent = "id：" + id + "，内容：" + content;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.PLATFORM_THESAURUS_LIST)
	public WebJson PLATFORM_THESAURUS_LIST(HttpServletRequest request,
			@RequestParam(value = "agentNo", required = false) String agentNo,
			@RequestParam(value = "agentName", required = false) String agentName,
			@RequestParam(value = "enable", required = false) Integer enable,
			@RequestParam(value = "content", required = false) String content,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			List<Criterion> criterionList = new ArrayList<>();
			if(!StringUtils.isEmpty(agentNo)) {
				criterionList.add(Restrictions.eq("agentNo", agentNo));
			}
			if(!StringUtils.isEmpty(agentName)) {
				criterionList.add(Restrictions.eq("agentName", agentName));
			}
			if(enable != null) {
				criterionList.add(Restrictions.eq("enable", enable));
			}
			if(!StringUtils.isEmpty(content)) {
				criterionList.add(Restrictions.like("content", content, MatchMode.ANYWHERE));
			}
			int firstResult = page * size;
			List<Order> orderList = new ArrayList<>();
			orderList.add(Order.desc("agentNo"));
			orderList.add(Order.asc("sort"));

			List<PlatformThesaurusVo> dataList = new ArrayList<>();

			int totalCount = getPlatformThesaurusDao().totalCount(criterionList);

			List<PlatformThesaurus> platformThesauruses =
					getPlatformThesaurusDao().find(criterionList, orderList, firstResult, size);

			platformThesauruses.forEach(entity -> {
				PlatformThesaurusVo vo = new PlatformThesaurusVo(entity);
				dataList.add(vo);
			});

			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", dataList);
			webJson.setData(data);

			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
			return webJson;
		}catch(Exception e) {
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_THESAURUS_ENABLE)
	public WebJson PLATFORM_THESAURUS_ENABLE(@RequestParam(name = "id") int id,
			@RequestParam(name = "enable") int enable) {
		WebJson webJson = new WebJson();
		PlatformThesaurus platformThesaurus = getPlatformThesaurusDao().getById(id);
		if(platformThesaurus != null) {
			platformThesaurus.setEnable(enable);
			getPlatformThesaurusDao().update(platformThesaurus);
			// 保存操作日志
			String adminLogContent = "id：" + id + "，启用状态：" + enable;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.System.PLATFORM_THESAURUS_SORT)
	public WebJson PLATFORM_THESAURUS_SORT(@RequestParam(name = "id") int id, @RequestParam(name = "sort") int sort) {
		WebJson webJson = new WebJson();
		PlatformThesaurus platformThesaurus = getPlatformThesaurusDao().getById(id);
		if(platformThesaurus != null) {
			boolean b = getAgentService().sortThesaurus(platformThesaurus, sort);
			if(b) {
				// 保存操作日志
				String adminLogContent = "id：" + id + "，排序值：" + sort;
				ThreadLocalUtil.setAdminLogContent(adminLogContent);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}else {
			setFail(webJson);
		}
		return webJson;
	}
	
	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.System.LIST_LOTTERY_CONFIG)
	public WebJson LIST_LOTTERY_CONFIG(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String group = "GAME_LOTTERY", key = "REBATE_LEVEL", value = "0";
		Map<String, Object> data = new HashMap<String, Object>();
		PlatformConfig config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, group, key);
		if (null != config) {
			value = config.getValue();
		}
		data.put(key, value);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}
	
	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.System.UPDATE_LOTTERY_CONFIG)
	public WebJson UPDATE_LOTTERY_CONFIG(HttpServletRequest request, @RequestParam String agentNo,
			@RequestParam(name = "rebateLevel") int rebateLevel) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		String group = "GAME_LOTTERY", key = "REBATE_LEVEL", value = String.valueOf(rebateLevel);
		Map<String, String> data = new HashMap<String, String>();
		data.put("REBATE_LEVEL", value);
		PlatformConfig config = new PlatformConfig(agentNo, group, key, value, "启/禁用平台代理返点");
		boolean result = getPlatformConfigDao().upsert(config);
		if (result) {
			getRedisService().setRebateLevel(agentNo, config);
			// 保存操作日志
			String adminLogContent = "启/禁用平台代理返点：" + value;
			ThreadLocalUtil.setAdminLogContent(adminLogContent);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}
	
}
