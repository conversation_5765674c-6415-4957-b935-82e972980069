package ph.yckj.admin.web.ctrl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.service.GameWarnRecordService;
import ph.yckj.admin.vo.game.GameWarnAutoDisableRecordVo;
import ph.yckj.admin.vo.game.GameWarnRecordVo;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;
import sy.hoodle.base.common.entity.GameWarnAutoDisableRecord;
import sy.hoodle.base.common.entity.GameWarnRecord;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 亏损量预警
 */
@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.System.PATH)
public class GameWarnRecordController extends SuperController {

    @Autowired
    private GameWarnRecordService gameWarnRecordService;

    /**
     * 查询亏损量预警配置信息
     * @param request
     * @param page
     * @param size
     * @return
     */
    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.GameLottery.SEARCH_EARLY_WARN_RECORD)
    @ResponseBody
    public WebJson SEARCH_EARLY_WARN_RECORD(HttpServletRequest request,
                                            @RequestParam(name = "status", required = false) String status,
                                            @RequestParam(name = "page", defaultValue = "0") int page,
                                            @RequestParam(name = "size", defaultValue = "10") int size) {
        WebJson webJson = new WebJson();
        try {
            Map<String, Object> data = gameWarnRecordService.searchRecords(page, size, status);
            webJson.setData(data);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Throwable t) {
            setFail(webJson);
            log.error(t.getMessage(), t);
        }
        return webJson;
    }

    /**
     * 查询亏损量预警配置信息
     * @param request
     * @return
     */
    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.GameLottery.CLEAR_EARLY_WARN_RECORD_COUNT)
    public WebJson CLEAR_EARLY_WARN_RECORD_COUNT(HttpServletRequest request) {
        WebJson webJson = new WebJson();
        try {
            webJson.setData(gameWarnRecordService.totalCount());
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Throwable t) {
            setFail(webJson);
            log.error(t.getMessage(), t);
        }
        return webJson;
    }


    /**
     * 清除预警记录
     */
    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.GameLottery.CLEAR_EARLY_WARN)
    @ResponseBody
    public WebJson CLEAR_EARLY_WARN(HttpServletRequest request, @RequestParam(name = "id") Long id) {
        WebJson webJson = new WebJson();
        try {
            boolean result = gameWarnRecordService.clearWarn(id);
            if (result) {
                setSuccess(webJson);
            } else {
                setFail(webJson);
            }
        } catch (ServiceException e) {
            setError(webJson, e);
        } catch (Throwable t) {
            setFail(webJson);
            log.error(t.getMessage(), t);
        }
        return webJson;
    }

    /**
     * 查询自动禁用记录列表
     */
    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.GameLottery.SEARCH_EARLY_WARN_AUTO_DISABLE_RECORD)
    @ResponseBody
    public WebJson SEARCH_EARLY_WARN_AUTO_DISABLE_RECORD(HttpServletRequest request,
                                                        @RequestParam(name = "page", defaultValue = "0") int page,
                                                        @RequestParam(name = "size", defaultValue = "10") int size) {
        WebJson webJson = new WebJson();
        try {
            Map<String, Object> data = gameWarnRecordService.searchAutoDisableRecords(page, size);
            webJson.setData(data);
            setSuccess(webJson);
        } catch (ServiceException e) {
            setError(webJson, e);
        } catch (Throwable t) {
            setFail(webJson);
            log.error(t.getMessage(), t);
        }
        return webJson;
    }

}
