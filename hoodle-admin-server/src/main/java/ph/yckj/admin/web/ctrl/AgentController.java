package ph.yckj.admin.web.ctrl;

import lombok.extern.slf4j.Slf4j;
import myutil.*;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.entity.AdminLog;
import ph.yckj.admin.entity.AdminRole;
import ph.yckj.admin.enums.AgentPlayerStatus;
import ph.yckj.admin.enums.AgentPlayerType;
import ph.yckj.admin.enums.AgentType;
import ph.yckj.admin.enums.CommonStatus;
import ph.yckj.admin.enums.PayTypeEnum;
import ph.yckj.admin.job.AdminLogJob;
import ph.yckj.admin.service.AgentPlayerService;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.*;
import ph.yckj.admin.vo.admin.AdminAccountVO;
import ph.yckj.admin.vo.agent.*;
import ph.yckj.admin.vo.game.AgentGameLotteryInfoVo;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.service.S3VideoUploadService;
import ph.yckj.common.util.RedisKey;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.StaticUtils;
import ph.yckj.common.util.WebJson;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;
import sy.hoodle.tool.redis.lock.RedisLock;
import sy.hoodle.tool.redis.utils.ProRedisLockUtil;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.Agent.PATH)
public class AgentController extends SuperController {

	@Autowired
	private S3VideoUploadService s3VideoUploadService;

	@Autowired
	private AgentPlayerService agentPlayerService;

	@RequestMapping(Route.Agent.MODIFY_APPLY_UP_DED_TRANSFER)
	public WebJson MODIFY_APPLY_UP_DED_TRANSFER(HttpServletRequest request,
			@RequestParam("id") long id,
			Boolean applyUpDedTransfer) {

		AdminAccount adminAccount = getSessionUser(request);
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}

			boolean result = getAgentPlayerInfoDao().updateApplyUpDedTransfer(player.getPlayerId(), applyUpDedTransfer);
			if (result) {
				// 保存操作日志
				String content = "用户：" + player.getPlayerName() + "，上级给下级下分权限："
						+ (applyUpDedTransfer ? "开启" : "关闭" + ",操作员:" + adminAccount.getUsername());
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@Autowired
	private ProRedisLockUtil proRedisLockUtil;

	@RequestMapping(Route.Agent.LIST_AGENT)
	public WebJson LIST_AGENT(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			List<Criterion> criterions = new ArrayList<Criterion>();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				criterions.add(Restrictions.eq("agentNo", defaultAgentNo));
			}
			criterions.add(Restrictions.eq("agentStatus", 0));
			List<AgentPlatformVo> list = new ArrayList<AgentPlatformVo>();
			int totalCount = getAgentInfoDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.asc("agentId"));
				List<AgentInfo> resultList = getAgentInfoDao().find(criterions, orders, 0, totalCount);
				for (AgentInfo agent : resultList) {
					list.add(new AgentPlatformVo(agent));
				}
			}
			webJson.setData(list);
			setSuccess(webJson);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error: " + error);
		}
		return webJson;
	}

	@RequestMapping(Route.Agent.LIST_THIRD_AGENT)
	public WebJson LIST_THIRD_AGENT(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String agentNo = adminAccount.getAgentNo();
			List<Criterion> criterions = new ArrayList<Criterion>();
			criterions.add(Restrictions.eq("agentType", AgentType.THIRD_PART.getCode()));
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			criterions.add(Restrictions.eq("agentStatus", 0));
			List<AgentPlatformVo> list = new ArrayList<AgentPlatformVo>();
			int totalCount = getAgentInfoDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.asc("agentId"));
				List<AgentInfo> resultList = getAgentInfoDao().find(criterions, orders, 0, totalCount);
				for (AgentInfo agent : resultList) {
					list.add(new AgentPlatformVo(agent));
				}
			}
			webJson.setData(list);
			setSuccess(webJson);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error: " + error);
		}
		return webJson;
	}

	@RequestMapping(Route.Agent.LIST_PLATFORM_AGENT)
	public WebJson LIST_PLATFORM_AGENT(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String agentNo = adminAccount.getAgentNo();
			List<Criterion> criterions = new ArrayList<Criterion>();
			criterions.add(Restrictions.eq("agentType", AgentType.PLATFORM.getCode()));
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			} else {
				AgentInfo agent = getAgent(request);
				if (agent != null) {
					criterions.add(Restrictions.eq("agentNo", agent.getAgentNo()));
				}
			}
			criterions.add(Restrictions.eq("agentStatus", 0));
			List<AgentPlatformVo> list = new ArrayList<AgentPlatformVo>();
			int totalCount = getAgentInfoDao().totalCount(criterions);
			if (totalCount > 0) {
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.asc("agentId"));
				List<AgentInfo> resultList = getAgentInfoDao().find(criterions, orders, 0, totalCount);
				for (AgentInfo agent : resultList) {
					list.add(new AgentPlatformVo(agent));
				}
			}
			webJson.setData(list);
			setSuccess(webJson);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error: " + error);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.ADD_AGENT)
	public WebJson ADD_AGENT(HttpServletRequest request, @RequestParam(name = "agentName") String agentName,
							 @RequestParam(name = "agentType") Integer agentType,
							 @RequestParam(name = "agentLevelCode", defaultValue = "0") Integer agentLevelCode,
							 @RequestParam(name = "serviceCharge", required = false) BigDecimal serviceCharge,
							 @RequestParam(name = "agentSettleMode", defaultValue = "1") int agentSettleMode,
							 @RequestParam(name = "language", defaultValue = "1") Integer language,
							 @RequestParam(name = "currency", defaultValue = "1") Integer currency,
							 @RequestParam(name = "branching", defaultValue = "0", required = false) Integer branching,
							 @RequestParam(name = "agentRatePercent", defaultValue = "0.00") double agentRatePercent,
							 @RequestParam(name = "agentPumpPercent", defaultValue = "0.00") double agentPumpPercent,
							 @RequestParam(name = "rewardRatio", defaultValue = "0.00") double rewardRatio,
							 @RequestParam(name = "thirdGameRatio", defaultValue = "0.00") double thirdGameRatio,
							 @RequestParam(name = "agentUsername", defaultValue = "") String agentUsername,
							 @RequestParam(name = "agentPassword", defaultValue = "") String agentPassword) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			AgentInfo entity = getAgentInfoDao().getByAgentName(agentName);
			if (null != entity) {
				throw newException("-1", "该代理商已存在！");
			}
			Integer agentPid = 0;
			String agentPids = "";
			if (AgentType.THIRD_PART.getCode() == agentType) {
				if (null == agentLevelCode) {
					throw newException("-1", "代理商层级不能为空！");
				}
				AgentInfo parentAgent = null;
				if (!StringUtils.isEmpty(defaultAgentNo)) {
					parentAgent = getAgentInfoDao().getByAgentNo(defaultAgentNo);
					if (0 == parentAgent.getBranching()) {
						throw newException("-1", "上级不允许分线！");
					}
					agentPid = parentAgent.getAgentId();
					agentPids = StringIdUtils.addFirst(parentAgent.getAgentPids(), parentAgent.getAgentId());
				}
			}

			AgentInfo agent = getAgentService().addAgentInfo(adminAccount, agentType, agentName, agentLevelCode,
					agentPid, agentPids, agentSettleMode, agentRatePercent, agentPumpPercent, serviceCharge, language,
					currency, branching, agentUsername.trim(), agentPassword,rewardRatio,thirdGameRatio);
			if (agent != null) {
				// 同步服务地区
				getAgentService().syncCountry(agent.getAgentNo());

				// 给代理商添加百家乐平台
				getGameSimulationPlatformTypeService().syncPlatform(agent);
				// 初始化系统配置
				getPlatformService().supplementPlatformConfig(agent);
				// 初始化用户类型
				getAgentPlayerAccountTypeService().initAccountType(agent);

				if (AgentType.THIRD_PART.getCode() == agent.getAgentType()) {
					// 代理层级权限获取
					AdminRole adminRole = getServiceUtils().getAgentLevelCode(agentLevelCode);
					boolean result = getAdminService().addAgentAccount(adminAccount, agentUsername.trim(),
							agentPassword.trim(), adminRole.getId(), agent.getAgentNo(), agent.getAgentName(),
							agent.getAgentLevelCode());
					if (result) {
						// 保存操作日志
						String button = (AgentType.THIRD_PART.getCode() == agentType) ? "三方" : "平台";
						String content = "添加" + button + "代理商：" + agentName;
						// 设置 ThreadLocal 变量
						ThreadLocalUtil.setAdminLogContent(content);
						setSuccess(webJson);
					} else {
						setFail(webJson);
					}
				} else {
					setSuccess(webJson);
				}
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.AGENT_SYNC_THIRD_PLATFORM)
	public WebJson AGENT_SYNC_THIRD_PLATFORM(HttpServletRequest request,
											 @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		try {
			AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
			if (agent == null) {
				throw newException("-1", "该代理商不存在！");
			}

			// 给代理商添加百家乐平台
			getGameSimulationPlatformTypeService().syncPlatform(agent);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.AGENT_SUPPLEMENT_PLATFORM_CONFIG)
	public WebJson AGENT_SUPPLEMENT_PLATFORM_CONFIG(HttpServletRequest request,
													@RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		try {
			AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
			if (agent == null) {
				throw newException("-1", "该代理商不存在！");
			}

			// 给代理商补全配置
			getPlatformService().supplementPlatformConfig(agent);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			log.error("error:" + ErrorUtils.getStackTraceInfo(e));
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.UPDATE_BAN_STATUS)
	public WebJson UPDATE_BAN_STATUS(@RequestParam(name = "gameId", required = true) long gameId,
									 @RequestParam(name = "banStatus", required = true) int banStatus) {
		WebJson webJson = new WebJson();
		try {
			if(AgentGameLotteryInfo.BAN_STATUS_ENABLED == banStatus) {
				// 代理商启用彩种时，判断总台彩种是否处理启用状态
				AgentGameLotteryInfo agentGameLotteryInfo = getAgentGameLotteryInfoDao().getById(gameId);
				if(agentGameLotteryInfo == null) {
					throw newException("500-128");
				}
				GameLotteryInfo gamelotteryInfo = getGameLotteryInfoDao().getByLottery(
						agentGameLotteryInfo.getLottery());
				if(gamelotteryInfo == null) {
					throw newException("500-128");
				}
				if(GameLotteryInfo.GAME_STATUS_NORMAL != gamelotteryInfo.getGameStatus()) {
					throw newException("500-129");
				}
			}
			boolean result = getAgentGameLotteryInfoDao().updateStatus(gameId, banStatus);

			if(result) {
				// 更新游戏缓存版本
				getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
				// 保存操作日志
				String content = "代理商游戏直播间id：" + gameId + "，状态：" + banStatus;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.UPDATE_RECOMMEND)
	public WebJson UPDATE_RECOMMEND(@RequestParam(name = "agentNo") String agentNo,
									@RequestParam(name = "lottery") String lottery, @RequestParam(name = "recommend") int recommend) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getAgentGameLotteryInfoDao().updateRecommend(agentNo, lottery, recommend);

			if (result) {
				// 更新游戏缓存版本
				getSystemService().updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_INFO);
				// 保存操作日志
				String content = "代理商游戏直播间agentNo：" + agentNo + "，lottery：" + lottery + "，推荐：" + recommend;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.LIST_AGENT_GAME_LOTTERY_INFO)
	public WebJson LIST_AGENT_GAME_LOTTERY_INFO(HttpServletRequest request,
			@RequestParam(name = "agentNo", required = false) String agentNo,
			@RequestParam(name = "agentName", required = false) String agentName,
			@RequestParam(name = "gameTypeCode", required = false) String gameTypeCode,
			@RequestParam(name = "gameGroupCode", required = false) String gameGroupCode,
			@RequestParam(name = "banStatus", required = false) Integer banStatus,
			@RequestParam(name = "agentType", required = false) Integer agentType,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size;
			List<Criterion> criterions = new ArrayList<>();

			String agentNumber = "";
			AgentInfo agent = getAgent(request);
			if (null == agent) {
				log.info("AgentInfo is null");
			} else {
                log.info("AgentInfo: {}", agent);
			}

			if (null != agent) {
				agentNumber = agent.getAgentNo();
                log.info("agentNumber from AgentInfo: {}", agentNumber);
			}

			if (!StringUtils.isEmpty(agentNumber)) {
				criterions.add(Restrictions.eq("agentNo", agentNumber));
			}
			if (!StringUtils.isEmpty(gameTypeCode)) {
				criterions.add(Restrictions.eq("gameTypeCode", gameTypeCode));
			}
			if (!StringUtils.isEmpty(gameGroupCode)) {
				criterions.add(Restrictions.eq("gameGroupCode", gameGroupCode));
			}
			if (null != banStatus) {
				criterions.add(Restrictions.eq("banStatus", banStatus));
			}
			if (null != agentType) {
				criterions.add(Restrictions.eq("agentType", agentType));
			}
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}

			criterions.add(Restrictions.eq("isDelete", 0));
			List<Order> orders = new ArrayList<>();
			orders.add(Order.asc("agentNo"));

			int totalCount = getAgentGameLotteryInfoDao().totalCount(criterions);
			List<AgentGameLotteryInfoVo> list = new ArrayList<>();
			if (totalCount > 0) {
				List<AgentGameLotteryInfo> resultList = getAgentGameLotteryInfoDao().find(criterions, orders,
						firstResult, size);
				for (AgentGameLotteryInfo entity : resultList) {
					GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(entity.getGameTypeCode());
					GameLotteryGroup lotteryGroup = getRedisService().getGameLotteryGroup(entity.getGameGroupCode());
					list.add(new AgentGameLotteryInfoVo(entity, gameLotteryType, lotteryGroup));
				}
			}

			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}

		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.UPDATE_AGENT)
	public WebJson UPDATE_AGENT(@RequestParam(name = "agentNo") String agentNo,
								@RequestParam(name = "serviceCharge", required = false) BigDecimal serviceCharge,
								@RequestParam(name = "language", defaultValue = "1") Integer language,
								@RequestParam(name = "currency", defaultValue = "1") Integer currency,
								@RequestParam(name = "agentSettleMode", defaultValue = "0") int agentSettleMode,
								@RequestParam(name = "agentRatePercent", defaultValue = "0.00") double agentRatePercent,
								@RequestParam(name = "agentPumpPercent", defaultValue = "0.00") double agentPumpPercent,
								@RequestParam(name = "rewardRatio", defaultValue = "0.00") double rewardRatio,
								@RequestParam(name = "thirdGameRatio", defaultValue = "0.00") double thirdGameRatio) {
		WebJson webJson = new WebJson();
		AgentInfo entity = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == entity) {
			throw newException("该代理商不存在。");
		}
		boolean result = getAgentService().updateAgentInfo(agentNo, agentSettleMode, agentRatePercent, agentPumpPercent,
				serviceCharge, language, currency,rewardRatio,thirdGameRatio);
		if (result) {
			// 保存操作日志
			String button = (AgentType.THIRD_PART.getCode() == entity.getAgentType().intValue()) ? "三方" : "平台";
			String mode = (1 == agentSettleMode) ? "账单模式" : "预充值模式";
			String content = "编辑" + button + "代理商：" + entity.getAgentName() + "，结算模式：（" + agentSettleMode + "）" + mode
					+ "，抽成：" + agentPumpPercent + "%，费率" + agentRatePercent + "%";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.BINDING_UID)
	public WebJson BINDING_UID(@RequestParam(name = "agentNo") String agentNo, @RequestParam(name = "agentUid") String agentUid) {
		WebJson webJson = new WebJson();
		AgentInfo entity = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == entity) {
			throw newException("该代理商不存在。");
		}
		boolean result = getAgentInfoDao().bindingUid(agentNo, agentUid);
		if (result) {
			// 保存操作日志
			String content = agentNo + "绑定代理商UID:" + agentUid;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_AGENT)
	public WebJson SEARCH_AGENT(HttpServletRequest request,
								@RequestParam(name = "agentNo", required = false) String agentNo,
								@RequestParam(name = "agentName", required = false) String agentName,
								@RequestParam(name = "agentType", required = false) Integer agentType,
								@RequestParam(name = "agentLevelCode", required = false) Integer agentLevelCode,
								@RequestParam(name = "parentAgentName", required = false) String parentAgentName,
								@RequestParam(name = "currency", required = false) Integer currency,
								@RequestParam(name = "agentStatus", required = false) Integer agentStatus,
								@RequestParam(name = "agentSettleMode", required = false) Integer agentSettleMode,
								@RequestParam(name = "page", defaultValue = "0") int page,
								@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			boolean doSearch = true;
			int firstResult = page * size, maxResults = size;
			List<Criterion> criterions = new ArrayList<Criterion>();

			// 空值检查 - 防止NullPointerException
			if (agentType != null && AgentType.THIRD_PART.getCode() == agentType
					&& !StringUtils.isEmpty(parentAgentName)) {
				AgentInfo parentAgent = getAgentInfoDao().getByAgentName(parentAgentName);
				if (null == parentAgent) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("agentPid", parentAgent.getAgentId()));
				}
			}

			if (doSearch) {
				if (!StringUtils.isEmpty(agentNo)) {
					criterions.add(Restrictions.eq("agentNo", agentNo));
				}
				if (agentType != null) {
					criterions.add(Restrictions.eq("agentType", agentType));
				}
				if (!StringUtils.isEmpty(agentName)) {
					criterions.add(Restrictions.eq("agentName", agentName));
				}
				if (null != agentLevelCode) {
					criterions.add(Restrictions.eq("agentLevelCode", agentLevelCode));
				}
				if (agentStatus != null) {
					criterions.add(Restrictions.eq("agentStatus", agentStatus));
				}
				if (agentSettleMode != null) {
					criterions.add(Restrictions.eq("agentSettleMode", agentSettleMode));
				}
				if (currency != null) {
					criterions.add(Restrictions.eq("currency", currency));
				}
				criterions.add(Restrictions.eq("isDelete", 0));
				List<Order> orders = new ArrayList<Order>();
				orders.add(Order.desc("agentId"));
				int totalCount = getAgentInfoDao().totalCount(criterions);
				List<AgentInfoVo> list = new ArrayList<AgentInfoVo>();
				if (totalCount > 0) {
					List<AgentInfo> resultList = getAgentInfoDao().find(criterions, orders, firstResult, maxResults);
					if (resultList != null) {
						for (AgentInfo entity : resultList) {
							if (entity != null && entity.getAgentType() != null
									&& AgentType.PLATFORM.getCode() == entity.getAgentType()) {
								// 遍历查询每个代理下面的玩家的可用余额
								if (entity.getAgentNo() != null) {
									BigDecimal agentAvailableBalance = getAgentPlayerInfoDao()
											.totalAvailableBalance(entity.getAgentNo());
									if (agentAvailableBalance != null) {
										entity.setAgentAvailableBalance(agentAvailableBalance);
									} else {
										entity.setAgentAvailableBalance(BigDecimal.ZERO);
									}
								} else {
									entity.setAgentAvailableBalance(BigDecimal.ZERO);
								}
								if (StringUtils.isEmpty(entity.getServiceCharge())) {
									entity.setServiceCharge(BigDecimal.ZERO);
								}
							}

							AgentInfoVo vo = new AgentInfoVo(entity);
							if (vo != null && vo.getAgentPid() > 0) {
								AgentInfo parentAgent = getAgentInfoDao().getById(vo.getAgentPid());
								if (null != parentAgent) {
									vo.setParentAgentName(parentAgent.getAgentName());
								}
							}
							list.add(vo);
						}
					}
				}
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("totalCount", totalCount);
				data.put("list", list);
				webJson.setData(data);
				setSuccess(webJson);
			} else {
				noPageData(webJson);
			}
		} catch (Exception e) {
			log.error("SEARCH_AGENT error: ", e);
			webJson.setMessage("查询代理商失败: " + e.getMessage());
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UPDATE_AGENT_STATUS)
	public WebJson UPDATE_AGENT_STATUS(@RequestParam(name = "agentNo") String agentNo,
									   @RequestParam(name = "agentStatus") int agentStatus) {
		WebJson webJson = new WebJson();
		AgentInfo entity = getAgentInfoDao().getByAgentNo(agentNo);
		if (entity == null) {
			throw newException("100-01");
		}
		boolean result = getAgentService().updateAgentStatus(entity.getAgentId(), agentStatus);
		if (result && AgentInfo.AGENT_STATUS_FORBIDDEN == agentStatus) {
			// 禁用代理商时，清空登录用户的SessionId，强制退出
			List<AgentPlayerInfo> players = getAgentPlayerInfoDao().listOnline(agentNo);
			if (!CollectionUtils.isEmpty(players)) {
				players.stream().forEach(t -> {
					getRedisService().delAgentPlayerInfo(t.getPlayerId());
				});
				getAgentPlayerInfoDao().setOfflineByAgentNo(agentNo);
			}
		}
		if (result) {
			// 保存操作日志
			String content = "代理商商户号：" + agentNo + "，状态：" + agentStatus;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MANUAL_AGENT_ADD_BALANCE)
	public WebJson MANUAL_AGENT_ADD_BALANCE(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
											@RequestParam(name = "amount") BigDecimal amount, @RequestParam(name = "password") String password,
											@RequestParam(name = "remarks", required = false) String remarks) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
			throw newException("1000-01");
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (agent == null) {
			throw newException("100-01");
		}
		boolean result = getAgentService().maualAgentAddBalance(adminAccount, agent, amount, remarks);
		if (result) {
			// 保存操作日志
			String content = "代理商商户号：" + agentNo + "，充值金额：" + amount + "，备注：" + remarks;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MANUAL_AGENT_RECHARGE)
	public WebJson MANUAL_AGENT_RECHARGE(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
										 @RequestParam(name = "currency") Integer currency, @RequestParam(name = "rate") BigDecimal rate,
										 @RequestParam(name = "actualAmount") BigDecimal actualAmount,
										 @RequestParam(name = "postedAmount") BigDecimal postedAmount,
										 @RequestParam(name = "amount") BigDecimal amount, @RequestParam(name = "password") String password,
										 @RequestParam(name = "remarks", required = false) String remarks) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
			throw newException("1000-01");
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (agent == null) {
			throw newException("100-01");
		}
		if (agent.getAgentPid() > 0) {
			AgentInfo parentAgent = getAgentInfoDao().getById(agent.getAgentPid());
			if (null != parentAgent) {
				if (amount.add(agent.getAgentAvailableBalance())
						.compareTo(parentAgent.getAgentAvailableBalance()) > 0) {
					throw newException("-1", "上分额度不能大于上级可用额度");
				}
			}
		}
		boolean result = getAgentService().maualAgentRecharge(adminAccount, agent, amount, rate, currency, remarks,
				actualAmount, postedAmount);
		if (result) {
			// 保存操作日志
			String content = "代理商商户号：" + agentNo + "，充值金额：" + amount + "，备注：" + remarks;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MANUAL_AGENT_WITHDRAW)
	public WebJson MANUAL_AGENT_WITHDRAW(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
										 @RequestParam(name = "amount") BigDecimal amount, @RequestParam(name = "password") String password,
										 @RequestParam(name = "remarks", required = false) String remarks) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
			throw newException("1000-01");
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (agent == null) {
			throw newException("100-01");
		}
		boolean result = getAgentService().maualAgentWithdraw(adminAccount, agent, amount.negate(), remarks);
		if (result) {
			// 保存操作日志
			String content = "代理商商户号：" + agentNo + "，提现金额：" + amount + "，备注：" + remarks;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.GET_AGENT_SECRETKEY)
	public WebJson GET_AGENT_SECRETKEY(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		AgentInfo entity = getAgentInfoDao().getByAgentNo(agentNo);
		if (entity == null) {
			throw newException("100-01");
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		AgentSecretVo vo = new AgentSecretVo(agent);
		if (null != agent) {
			// 保存操作日志
			String content = "代理商商户号：" + agentNo + "对接秘钥信息已被(" + adminAccount.getUsername() + "->"
					+ adminAccount.getAgentNo() + ")查看";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(vo);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UPDATE_AGENT_WHITELIST)
	public WebJson MANUAL_AGENT_WITHDRAW(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
										 @RequestParam(name = "agentApiWhiteIps") String agentApiWhiteIps,
										 @RequestParam(name = "password") String password) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);

		if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
			throw newException("1000-01");
		}
		AgentInfo entity = getAgentInfoDao().getByAgentNo(agentNo);
		if (entity == null) {
			throw newException("100-01");
		}
		boolean result = getAgentService().updateAgentWhiteIps(entity.getAgentId(), agentApiWhiteIps);
		if (result) {
			// 保存操作日志
			String content = "代理商商户号：" + agentNo + "，IP白名单：" + agentApiWhiteIps;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.LIST_PLATFORM_LINE)
	public WebJson LIST_PLATFORM_LINE(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo) {
		WebJson webJson = new WebJson();
		AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(agentNo);
		if (agentInfo == null) {
			throw new ServiceException("1", "代理商不存在");
		}
		List<Criterion> criterions = new ArrayList<Criterion>();
		criterions.add(Restrictions.eq("agentNo", agentNo));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		List<PlatformLine> platformLines = getPlatformLineDao().find(criterions, orders);
		List<PlatformLineVo> collect = platformLines.stream().map(PlatformLineVo::new).collect(Collectors.toList());
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("list", collect);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.SAVE_PLATFORM_LINE)
	public WebJson SAVE_PLATFORM_LINE(@RequestParam(name = "agentNo") String agentNo,
									  @RequestBody List<PlatformLineVo> lines) {
		WebJson webJson = new WebJson();
		try {
			// 校验代理商是否存在
			AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
			if (agent == null) {
				throw new ServiceException("1", "代理商不存在");
			}
			boolean result = getAgentService().savePlateFormLine(agent, lines);
			if (result) {
				// 保存操作日志
				String content = "代理商商户号：" + agentNo + "，线路列表：" + lines;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_AGENT_ACCOUNT)
	public WebJson ADD_AGENT_ACCOUNT(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
									 @RequestParam(name = "agentType") String agentType, @RequestParam(name = "username") String username) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		AgentInfo byAgentNo = getAgentInfoDao().getByAgentNo(agentNo);
		if (byAgentNo == null) {
			throw new ServiceException("1", "代理商不存在");
		}

		if (!byAgentNo.getAgentType()
				.equals(agentType.equals("AGENT_T") ? AgentType.THIRD_PART.getCode() : AgentType.PLATFORM.getCode())) {
			throw new ServiceException("1", "代理商类型不正确");
		}

		// 代理商权限信息
		AdminRole adminRole = new AdminRole();
		if (AgentType.THIRD_PART.getCode() == byAgentNo.getAgentType()) {
			adminRole = getServiceUtils().getAgentLevelCode(byAgentNo.getAgentLevelCode());
		} else {
			adminRole = getAdminRoleDao().getByDefaultAccess(agentType);
		}
		boolean result = getAdminService().addAgentAccount(adminAccount, username, AdminAccount.DEFAULT_PASSWORD,
				adminRole.getId(), agentNo, byAgentNo.getAgentName(), byAgentNo.getAgentLevelCode());
		if (result) {
			// 保存操作日志
			String button = (agentType.equals("AGENT_T")) ? "三方" : "平台";
			String content = "添加" + button + "代理商后台账号：" + username + "，代理商商户号：" + agentNo + "，角色ID："
					+ adminRole.getId();
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_AGENT_ACCOUNT)
	public WebJson SEARCH_AGENT_ACCOUNT(HttpServletRequest request,
										@RequestParam(name = "agentNo", required = false) String agentNo,
										@RequestParam(name = "agentName", required = false) String agentName,
										@RequestParam(name = "username", required = false) String username,
										@RequestParam(name = "agentType", required = false) String agentType,
										@RequestParam(name = "status", required = false) Integer status,
										@RequestParam(name = "roleId", required = false) Integer roleId,
										@RequestParam(name = "agentId", required = false) String agentId,
										@RequestParam(name = "page", defaultValue = "0") int page,
										@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);

		int firstResult = page * size, maxResults = size;
		List<Criterion> criterions = new ArrayList<Criterion>();
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo agent = getAgentInfoDao().getByAgentName(agentName);
			if (null == agent) {
				noPageData(webJson);
				return webJson;
			}
			if (!agent.getAgentNo().equals(agentNo)) {
				if (StringUtils.isEmpty(agentNo)) {
					criterions.add(Restrictions.eq("agentNo", agent.getAgentNo()));
				}
			}
		}

		int id = adminAccount.getId();
		// 判断当前登录用户是否是管理员 或者代理商 直接取id去查询
		if (getAdminUtils().isAdmin(adminAccount)) {
			id = adminAccount.getId();
		} else if (getAdminUtils().isAgent(adminAccount)) {
			id = adminAccount.getId();
		} else { // 子账号
			id = adminAccount.getPid();
		}
		criterions.add(Restrictions.like("pids", "[" + id + "]", MatchMode.ANYWHERE));
		if (!StringUtils.isEmpty(username)) {
			criterions.add(Restrictions.eq("username", username));
		}

		if (status != null) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (roleId != null) {
			criterions.add(Restrictions.eq("roleId", roleId));
		}
		// 代理商权限信息
		int agentTypeCode = agentType.equals("AGENT_T") ? AgentType.THIRD_PART.getCode() : AgentType.PLATFORM.getCode();
		if (AgentType.PLATFORM.getCode() == agentTypeCode) {
			AdminRole adminRole = getAdminRoleDao().getByDefaultAccess(agentType);
			criterions.add(Restrictions.eq("roleId", adminRole.getId()));
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		List<AdminAccount> resultList = getAdminAccountDao().find(criterions, orders, firstResult, maxResults);
		int totalCount = getAdminAccountDao().totalCount(criterions);
		List<AdminAccountVO> list = new ArrayList<AdminAccountVO>();
		for (AdminAccount tmpBean : resultList) {
			list.add(new AdminAccountVO(tmpBean, getDataFactory()));
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_AGENT_BILL)
	public WebJson SEARCH_AGENT_BILL(@RequestParam(name = "agentName", required = false) String agentName,
									 @RequestParam(name = "agentLevelCode", required = false) Integer agentLevelCode, // 代理商层级类型
									 @RequestParam(name = "billType", required = false) Integer billType,
									 @RequestParam(name = "agentBillNo", required = false) String agentBillNo,
									 @RequestParam(name = "sTime", required = false) String sTime,
									 @RequestParam(name = "eTime", required = false) String eTime,
									 @RequestParam(name = "page", defaultValue = "0") int page,
									 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();

		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (null != billType) {
			criterions.add(Restrictions.eq("billType", billType));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (!StringUtils.isEmpty(agentBillNo)) {
			criterions.add(Restrictions.eq("agentBillNo", agentBillNo));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));

		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));

		List<String> agentNos = new ArrayList<>();
		if (agentLevelCode != null) {
			List<AgentInfo> agents = getAgentInfoDao()
					.find(Arrays.asList(Restrictions.eq("agentLevelCode", agentLevelCode)), null, 0, 0);
			agentNos = agents.stream().map(AgentInfo::getAgentNo).collect(Collectors.toList());

			criterions.removeIf(c -> c.toString().contains("agentLevelCode"));
			if (!agentNos.isEmpty()) {
				criterions.add(Restrictions.in("agentNo", agentNos));
			}
		}

		int totalCount = getAgentAccountBillDao().totalCount(criterions);
		List<AgentAccountBillVo> list = new ArrayList<AgentAccountBillVo>();
		if (totalCount > 0) {
			List<AgentAccountBill> resultList = getAgentAccountBillDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentAccountBill entity : resultList) {
				AgentAccountBillVo vo = new AgentAccountBillVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_AGENT_WITHDRAW_RECORD)
	public WebJson SEARCH_AGENT_WITHDRAW_RECORD(@RequestParam(name = "agentNo", required = false) String agentNo,
												@RequestParam(name = "agentName", required = false) String agentName,
												@RequestParam(name = "withdrawType", required = false) Integer withdrawType,
												@RequestParam(name = "withdrawBillNo", required = false) String withdrawBillNo,
												@RequestParam(name = "sTime", required = false) String sTime,
												@RequestParam(name = "eTime", required = false) String eTime,
												@RequestParam(name = "page", defaultValue = "0") int page,
												@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();

		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (null != withdrawType) {
			criterions.add(Restrictions.eq("withdrawType", withdrawType));
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (!StringUtils.isEmpty(withdrawBillNo)) {
			criterions.add(Restrictions.eq("withdrawBillNo", withdrawBillNo));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));

		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getAgentWithdrawRecordDao().totalCount(criterions);
		List<AgentWithdrawRecordVo> list = new ArrayList<AgentWithdrawRecordVo>();
		if (totalCount > 0) {
			List<AgentWithdrawRecord> resultList = getAgentWithdrawRecordDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentWithdrawRecord entity : resultList) {
				AgentWithdrawRecordVo vo = new AgentWithdrawRecordVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_AGENT_RECHARGE_RECORD)
	public WebJson SEARCH_AGENT_RECHARGE_RECORD(@RequestParam(name = "agentNo", required = false) String agentNo,
												@RequestParam(name = "agentName", required = false) String agentName,
												@RequestParam(name = "agentLevelCode", required = false) Integer agentLevelCode,
												@RequestParam(name = "rechargeBillNo", required = false) String rechargeBillNo,
												@RequestParam(name = "sTime", required = false) String sTime,
												@RequestParam(name = "eTime", required = false) String eTime,
												@RequestParam(name = "page", defaultValue = "0") int page,
												@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();

		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(agentLevelCode)) {
			criterions.add(Restrictions.eq("agentLevelCode", agentLevelCode));
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (!StringUtils.isEmpty(rechargeBillNo)) {
			criterions.add(Restrictions.eq("rechargeBillNo", rechargeBillNo));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));

		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getAgentRechargeRecordDao().totalCount(criterions);
		List<AgentRechargeRecordVo> list = new ArrayList<AgentRechargeRecordVo>();
		if (totalCount > 0) {
			List<AgentRechargeRecord> resultList = getAgentRechargeRecordDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentRechargeRecord entity : resultList) {
				AgentRechargeRecordVo vo = new AgentRechargeRecordVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_ACCOUNT)
	public WebJson ADD_PLAYER_ACCOUNT(HttpServletRequest request,
									  @RequestParam(name = "agentNo", required = false) String agentNo,
									  @RequestParam(name = "playerType", required = false) Integer playerType,
									  @RequestParam(name = "parentPlayerName") String parentPlayerName,
									  @RequestParam(name = "playerName") String playerName, @RequestParam(name = "point") Double point) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}
		if (point == null) {
			point = 0D;
		}
		if (point < 0) {
			throw new ServiceException("1", "返点不能小于0");
		}
		Long pid = 0L;
		String pids = "";
		if (!StringUtils.isEmpty(parentPlayerName)) {
			AgentPlayerInfo byPlayerName = getAgentPlayerInfoDao().getByPlayerName(agentNo, parentPlayerName);
			if (byPlayerName == null) {
				throw new ServiceException("1", "上级代理输入的用户名有误");
			}
			if (AgentPlayerType.AGENT.getCode() != byPlayerName.getPlayerType()) {
				throw new ServiceException("1", "上级代理输入的用户名用户类型不是代理");
			}
			if (point > byPlayerName.getPoint()) {
				throw new ServiceException("1", "返点不能大于上级返点" + byPlayerName.getPoint());
			}
			if (point.doubleValue() == byPlayerName.getPoint()
					&& byPlayerName.getEqualLevel() != AgentPlayerInfo.EQUAL_LEVEL_ON) {
				throw new ServiceException("1", "返点不能大于上级返点" + byPlayerName.getPoint());
			}

			pid = byPlayerName.getPlayerId();
			pids = StringIdUtils.addFirst(byPlayerName.getPids(), byPlayerName.getPlayerId());
		}
		// 获取系统最高返点
		PlatformConfig sysPointConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY",
				"SYS_POINT");
		if (sysPointConfig == null) {
			throw newException("500-071");
		}
		if (point > Double.parseDouble(sysPointConfig.getValue())) {
			throw new ServiceException("1", "返点不能大于系统最高返点返点" + sysPointConfig.getValue());
		}

		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == agent) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw newException("100-01");
		}
		// 验证用户名
		boolean testPlayerName = StaticUtils.testUsername(playerName);
		if (!testPlayerName) {
			// ERR:用户名格式错误
			throw newException("101-02");
		}
		AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null != player) {
			log.warn(player.getPlayerName() + " 该玩家已存在。");
			throw newException("100-02");
		}

		String agentName = agent.getAgentName();
		String playerPassword = CipherUtils.encodePwd(AgentPlayerInfo.DEFAULT_LOGIN_PASSWORD);
		player = getAgentPlayerService().initPlayer(agentNo, agent.getAgentType(), agentName, pid, pids, playerName,
				playerPassword, playerType, point);
		boolean result = getAgentPlayerInfoDao().save(player);
		if (result) {
			// 创建契约
			getContractService().createContractPlayerConfig(player);
			getTCChatService().importUser(player.getAgentNo() + "_" + player.getPlayerName(), player.getPlayerNick());
			// 保存操作日志
			String button = AgentPlayerType.getByCode(playerType).getDesc();
			String content = "添加" + "代理商商户号：" + agentNo + "，玩家类型：" + button + "玩家用户名：" + playerName;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER)
	public WebJson SEARCH_PLAYER(HttpServletRequest request,
								 @RequestParam(name = "agentNo", required = false) String agentNo,
								 @RequestParam(name = "agentType", required = false) Integer agentType,
								 @RequestParam(name = "agentName", required = false) String agentName,
								 @RequestParam(name = "accountType", required = false) String accountType,
								 @RequestParam(name = "playerType", required = false) Integer playerType,
								 @RequestParam(name = "playerName", required = false) String playerName,
								 @RequestParam(name = "amountGt", required = false) BigDecimal amountGt,
								 @RequestParam(name = "amountLt", required = false) BigDecimal amountLt,
								 @RequestParam(name = "playerStatus", required = false) Integer playerStatus,
								 @RequestParam(name = "onlineStatus", required = false) Integer onlineStatus,
								 @RequestParam(name = "markersStatus", required = false) Integer markersStatus,
								 @RequestParam(name = "orderField", defaultValue = "") String orderField,
								 @RequestParam(name = "orderWay", defaultValue = "") String orderWay,
								 @RequestParam(name = "page", defaultValue = "0") int page,
								 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		List<Criterion> criterions = new ArrayList<>();
		if (!StringUtils.isEmpty(adminAccount.getAgentNo())) {
			criterions.add(Restrictions.eq("agentNo", adminAccount.getAgentNo()));
			agentNo = adminAccount.getAgentNo();
		} else {
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
		}
		if (!StringUtils.isEmpty(agentType)) {
			if (agentType == 2) {
				criterions.add(Restrictions.ne("agentType", 1));
			} else {
				criterions.add(Restrictions.eq("agentType", agentType));
			}
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (!StringUtils.isEmpty(accountType)) {
			criterions.add(Restrictions.eq("accountType", accountType));
		}
		if (playerType != null) {
			criterions.add(Restrictions.eq("playerType", playerType));
		}
		AgentPlayerInfo player = null;
		if (!StringUtils.isEmpty(playerName)) {
			if (!StringUtils.isEmpty(agentNo)) {
				player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
			}
			if (player != null) {
				criterions.add(Restrictions.eq("pid", player.getPlayerId()));
			} else {
				criterions.add(Restrictions.eq("playerName", playerName));
			}
		}
		if (null != amountGt) {
			criterions.add(Restrictions.gt("playerAvailableBalance", amountGt));
		}
		if (null != amountLt) {
			criterions.add(Restrictions.lt("playerAvailableBalance", amountLt));
		}
		if (playerStatus != null) {
			criterions.add(Restrictions.eq("playerStatus", playerStatus));
		}
		if (onlineStatus != null) {
			criterions.add(Restrictions.eq("onlineStatus", onlineStatus));
		}
		if (markersStatus != null) {
			criterions.add(Restrictions.eq("markersStatus", markersStatus));
		}

		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<>();
		if(!StringUtils.isEmpty(orderField)) {
			if("playerAvailableBalance".equals(orderField)) {
				if("desc".equals(orderWay)) {
					orders.add(Order.desc("playerAvailableBalance"));
				}else {
					orders.add(Order.asc("playerAvailableBalance"));
				}
			}
		}else {
			orders.add(Order.desc("playerId"));
		}
		int totalCount = getAgentPlayerInfoDao().totalCount(criterions);
		List<AgentPlayerInfoVo> list = new ArrayList<>();
		if (totalCount > 0) {
			List<AgentPlayerInfo> resultList = getAgentPlayerInfoDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentPlayerInfo entity : resultList) {
				AgentPlayerInfoVo vo = new AgentPlayerInfoVo(entity, getToolsService());

				AgentPlayerAccountType agentPlayerAccountType = getAgentPlayerAccountTypeDao().getByCode(vo.getAgentNo(),
						vo.getAccountType());
				if (agentPlayerAccountType == null) {
					vo.setAccountTypeName(vo.getAccountType());
				} else {
					vo.setAccountTypeName(agentPlayerAccountType.getName());
				}
				list.add(vo);
			}
		}
		if (player != null) {
			criterions.add(Restrictions.eq("playerType", AgentPlayerType.AGENT.getCode()));
			int totalAgentCount = getAgentPlayerInfoDao().totalCount(criterions);

			AgentPlayerInfoVo vo = new AgentPlayerInfoVo(player, getToolsService());
			AgentPlayerAccountType agentPlayerAccountType = getAgentPlayerAccountTypeDao().getByCode(vo.getAgentNo(),
					vo.getAccountType());
			if (agentPlayerAccountType == null) {
				vo.setAccountTypeName(vo.getAccountType());
			} else {
				vo.setAccountTypeName(agentPlayerAccountType.getName());
			}

			if (player.getPid() == null || player.getPid() == 0) {
				vo.setUpPlayerName("");
			} else {
				AgentPlayerInfo upPlayer = getAgentPlayerInfoDao().getByPlayerId(player.getPid());
				vo.setUpPlayerName(upPlayer.getPlayerName());
			}

			Map<String, Object> totalQueryInfo = new HashMap<>();
			totalQueryInfo.put("playerName", player.getPlayerName());
			totalQueryInfo.put("totalCount", totalCount);
			totalQueryInfo.put("totalAgentCount", totalAgentCount);
			totalQueryInfo.put("totalPlayerCount", totalCount - totalAgentCount);
			vo.setTotalQueryInfo(totalQueryInfo);

			totalCount += 1;
			list.add(0, vo);
		}

		Map<String, Object> data = new HashMap<>();
		data.put("list", list);
		data.put("totalCount", totalCount);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.GET_PLAYER_DETAILS)
	public WebJson GET_ACCOUNT_DETAILS(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(id);
		AgentPlayerInfoVo vo = new AgentPlayerInfoVo(player, getToolsService());
		AgentPlayerAccountType accountType = getAgentPlayerAccountTypeDao().getByCode(vo.getAgentNo(),
				vo.getAccountType());
		if (accountType == null) {
			vo.setAccountTypeName(vo.getAccountType());
		} else {
			vo.setAccountTypeName(accountType.getName());
		}
		vo.setUpPlayerNames(getAgentPlayerService().getUpPlayerNames(vo.getPids()));
		vo.setPlayerAvailableWithdrawBalance(getServiceUtils().getAvailableWithdrawBalance(player));
		webJson.setData(vo);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.GET_DEFAULT_ACCOUNT_PASSWORD)
	public WebJson GET_DEFAULT_ACCOUNT_PASSWORD(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		AgentPlayerInfo agentPlayerInfo = getAgentPlayerInfoDao().getById(id);
		webJson.setData(agentPlayerInfo.getPlayerPassword());
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MANUAL_PLAYER_RECHARGE)
	public WebJson MANUAL_PLAYER_RECHARGE(HttpServletRequest request, @RequestParam(name = "id") Long id,
										  @RequestParam(name = "opType", defaultValue = "1") Integer opType, // 1-充值未到账，2-活动补贴， 3-修正资金， 4-人工下分
										  @RequestParam(name = "amount") BigDecimal amount, @RequestParam(name = "password") String password,
										  @RequestParam(name = "remarks", required = false) String remarks) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);

		if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
			throw newException("1000-01");
		}
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(id);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		if (amount.compareTo(BigDecimal.ZERO) == 0) {
			throw newException("-1", "操作金额不能为0");
		}

		if(opType != 3  && amount.compareTo(BigDecimal.ZERO) < 0){
			throw newException("-1", "操作金额小于0");
		}

		boolean result = false;

		String mag = "";
		switch (opType){
			case 1:
				mag = "充值未到账";
				result = doRecharge(request, adminAccount, playerInfo, amount, remarks);
				break;
			case 2:
				mag = "活动补贴";
				result = doActivity(request, adminAccount, playerInfo, amount, remarks);
				break;
			case 3:
				mag = "修正资金";
				result = doFixBalance(request, adminAccount, playerInfo, amount, remarks);
				break;
			case 4:
				mag = "人工取款";
				result = doWithdraw(request, adminAccount, playerInfo, amount, remarks);
				break;
		}
		if (result) {
			// 保存操作日志
			String content = "玩家用户名：" + playerInfo.getPlayerName() + "执行" + mag + "，金额：" + amount + "，备注：" + remarks;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	private boolean doRecharge(HttpServletRequest request, AdminAccount adminAccount, AgentPlayerInfo playerInfo,
							   BigDecimal amount, String remarks){
		return getAgentService().manualPlayerRecharge(request, adminAccount, playerInfo, amount, remarks);

	}

	private boolean doActivity(HttpServletRequest request, AdminAccount adminAccount, AgentPlayerInfo playerInfo,
							   BigDecimal amount, String remarks){
		return getAgentService().manualPlayerActivity(adminAccount, playerInfo, amount, remarks);


	}

	private boolean doFixBalance(HttpServletRequest request, AdminAccount adminAccount, AgentPlayerInfo playerInfo,
								 BigDecimal amount, String remarks){
		return getAgentService().manualFixBalance(adminAccount, playerInfo, amount, remarks);
	}


	private boolean doWithdraw(HttpServletRequest request, AdminAccount adminAccount, AgentPlayerInfo playerInfo,
							   BigDecimal amount, String remarks){

		if(playerInfo.getPlayerAvailableBalance().compareTo(amount) < 0 ){
			throw new ServiceException("1", "用户余额小于提款金额，无法提现");
		}
		BigDecimal optAmount = amount.negate();

		return getAgentService().manualPlayerRecharge(request, adminAccount, playerInfo, optAmount, remarks);

	}

	@CheckLogin
	@RequestMapping(Route.Agent.MANUAL_PLAYER_WITHDRAW)
	public WebJson MANUAL_PLAYER_WITHDRAW(HttpServletRequest request, @RequestParam(name = "id") Long id,
										  @RequestParam(name = "amount") BigDecimal amount, @RequestParam(name = "password") String password,
										  @RequestParam(name = "remarks", required = false) String remarks) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);

		if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
			throw newException("1000-01");
		}
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(id);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		boolean result = getAgentService().manualPlayerWithdraw(adminAccount, playerInfo, amount.negate(), remarks);
		if (result) {
			// 保存操作日志
			String content = "玩家用户名：" + playerInfo.getPlayerName() + "，管理员减金额：" + amount + "，备注：" + remarks;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UPDATE_PLAYER_STATUS)
	public WebJson UPDATE_PLAYER_STATUS(@RequestParam(name = "id") Long id, @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().enableOrDisableAgentPlayer(id, status);
		if (b) {
			// 保存操作日志
			String playerStatus = AgentPlayerStatus.getByCode(status).getDesc();
			String content = "玩家ID：" + id + "，账号状态：" + playerStatus;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}



	@CheckLogin
	@RequestMapping(Route.Agent.RESET_ACCOUNT_LOGIN_FAILED_NUM)
	public WebJson RESET_ACCOUNT_LOGIN_FAILED_NUM(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		boolean b = getAgentPlayerInfoDao().updateAgentPlayerInfoLoginFailedNum(id, 0);
		if (b) {
			// 保存操作日志
			String content = "玩家ID：" + id + " 重置登录失败次数";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.RESET_PAYMENT_PASSWORD_FAILED_NUM)
	public WebJson RESET_PAYMENT_PASSWORD_FAILED_NUM(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		boolean b = getAgentPlayerInfoDao().updateAgentPlayerInfoPaymentPasswordFailedNum(id, 0);
		if (b) {
			// 保存操作日志
			String content = "玩家ID：" + id + " 重置资金密码失败次数";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		setSuccess(webJson);
		return webJson;
	}


	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_LOGIN_PASSWORD)
	public WebJson MODIFY_LOGIN_PASSWORD(@RequestParam(name = "id") Long id,
										 @RequestParam(name = "password") String password) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().updatePassword(id, password);
		if (b) {
			// 保存操作日志
			String content = "给玩家ID：" + id + "，修改了登录密码";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_WITHDRAW_PASSWORD)
	public WebJson MODIFY_WITHDRAW_PASSWORD(@RequestParam(name = "id") Long id,
											@RequestParam(name = "password") String password) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().updateWithdrawPassword(id, password);
		if (b) {
			getRedisService().delAgentPlayerInfo(id);
			// 保存操作日志
			String content = "给玩家ID：" + id + "，修改了提现密码";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_WITHDRAW_NAME)
	public WebJson MODIFY_WITHDRAW_NAME(@RequestParam(name = "id") Long id, @RequestParam(name = "name") String name) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().updateWithdrawName(id, name);
		if (b) {
			// 保存操作日志
			String content = "玩家ID：" + id + "，修改了提现人：" + name;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.RESET_ACCOUNT_LOCK_TIME)
	public WebJson RESET_ACCOUNT_LOCK_TIME(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().resetAccountLockTime(id);
		if (b) {
			// 保存操作日志
			String content = "玩家ID：" + id + "，重置了锁定时间";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.CLEAR_ACCOUNT_WITHDRAW_LIMIT)
	public WebJson CLEAR_ACCOUNT_WITHDRAW_LIMIT(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(id);
		boolean b = getAgentService().clearWithdrawLimitBalance(player.getPlayerId());
		if (b) {
			// 保存操作日志
			String content = "代理商： " + player.getAgentName() + "(" + player.getAgentNo() + ")，玩家："
					+ player.getPlayerName() + "，清除了提现限制";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_GOOGLE_LOGIN)
	public WebJson MODIFY_GOOGLE_LOGIN(@RequestParam(name = "id") Long id, @RequestParam(name = "flag") int flag) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().updateAccountGoogleLogin(id, flag);
		if (b) {
			// 保存操作日志
			String desc = CommonStatus.getByCode(flag).getDesc();
			String content = "玩家ID：" + id + "，谷歌登录开关：" + desc;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.RESET_GOOGLE_BIND)
	public WebJson RESET_GOOGLE_BIND(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().resetAccountGoogleBind(id);
		if (b) {
			// 保存操作日志
			String content = "玩家ID：" + id + "，重置谷歌绑定状态";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.RESET_ACCOUNT_PASSWORD_ERROR_COUNT)
	public WebJson RESET_ACCOUNT_PASSWORD_ERROR_COUNT(@RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().resetAccountSecurity(id);
		if (b) {
			// 保存操作日志
			String content = "玩家ID：" + id + "，重置了账号错误次数";
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}

		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.PREPARE_MODIFY_POINT)
	public WebJson PREPARE_MODIFY_POINT(HttpServletRequest request, @RequestParam("id") long id) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}
			PointRangeVo data = getServiceUtils().getLotteryAdminFixedRange(player);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_POINT)
	public WebJson MODIFY_POINT(HttpServletRequest request, @RequestParam("id") long id,
								@RequestParam("point") double point) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}
			PointRangeVo rangeVo = getServiceUtils().getLotteryAdminFixedRange(player);
			if (point < rangeVo.getMinPoint() || point > rangeVo.getMaxPoint()) {
				throw newException("500-012");
			}

			boolean result = getAgentPlayerService().updatePoint(player, point);
			if (result) {
				// 保存操作日志
				String content = "修改会员：" + player.getPlayerName() + "，旧返点：" + player.getPoint() + "，新返点：" +
						point;
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_LINE_POINT)
	public WebJson MODIFY_LINE_POINT(HttpServletRequest request, @RequestParam("id") long id) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}
			boolean result = getAgentPlayerService().downLinePoint(player);
			if (result) {
				// 保存操作日志
				String content = "线路统一降点，用户：" + player.getPlayerName();
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.INCREASE_LINE_POINT)
	public WebJson INCREASE_LINE_POINT(HttpServletRequest request, @RequestParam("id") long id) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}

			boolean result = getAgentPlayerService().increaseLinePoint(player);
			if (result) {
				// 保存操作日志
				String content = "线路统一升点，用户：" + player.getPlayerName();
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_PLAYER_TYPE)
	public WebJson MODIFY_PLAYER_TYPE(HttpServletRequest request, @RequestParam long id, @RequestParam int playerType) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if(player == null) {
				throw newException("100-01");
			}
			if(playerType == player.getPlayerType()) {
				setSuccess(webJson);
				return webJson;
			}

			boolean result = getAgentPlayerService().updatePlayerType(player, playerType);
			if(result) {
				// 保存操作日志
				String content = "修改会员：" + player.getPlayerName() + "，用户类型：";
				if(playerType == AgentPlayerInfo.PLAYER_TYPE_AGENT) {
					content += "代理";
				}else {
					content += "玩家";
				}
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			}else {
				setFail(webJson);
			}
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_EQUAL_LEVEL)
	public WebJson MODIFY_EQUAL_LEVEL(HttpServletRequest request, @RequestParam("id") long id,
									  @RequestParam("equalLevel") int equalLevel) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}
			if (equalLevel != AgentPlayerInfo.EQUAL_LEVEL_ON) {
				equalLevel = AgentPlayerInfo.EQUAL_LEVEL_OFF;
			}

			boolean result = getAgentPlayerService().updateEqualLevel(player.getPlayerId(), equalLevel);
			if (result) {
				// 保存操作日志
				String content = "同级开号权限，用户：" + player.getPlayerName() + "，状态："
						+ (AgentPlayerInfo.EQUAL_LEVEL_ON == equalLevel ? "开启" : "关闭");
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MODIFY_ALLOW_TRANSFER)
	public WebJson MODIFY_ALLOW_TRANSFER(HttpServletRequest request, @RequestParam("id") long id,
										 @RequestParam("allowTransfer") int allowTransfer) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(id);
			if (player == null) {
				throw newException("100-01");
			}

			boolean result = getAgentPlayerService().updateAllowTransfer(player.getPlayerId(), allowTransfer);
			if (result) {
				// 保存操作日志
				String content = "用户：" + player.getPlayerName() + "，给下级转账权限：" +
						((AgentPlayerInfo.ALLOW_TRANSFER_ON == allowTransfer ||
								AgentPlayerInfo.ALLOW_TRANSFER_TO_DOWN == allowTransfer) ? "开启" : "关闭") +
						"，给上级转账权限：" + ((AgentPlayerInfo.ALLOW_TRANSFER_ON == allowTransfer ||
						AgentPlayerInfo.ALLOW_TRANSFER_TO_UP == allowTransfer) ? "开启" : "关闭");
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error(t.getMessage(), t);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UPDATE_PLAYER_MARKER_STATUS)
	public WebJson UPDATE_PLAYER_MARKER_STATUS(@RequestParam(name = "id") Long id,
											   @RequestParam(name = "status") int status) {
		WebJson webJson = new WebJson();
		boolean b = getAgentService().markAgentPlayer(id, status);
		if (b) {
			// 保存操作日志
			String desc = CommonStatus.getByCode(status).getDesc();
			String content = "玩家ID：" + id + "，账号标记状态：" + desc;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_BILL)
	public WebJson SEARCH_PLAYER_BILL(HttpServletRequest request,
									  @RequestParam(name = "agentNo", required = false) String agentNo,
									  @RequestParam(name = "agentType", required = false) Integer agentType,
									  @RequestParam(name = "agentName", required = false) String agentName,
									  @RequestParam(name = "playerName", required = false) String playerName,
									  @RequestParam(name = "billNo", required = false) String billNo,
									  @RequestParam(name = "billType", required = false) Integer billType,
									  @RequestParam(name = "sTime", required = false) String sTime,
									  @RequestParam(name = "eTime", required = false) String eTime,
									  @RequestParam(name = "minAmount", required = false) BigDecimal minAmount,
									  @RequestParam(name = "maxAmount", required = false) BigDecimal maxAmount,
									  @RequestParam(name = "page", defaultValue = "0") int page,
									  @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (agentType != null) {
			criterions.add(Restrictions.eq("agentType", agentType));
		}
		if (!StringUtils.isEmpty(playerName)) {
			criterions.add(Restrictions.eq("playerName", playerName));
		}
		if (!StringUtils.isEmpty(billNo)) {
			criterions.add(Restrictions.eq("playerBillNo", billNo));
		}
		if (billType != null) {
			criterions.add(Restrictions.eq("billType", billType));
		}
		if (minAmount != null) {
			criterions.add(Restrictions.gt("billAmount", minAmount));
		}
		if (maxAmount != null) {
			criterions.add(Restrictions.lt("billAmount", maxAmount));
		}
		if (!StringUtils.isEmpty(sTime)) {
			criterions.add(Restrictions.ge("createTime", sDate));
		}
		if (!StringUtils.isEmpty(eTime)) {
			criterions.add(Restrictions.lt("createTime", eDate));
		}
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("billId"));
		int totalCount = getAgentPlayerAccountBillDao().totalCount(criterions);
		List<AgentPlayerAccountBillVo> list = new ArrayList<AgentPlayerAccountBillVo>();
		if (totalCount > 0) {
			List<AgentPlayerAccountBill> resultList = getAgentPlayerAccountBillDao().find(criterions, orders,
					firstResult, maxResults);
			for (AgentPlayerAccountBill entity : resultList) {
				AgentPlayerAccountBillVo vo = new AgentPlayerAccountBillVo(entity);
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_GAME_RECORD)
	public WebJson SEARCH_PLAYER_GAME_RECORD(HttpServletRequest request,
											 @RequestParam(name = "agentNo", required = false) String agentNo,
											 @RequestParam(name = "agentType", required = false) Integer agentType,
											 @RequestParam(name = "agentName", required = false) String agentName,
											 @RequestParam(name = "playerName", required = false) String playerName,
											 @RequestParam(name = "lottery", required = false) String lottery,
											 @RequestParam(name = "methodType", required = false) Integer methodType,
											 @RequestParam(name = "gameTypeCode", required = false) String gameTypeCode,
											 @RequestParam(name = "gameIssue", required = false) String gameIssue,
											 @RequestParam(name = "gameOrderNo", required = false) String gameOrderNo,
											 @RequestParam(name = "orderStatus", required = false) Integer orderStatus,
											 @RequestParam(name = "minBetAmount", required = false) BigDecimal minBetAmount,
											 @RequestParam(name = "maxBetAmount", required = false) BigDecimal maxBetAmount,
											 @RequestParam(name = "minBonusAmount", required = false) BigDecimal minBonusAmount,
											 @RequestParam(name = "maxBonusAmount", required = false) BigDecimal maxBonusAmount,
											 @RequestParam(name = "sTime", required = false) String sTime,
											 @RequestParam(name = "eTime", required = false) String eTime,
											 @RequestParam(name = "page", defaultValue = "0") int page,
											 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		AdminAccount adminAccount = getSessionUser(request);
		if (!StringUtils.isEmpty(adminAccount.getAgentNo())) {
			criterions.add(Restrictions.eq("agentNo", adminAccount.getAgentNo()));
		} else {
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
		}

		if (agentType != null) {
			criterions.add(Restrictions.eq("agentType", agentType));
		}
		if (!StringUtils.isEmpty(playerName)) {
			criterions.add(Restrictions.eq("playerName", playerName));
		}
		if (!StringUtils.isEmpty(lottery)) {
			criterions.add(Restrictions.eq("lottery", lottery));
		}
		if (methodType != null) {
			criterions.add(Restrictions.eq("methodType", methodType));
		}
		if (!StringUtils.isEmpty(gameTypeCode)) {
			criterions.add(Restrictions.eq("gameTypeCode", gameTypeCode));
		}
		if (!StringUtils.isEmpty(gameIssue)) {
			criterions.add(Restrictions.eq("gameIssue", gameIssue));
		}
		if (!StringUtils.isEmpty(gameOrderNo)) {
			criterions.add(Restrictions.eq("gameOrderNo", gameOrderNo));
		}
		if (null != orderStatus) {
			criterions.add(Restrictions.eq("orderStatus", orderStatus));
		}
		if (null != minBetAmount) {
			criterions.add(Restrictions.gt("betAmount", minBetAmount));
		}
		if (null != maxBetAmount) {
			criterions.add(Restrictions.lt("betAmount", maxBetAmount));
		}
		if (null != minBonusAmount) {
			criterions.add(Restrictions.gt("bonusAmount", minBonusAmount));
		}
		if (null != maxBonusAmount) {
			criterions.add(Restrictions.lt("bonusAmount", maxBonusAmount));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("orderId"));
		int totalCount = getAgentPlayerGameOrderDao().totalCount(criterions);
		List<AgentPlayerGameOrderVo> list = new ArrayList<AgentPlayerGameOrderVo>();
		if (totalCount > 0) {
			List<AgentPlayerGameOrder> resultList = getAgentPlayerGameOrderDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentPlayerGameOrder entity : resultList) {
				String methodName = getRedisService().getGameLotteryMethod(entity.getGameTypeCode(),
						entity.getGameMethod()).getMethodName();
				entity.setGameMethod(methodName);
				AgentPlayerGameOrderVo vo = new AgentPlayerGameOrderVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.CANCEL_PLAYER_GAME_RECORD)
	public WebJson CANCEL_PLAYER_GAME_RECORD(HttpServletRequest request, @RequestParam String gameOrderNo) {
		WebJson webJson = new WebJson();
		try {
			boolean result = getGameLotteryService().cancelGeneralOrder(gameOrderNo);
			if (result) {
				String content = "玩家游戏记录撤单: gameOrderNo=" + gameOrderNo;
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			log.error("撤单异常,gameOrderNo:{}", gameOrderNo, e);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_CARD_BANK)
	public WebJson SEARCH_PLAYER_CARD_BANK(HttpServletRequest request,
										   @RequestParam(name = "agentNo", required = false) String agentNo,
										   @RequestParam(name = "agentName", required = false) String agentName,
										   @RequestParam(name = "playerName", required = false) String playerName,
										   @RequestParam(name = "bankCardName", required = false) String bankCardName,
										   @RequestParam(name = "bankCardNo", required = false) String bankCardNo,
										   @RequestParam(name = "status", required = false) Integer status,
										   @RequestParam(name = "lockStatus", required = false) Integer lockStatus,

										   @RequestParam(name = "page", defaultValue = "0") int page,
										   @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		boolean doSearch = true;

		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo byAgentName = getAgentInfoDao().getByAgentName(agentName);
			if (byAgentName != null) {
				agentNo = byAgentName.getAgentNo();
			} else {
				noPageData(webJson);
				return webJson;
			}
		}

		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(bankCardName)) {
			criterions.add(Restrictions.eq("bankCardName", bankCardName));
		}
		if (!StringUtils.isEmpty(bankCardNo)) {
			String encryptedBankCardNo = getToolsService().encrypt(bankCardNo);
			criterions.add(Restrictions.eq("bankCardNo", encryptedBankCardNo));
		}
		if (null != status) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (null != lockStatus) {
			try {
				Boolean lockStatusBoolean = (lockStatus == 1);
				criterions.add(Restrictions.eq("lockStatus", lockStatusBoolean));
			} catch (Exception e) {
				throw new ServiceException("1", "该状态不存在");
			}
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerCardBankDao().totalCount(criterions);
		List<PlayerCardBankVo> list = new ArrayList<PlayerCardBankVo>();
		if (totalCount > 0) {
			List<PlayerCardBank> resultList = getPlayerCardBankDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerCardBank entity : resultList) {
				PlayerCardBankVo vo = new PlayerCardBankVo(entity, getToolsService());
				AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(entity.getAgentNo());
				if (agentInfo != null) {
					vo.setAgentName(agentInfo.getAgentName());
				}
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
				if (playerInfo != null) {
					vo.setPlayerName(playerInfo.getPlayerName());
				}
				getPaymentBankDao().listAll().stream().filter(p -> p.getId().equals(entity.getBankId())).findFirst()
						.ifPresent(paymentBank -> vo.setBankName(paymentBank.getName()));
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ENABLE_PLAYER_CARD_BANK)
	public WebJson ENABLE_PLAYER_CARD_BANK(HttpServletRequest request, @RequestParam(name = "id") Long id,
										   @RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();
		PlayerCardBank byId = getPlayerCardBankDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		byId.setStatus(status);
		getPlayerCardBankDao().update(byId);
		// 保存操作日志
		String desc = CommonStatus.getByCode(status).getDesc();
		String content = "玩家提现银行卡：" + byId.getBankCardNo() + "，状态改为：" + desc;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_CARD_BANK)
	public WebJson ADD_PLAYER_CARD_BANK(HttpServletRequest request,
										@RequestParam(name = "agentNo", required = false) String agentNo,
										@RequestParam(name = "playerName", required = false) String playerName,
										@RequestParam(name = "bankId") Long bankId, @RequestParam(name = "bankCardBranch") String bankCardBranch,
										@RequestParam(name = "bankCardName") String bankCardName,
										@RequestParam(name = "bankCardNo") String bankCardNo) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}
		AgentInfo info = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == info) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw newException("100-01");
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null == playerInfo) {
			log.warn(playerName + " 该玩家不存在。");
			throw newException("100-02");
		}

		// TODO 判断银行卡重复

		PlayerCardBank playerCardBank = new PlayerCardBank();
		playerCardBank.setAgentNo(agentNo);
		playerCardBank.setPlayerId(playerInfo.getPlayerId());
		playerCardBank.setBankId(bankId);
		playerCardBank.setBankCardBranch(bankCardBranch);
		playerCardBank.setBankCardName(bankCardName);
		String encryptedBankCardNo = getToolsService().encrypt(bankCardNo);
		playerCardBank.setBankCardNo(encryptedBankCardNo);
		playerCardBank.setLockStatus(false);
		playerCardBank.setStatus(0);
		playerCardBank.setIsDefault(false);
		playerCardBank.setCreateTime(new Date());
		AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
		if (sessionUser != null) {
			playerCardBank.setCreateBy(sessionUser.getUsername());
		} else {
			playerCardBank.setCreateBy("admin");
		}
		boolean save = getPlayerCardBankDao().save(playerCardBank);
		if (save) {
			// 保存操作日志
			String content = "代理商：" + info.getAgentName() + "，玩家名：" + playerName + " 支行：" + bankCardBranch + " 姓名："
					+ bankCardName + " 银行卡号：" + bankCardNo;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
		}
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.EDIT_PLAYER_CARD_BANK)
	public WebJson EDIT_PLAYER_CARD_BANK(HttpServletRequest request, @RequestParam(name = "id") Long id,
										 @RequestParam(name = "bankId") Long bankId, @RequestParam(name = "bankCardBranch") String bankCardBranch,
										 @RequestParam(name = "bankCardName") String bankCardName,
										 @RequestParam(name = "bankCardNo") String bankCardNo) {
		WebJson webJson = new WebJson();
		PlayerCardBank playerCardBank = getPlayerCardBankDao().getById(id);
		if (playerCardBank == null) {
			throw new ServiceException("1", "数据不存在");
		}

		playerCardBank.setBankId(bankId);
		playerCardBank.setBankCardBranch(bankCardBranch);
		playerCardBank.setBankCardName(bankCardName);
		String encryptedBankCardNo = getToolsService().encrypt(bankCardNo);
		playerCardBank.setBankCardNo(encryptedBankCardNo);

		getPlayerCardBankDao().update(playerCardBank);

		// 保存操作日志
		String content = "id：" + id + " 支行：" + bankCardBranch + " 姓名：" + bankCardName + " 银行卡号：" + bankCardNo;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UNLOCK_PLAYER_CARD_BANK)
	public WebJson UNLOCK_PLAYER_CARD_BANK(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();

		PlayerCardBank byId = getPlayerCardBankDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}

		byId.setLockStatus(false);
		byId.setLockedTime(null);

		getPlayerCardBankDao().update(byId);

		// 保存操作日志
		String content = "id：" + id + "解锁 ";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.LOCK_PLAYER_CARD_BANK)
	public WebJson LOCK_PLAYER_CARD_BANK(HttpServletRequest request, @RequestParam(name = "id") Long id,
										 @RequestParam(name = "lockedTime") Integer lockedTime) {
		WebJson webJson = new WebJson();

		PlayerCardBank byId = getPlayerCardBankDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		Date date = new Date(System.currentTimeMillis() + lockedTime * 1000 * 3600);

		byId.setLockStatus(true);
		byId.setLockedTime(date);

		getPlayerCardBankDao().update(byId);
		// 保存操作日志
		String content = "id：" + id + "锁定 " + lockedTime + "小时";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.DELETE_PLAYER_CARD_BANK)
	public WebJson DELETE_PLAYER_CARD_BANK(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();

		PlayerCardBank byId = getPlayerCardBankDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}

		boolean result = getPlayerCardBankDao().delete(byId);
		if (result) {
			String decryptedBankCardNo = getToolsService().decrypt(byId.getBankCardNo());
			AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(byId.getPlayerId());
			// 保存操作日志
			String content = "id：" + id + "，所属玩家：" + player.getPlayerName() + "，地址：" + decryptedBankCardNo;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_CARD_USDT)
	public WebJson SEARCH_PLAYER_CARD_USDT(HttpServletRequest request,
										   @RequestParam(name = "agentNo", required = false) String agentNo,
										   @RequestParam(name = "agentName", required = false) String agentName,
										   @RequestParam(name = "playerName", required = false) String playerName,
										   @RequestParam(name = "name", required = false) String name,
										   @RequestParam(name = "lockStatus", required = false) Integer lockStatus,
										   @RequestParam(name = "status", required = false) Integer status,
										   @RequestParam(name = "address", required = false) String cardAddress,
										   @RequestParam(name = "page", defaultValue = "0") int page,
										   @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}

		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo byAgentName = getAgentInfoDao().getByAgentName(agentName);
			if (byAgentName != null) {
				agentNo = byAgentName.getAgentNo();
			} else {
				noPageData(webJson);
				return webJson;
			}
		}

		boolean doSearch = true;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}

		if (!StringUtils.isEmpty(name)) {
			criterions.add(Restrictions.eq("name", name));
		}
		if (!StringUtils.isEmpty(cardAddress)) {
			String encryptedCardAddress = getToolsService().encrypt(cardAddress);
			criterions.add(Restrictions.or(Restrictions.like("usdtTrc20Address", "%" + encryptedCardAddress + "%"),
					Restrictions.like("usdtErc20Address", "%" + encryptedCardAddress + "%")));
		}
		if (null != status) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (null != lockStatus) {
			try {
				Boolean lockStatusBoolean = (lockStatus == 1);
				criterions.add(Restrictions.eq("lockStatus", lockStatusBoolean));
			} catch (Exception e) {
				throw new ServiceException("1", "该状态不存在");
			}
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerCardUstdDao().totalCount(criterions);
		List<PlayerCardUsdtVo> list = new ArrayList<PlayerCardUsdtVo>();
		if (totalCount > 0) {
			List<PlayerCardUsdt> resultList = getPlayerCardUstdDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerCardUsdt entity : resultList) {
				PlayerCardUsdtVo vo = new PlayerCardUsdtVo(entity, getToolsService());
				AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(entity.getAgentNo());
				if (agentInfo != null) {
					vo.setAgentName(agentInfo.getAgentName());
				}
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
				if (playerInfo != null) {
					vo.setPlayerName(playerInfo.getPlayerName());
				}
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ENABLE_PLAYER_CARD_USDT)
	public WebJson ENABLE_PLAYER_CARD_USDT(HttpServletRequest request, @RequestParam(name = "id") Long id,
										   @RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();

		PlayerCardUsdt byId = getPlayerCardUstdDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		byId.setStatus(status);

		getPlayerCardUstdDao().update(byId);

		// 保存操作日志
		String desc = CommonStatus.getByCode(status).getDesc();
		String content = "玩家提现USDT：" + byId.getName() + "，状态改为：" + desc;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_CARD_USDT)
	public WebJson ADD_PLAYER_CARD_USDT(HttpServletRequest request,
										@RequestParam(name = "agentNo", required = false) String agentNo,
										@RequestParam(name = "playerName", required = false) String playerName,
										@RequestParam(name = "name") String name,
										@RequestParam(name = "usdtTRC20", required = false) String usdtTrc20Address,
										@RequestParam(name = "usdtERC20", required = false) String usdtErc20Address) {
		WebJson webJson = new WebJson();

		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}

		AgentInfo info = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == info) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw new ServiceException("1", "代理商号码：" + agentNo + "对应的代理商不存在");
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null == playerInfo) {
			log.warn(playerName + " 该玩家不存在。");
			throw new ServiceException("1", "用户名：" + playerName + "不存在");
		}

		// TODO 判断USDT重复

		PlayerCardUsdt playerCardUsdt = new PlayerCardUsdt();
		playerCardUsdt.setAgentNo(agentNo);
		playerCardUsdt.setPlayerId(playerInfo.getPlayerId());
		playerCardUsdt.setName(name);
		playerCardUsdt.setUsdtTrc20Address("");
		playerCardUsdt.setUsdtErc20Address("");
		if (!StringUtils.isEmpty(usdtTrc20Address)) {
			String encryptedUsdtTrc20Address = getToolsService().encrypt(usdtTrc20Address);
			playerCardUsdt.setUsdtTrc20Address(encryptedUsdtTrc20Address);
		}
		if (!StringUtils.isEmpty(usdtErc20Address)) {
			String encryptedUsdtErc20Address = getToolsService().encrypt(usdtErc20Address);
			playerCardUsdt.setUsdtErc20Address(encryptedUsdtErc20Address);
		}
		playerCardUsdt.setUsdtType(1);
		playerCardUsdt.setLockStatus(false);
		playerCardUsdt.setStatus(0);
		playerCardUsdt.setIsDefault(false);
		playerCardUsdt.setCreateTime(new Date());
		AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
		if (sessionUser != null) {
			playerCardUsdt.setCreateBy(sessionUser.getUsername());
		} else {
			playerCardUsdt.setCreateBy("admin");
		}

		boolean save = getPlayerCardUstdDao().save(playerCardUsdt);
		if (save) {
			// 保存操作日志
			String content = "代理商：" + info.getAgentName() + "，玩家名：" + playerName + " 名称：" + name + " Trc20地址："
					+ usdtTrc20Address;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.EDIT_PLAYER_CARD_USDT)
	public WebJson EDIT_PLAYER_CARD_USDT(HttpServletRequest request, @RequestParam(name = "id") Long id,
										 @RequestParam(name = "name") String name,
										 @RequestParam(name = "usdtTRC20", required = false) String usdtTrc20Address,
										 @RequestParam(name = "usdtERC20", required = false) String usdtErc20Address) {
		WebJson webJson = new WebJson();
		PlayerCardUsdt byId = getPlayerCardUstdDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		byId.setName(name);
		if (!StringUtils.isEmpty(usdtTrc20Address)) {
			String encryptedUsdtTrc20Address = getToolsService().encrypt(usdtTrc20Address);
			byId.setUsdtTrc20Address(encryptedUsdtTrc20Address);
		}
		if (!StringUtils.isEmpty(usdtErc20Address)) {
			String encryptedUsdtErc20Address = getToolsService().encrypt(usdtErc20Address);
			byId.setUsdtErc20Address(encryptedUsdtErc20Address);
		}
		getPlayerCardUstdDao().update(byId);
		// 保存操作日志
		String content = "id：" + id + " 名称：" + name + " Trc20地址：" + usdtTrc20Address;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UNLOCK_PLAYER_CARD_USDT)
	public WebJson UNLOCK_PLAYER_CARD_USDT(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();

		PlayerCardUsdt byId = getPlayerCardUstdDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}

		byId.setLockStatus(false);
		byId.setLockedTime(null);

		getPlayerCardUstdDao().update(byId);

		// 保存操作日志
		String content = "id：" + id + "解锁";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.LOCK_PLAYER_CARD_USDT)
	public WebJson LOCK_PLAYER_CARD_USDT(HttpServletRequest request, @RequestParam(name = "id") Long id,
										 @RequestParam(name = "lockedTime") Integer lockTime) {
		WebJson webJson = new WebJson();

		PlayerCardUsdt byId = getPlayerCardUstdDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		Date date = new Date(System.currentTimeMillis() + lockTime * 1000 * 3600);

		byId.setLockStatus(true);
		byId.setLockedTime(date);

		getPlayerCardUstdDao().update(byId);

		// 保存操作日志
		String content = "id：" + id + "锁定" + lockTime + "小时";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.DELETE_PLAYER_CARD_USDT)
	public WebJson DELETE_PLAYER_CARD_USDT(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardUsdt byId = getPlayerCardUstdDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		boolean result = getPlayerCardUstdDao().delete(byId);
		if (result) {
			String encryptedUsdtAddress = !StringUtils.isEmpty(byId.getUsdtTrc20Address()) ? byId.getUsdtTrc20Address() : byId.getUsdtErc20Address();
			String decryptedUsdtAddress = getToolsService().encrypt(encryptedUsdtAddress);
			AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(byId.getPlayerId());
			// 保存操作日志
			String content = "id：" + id + "，所属玩家：" + player.getPlayerName() + "，地址：" + decryptedUsdtAddress;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_RECHARGE_RECORD)
	public WebJson SEARCH_PLAYER_RECHARGE_RECORD(HttpServletRequest request,
												 @RequestParam(name = "agentNo", required = false) String agentNo,
												 @RequestParam(name = "agentName", required = false) String agentName,
												 @RequestParam(name = "playerName", required = false) String playerName,
												 @RequestParam(name = "billno", required = false) String billno,
												 @RequestParam(name = "method", required = false) Integer method,
												 @RequestParam(name = "payType", required = false) Integer payType,
												 @RequestParam(name = "orderStatus", required = false) Integer orderStatus,
												 @RequestParam(name = "amountGt", required = false) BigDecimal amountGt,
												 @RequestParam(name = "amountLt", required = false) BigDecimal amountLt,
												 @RequestParam(name = "sTime", required = false) String sTime,
												 @RequestParam(name = "eTime", required = false) String eTime,
												 @RequestParam(name = "page", defaultValue = "0") int page,
												 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		boolean doSearch = true;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (!StringUtils.isEmpty(sTime)) {
			Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
			criterions.add(Restrictions.ge("orderTime", sDate));
		}
		if (!StringUtils.isEmpty(eTime)) {
			Date eDate = HttpUtils.prepareQueryDate(eTime, 0).toDate();
			criterions.add(Restrictions.lt("orderTime", eDate));
		}
		if (method != null) {
			criterions.add(Restrictions.eq("method", method));
		}
		if (payType != null) {
			criterions.add(Restrictions.eq("payType", payType));
		}
		if (!StringUtils.isEmpty(billno)) {
			criterions.add(Restrictions.eq("billno", billno));
		}
		if (null != orderStatus) {
			criterions.add(Restrictions.eq("orderStatus", orderStatus));
		}
		if (null != amountGt) {
			criterions.add(Restrictions.gt("amount", amountGt));
		}
		if (null != amountLt) {
			criterions.add(Restrictions.lt("amount", amountLt));
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerRechargeDao().totalCount(criterions);
		List<PlayerRechargeVo> list = new ArrayList<PlayerRechargeVo>();
		if (totalCount > 0) {
			List<PlayerRecharge> resultList = getPlayerRechargeDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerRecharge entity : resultList) {
				PlayerRechargeVo vo = new PlayerRechargeVo(entity);
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
				if (playerInfo != null) {
					vo.setPlayerName(playerInfo.getPlayerName());
				}

				// 设置支付方式中文名称
				if (vo.getPayType() != null) {
					vo.setPayTypeStr(PayTypeEnum.getDisplayNameByCode(vo.getPayType()));
				}

				if(vo.getMethod() == PlayerRecharge.METHOD_THRID) {
					PaymentThirdPay paymentThirdPay = getPaymentThirdPayDao().getById(vo.getPayId());
					if (paymentThirdPay != null) {
						PaymentThird paymentThird =  getRedisService().getPaymentThird(paymentThirdPay.getThirdId());
						if(null != paymentThird) {
							vo.setPayName(paymentThird.getName());
						}
					}
				}
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.WRITE)
	@RequestMapping(Route.Agent.CALCEL_PLAYER_RECHARGE)
	public WebJson CALCEL_PLAYER_RECHARGE(HttpServletRequest request, @RequestParam(name = "billno") String billno) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount sessionUser = getSessionUser(request);
			boolean result = getAgentService().cancelRecharge(billno);
			if (result) {
				// 保存操作日志
				String content = "取消充值订单：" + billno + "，操作人：" + sessionUser.getUsername();
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("补单异常", t);
		}
		return webJson;
	}


	@CheckLogin
	@RequestMapping(Route.Agent.PATCH_ACCOUNT_RECHARGE)
	public WebJson PATCH_ACCOUNT_RECHARGE(HttpServletRequest request, @RequestParam(name = "billno") String billno,
			@RequestParam(name = "amount") BigDecimal amount,
			@RequestParam(name = "payBillno", required = false) String payBillno,
			@RequestParam(name = "remarks", required = false) String remarks,
			@RequestParam(name = "password") String password) {
		WebJson webJson = new WebJson();
		// 加锁
		String patchAccountRechargeKey = RedisKey.getPatchAccountRecharge(billno);
		try {
			Optional<Boolean> lockResult = proRedisLockUtil.around(patchAccountRechargeKey, 0, 15_000, () -> {
				AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
				if (!CipherUtils.verify(sessionUser.getOperationPassword(), password)) {
					throw new ServiceException("1", "操作密码不正确");
				}
				boolean result = getAgentService().patchRecharge(billno, amount, payBillno, remarks);
				if (result) {
					// 保存操作日志
					String content = "补单 订单号：" + billno + "支付单号： " + payBillno + ", 备注：" + remarks;
					// 设置 ThreadLocal 变量
					ThreadLocalUtil.setAdminLogContent(content);
					setSuccess(webJson);
				} else {
					setFail(webJson);
				}
				return true;
			});

			if (!lockResult.isPresent()) {
				setFail(webJson);
				webJson.setMessage("订单处理中");
			}
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("补单异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.GET_PLAYER_RECHARGE_RECORD_DETAIL)
	public WebJson GET_PLAYER_RECHARGE_RECORD_DETAIL(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();

		PlayerRecharge entity = getPlayerRechargeDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}

		PlayerRechargeVo vo = new PlayerRechargeVo(entity);
		AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(entity.getAgentNo());
		if (agentInfo != null) {
			vo.setAgentName(agentInfo.getAgentName());
		}
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
		if (playerInfo != null) {
			vo.setPlayerName(playerInfo.getPlayerName());
		}

		webJson.setData(vo);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_WITHDRAW_RECORD)
	public WebJson SEARCH_PLAYER_WITHDRAW_RECORD(HttpServletRequest request,
												 @RequestParam(name = "agentNo", required = false) String agentNo,
												 @RequestParam(name = "agentName", required = false) String agentName,
												 @RequestParam(name = "playerName", required = false) String playerName,
												 @RequestParam(name = "billno", required = false) String billno,
												 @RequestParam(name = "cardName", required = false) String cardName,
												 @RequestParam(name = "bankCardAddress", required = false) String bankCardAddress,
												 @RequestParam(name = "remitType", required = false) Integer remitType,
												 @RequestParam(name = "payStatus", required = false) Integer payStatus,
												 @RequestParam(name = "orderStatus", required = false) Integer orderStatus,
												 @RequestParam(name = "amountGt", required = false) BigDecimal amountGt,
												 @RequestParam(name = "amountLt", required = false) BigDecimal amountLt,
												 @RequestParam(name = "sTime", required = false) String sTime,
												 @RequestParam(name = "eTime", required = false) String eTime,
												 @RequestParam(name = "page", defaultValue = "0") int page,
												 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		boolean doSearch = true;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (player == null) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}
		if (!StringUtils.isEmpty(sTime)) {
			Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
			criterions.add(Restrictions.ge("orderTime", sDate));
		}
		if (!StringUtils.isEmpty(eTime)) {
			Date eDate = HttpUtils.prepareQueryDate(eTime, 0).toDate();
			criterions.add(Restrictions.lt("orderTime", eDate));
		}
		if (!StringUtils.isEmpty(cardName)) {
			criterions.add(Restrictions.eq("bankCardName", cardName));
		}
		if (!StringUtils.isEmpty(bankCardAddress)) {
			String encryptedBankCardAddress = getToolsService().encrypt(bankCardAddress);
			criterions.add(Restrictions.eq("bankCardAddress", encryptedBankCardAddress));
		}
		if (remitType != null) {
			criterions.add(Restrictions.eq("remitType", remitType));
		}
		if (payStatus != null) {
			criterions.add(Restrictions.eq("payStatus", payStatus));
		}
		if (!StringUtils.isEmpty(billno)) {
			criterions.add(Restrictions.eq("billno", billno));
		}
		if (null != orderStatus) {
			criterions.add(Restrictions.eq("orderStatus", orderStatus));
		}
		if (null != amountGt) {
			criterions.add(Restrictions.gt("amount", amountGt));
		}
		if (null != amountLt) {
			criterions.add(Restrictions.lt("amount", amountLt));
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerWithdrawDao().totalCount(criterions);
		List<PlayerWithdrawVo> list = new ArrayList<PlayerWithdrawVo>();
		if (totalCount > 0) {
			List<PlayerWithdraw> resultList = getPlayerWithdrawDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerWithdraw entity : resultList) {
				PlayerWithdrawVo vo = new PlayerWithdrawVo(entity, getRedisService(), getToolsService());

				// 设置提现方式中文名称
				if (vo.getRemitType() != null) {
					vo.setRemitTypeStr(PayTypeEnum.getDisplayNameByCode(vo.getRemitType()));
				}

				if (0 != vo.getRemitId()) {
					PaymentThirdRemit remit = getPaymentThirdRemitDao().getById(Long.valueOf(vo.getRemitId()));
					if (null != remit) {
						PaymentThird third = getRedisService().getPaymentThird(remit.getThirdId());
						if (null != third) {
							vo.setThirdName(third.getName());
							vo.setRemitName(remit.getName());
							vo.setRemitTypeName(remit.getRemitType());
						}
					}
				}
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.GET_PLAYER_WITHDRAW_RECORD_DETAIL)
	public WebJson GET_PLAYER_WITHDRAW_RECORD_DETAIL(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerWithdraw byId = getPlayerWithdrawDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		webJson.setData(new PlayerWithdrawVo(byId, getRedisService(), getToolsService()));
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UPDATE_PLAYER_WITHDRAW_RECORD_STATUS)
	public WebJson UPDATE_PLAYER_WITHDRAW_RECORD_STATUS(HttpServletRequest request, @RequestParam(name = "id") Long id,
														@RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);
			boolean result = false;
			if (status == 1) {
				// 审核通过
				result = getPaymentService().updateWithdrawCheckStatus(adminAccount, id, status);
			} else if (status == 4) {
				// 重新打款
				result = getPaymentService().updateWithdrawRepay(adminAccount, id, status);
			} else {
				result = getGameLotteryService().updatePlayerWithdrawStatus(adminAccount, id, status);
			}
			if (result) {
				// 保存操作日志
				String desc = "";
				if (status == 1) {
					desc = "审核通过";
				} else if (status == 2) {
					desc = "拒绝";
				} else if (status == 3) {
					desc = "打款完成";
				} else if (status == 4) {
					desc = "重新打款";
				}
				String content = "更新提现记录状态 Id：" + id + "更新状态为： " + desc;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.MANUAL_WITHDRAW_DEDUCTION)
	public WebJson MANUAL_WITHDRAW_DEDUCTION(HttpServletRequest request,
			@RequestParam(name = "id") Long id,
			@RequestParam(name = "remarks", required = false) String remarks,
			@RequestParam(name = "password") String password) {
		WebJson webJson = new WebJson();
		try {
			AdminAccount adminAccount = getSessionUser(request);

			// 验证操作密码
			if (!CipherUtils.verify(adminAccount.getOperationPassword(), password)) {
				throw newException("1000-01");
			}

			// 获取提现订单
			PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
			if (entity == null) {
				throw new ServiceException("-1", "订单数据不存在");
			}

			// 验证订单状态：必须是审核通过且未完成的订单
			if (entity.getCheckStatus() != PlayerWithdraw.CHECK_STATUS_PASS) {
				throw new ServiceException("-1", "该订单未审核通过，无法执行人工下分");
			}

			if (entity.getOrderStatus() != PlayerWithdraw.ORDER_STATUS_WAITING) {
				throw new ServiceException("-1", "该订单已处理完成，无法重复操作");
			}

			// 执行人工下分
			boolean result = getPaymentService().manualWithdrawDeduction(adminAccount, id, remarks);

			if (result) {
				// 保存操作日志
				String content = "人工下分操作 订单ID：" + id + "，备注：" + (remarks != null ? remarks : "无");
				ThreadLocalUtil.setAdminLogContent(content);
				setSuccess(webJson);
			} else {
				setFail(webJson);
			}
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			log.info("error:" + ErrorUtils.getStackTraceInfo(e));
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_REWARD)
	public WebJson SEARCH_PLAYER_REWARD(HttpServletRequest request,
										@RequestParam(name = "agentNo", required = false) String agentNo,
										@RequestParam(name = "agentType", required = false) Integer agentType,
										@RequestParam(name = "agentName", required = false) String agentName,
										@RequestParam(name = "playerName", required = false) String playerName,
										@RequestParam(name = "lottery", required = false) String lottery,
										@RequestParam(name = "gameTypeCode", required = false) String gameTypeCode,
										@RequestParam(name = "sTime", required = false) String sTime,
										@RequestParam(name = "eTime", required = false) String eTime,
										@RequestParam(name = "page", defaultValue = "0") int page,
										@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(adminAccount.getAgentNo())) {
			criterions.add(Restrictions.eq("agentNo", adminAccount.getAgentNo()));
		} else {
			if (!StringUtils.isEmpty(agentNo)) {
				criterions.add(Restrictions.eq("agentNo", agentNo));
			}
			if (!StringUtils.isEmpty(agentName)) {
				criterions.add(Restrictions.eq("agentName", agentName));
			}
		}

		if (agentType != null) {
			criterions.add(Restrictions.eq("agentType", agentType));
		}
		if (!StringUtils.isEmpty(playerName)) {
			criterions.add(Restrictions.eq("playerName", playerName));
		}
		if (!StringUtils.isEmpty(lottery)) {
			criterions.add(Restrictions.eq("lottery", lottery));
		}
		if (!StringUtils.isEmpty(gameTypeCode)) {
			criterions.add(Restrictions.eq("gameTypeCode", gameTypeCode));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("rewardId"));
		int totalCount = getAgentPlayerRewardDao().totalCount(criterions);
		List<AgentPlayerRewardVo> list = new ArrayList<AgentPlayerRewardVo>();
		if (totalCount > 0) {
			List<AgentPlayerReward> resultList = getAgentPlayerRewardDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentPlayerReward entity : resultList) {
				AgentPlayerRewardVo vo = new AgentPlayerRewardVo(entity);
				GiftInfo giftInfo = getGiftInfoDao().getByCode(vo.getGiftCode());
				if (giftInfo != null) {
					vo.setGiftName(giftInfo.getGiftName());
				}

				GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(vo.getGameTypeCode());
				if (gameLotteryType != null) {
					vo.setGameTypeName(gameLotteryType.getGameTypeName());
				}
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.GET_PLAYER_GAME_RECORD_DETAILS)
	public WebJson GET_PLAYER_GAME_RECORD_DETAILS(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		AgentPlayerGameOrder entity = getAgentPlayerGameOrderDao().getById(id);
		if (null == entity) {
			throw newException("该订单信息不存在");
		}
		String methodName = getRedisService().getGameLotteryMethod(entity.getGameTypeCode(), entity.getGameMethod())
				.getMethodName();
		entity.setGameMethod(methodName);
		AgentPlayerGameOrderVo vo = new AgentPlayerGameOrderVo(entity);
		webJson.setData(vo);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_TRANSFER_RECORD)
	public WebJson SEARCH_PLAYER_TRANSFER_RECORD(HttpServletRequest request,
												 @RequestParam(name = "agentNo", required = false) String agentNo,
												 @RequestParam(name = "agentType", required = false) Integer agentType,
												 @RequestParam(name = "agentName", required = false) String agentName,
												 @RequestParam(name = "playerName", required = false) String playerName,
												 @RequestParam(name = "transferType", required = false) Integer transferType,
												 @RequestParam(name = "sTime", required = false) String sTime,
												 @RequestParam(name = "eTime", required = false) String eTime,
												 @RequestParam(name = "page", defaultValue = "0") int page,
												 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (agentType != null) {
			criterions.add(Restrictions.eq("agentType", agentType));
		}

		if (!StringUtils.isEmpty(agentName)) {
			criterions.add(Restrictions.eq("agentName", agentName));
		}

		if (!StringUtils.isEmpty(playerName)) {
			criterions.add(Restrictions.eq("playerName", playerName));
		}

		if (transferType != null) {
			criterions.add(Restrictions.eq("transferType", transferType));
		}

		if (!StringUtils.isEmpty(sTime)) {
			criterions.add(Restrictions.ge("createTime", sDate));
		}

		if (!StringUtils.isEmpty(eTime)) {
			criterions.add(Restrictions.lt("createTime", eDate));
		}

		criterions.add(Restrictions.eq("isDelete", 0));

		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getAgentPlayerTransferRecordDao().totalCount(criterions);
		List<AgentPlayerTransferRecordVo> list = new ArrayList<AgentPlayerTransferRecordVo>();
		if (totalCount > 0) {
			List<AgentPlayerTransferRecord> resultList = getAgentPlayerTransferRecordDao().find(criterions, orders,
					firstResult, maxResults);
			for (AgentPlayerTransferRecord entity : resultList) {
				AgentPlayerTransferRecordVo vo = new AgentPlayerTransferRecordVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_GAME_REPORT)
	public WebJson SEARCH_PLAYER_GAME_REPORT(HttpServletRequest request,
											 @RequestParam(name = "agentNo", required = false) String agentNo,
											 @RequestParam(name = "playerName", required = false) String playerName,
											 @RequestParam(name = "sTime", required = false) String sTime,
											 @RequestParam(name = "eTime", required = false) String eTime,
											 @RequestParam(name = "page", defaultValue = "0") int page,
											 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();

		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		} else {
			AgentInfo agent = getAgent(request);
			if (agent != null) {
				criterions.add(Restrictions.eq("agentNo", agent.getAgentNo()));
			}
		}
		if (!StringUtils.isEmpty(playerName)) {
			criterions.add(Restrictions.eq("playerName", playerName));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getAgentPlayerGameReportDao().totalCount(criterions);
		List<AgentPlayerGameReportVo> list = new ArrayList<AgentPlayerGameReportVo>();
		if (totalCount > 0) {
			List<AgentPlayerGameReport> resultList = getAgentPlayerGameReportDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentPlayerGameReport entity : resultList) {
				AgentPlayerGameReportVo vo = new AgentPlayerGameReportVo(entity);
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_OPINION)
	public WebJson SEARCH_PLAYER_OPINION(@RequestParam(name = "playerName", required = false) String playerName,
										 @RequestParam(name = "feedbackType", required = false) Integer feedbackType,
										 @RequestParam(name = "processStatus", required = false) Integer processStatus,
										 @RequestParam(name = "sTime", required = false) String sTime,
										 @RequestParam(name = "eTime", required = false) String eTime,
										 @RequestParam(name = "page", defaultValue = "0") int page,
										 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();

		int firstResult = page * size, maxResults = size;
		Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
		Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();
		List<Criterion> criterions = new ArrayList<Criterion>();

		if (!StringUtils.isEmpty(playerName)) {
			criterions.add(Restrictions.eq("playerName", playerName));
		}
		if (feedbackType != null) {
			criterions.add(Restrictions.eq("feedbackType", feedbackType));
		}
		if (processStatus != null) {
			criterions.add(Restrictions.eq("processStatus", processStatus));
		}
		criterions.add(Restrictions.ge("createTime", sDate));
		criterions.add(Restrictions.lt("createTime", eDate));
		criterions.add(Restrictions.eq("isDelete", 0));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.asc("processStatus"));
		orders.add(Order.desc("id"));
		int totalCount = getAgentPlayerOpinionDao().totalCount(criterions);
		List<AgentPlayerOpinionVo> list = new ArrayList<AgentPlayerOpinionVo>();
		if (totalCount > 0) {
			List<AgentPlayerOpinion> resultList = getAgentPlayerOpinionDao().find(criterions, orders, firstResult,
					maxResults);
			for (AgentPlayerOpinion entity : resultList) {
				AgentPlayerOpinionVo vo = new AgentPlayerOpinionVo(entity);
				vo.setFeedbackPic(s3VideoUploadService.generatePresignedGiftUrlGlobal(entity.getLink()).toString());
				list.add(vo);
			}
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.PROCESS_PLAYER_OPINION)
	public WebJson PROCESS_PLAYER_OPINION(HttpServletRequest request, @RequestParam(name = "id") Long id,
										  @RequestParam(name = "processContent") String processContent) {
		WebJson webJson = new WebJson();

		AdminAccount adminAccount = getSessionUser(request);

		AgentPlayerOpinion byId = getAgentPlayerOpinionDao().getById(id);
		if (byId != null && byId.getProcessStatus() == 0) {
			byId.setProcessContent(processContent);
			byId.setProcessStatus(1);
			byId.setProcessAccountId((long) adminAccount.getId());
			byId.setProcessUsername(adminAccount.getUsername());
			byId.setProcessTime(new Timestamp(System.currentTimeMillis()));
			getAgentPlayerOpinionDao().update(byId);
		}
		// 保存操作日志
		String content = "反馈ID：" + id + "，处理结果：" + processContent;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_AGENT_LOGIN_LOG)
	public WebJson SEARCH_AGENT_LOGIN_LOG(HttpServletRequest request,
										  @RequestParam(name = "agentNo", required = false) String agentNo,
										  @RequestParam(name = "agentName", required = false) String agentName,
										  @RequestParam(name = "playerName", required = false) String playerName,
										  @RequestParam(name = "agentType", required = false) Integer agentType,
										  @RequestParam(name = "ip", required = false) String ip,
										  @RequestParam(name = "address", required = false) String address,
										  @RequestParam(name = "keyword", required = false) String keyword,
										  @RequestParam(name = "client", required = false) String client,
										  @RequestParam(name = "loginDate", required = false) String loginDate,
										  @RequestParam(name = "loginSucess", required = false) Boolean loginSucess,
										  @RequestParam(name = "remarkKeyword", required = false) String remarkKeyword,
										  @RequestParam(name = "page", defaultValue = "0") int page,
										  @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size, maxResults = size;
			Map<String, Object> data = new HashMap<String, Object>();
			int totalCount = 0;
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			Criteria criteria = new Criteria();
			if (!StringUtils.isEmpty(playerName)) {
				if (StringUtils.isEmpty(agentNo)) {
					List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
					if (!CollectionUtils.isEmpty(playerIds)) {
						criteria.and("playerId").in(playerIds);
					}else{
						data.put("totalCount", totalCount);
						data.put("list", null);
						webJson.setData(data);
						setSuccess(webJson);
						return webJson;
					}
				} else {
					AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
					if (null != player) {
						criteria.and("playerId").is(player.getPlayerId());
					}else{
						data.put("totalCount", totalCount);
						data.put("list", null);
						webJson.setData(data);
						setSuccess(webJson);
						return webJson;
					}
				}
			}
			if (!StringUtils.isEmpty(agentNo)) {
				criteria.and("agentNo").is(agentNo);
			}
			if (!StringUtils.isEmpty(agentName)) {
				criteria.and("agentName").is(agentName);
			}
			if (!StringUtils.isEmpty(ip)) {
				criteria.and("ip").regex(ip, "i");
			}
			if (!StringUtils.isEmpty(keyword)) {
				criteria.and("url").regex(keyword, "i");
			}
			if (!StringUtils.isEmpty(remarkKeyword)) {
				criteria.and("remarks").regex(remarkKeyword, "i");
			}
			if (!StringUtils.isEmpty(address)) {
				criteria.and("address").regex(address, "i");
			}
			if (!StringUtils.isEmpty(client)) {
				criteria.and("client").regex(client, "i");
			}
			if (!StringUtils.isEmpty(loginDate)) {
				Timestamp sTimestamp = new Moment().fromDate(loginDate).toTimestamp();
				Timestamp eTimestamp = new Moment().fromDate(loginDate).tomorrow().toTimestamp();
				criteria.and("loginTime").gte(sTimestamp).lt(eTimestamp);
			}
			if (loginSucess != null) {
				criteria.and("loginSucess").is(loginSucess);
			}
			Sort sort = Sort.by(Direction.DESC, "id");
			List<PlayerLoginLogVo> list = new ArrayList<PlayerLoginLogVo>();
			totalCount = getPlayerLoginLogDao().totalCount(criteria);
			if (totalCount > 0) {
				List<PlayerLoginLog> resultList = getPlayerLoginLogDao().find(criteria, sort, firstResult, maxResults);
				for (PlayerLoginLog tmpBean : resultList) {
					list.add(new PlayerLoginLogVo(tmpBean, getRedisService()));
				}
			}
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("查询代理商登录日志异常", t);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_ACCOUNT_ACTION_LOG)
	public WebJson SEARCH_ACCOUNT_ACTION_LOG(HttpServletRequest request,
											 @RequestParam(name = "agentNo", required = false) String agentNo,
											 @RequestParam(name = "agentName", required = false) String agentName,
											 @RequestParam(name = "playerName", required = false) String playerName,
											 @RequestParam(name = "ip", required = false) String ip,
											 @RequestParam(name = "address", required = false) String address,
											 @RequestParam(name = "keyword", required = false) String keyword,
											 @RequestParam(name = "time", required = false) String time,
											 @RequestParam(name = "page", defaultValue = "0") int page,
											 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size, maxResults = size;
			Map<String, Object> data = new HashMap<String, Object>();
			int totalCount = 0;
			AdminAccount adminAccount = getSessionUser(request);
			String defaultAgentNo = adminAccount.getAgentNo();
			if (!StringUtils.isEmpty(defaultAgentNo)) {
				agentNo = defaultAgentNo;
			}
			Criteria criteria = new Criteria();
			if (!StringUtils.isEmpty(playerName)) {
				if (StringUtils.isEmpty(agentNo)) {
					List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
					if (!CollectionUtils.isEmpty(playerIds)) {
						criteria.and("playerId").in(playerIds);
					}else{
						data.put("totalCount", totalCount);
						data.put("list", null);
						webJson.setData(data);
						setSuccess(webJson);
						return webJson;
					}
				} else {
					AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(defaultAgentNo, playerName);
					if (null != player) {
						criteria.and("playerId").is(player.getPlayerId());
					}else{
						data.put("totalCount", totalCount);
						data.put("list", null);
						webJson.setData(data);
						setSuccess(webJson);
						return webJson;
					}
				}
			}
			if (!StringUtils.isEmpty(agentNo)) {
				criteria.and("agentNo").is(agentNo);
			}
			if (!StringUtils.isEmpty(agentName)) {
				criteria.and("agentName").is(agentName);
			}
			if (!StringUtils.isEmpty(ip)) {
				criteria.and("ip").regex(ip, "i");
			}
			if (!StringUtils.isEmpty(address)) {
				criteria.and("address").regex(address, "i");
			}
			if (!StringUtils.isEmpty(keyword)) {
				Criteria c1 = Criteria.where("title").regex(keyword, "i");
				Criteria c2 = Criteria.where("content").regex(keyword, "i");
				criteria.orOperator(c1, c2);
			}
			if (!StringUtils.isEmpty(time)) {
				Date sDate = new Moment().fromDate(time).toDate();
				Date eDate = new Moment().fromDate(time).tomorrow().toDate();
				criteria.and("time").gte(sDate).lt(eDate);
			}
			Sort sort = Sort.by(Direction.DESC, "id");
			List<PlayerActionLog> resultList = getPlayerActionLogDao().find(criteria, sort, firstResult, maxResults);
			totalCount = getPlayerActionLogDao().totalCount(criteria);
			List<PlayerActionLogVo> list = new ArrayList<PlayerActionLogVo>();
			for (PlayerActionLog tmpBean : resultList) {
				list.add(new PlayerActionLogVo(tmpBean, getRedisService()));
			}
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("查询后台账号操作异常", t);
		}
		return webJson;
	}

	@RequestMapping(Route.Agent.FIX_PLAYER_REPORT)
	public WebJson FIX_PLAYER_REPORT(HttpServletRequest request, @RequestParam(name = "agentNo") String agentNo,
									 @RequestParam(name = "playerName") String playerName) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo byPlayerName = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
			webJson.setData(true);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
		} catch (Throwable t) {
			setFail(webJson);
			log.error("修复用户报表异常", t);
		}
		return webJson;
	}

	@RequestMapping(Route.Agent.SEARCH_AGENT_THIRD_LEVEL)
	public WebJson SEARCH_AGENT_THIRD_LEVEL(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		Integer agentLevelCode = adminAccount.getAgentLevelCode();
		List<AgentThirdLevelVo> list = new ArrayList<AgentThirdLevelVo>();
		if (agentLevelCode == 0) {
			list.add(new AgentThirdLevelVo(1, "股东"));
			list.add(new AgentThirdLevelVo(2, "总代理商"));
			list.add(new AgentThirdLevelVo(3, "代理商"));
		} else if (agentLevelCode == 1) {
			list.add(new AgentThirdLevelVo(2, "总代理商"));
			list.add(new AgentThirdLevelVo(3, "代理商"));
		} else if (agentLevelCode == 2) {
			list.add(new AgentThirdLevelVo(3, "代理商"));
		}
		webJson.setData(list);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UPDATE_PID)
	public WebJson UPDATE_PID(HttpServletRequest request, @RequestParam Long playerId,
			@RequestParam String newPlayerName, @RequestParam Integer type) {
		WebJson webJson = new WebJson();
		try {
			boolean isSuccess = agentPlayerService.changePlayerRelation(playerId, newPlayerName, type);
			if(isSuccess) {
				// 保存操作日志
				String content = "线路转移:playerId=" + playerId + ",newPlayerName=" + newPlayerName + ",type=" + type;
				// 设置 ThreadLocal 变量
				ThreadLocalUtil.setAdminLogContent(content);
			}
			webJson.setData(isSuccess);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			log.error(e.getMessage(), e);
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_CARD_ALIPAY)
	public WebJson SEARCH_PLAYER_CARD_ALIPAY(HttpServletRequest request,
											 @RequestParam(name = "agentNo", required = false) String agentNo,
											 @RequestParam(name = "agentName", required = false) String agentName,
											 @RequestParam(name = "playerName", required = false) String playerName,
											 @RequestParam(name = "alipayName", required = false) String alipayName,
											 @RequestParam(name = "alipayAccount", required = false) String alipayAccount,
											 @RequestParam(name = "status", required = false) Integer status,
											 @RequestParam(name = "lockStatus", required = false) Integer lockStatus,

											 @RequestParam(name = "page", defaultValue = "0") int page,
											 @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		boolean doSearch = true;

		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo byAgentName = getAgentInfoDao().getByAgentName(agentName);
			if (byAgentName != null) {
				agentNo = byAgentName.getAgentNo();
			} else {
				noPageData(webJson);
				return webJson;
			}
		}

		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(alipayName)) {
			criterions.add(Restrictions.eq("alipayName", alipayName));
		}
		if (!StringUtils.isEmpty(alipayAccount)) {
			String encryptedBankCardNo = getToolsService().encrypt(alipayAccount);
			criterions.add(Restrictions.eq("alipayAccount", encryptedBankCardNo));
		}
		if (null != status) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (null != lockStatus) {
			criterions.add(Restrictions.eq("lockStatus", lockStatus));
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerCardAlipayDao().totalCount(criterions);
		List<PlayerCardAlipayVo> list = new ArrayList<PlayerCardAlipayVo>();
		if (totalCount > 0) {
			List<PlayerCardAlipay> resultList = getPlayerCardAlipayDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerCardAlipay entity : resultList) {
				PlayerCardAlipayVo vo = new PlayerCardAlipayVo(entity, getToolsService());
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ENABLE_PLAYER_CARD_ALIPAY)
	public WebJson ENABLE_PLAYER_CARD_ALIPAY(HttpServletRequest request, @RequestParam(name = "id") Long id,
											 @RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();
		PlayerCardAlipay byId = getPlayerCardAlipayDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		byId.setStatus(status);
		getPlayerCardAlipayDao().update(byId);
		// 保存操作日志
		String desc = CommonStatus.getByCode(status).getDesc();
		String content = "玩家提现支付宝：" + byId.getAlipayAccount() + "，状态改为：" + desc;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_CARD_ALIPAY)
	public WebJson ADD_PLAYER_CARD_ALIPAY(HttpServletRequest request,
										  @RequestParam(name = "agentNo", required = false) String agentNo,
										  @RequestParam(name = "playerName", required = false) String playerName,
										  @RequestParam(name = "alipayName") String alipayName,
										  @RequestParam(name = "alipayAccount") String alipayAccount) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}
		AgentInfo info = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == info) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw newException("100-01");
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null == playerInfo) {
			log.warn(playerName + " 该玩家不存在。");
			throw newException("100-01");
		}

		// TODO 判断支付宝账号重复

		PlayerCardAlipay playerCardAlipay = new PlayerCardAlipay();
		playerCardAlipay.setAgentNo(agentNo);
		playerCardAlipay.setAgentName(info.getAgentName());
		playerCardAlipay.setPlayerId(playerInfo.getPlayerId());
		playerCardAlipay.setPlayerName(playerName);
		playerCardAlipay.setAlipayName(alipayName);
		String encryptedAlipayCarNo = getToolsService().encrypt(alipayAccount);
		playerCardAlipay.setAlipayAccount(encryptedAlipayCarNo);
		playerCardAlipay.setLockStatus(0);
		playerCardAlipay.setStatus(0);
		playerCardAlipay.setIsDefault(false);
		playerCardAlipay.setCreateTime(new Date());
		AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
		if (sessionUser != null) {
			playerCardAlipay.setCreateBy(sessionUser.getUsername());
		} else {
			playerCardAlipay.setCreateBy("admin");
		}
		boolean save = getPlayerCardAlipayDao().save(playerCardAlipay);
		if (save) {
			// 保存操作日志
			String content = "代理商：" + info.getAgentName() + "，玩家名：" + playerName + " 支付宝账号名：" + alipayName + " 支付宝账号："
					+ alipayAccount;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
		}
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.EDIT_PLAYER_CARD_ALIPAY)
	public WebJson EDIT_PLAYER_CARD_ALIPAY(HttpServletRequest request, @RequestParam(name = "id") Long id,
										   @RequestParam(name = "alipayName") String alipayName,
										   @RequestParam(name = "alipayAccount") String alipayAccount) {
		WebJson webJson = new WebJson();
		PlayerCardAlipay playerCardAlipay = getPlayerCardAlipayDao().getById(id);
		if (playerCardAlipay == null) {
			throw new ServiceException("1", "数据不存在");
		}

		playerCardAlipay.setAlipayName(alipayName);
		String encryptedAlipayCardNo = getToolsService().encrypt(alipayAccount);
		playerCardAlipay.setAlipayAccount(encryptedAlipayCardNo);

		getPlayerCardAlipayDao().update(playerCardAlipay);

		// 保存操作日志
		String content = "id：" + id + " 支付宝账号名：" + alipayName + " 支付宝账号：" + alipayAccount;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UNLOCK_PLAYER_CARD_ALIPAY)
	public WebJson UNLOCK_PLAYER_CARD_ALIPAY(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();

		PlayerCardAlipay byId = getPlayerCardAlipayDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}

		byId.setLockStatus(0);
		byId.setLockedTime(null);

		getPlayerCardAlipayDao().update(byId);

		// 保存操作日志
		String content = "id：" + id + "解锁 ";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.LOCK_PLAYER_CARD_ALIPAY)
	public WebJson LOCK_PLAYER_CARD_ALIPAY(HttpServletRequest request, @RequestParam(name = "id") Long id,
										   @RequestParam(name = "lockedTime") Integer lockedTime) {
		WebJson webJson = new WebJson();

		PlayerCardAlipay byId = getPlayerCardAlipayDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}
		Date date = new Date(System.currentTimeMillis() + lockedTime * 1000 * 3600);

		byId.setLockStatus(1);
		byId.setLockedTime(date);

		getPlayerCardAlipayDao().update(byId);
		// 保存操作日志
		String content = "id：" + id + "锁定 " + lockedTime + "小时";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);

		webJson.setData(true);
		setSuccess(webJson);

		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.DELETE_PLAYER_CARD_ALIPAY)
	public WebJson DELETE_PLAYER_CARD_ALIPAY(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();

		PlayerCardAlipay byId = getPlayerCardAlipayDao().getById(id);
		if (byId == null) {
			throw new ServiceException("1", "数据不存在");
		}

		boolean result = getPlayerCardAlipayDao().delete(byId);
		if (result) {
			String decryptedBankCardNo = getToolsService().decrypt(byId.getAlipayAccount());
			AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(byId.getPlayerId());
			// 保存操作日志
			String content = "id：" + id + "，所属玩家：" + player.getPlayerName() + "，支付宝账号：" + decryptedBankCardNo;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_CARD_M)
	public WebJson SEARCH_PLAYER_CARD_M(HttpServletRequest request,
										@RequestParam(name = "agentNo", required = false) String agentNo,
										@RequestParam(name = "agentName", required = false) String agentName,
										@RequestParam(name = "playerName", required = false) String playerName,
										@RequestParam(name = "name", required = false) String name,
										@RequestParam(name = "lockStatus", required = false) Integer lockStatus,
										@RequestParam(name = "status", required = false) Integer status,
										@RequestParam(name = "address", required = false) String address,
										@RequestParam(name = "page", defaultValue = "0") int page,
										@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo byAgentName = getAgentInfoDao().getByAgentName(agentName);
			if (byAgentName != null) {
				agentNo = byAgentName.getAgentNo();
			} else {
				noPageData(webJson);
				return webJson;
			}
		}
		boolean doSearch = true;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(name)) {
			criterions.add(Restrictions.eq("name", name));
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(address)) {
			String encryptedCardAddress = getToolsService().encrypt(address);
			criterions.add(Restrictions.like("mAddress", "%" + encryptedCardAddress + "%"));
		}
		if (null != status) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (null != lockStatus) {
			try {
				Boolean lockStatusBoolean = (lockStatus == 1);
				criterions.add(Restrictions.eq("lockStatus", lockStatusBoolean));
			} catch (Exception e) {
				throw new ServiceException("1", "该状态不存在");
			}
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerCardMDao().totalCount(criterions);
		List<PlayerCardMVo> list = new ArrayList<PlayerCardMVo>();
		if (totalCount > 0) {
			List<PlayerCardM> resultList = getPlayerCardMDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerCardM entity : resultList) {
				PlayerCardMVo vo = new PlayerCardMVo(entity, getToolsService());
				AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(entity.getAgentNo());
				if (agentInfo != null) {
					vo.setAgentName(agentInfo.getAgentName());
				}
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
				if (playerInfo != null) {
					vo.setPlayerName(playerInfo.getPlayerName());
				}
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_CARD_M)
	public WebJson ADD_PLAYER_CARD_M(HttpServletRequest request,
									 @RequestParam(name = "agentNo", required = false) String agentNo,
									 @RequestParam(name = "playerName", required = false) String playerName,
									 @RequestParam(name = "name") String name, @RequestParam(name = "address") String address) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == agent) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw new ServiceException("1", "代理商号码：" + agentNo + "对应的代理商不存在");
		}

		AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null == player) {
			log.warn(playerName + " 该玩家不存在。");
			throw new ServiceException("1", "用户名：" + playerName + "不存在");
		}
		String encryptedCardAddress = getToolsService().encrypt(address);
		PlayerCardM entity = new PlayerCardM();
		entity.setAgentNo(agentNo);
		entity.setAgentName(player.getAgentName());
		entity.setPlayerId(player.getPlayerId());
		entity.setName(name);
		entity.setMAddress(encryptedCardAddress);
		entity.setLockStatus(false);
		entity.setCreateTime(new Date());
		entity.setStatus(0);
		entity.setIsDefault(false);
		AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
		if (sessionUser != null) {
			entity.setCreateBy(sessionUser.getUsername());
		} else {
			entity.setCreateBy("admin");
		}
		boolean save = getPlayerCardMDao().save(entity);
		if (save) {
			// 保存操作日志
			String content = "代理商：" + player.getAgentName() + "，玩家名：" + playerName + "，名称：" + name + "，M地址：" + address;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.EDIT_PLAYER_CARD_M)
	public WebJson EDIT_PLAYER_CARD_M(HttpServletRequest request, @RequestParam(name = "id") Long id,
									  @RequestParam(name = "name") String name, @RequestParam(name = "address") String address) {
		WebJson webJson = new WebJson();
		PlayerCardM entity = getPlayerCardMDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setName(name);
		String encryptedCardAddress = getToolsService().encrypt(address);
		entity.setMAddress(encryptedCardAddress);
		getPlayerCardMDao().update(entity);
		// 保存操作日志
		String content = "id：" + id + " 名称：" + name + "，M地址：" + address;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ENABLE_PLAYER_CARD_M)
	public WebJson ENABLE_PLAYER_CARD_M(HttpServletRequest request, @RequestParam(name = "id") Long id,
										@RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();
		PlayerCardM entity = getPlayerCardMDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setStatus(status);
		getPlayerCardMDao().update(entity);
		// 保存操作日志
		String desc = CommonStatus.getByCode(status).getDesc();
		String content = "玩家提现M地址：" + entity.getName() + "，状态改为：" + desc;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.LOCK_PLAYER_CARD_M)
	public WebJson LOCK_PLAYER_CARD_M(HttpServletRequest request, @RequestParam(name = "id") Long id,
									  @RequestParam(name = "lockedTime") Integer lockTime) {
		WebJson webJson = new WebJson();
		PlayerCardM entity = getPlayerCardMDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		Date date = new Date(System.currentTimeMillis() + lockTime * 1000 * 3600);
		entity.setLockStatus(true);
		entity.setLockedTime(date);
		getPlayerCardMDao().update(entity);
		// 保存操作日志
		String content = "id：" + id + "锁定" + lockTime + "小时";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UNLOCK_PLAYER_CARD_M)
	public WebJson UNLOCK_PLAYER_CARD_M(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardM entity = getPlayerCardMDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setLockStatus(false);
		entity.setLockedTime(null);
		getPlayerCardMDao().update(entity);
		// 保存操作日志
		String content = "id：" + id + "解锁";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.DELETE_PLAYER_CARD_M)
	public WebJson DELETE_PLAYER_CARD_M(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardM entity = getPlayerCardMDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		getPlayerCardMDao().delete(entity);
		String encryptedCardAddress = entity.getMAddress();
		String decryptedCardAddress = getToolsService().encrypt(encryptedCardAddress);
		AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(entity.getPlayerId());
		// 保存操作日志
		String content = "id：" + id + "，所属玩家：" + player.getPlayerName() + "，M地址：" + decryptedCardAddress;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_CARD_HH5)
	public WebJson SEARCH_PLAYER_CARD_HH5(HttpServletRequest request,
										  @RequestParam(name = "agentNo", required = false) String agentNo,
										  @RequestParam(name = "agentName", required = false) String agentName,
										  @RequestParam(name = "playerName", required = false) String playerName,
										  @RequestParam(name = "name", required = false) String name,
										  @RequestParam(name = "lockStatus", required = false) Integer lockStatus,
										  @RequestParam(name = "status", required = false) Integer status,
										  @RequestParam(name = "address", required = false) String address,
										  @RequestParam(name = "page", defaultValue = "0") int page,
										  @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo byAgentName = getAgentInfoDao().getByAgentName(agentName);
			if (byAgentName != null) {
				agentNo = byAgentName.getAgentNo();
			} else {
				noPageData(webJson);
				return webJson;
			}
		}
		boolean doSearch = true;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(name)) {
			criterions.add(Restrictions.eq("name", name));
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(address)) {
			String encryptedCardAddress = getToolsService().encrypt(address);
			criterions.add(Restrictions.like("hh5Address", "%" + encryptedCardAddress + "%"));
		}
		if (null != status) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (null != lockStatus) {
			try {
				Boolean lockStatusBoolean = (lockStatus == 1);
				criterions.add(Restrictions.eq("lockStatus", lockStatusBoolean));
			} catch (Exception e) {
				throw new ServiceException("1", "该状态不存在");
			}
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerCardHh5Dao().totalCount(criterions);
		List<PlayerCardHh5Vo> list = new ArrayList<PlayerCardHh5Vo>();
		if (totalCount > 0) {
			List<PlayerCardHh5> resultList = getPlayerCardHh5Dao().find(criterions, orders, firstResult, maxResults);
			for (PlayerCardHh5 entity : resultList) {
				PlayerCardHh5Vo vo = new PlayerCardHh5Vo(entity, getToolsService());
				AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(entity.getAgentNo());
				if (agentInfo != null) {
					vo.setAgentName(agentInfo.getAgentName());
				}
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
				if (playerInfo != null) {
					vo.setPlayerName(playerInfo.getPlayerName());
				}
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_CARD_HH5)
	public WebJson ADD_PLAYER_CARD_HH5(HttpServletRequest request,
									   @RequestParam(name = "agentNo", required = false) String agentNo,
									   @RequestParam(name = "playerName", required = false) String playerName,
									   @RequestParam(name = "name") String name, @RequestParam(name = "address") String address) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == agent) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw new ServiceException("1", "代理商号码：" + agentNo + "对应的代理商不存在");
		}
		AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null == player) {
			log.warn(playerName + " 该玩家不存在。");
			throw new ServiceException("1", "用户名：" + playerName + "不存在");
		}
		String encryptedCardAddress = getToolsService().encrypt(address);
		PlayerCardHh5 entity = new PlayerCardHh5();
		entity.setAgentNo(agentNo);
		entity.setAgentName(player.getAgentName());
		entity.setPlayerId(player.getPlayerId());
		entity.setName(name);
		entity.setHh5Address(encryptedCardAddress);
		entity.setLockStatus(false);
		entity.setCreateTime(new Date());
		entity.setStatus(0);
		entity.setIsDefault(false);
		AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
		if (sessionUser != null) {
			entity.setCreateBy(sessionUser.getUsername());
		} else {
			entity.setCreateBy("admin");
		}
		boolean save = getPlayerCardHh5Dao().save(entity);
		if (save) {
			// 保存操作日志
			String content = "代理商：" + player.getAgentName() + "，玩家名：" + playerName + "，名称：" + name + "，HH5地址："
					+ address;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.EDIT_PLAYER_CARD_HH5)
	public WebJson EDIT_PLAYER_CARD_HH5(HttpServletRequest request, @RequestParam(name = "id") Long id,
										@RequestParam(name = "name") String name, @RequestParam(name = "address") String address) {
		WebJson webJson = new WebJson();
		PlayerCardHh5 entity = getPlayerCardHh5Dao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setName(name);
		String encryptedCardAddress = getToolsService().encrypt(address);
		entity.setHh5Address(encryptedCardAddress);
		getPlayerCardHh5Dao().update(entity);
		// 保存操作日志
		String content = "id：" + id + " 名称：" + name + "，HH5地址：" + address;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ENABLE_PLAYER_CARD_HH5)
	public WebJson ENABLE_PLAYER_CARD_HH5(HttpServletRequest request, @RequestParam(name = "id") Long id,
										  @RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();
		PlayerCardHh5 entity = getPlayerCardHh5Dao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setStatus(status);
		getPlayerCardHh5Dao().update(entity);
		// 保存操作日志
		String desc = CommonStatus.getByCode(status).getDesc();
		String content = "玩家提现HH5地址：" + entity.getName() + "，状态改为：" + desc;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.LOCK_PLAYER_CARD_HH5)
	public WebJson LOCK_PLAYER_CARD_HH5(HttpServletRequest request, @RequestParam(name = "id") Long id,
										@RequestParam(name = "lockedTime") Integer lockTime) {
		WebJson webJson = new WebJson();
		PlayerCardHh5 entity = getPlayerCardHh5Dao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		Date date = new Date(System.currentTimeMillis() + lockTime * 1000 * 3600);
		entity.setLockStatus(true);
		entity.setLockedTime(date);
		getPlayerCardHh5Dao().update(entity);
		// 保存操作日志
		String content = "id：" + id + "锁定" + lockTime + "小时";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UNLOCK_PLAYER_CARD_HH5)
	public WebJson UNLOCK_PLAYER_CARD_HH5(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardHh5 entity = getPlayerCardHh5Dao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setLockStatus(false);
		entity.setLockedTime(null);
		getPlayerCardHh5Dao().update(entity);
		// 保存操作日志
		String content = "id：" + id + "解锁";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.DELETE_PLAYER_CARD_HH5)
	public WebJson DELETE_PLAYER_CARD_HH5(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardHh5 entity = getPlayerCardHh5Dao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		getPlayerCardHh5Dao().delete(entity);
		String encryptedCardAddress = entity.getHh5Address();
		String decryptedCardAddress = getToolsService().encrypt(encryptedCardAddress);
		AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(entity.getPlayerId());
		// 保存操作日志
		String content = "id：" + id + "，所属玩家：" + player.getPlayerName() + "，HH5地址：" + decryptedCardAddress;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin(actionType = ActionType.READ)
	@RequestMapping(Route.Agent.SEARCH_PLAYER_CARD_OKG)
	public WebJson SEARCH_PLAYER_CARD_OKG(HttpServletRequest request,
										  @RequestParam(name = "agentNo", required = false) String agentNo,
										  @RequestParam(name = "agentName", required = false) String agentName,
										  @RequestParam(name = "playerName", required = false) String playerName,
										  @RequestParam(name = "name", required = false) String name,
										  @RequestParam(name = "lockStatus", required = false) Integer lockStatus,
										  @RequestParam(name = "status", required = false) Integer status,
										  @RequestParam(name = "address", required = false) String address,
										  @RequestParam(name = "page", defaultValue = "0") int page,
										  @RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		int firstResult = page * size, maxResults = size;
		AdminAccount adminAccount = getSessionUser(request);
		String defaultAgentNo = adminAccount.getAgentNo();
		if (!StringUtils.isEmpty(defaultAgentNo)) {
			agentNo = defaultAgentNo;
		}
		if (!StringUtils.isEmpty(agentName)) {
			AgentInfo byAgentName = getAgentInfoDao().getByAgentName(agentName);
			if (byAgentName != null) {
				agentNo = byAgentName.getAgentNo();
			} else {
				noPageData(webJson);
				return webJson;
			}
		}
		boolean doSearch = true;
		List<Criterion> criterions = new ArrayList<Criterion>();
		if (!StringUtils.isEmpty(playerName)) {
			if (StringUtils.isEmpty(agentNo)) {
				List<Long> playerIds = getAgentPlayerInfoDao().getByPlayerIds(playerName);
				if (CollectionUtils.isEmpty(playerIds)) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.in("playerId", playerIds));
				}
			} else {
				AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
				if (null == player) {
					doSearch = false;
				} else {
					criterions.add(Restrictions.eq("playerId", player.getPlayerId()));
				}
			}
		}
		if (!doSearch) {
			noPageData(webJson);
			return webJson;
		}
		if (!StringUtils.isEmpty(name)) {
			criterions.add(Restrictions.eq("name", name));
		}
		if (!StringUtils.isEmpty(agentNo)) {
			criterions.add(Restrictions.eq("agentNo", agentNo));
		}
		if (!StringUtils.isEmpty(address)) {
			String encryptedCardAddress = getToolsService().encrypt(address);
			criterions.add(Restrictions.like("okgAddress", "%" + encryptedCardAddress + "%"));
		}
		if (null != status) {
			criterions.add(Restrictions.eq("status", status));
		}
		if (null != lockStatus) {
			try {
				Boolean lockStatusBoolean = (lockStatus == 1);
				criterions.add(Restrictions.eq("lockStatus", lockStatusBoolean));
			} catch (Exception e) {
				throw new ServiceException("1", "该状态不存在");
			}
		}
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		int totalCount = getPlayerCardOkgDao().totalCount(criterions);
		List<PlayerCardOkgVo> list = new ArrayList<PlayerCardOkgVo>();
		if (totalCount > 0) {
			List<PlayerCardOkg> resultList = getPlayerCardOkgDao().find(criterions, orders, firstResult, maxResults);
			for (PlayerCardOkg entity : resultList) {
				PlayerCardOkgVo vo = new PlayerCardOkgVo(entity, getToolsService());
				AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(entity.getAgentNo());
				if (agentInfo != null) {
					vo.setAgentName(agentInfo.getAgentName());
				}
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(vo.getPlayerId());
				if (playerInfo != null) {
					vo.setPlayerName(playerInfo.getPlayerName());
				}
				list.add(vo);
			}
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		webJson.setData(data);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ADD_PLAYER_CARD_OKG)
	public WebJson ADD_PLAYER_CARD_OKG(HttpServletRequest request,
									   @RequestParam(name = "agentNo", required = false) String agentNo,
									   @RequestParam(name = "playerName", required = false) String playerName,
									   @RequestParam(name = "name") String name, @RequestParam(name = "address") String address) {
		WebJson webJson = new WebJson();
		AdminAccount adminAccount = getSessionUser(request);
		boolean admin = getAdminUtils().isAdmin(adminAccount);
		if (StringUtils.isEmpty(agentNo)) {
			if (admin) {
				throw new ServiceException("1", "超管创建必须指定代理商，请指定代理商后再创建");
			} else {
				String agentNo1 = adminAccount.getAgentNo();
				if (StringUtils.isEmpty(agentNo1)) {
					throw new ServiceException("1", "当前后台账号没有绑定代理商，请绑定代理商后再创建");
				} else {
					agentNo = agentNo1;
				}
			}
		}
		AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
		if (null == agent) {
			log.warn(agentNo + " 该代理商号码不存在。");
			throw new ServiceException("1", "代理商号码：" + agentNo + "对应的代理商不存在");
		}
		AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
		if (null == player) {
			log.warn(playerName + " 该玩家不存在。");
			throw new ServiceException("1", "用户名：" + playerName + "不存在");
		}
		String encryptedCardAddress = getToolsService().encrypt(address);
		PlayerCardOkg entity = new PlayerCardOkg();
		entity.setAgentNo(agentNo);
		entity.setAgentName(player.getAgentName());
		entity.setPlayerId(player.getPlayerId());
		entity.setName(name);
		entity.setOkgAddress(encryptedCardAddress);
		entity.setLockStatus(false);
		entity.setCreateTime(new Date());
		entity.setStatus(0);
		entity.setIsDefault(false);
		AdminAccount sessionUser = ThreadLocalUtil.getSessionUser();
		if (sessionUser != null) {
			entity.setCreateBy(sessionUser.getUsername());
		} else {
			entity.setCreateBy("admin");
		}
		boolean save = getPlayerCardOkgDao().save(entity);
		if (save) {
			// 保存操作日志
			String content = "代理商：" + player.getAgentName() + "，玩家名：" + playerName + "，名称：" + name + "，OKG地址："
					+ address;
			// 设置 ThreadLocal 变量
			ThreadLocalUtil.setAdminLogContent(content);
			webJson.setData(true);
			setSuccess(webJson);
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.EDIT_PLAYER_CARD_OKG)
	public WebJson EDIT_PLAYER_CARD_OKG(HttpServletRequest request, @RequestParam(name = "id") Long id,
										@RequestParam(name = "name") String name, @RequestParam(name = "address") String address) {
		WebJson webJson = new WebJson();
		PlayerCardOkg entity = getPlayerCardOkgDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setName(name);
		String encryptedCardAddress = getToolsService().encrypt(address);
		entity.setOkgAddress(encryptedCardAddress);
		getPlayerCardOkgDao().update(entity);
		// 保存操作日志
		String content = "id：" + id + " 名称：" + name + "，OKG地址：" + address;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.ENABLE_PLAYER_CARD_OKG)
	public WebJson ENABLE_PLAYER_CARD_OKG(HttpServletRequest request, @RequestParam(name = "id") Long id,
										  @RequestParam(name = "status") Integer status) {
		WebJson webJson = new WebJson();
		PlayerCardOkg entity = getPlayerCardOkgDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setStatus(status);
		getPlayerCardOkgDao().update(entity);
		// 保存操作日志
		String desc = CommonStatus.getByCode(status).getDesc();
		String content = "玩家提现OKG地址：" + entity.getName() + "，状态改为：" + desc;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.LOCK_PLAYER_CARD_OKG)
	public WebJson LOCK_PLAYER_CARD_OKG(HttpServletRequest request, @RequestParam(name = "id") Long id,
										@RequestParam(name = "lockedTime") Integer lockTime) {
		WebJson webJson = new WebJson();
		PlayerCardOkg entity = getPlayerCardOkgDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		Date date = new Date(System.currentTimeMillis() + lockTime * 1000 * 3600);
		entity.setLockStatus(true);
		entity.setLockedTime(date);
		getPlayerCardOkgDao().update(entity);
		// 保存操作日志
		String content = "id：" + id + "锁定" + lockTime + "小时";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.UNLOCK_PLAYER_CARD_OKG)
	public WebJson UNLOCK_PLAYER_CARD_OKG(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardOkg entity = getPlayerCardOkgDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		entity.setLockStatus(false);
		entity.setLockedTime(null);
		getPlayerCardOkgDao().update(entity);
		// 保存操作日志
		String content = "id：" + id + "解锁";
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}

	@CheckLogin
	@RequestMapping(Route.Agent.DELETE_PLAYER_CARD_OKG)
	public WebJson DELETE_PLAYER_CARD_OKG(HttpServletRequest request, @RequestParam(name = "id") Long id) {
		WebJson webJson = new WebJson();
		PlayerCardOkg entity = getPlayerCardOkgDao().getById(id);
		if (entity == null) {
			throw new ServiceException("1", "数据不存在");
		}
		getPlayerCardOkgDao().delete(entity);
		String encryptedCardAddress = entity.getOkgAddress();
		String decryptedCardAddress = getToolsService().encrypt(encryptedCardAddress);
		AgentPlayerInfo player = getRedisService().getAgentPlayerInfo(entity.getPlayerId());
		// 保存操作日志
		String content = "id：" + id + "，所属玩家：" + player.getPlayerName() + "，OKG地址：" + decryptedCardAddress;
		// 设置 ThreadLocal 变量
		ThreadLocalUtil.setAdminLogContent(content);
		webJson.setData(true);
		setSuccess(webJson);
		return webJson;
	}
}