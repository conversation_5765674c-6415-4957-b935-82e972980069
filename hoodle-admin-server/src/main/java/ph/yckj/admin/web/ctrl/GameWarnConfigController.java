package ph.yckj.admin.web.ctrl;

import lombok.extern.slf4j.Slf4j;
import myutil.ErrorUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ph.yckj.admin.aop.ActionType;
import ph.yckj.admin.aop.CheckLogin;
import ph.yckj.admin.service.GameWarnConfigInfoService;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.game.GameWarnConfigInfoVo;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;
import sy.hoodle.base.common.entity.AgentInfo;
import sy.hoodle.base.common.entity.GameWarnConfigInfo;
import sy.hoodle.base.common.dao.GameWarnConfigInfoDao;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 亏损量预警
 */
@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.System.PATH)
public class GameWarnConfigController extends SuperController {

    @Autowired
    private GameWarnConfigInfoService gameWarnConfigInfoService;

    @Autowired
    private GameWarnConfigInfoDao gameWarnConfigInfoDao;

    public GameWarnConfigInfoDao getGameWarnConfigInfoDao() {
        return gameWarnConfigInfoDao;
    }

    /**
     * 查询亏损量预警配置信息
     * @param request
     * @param page
     * @param size
     * @return
     */
    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.GameLottery.SEARCH_EARLY_WARN_CONFIG)
    public WebJson SEARCH_EARLY_WARN_CONFIG(HttpServletRequest request,
                                         @RequestParam(name = "page", defaultValue = "0") int page,
                                         @RequestParam(name = "size", defaultValue = "10") int size) {
        WebJson webJson = new WebJson();
        try {
            List<Criterion> criterions = new ArrayList<>();
            criterions.add(Restrictions.eq("isDelete", 0));
            int totalCount = getGameWarnConfigInfoDao().totalCount(criterions);
            List<GameWarnConfigInfoVo> list = new ArrayList<>();
            if(totalCount > 0){
                List<Order> orders = new ArrayList<Order>();
                orders.add(Order.desc("id"));
                List<GameWarnConfigInfo> resultList = getGameWarnConfigInfoDao().find(criterions, orders,
                        page, size);
                for (GameWarnConfigInfo entity : resultList) {
                    list.add(new GameWarnConfigInfoVo(entity));
                }
            }
            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", totalCount);
            data.put("totalPages", (int) Math.ceil((double) totalCount / size));
            data.put("list", list);
            webJson.setData(data);
            setSuccess(webJson);
        }catch(ServiceException e) {
            setError(webJson, e);
        }catch(Throwable t) {
            setFail(webJson);
            log.error(t.getMessage(), t);
        }
        return webJson;
    }


    /**
     * 修改亏损量预警配置信息
     * @param request
     * @param id
     * @param warnLossAmount
     * @param warnDisableLossVolume
     * @param measurementWay
     * @param automaticDisableWay
     * @return
     */
    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.GameLottery.UPDATE_EARLY_WARN_CONFIG)
    @ResponseBody
    public WebJson UPDATE_EARLY_WARN_CONFIG(HttpServletRequest request, @RequestParam(name = "id") Long id,
                                            @RequestParam(name = "agentNo") String agentNo,
                                            @RequestParam(name = "warnLossAmount") BigDecimal warnLossAmount,
                                            @RequestParam(name = "warnDisableLossVolume") BigDecimal warnDisableLossVolume,
                                            @RequestParam(name = "measurementWay") String measurementWay,
                                            @RequestParam(name = "warnWay", required = false) String warnWay,
                                            @RequestParam(name = "automaticDisableWay") String automaticDisableWay,
                                            @RequestParam(name = "warnClear", required = false) String warnClear,
                                            @RequestParam(name = "warnStatus", required = false) String warnStatus,
                                            @RequestParam(name = "warnRemark", required = false) String warnRemark,
                                            @RequestParam(name = "isDelete", required = false, defaultValue = "0") int isDelete) {
        WebJson webJson = new WebJson();

        try {
            log.info("UPDATE_EARLY_WARN_CONFIG 参数: id={},agentNo={}, warnLossAmount={}, warnDisableLossVolume={}, measurementWay={}, warnWay={}, automaticDisableWay={}, warnClear={}, warnStatus={}, warnRemark={}, isDelete={}",
                    id, agentNo, warnLossAmount, warnDisableLossVolume, measurementWay, warnWay, automaticDisableWay, warnClear, warnStatus, warnRemark, isDelete);
            GameWarnConfigInfo entity = getGameWarnConfigInfoDao().getById(id);
            if (null == entity) {
                throw newException("9999", "亏损量预警记录不存在。");
            }

            // 如果计量方式发生变化，需要校验是否已存在相同计量方式的其他配置
            if (!entity.getMeasurementWay().equals(measurementWay)) {
                List<Criterion> criterions = new ArrayList<>();
                criterions.add(Restrictions.eq("measurementWay", measurementWay));
                criterions.add(Restrictions.eq("isDelete", 0));
                criterions.add(Restrictions.ne("id", id)); // 排除当前记录
                int existingCount = getGameWarnConfigInfoDao().totalCount(criterions);

                if (existingCount > 0) {
                    String measurementWayText = "1".equals(measurementWay) ? "按彩种" : "按玩法";
                    throw newException("9999", measurementWayText + "的预警配置已存在，不能重复添加");
                }
            }

            // 更新实体属性
            entity.setAgentNo(agentNo);
            entity.setWarnLossAmount(warnLossAmount);
            entity.setWarnDisableLossVolume(warnDisableLossVolume);
            entity.setMeasurementWay(measurementWay);
            entity.setAutomaticDisableWay(automaticDisableWay);

            // 安全地设置可选字段
            try {
                if (warnWay != null && !warnWay.trim().isEmpty()) {
                    entity.setWarnWay(warnWay);
                }
                if (warnClear != null && !warnClear.trim().isEmpty()) {
                    entity.setWarnClear(warnClear);
                }
                if (warnStatus != null && !warnStatus.trim().isEmpty()) {
                    entity.setWarnStatus(warnStatus);
                }
                if (warnRemark != null) {
                    entity.setWarnRemark(warnRemark);
                }
                entity.setIsDelete(isDelete);
                entity.setLastUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));

                log.info("准备更新实体: {}", entity);
                getGameWarnConfigInfoDao().update(entity);
                setSuccess(webJson);
            } catch (Exception updateEx) {
                log.error("更新实体时出错: ", updateEx);
                throw updateEx;
            }
        } catch (ServiceException e) {
            log.error("ServiceException: ", e);
            setError(webJson, e);
        } catch (Exception e) {
            log.error("Exception: ", e);
            setFail(webJson);
            String error = ErrorUtils.getStackTraceInfo(e);
            log.error("error:" + error);
        }
        return webJson;
    }

    /**
     * 新增亏损量配置信息
     * @param request
     * @param warnLossAmount
     * @param warnDisableLossVolume
     * @param measurementWay
     * @param warnWay
     * @param automaticDisableWay
     * @param warnClear
     * @param warnStatus
     * @param warnRemark
     * @param isDelete
     * @return
     */
    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.GameLottery.ADD_EARLY_WARN_CONFIG)
    @ResponseBody
    public WebJson ADD_EARLY_WARN_CONFIG(HttpServletRequest request,
                                            @RequestParam(name = "agentNo") String agentNo,
                                            @RequestParam(name = "warnLossAmount") BigDecimal warnLossAmount,
                                            @RequestParam(name = "warnDisableLossVolume") BigDecimal warnDisableLossVolume,
                                            @RequestParam(name = "measurementWay") String measurementWay,
                                            @RequestParam(name = "warnWay", required = false) String warnWay,
                                            @RequestParam(name = "automaticDisableWay") String automaticDisableWay,
                                            @RequestParam(name = "warnClear", required = false) String warnClear,
                                            @RequestParam(name = "warnStatus", required = false) String warnStatus,
                                            @RequestParam(name = "warnRemark", required = false) String warnRemark,
                                            @RequestParam(name = "isDelete", required = false) int isDelete) {
        WebJson webJson = new WebJson();
        try {
            log.info("ADD_EARLY_WARN_CONFIG 参数: agentNo={}, warnLossAmount={}, warnDisableLossVolume={}, measurementWay={}, warnWay={}, automaticDisableWay={}, warnClear={}, warnStatus={}, warnRemark={}, isDelete={}",
                    agentNo, warnLossAmount, warnDisableLossVolume, measurementWay, warnWay, automaticDisableWay, warnClear, warnStatus, warnRemark, isDelete);

            // 校验是否已存在相同计量方式的配置
            List<Criterion> criterions = new ArrayList<>();
            criterions.add(Restrictions.eq("measurementWay", measurementWay));
            criterions.add(Restrictions.eq("agentNo", agentNo));
            criterions.add(Restrictions.eq("isDelete", 0));
            int existingCount = getGameWarnConfigInfoDao().totalCount(criterions);
            // 获取代理商
            AgentInfo agent = getRedisService().getAgentInfo(agentNo);
            if (existingCount > 0) {
                String measurementWayText = "1".equals(measurementWay) ? "按彩种" : "按玩法";
                throw newException("9999", "代理商: " + agent.getAgentName()+ ", " + measurementWayText + "的预警配置已存在，不能重复添加");
            }

            boolean result = gameWarnConfigInfoService.save(agentNo, agent.getAgentName(),  warnLossAmount,  warnDisableLossVolume,  measurementWay,  warnWay,
                    automaticDisableWay,  warnClear,  warnStatus,  warnRemark, isDelete);
            if (result) {
                String measurementWayStr = resultConversion(measurementWay);
                // 保存操作日志
                String content = "计量方式：" + measurementWayStr + "预警亏损量：" + warnLossAmount + "，自动禁用亏损量：" + warnDisableLossVolume;
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);
                setSuccess(webJson);
            } else {
                log.error("保存失败，返回false");
                setFail(webJson);
            }
        } catch (ServiceException e) {
            log.error("ServiceException: ", e);
            setError(webJson, e);
        } catch (Exception e) {
            log.error("Exception: ", e);
            setFail(webJson);
            String error = ErrorUtils.getStackTraceInfo(e);
            log.error("error:" + error);
        }
        return webJson;
    }


    /**
     * 修改亏损量配置信息状态
     * @param request
     * @param id
     * @param warnStatus
     * @return
     */
    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.GameLottery.UPDATE_EARLY_WARN_CONFIG_STATUS)
    @ResponseBody
    public WebJson UPDATE_EARLY_WARN_CONFIG_STATUS(HttpServletRequest request, @RequestParam(name = "id") Long id,
                                            @RequestParam(name = "warnStatus") String warnStatus) {
        WebJson webJson = new WebJson();
        try {
            GameWarnConfigInfo entity = getGameWarnConfigInfoDao().getById(id);
            if (null == entity) {
                throw newException("9999", "亏损量预警记录不存在。");
            }
            boolean result = gameWarnConfigInfoService.updateStatus(id, warnStatus);
            if(result){
                String status = "1".equals(entity.getWarnStatus()) ? "启用" : "禁用";
                String content = "计量方式：" + resultConversion(entity.getMeasurementWay()) + "状态设置：" + status;
                // 设置 ThreadLocal 变量
                ThreadLocalUtil.setAdminLogContent(content);
                setSuccess(webJson);
            }
        } catch (ServiceException e) {
            setError(webJson, e);
        } catch (Exception e) {
            setFail(webJson);
            String error = ErrorUtils.getStackTraceInfo(e);
            log.error("error:" + error);
        }
        return webJson;
    }


    /**
     * 获取预警配置详情
     * @param request
     * @param id
     * @return
     */
    @CheckLogin(actionType = ActionType.READ)
    @RequestMapping(Route.GameLottery.GET_EARLY_WARN_CONFIG)
    @ResponseBody
    public WebJson GET_EARLY_WARN_CONFIG(HttpServletRequest request, @RequestParam(name = "id") Long id) {
        WebJson webJson = new WebJson();
        try {
            GameWarnConfigInfo entity = getGameWarnConfigInfoDao().getById(id);
            if (entity == null) {
                throw newException("9999", "预警配置不存在");
            }
            GameWarnConfigInfoVo vo = new GameWarnConfigInfoVo(entity);
            webJson.setData(vo);
            setSuccess(webJson);
        } catch (ServiceException e) {
            setError(webJson, e);
        } catch (Exception e) {
            setFail(webJson);
            String error = ErrorUtils.getStackTraceInfo(e);
            log.error("error:" + error);
        }
        return webJson;
    }

    /**
     * 设置预警配置关联的彩种
     * @param request
     * @param id
     * @param lotteryList
     * @return
     */
    @CheckLogin(actionType = ActionType.WRITE)
    @RequestMapping(Route.GameLottery.SET_EARLY_WARN_CONFIG_LOTTERY)
    @ResponseBody
    public WebJson SET_EARLY_WARN_CONFIG_LOTTERY(HttpServletRequest request,
                                                 @RequestParam(name = "id") Long id,
                                                 @RequestParam(name = "lotteryList") String lotteryList) {
        WebJson webJson = new WebJson();
        try {
            boolean result = gameWarnConfigInfoService.setLotteryList(id, lotteryList);
            if (result) {
                String content = "设置预警配置彩种，配置ID：" + id + "，彩种列表：" + lotteryList;
                ThreadLocalUtil.setAdminLogContent(content);
                setSuccess(webJson);
            }
        } catch (ServiceException e) {
            setError(webJson, e);
        } catch (Exception e) {
            setFail(webJson);
            String error = ErrorUtils.getStackTraceInfo(e);
            log.error("error:" + error);
        }
        return webJson;
    }





    /**
     * 数据转换
     * @param param
     * @return
     */
    private String resultConversion(String param) {
        String result = "";
        switch (param) {
            case "1":
                result = "彩种";
                break;
            default:
                result = "玩法";
                break;
        }
        return result;
    }
}
