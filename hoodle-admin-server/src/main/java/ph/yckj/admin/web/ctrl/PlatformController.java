package ph.yckj.admin.web.ctrl;

import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;
import myutil.ErrorUtils;
import myutil.HttpUtils;
import ph.yckj.admin.web.helper.Route;
import ph.yckj.admin.web.helper.SuperController;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;

@Slf4j
@RestController
@RequestMapping(Route.PATH)
public class PlatformController extends SuperController {

	@RequestMapping(Route.PLATFORM_RTS)
	@ResponseBody
	public WebJson PLATFORM_RTS(HttpServletRequest request, @RequestParam String agentNo,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "playerName", required = false) String playerName) {
		WebJson webJson = new WebJson();
		try {
			Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
			Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

			Map<String, Object> data = getPlatformService().platformRTS(agentNo, playerName, sDate, eDate);
			webJson.setData(data);
			setSuccess(webJson);
		} catch (ServiceException e) {
			setError(webJson, e);
			return webJson;
		} catch (Exception e) {
			setFail(webJson);
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error: " + error);
		}
		return webJson;
	}

}
