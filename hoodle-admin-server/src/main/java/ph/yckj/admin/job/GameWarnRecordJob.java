package ph.yckj.admin.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import myutil.DateUtils;
import myutil.ErrorUtils;
import myutil.HttpUtils;
import myutil.Moment;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.game.GameLotteryMethodVo;
import ph.yckj.admin.vo.game.GameWarnMethodReportVo;
import ph.yckj.admin.vo.report.GameLotteryReportVo;
import sy.hoodle.base.common.dao.*;
import sy.hoodle.base.common.entity.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GameWarnRecordJob extends AbstractService {

    @Autowired
    private GameMethodReportDao gameMethodReportDao;

    @Autowired
    private GameWarnConfigInfoDao gameWarnConfigInfoDao;

    @Autowired
    private GameWarnRecordDao gameWarnRecordDao;

    @Autowired
    private GameWarnAutoDisableRecordDao gameWarnAutoDisableRecordDao;

    @Autowired
    private GameLotteryMethodDao gameLotteryMethodDao;

    // 暂时不执行，会影响报表数据。
//    @Scheduled(cron = "0 */1 * * * ?")
    public void start() {
        try {
            // 记录开始时间
            LocalDateTime start = LocalDateTime.now();
            Date thisDate = new Date();
            log.info("GameWarnRecord开始监控预警任务任务{}", DateUtils.getFullString(thisDate));
            // 测试用
//            LocalDateTime sDate = LocalDateTime.now().minusMonths(5).with(TemporalAdjusters.lastDayOfMonth());
            // 获取当前日期（不含时间）
            LocalDate today = LocalDate.now();
            // 获取明天日期（不含时间）
            LocalDate tomorrow = today.plusDays(1);
            // 转换为当天00:00的LocalDateTime
            LocalDateTime startDateTime = today.atStartOfDay();
            // 转换为明天00:00的LocalDateTime（时间区间为 [start, end) ）
            LocalDateTime endDateTime = tomorrow.atStartOfDay();
            // 转换为Date类型（兼容旧API）
            Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
            warnLottery(startDate, endDate);
            warnGame(startDate, endDate);
            // 记录结束时间并计算毫秒差
            LocalDateTime end = LocalDateTime.now();
            long diffMs = ChronoUnit.MILLIS.between(start, end);
            log.info("GameWarnRecordJob>>>>>耗时: " + diffMs + " 毫秒");
        } catch(Exception e) {
            String error = ErrorUtils.getStackTraceInfo(e);
            log.error("error:" + error);
        }
    }

    /**
     * 彩种预警记录
     */
    public void warnLottery(Date startDate, Date endDate){
        /**
         * 查询预警配置，有多条记录，倒序取第一条数据
         * 后续如果要颗粒度更小，扫描设置的彩种方案；
         *  1、可以再增加一个一对多的配置表，记录彩种玩法和游戏玩法更小颗粒度的数据
         *  2、查询的时候用报表的玩法code匹配配置表，匹配上了才处理
         */
        List<Criterion> criterionsParam = new ArrayList<>();
        criterionsParam.add(Restrictions.eq("isDelete", 0));
        criterionsParam.add(Restrictions.eq("measurementWay", "1"));
        criterionsParam.add(Restrictions.eq("warnStatus", "1"));
        // 查询配置表
        List<GameWarnConfigInfo> gameWarnConfigInfos = gameWarnConfigInfoDao.find(criterionsParam, null, -1, -1)
                .stream()
                .sorted(Comparator.comparing(
                        GameWarnConfigInfo::getWarnLossAmount,
                        Comparator.reverseOrder()  // 倒序排序
                ))
                .limit(1)  // 取第一条记录
                .collect(Collectors.toList());

        // 预警记录
        List<GameWarnRecord> records = new ArrayList<>();

        // 自动预警记录
        List<GameWarnAutoDisableRecord> autoDisableRecords = new ArrayList<>();

        // 查询当天游戏列表未禁用未删除的数据关联报表列表金额的数据  // 调用接口
//        List<?> gameLotteryMethods = gameLotteryMethodDao.find(startDate, endDate, "0");

        // 查询彩种-调用带代理商彩种报表接口-只查询当天盈亏
        List<GameLotteryReportVo> list = (List<GameLotteryReportVo>) getGameMethodReportDao().searchGameLotteryReportByAgentNo(null, null, null, null,
                startDate, endDate, "profitAmount", "desc", -1, -1);

        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.ge("lastWarnTime",startDate));
        criterions.add(Restrictions.lt("lastWarnTime",endDate));
        criterions.add(Restrictions.eq("isDelete",0));
        // 查询当天的彩种已经预警的数据
        List<GameWarnRecord> gameWarnRecords = gameWarnRecordDao.find(criterions);
        // 查询当天彩种已经自动禁用的数据
        List<Criterion> criterionsDis = new ArrayList<>();
        criterionsDis.add(Restrictions.ge("createTime",startDate));
        criterionsDis.add(Restrictions.lt("createTime",endDate));
        criterionsDis.add(Restrictions.eq("isDelete",0));
        List<GameWarnAutoDisableRecord> gameWarnAutoDisableRecords = gameWarnAutoDisableRecordDao.find(criterionsDis);

        Timestamp currentDate = new Moment().toTimestamp();

//        // 按彩种扭亏为盈
//        swungWarnLottery(gameWarnConfigInfos, listDis, gameWarnRecords, gameWarnAutoDisableRecords);

        Date date = new Date();
        // 按彩种 报表数据
        for(GameLotteryReportVo gameLotteryMethod : list){
            String currentLottery = gameLotteryMethod.getLottery();
            String agentNo = gameLotteryMethod.getAgentNo();
            GameLotteryInfo gamelotteryInfo = getGameLotteryInfoDao().getByLottery(currentLottery);
            gameLotteryMethod.setGameName(gamelotteryInfo.getGameName());

            // 预警表中是否有相同的代理商+彩种+计量方式->有就返回true
            boolean isExist = gameWarnRecords.stream().anyMatch(item -> Objects.equals(item.getAgentNo(), agentNo) && item.getLotteryGameplay().equals(currentLottery) && item.getMeasurementWay().equals("1") && 0 == item.getIsDelete());
            // 自动禁用预警表是否有相同的代理商+彩种+计量方式->有就返回true
            boolean isExistAutoDis = gameWarnAutoDisableRecords.stream().anyMatch(item -> Objects.equals(item.getAgentNo(), agentNo) && item.getDisableLottery().equals(currentLottery)  && item.getMeasurementWay().equals("1") && 0 == item.getIsDelete());

            // 循环配置表数据
            for(GameWarnConfigInfo configInfo : gameWarnConfigInfos){
                boolean isExistRecords = false;
                if(!records.isEmpty()){
                    // 当前list中是否已经有想通的代理商+彩种+计量方式->有就返回true
                    isExistRecords = records.stream().anyMatch(item -> Objects.equals(item.getAgentNo(), agentNo) &&  item.getLotteryGameplay().equals(currentLottery) && item.getMeasurementWay().equals("1") );
                }
                boolean isExistDisRecords = false;
                if(!autoDisableRecords.isEmpty()){
                    // 当前list中是否已经有想通的代理商+彩种+计量方式->有就返回true
                    isExistDisRecords = autoDisableRecords.stream().anyMatch(item -> Objects.equals(item.getAgentNo(), agentNo) &&  item.getDisableLottery().equals(currentLottery) && item.getMeasurementWay().equals("1"));
                }
                /**
                 *
                 * 1、报表金额大于预警表亏损量金额： gameLotteryMethod.getTotalProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) > 0
                 * 2、isExist：预警表中没有相同的代理商+彩种+计量方式,
                 * 3、isExistRecords： list集合中没有已存在的代理商+彩种+计量方式，则新增预警数据
                 */
                if(gameLotteryMethod.getTotalProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) > 0 && !isExist && !isExistRecords){
                    // 设置预警值
                    GameWarnRecord gameWarnRecord = getGameWarnRecord(gameLotteryMethod, configInfo, currentDate, currentLottery);
                    records.add(gameWarnRecord);
                }

                /**
                 * 1、报表金额大于预警表自动禁用亏损量金额 ： gameLotteryMethod.getTotalProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) > 0
                 * 2、isExistAutoDis：当天自动禁用表中没有相同的代理商+彩种+计量方式,
                 * 3、isExistDisRecords： list集合中没有已存在的代理商+彩种+计量方式，则新增自动禁用预警数据
                 */
                if(gameLotteryMethod.getTotalProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) > 0 && !isExistAutoDis && !isExistDisRecords){
                    // 设置禁用自动预警值
                    GameWarnAutoDisableRecord gameWarnAutoDisableRecord = getGameWarnAutoDisableRecord(gameLotteryMethod, configInfo, currentLottery, currentDate);
                    autoDisableRecords.add(gameWarnAutoDisableRecord);
                    // 修改游戏列表彩种状态为禁用 status: 0启动，1禁用
                    getAgentGameLotteryInfoDao().updateStatusByWarn(agentNo, currentLottery, "1");
//                    AgentGameLotteryInfo agentGameLotteryInfo = getAgentGameLotteryInfoDao().getByAgentNoAndLottery(agentNo, currentLottery);
//                    if(agentGameLotteryInfo == null) {
//                        throw newException("500-128");
//                    }
//                    getAgentGameLotteryInfoDao().updateStatus(agentGameLotteryInfo.getGameId(), 1);
                }
                // 预警值扭亏为盈
                if(isExist && gameLotteryMethod.getTotalProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) < 0){
                    // 删除预警数据
                    getGameWarnRecordDao().deleteAgentOrAll(agentNo, currentLottery, date);
                }

                // 自动禁用预警值扭亏为盈
                if(isExistAutoDis && gameLotteryMethod.getTotalProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) < 0){
                    /**
                     * 1、删除自动亏损量预警数据
                     * 2、修改游戏列表禁用状态改为启用
                     */
                    getGameWarnAutoDisableRecordDao().deleteAgentOrAll(agentNo, currentLottery, date);
                    getGameLotteryInfoDao().updateStatus(agentNo, currentLottery, "0");
                }
            }
        }
        // 批量插入
        if(CollUtil.isNotEmpty(records)) gameWarnRecordDao.insert(records);
        if(CollUtil.isNotEmpty(autoDisableRecords)) gameWarnAutoDisableRecordDao.insert(autoDisableRecords);
    }

    /**
     * 按玩法
     */
    public void warnGame(Date startDate, Date endDate) {
        List<Criterion> criterionsParam = new ArrayList<>();
        criterionsParam.add(Restrictions.eq("isDelete", 0));
        criterionsParam.add(Restrictions.eq("measurementWay", "2"));  // 计量方式1彩种，2玩法
        criterionsParam.add(Restrictions.eq("warnStatus", "1"));   // '状态：1,启用, 2,禁用',
        // 查询配置表
        List<GameWarnConfigInfo> gameWarnConfigInfos = gameWarnConfigInfoDao.find(criterionsParam, null, -1, -1)
                .stream()
                .sorted(Comparator.comparing(
                        GameWarnConfigInfo::getWarnLossAmount,
                        Comparator.reverseOrder()  // 倒序排序
                ))
                .limit(1)  // 取第一条记录
                .collect(Collectors.toList());

        List<GameWarnRecord> records = new ArrayList<>();
        List<GameWarnAutoDisableRecord> autoDisableRecords = new ArrayList<>();

        // 查询当天玩法列表未禁用未删除的数据关联报表列表金额 的数据
        List<?> gameMethodReports = gameMethodReportDao.find(startDate, endDate);

        Timestamp currentDate = new Moment().toTimestamp();

        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.ge("lastWarnTime", startDate));
        criterions.add(Restrictions.lt("lastWarnTime", endDate));
        criterions.add(Restrictions.eq("isDelete", 0));
        // 查询当天的预警的游戏数据
        List<GameWarnRecord> gameWarnRecords = gameWarnRecordDao.find(criterions).stream().filter(item -> item.getMeasurementWay().equals("2")).collect(Collectors.toList());

        List<Criterion> criterionsDis = new ArrayList<>();
        criterionsDis.add(Restrictions.ge("createTime", startDate));
        criterionsDis.add(Restrictions.lt("createTime", endDate));
        criterionsDis.add(Restrictions.eq("isDelete", 0));
        List<GameWarnAutoDisableRecord> gameWarnAutoDisableRecords = gameWarnAutoDisableRecordDao.find(criterionsDis);

        List<GameWarnMethodReportVo> list = new ArrayList<>();

        Date date = new Date();

        for (Object obj : gameMethodReports) {
            Object[] row = (Object[]) obj;
            GameWarnMethodReportVo vo = new GameWarnMethodReportVo();
            vo.setGameTypeCode(getStringSafe(row[0]));
            vo.setLottery(getStringSafe(row[1]));
            vo.setMethodType(getStringSafe(row[2]));
            vo.setMethodCode(getStringSafe(row[3]));
            vo.setGameName(getStringSafe(row[4]));
            vo.setMethodName(getStringSafe(row[5]));
            vo.setAgentNo(getStringSafe(row[6]));       // 代理商
            vo.setAgentName(getStringSafe(row[7]));
            vo.setProfitAmount(getBigDecimalSafe(row[8])); // 注意顺序对应select中的字段
            list.add(vo);
        }

//        swungWarnGame(gameWarnConfigInfos, list, gameWarnRecords, gameWarnAutoDisableRecords);

        for (GameWarnMethodReportVo gameMethodReport : list) {
            String currentLottery = gameMethodReport.getLottery();
            String methodCode = gameMethodReport.getMethodCode();
            String methodType = gameMethodReport.getMethodType();
            String agentNo = gameMethodReport.getAgentNo();
            boolean isExist = isExist(gameWarnRecords, currentLottery, methodCode, agentNo);
            boolean disFLag = isDisFLag(gameWarnAutoDisableRecords, currentLottery, methodType, methodCode, agentNo);

            for (GameWarnConfigInfo configInfo : gameWarnConfigInfos) {
                boolean isExistRecords = false;
                if(!records.isEmpty()){
                    // 一个彩种当天加一次
                    isExistRecords = records.stream()
                            .anyMatch(item -> Objects.equals(item.getAgentNo(), agentNo)
                                    && item.getMethodCode().equals(methodCode)
                                    && item.getLotteryGameplay().equals(currentLottery)
                                    && item.getMeasurementWay().equals("2") );
                }

                boolean isExistDisRecords = false;
                if(!autoDisableRecords.isEmpty()){
                    // 一个彩种当天加一次
                    isExistDisRecords = autoDisableRecords.stream()
                            .anyMatch(item -> Objects.equals(item.getAgentNo(), agentNo)
                                    &&  item.getMethodCode().equals(methodCode)
                                    && item.getDisableGameplay().equals(methodType)
                                    && item.getDisableLottery().equals(currentLottery)
                                    && item.getMeasurementWay().equals("2") );
                }

                // 按玩法
                if (gameMethodReport.getProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) > 0 && !isExist && !isExistRecords) {
                    GameWarnRecord gameWarnRecord = getGameWarnRecord(gameMethodReport, configInfo, currentDate, currentLottery, methodCode);
                    records.add(gameWarnRecord);
                }
                if (gameMethodReport.getProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) > 0 && !disFLag && !isExistDisRecords) {
                    // 禁用自动预警记录
                    GameWarnAutoDisableRecord gameWarnAutoDisableRecord = getGameWarnAutoDisableRecord(gameMethodReport, configInfo, currentLottery, methodCode, currentDate);
                    autoDisableRecords.add(gameWarnAutoDisableRecord);
                    // 禁用当前彩种
                    getGameLotteryService().enableOrDisableLotteryMethod(agentNo, currentLottery, methodType, methodCode, 1);
                }

                if(isExist && gameMethodReport.getProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) < 0){
                    // 删除玩法预警数据
                    getGameWarnRecordDao().delete(agentNo, currentLottery, methodCode, date);
                }

                if(disFLag && gameMethodReport.getProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) < 0){
                    /**
                     * 1、删除自动亏损量预警数据
                     * 2、修改游戏玩法列表禁用状态改为启用
                     */
                    getGameWarnAutoDisableRecordDao().delete(agentNo, currentLottery, methodCode, date);
                    gameLotteryMethodDao.updateStatus(methodType, methodCode, 1, "0");
                }

            }
        }
        // 批量插入
        if(CollUtil.isNotEmpty(records)) gameWarnRecordDao.insert(records);
        if(CollUtil.isNotEmpty(autoDisableRecords)) gameWarnAutoDisableRecordDao.insert(autoDisableRecords);
    }

    private static GameWarnAutoDisableRecord getGameWarnAutoDisableRecord(GameWarnMethodReportVo gameMethodReport, GameWarnConfigInfo configInfo, String currentLottery, String methodCode, Timestamp currentDate) {
        GameWarnAutoDisableRecord gameWarnAutoDisableRecord = new GameWarnAutoDisableRecord();
        gameWarnAutoDisableRecord.setConfigId(configInfo.getId());
        gameWarnAutoDisableRecord.setAgentNo(gameMethodReport.getAgentNo());
        gameWarnAutoDisableRecord.setAgentName(gameMethodReport.getAgentName());
        gameWarnAutoDisableRecord.setDisableLottery(currentLottery);
        gameWarnAutoDisableRecord.setMethodCode(methodCode);
        gameWarnAutoDisableRecord.setMethodName(gameMethodReport.getMethodName());
        gameWarnAutoDisableRecord.setDisableLotteryName(gameMethodReport.getGameName());
        gameWarnAutoDisableRecord.setDisableGameplay(gameMethodReport.getMethodType());
        gameWarnAutoDisableRecord.setAutoDisableLossAmount(configInfo.getWarnDisableLossVolume());
        gameWarnAutoDisableRecord.setStatus("1");
        gameWarnAutoDisableRecord.setDisableTime(currentDate);
        gameWarnAutoDisableRecord.setIsDelete(0);
        gameWarnAutoDisableRecord.setMeasurementWay("2");
        gameWarnAutoDisableRecord.setRecoveredTime(currentDate);
        gameWarnAutoDisableRecord.setCreateTime(currentDate);
        gameWarnAutoDisableRecord.setLastUpdateTime(currentDate);
        return gameWarnAutoDisableRecord;
    }

    private static GameWarnRecord getGameWarnRecord(GameWarnMethodReportVo gameMethodReport, GameWarnConfigInfo configInfo, Timestamp currentDate, String currentLottery, String methodCode) {
        GameWarnRecord gameWarnRecord = new GameWarnRecord();
        gameWarnRecord.setConfigId(configInfo.getId());
        gameWarnRecord.setAgentNo(gameMethodReport.getAgentNo());
        gameWarnRecord.setAgentName(gameMethodReport.getAgentName());
        gameWarnRecord.setLastWarnTime(currentDate);
        gameWarnRecord.setLotteryGameplayName(gameMethodReport.getGameName());
        gameWarnRecord.setLotteryGameplay(currentLottery);
        gameWarnRecord.setMethodCode(methodCode);
        gameWarnRecord.setMethodName(gameMethodReport.getMethodName());
        gameWarnRecord.setMeasurementWay("2");
        gameWarnRecord.setWarnLossAmount(configInfo.getWarnLossAmount());
        gameWarnRecord.setStatus("1");
        gameWarnRecord.setIsDelete(0);
        gameWarnRecord.setProcessedBy(null);
        gameWarnRecord.setCreateTime(currentDate);
        gameWarnRecord.setLastUpdateTime(currentDate);
        return gameWarnRecord;
    }

    private static boolean isDisFLag(List<GameWarnAutoDisableRecord> gameWarnAutoDisableRecords, String currentLottery, String methodType, String methodCode, String agentNo) {
        boolean disFLag = gameWarnAutoDisableRecords.stream()
                .filter(item -> "2".equals(item.getMeasurementWay()) && item.getIsDelete() == 0) // 前置确定性条件
                .anyMatch(item ->
                        Objects.equals(item.getAgentNo(), agentNo) &&
                                item.getDisableLottery().equals(currentLottery) &&
                                methodType != null && item.getDisableGameplay() != null &&
                                item.getDisableGameplay().equals(methodType) &&
                                methodCode != null && item.getMethodCode() != null &&
                                item.getMethodCode().contains(methodCode)
                );
        return disFLag;
    }

    private static boolean isExist(List<GameWarnRecord> gameWarnRecords, String currentLottery, String methodCode, String agentNo) {
        boolean isExist = gameWarnRecords.stream()
                .filter(item -> "2".equals(item.getMeasurementWay()) && item.getIsDelete() == 0) // 前置确定性条件
                .anyMatch(item ->
                        Objects.equals(item.getAgentNo(), agentNo) && currentLottery != null && item.getLotteryGameplay() != null && // 关键非空检查
                                item.getLotteryGameplay().equals(currentLottery) &&
                                methodCode != null && item.getMethodCode() != null &&
                                item.getMethodCode().contains(methodCode)
                );
        return isExist;
    }

    /**
     * TODO
     * 按彩种扭亏为盈-取消禁用
     */
//    public void swungWarnLottery(List<GameWarnConfigInfo> gameWarnConfigInfos, List<GameLotteryMethodVo> listReport, List<GameWarnRecord> gameWarnRecords, List<GameWarnAutoDisableRecord> gameWarnAutoDisableRecords) {
//        Date date = new Date();
//        // 彩种
//        for(GameLotteryMethodVo gameLotteryMethod : listReport){
//            for(GameWarnConfigInfo configInfo : gameWarnConfigInfos){
//                String currentLottery = gameLotteryMethod.getLottery();
//                // 查询彩种亏损量预警数据
//                boolean isExist = gameWarnRecords.stream().anyMatch(item -> item.getLotteryGameplay().equals(currentLottery) && item.getMeasurementWay().equals("1") && 0 == item.getIsDelete());
//                // 查询彩种自动亏损量预警数据
//                boolean isExistAutoDis = gameWarnAutoDisableRecords.stream().anyMatch(item -> item.getDisableLottery().equals(currentLottery)  && item.getMeasurementWay().equals("1") && 0 == item.getIsDelete());
//
//                // 预警值扭亏为盈
//                if(isExist && gameLotteryMethod.getProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) < 0){
//                    // 删除预警数据
//                    getGameWarnRecordDao().delete(currentLottery, date);
//                }
//
//                // 自动禁用预警值扭亏为盈
//                if(isExistAutoDis && gameLotteryMethod.getProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) < 0){
//                    /**
//                     * 1、删除自动亏损量预警数据
//                     * 2、修改游戏列表禁用状态改为启用
//                     */
//                    getGameWarnAutoDisableRecordDao().delete(currentLottery, date);
//                    getGameLotteryInfoDao().updateStatus(agentNo, currentLottery, "0");
//                }
//            }
//        }
//    }

    /**
     * TODO
     * 按玩法扭亏为盈-取消禁用
     */
//    public void swungWarnGame(List<GameWarnConfigInfo> gameWarnConfigInfos, List<GameWarnMethodReportVo> list, List<GameWarnRecord> gameWarnRecords, List<GameWarnAutoDisableRecord> gameWarnAutoDisableRecords) {
//        Date date = new Date();
//        for (GameWarnMethodReportVo gameMethodReport : list) {
//            String currentLottery = gameMethodReport.getLottery();
//            String methodCode = gameMethodReport.getMethodCode();
//            String methodType = gameMethodReport.getGameTypeCode();
//            String agnetNo = gameMethodReport.getAgentNo();
//            for(GameWarnConfigInfo configInfo : gameWarnConfigInfos){
//                boolean isExist = isExist(gameWarnRecords, currentLottery, methodCode, agnetNo);
//                boolean disFLag = isDisFLag(gameWarnAutoDisableRecords, methodType, methodCode, agnetNo);
//                if(isExist && gameMethodReport.getProfitAmount().subtract(configInfo.getWarnLossAmount()).compareTo(BigDecimal.ZERO) < 0){
//                    // 删除玩法预警数据
//                    getGameWarnRecordDao().delete(currentLottery, methodCode, date);
//                }
//
//                if(disFLag && gameMethodReport.getProfitAmount().subtract(configInfo.getWarnDisableLossVolume()).compareTo(BigDecimal.ZERO) < 0){
//                    /**
//                     * 1、删除自动亏损量预警数据
//                     * 2、修改游戏玩法列表禁用状态改为启用
//                     */
//                    getGameWarnAutoDisableRecordDao().delete(currentLottery, methodCode, date);
//                    gameLotteryMethodDao.updateStatus(methodType, methodCode, 1, "0");
//                }
//            }
//        }
//    }

    private static GameWarnAutoDisableRecord getGameWarnAutoDisableRecord(GameLotteryReportVo gameLotteryMethod, GameWarnConfigInfo configInfo, String currentLottery, Timestamp currentDate) {
        GameWarnAutoDisableRecord gameWarnAutoDisableRecord = new GameWarnAutoDisableRecord();
        gameWarnAutoDisableRecord.setConfigId(configInfo.getId());
        gameWarnAutoDisableRecord.setAgentNo(gameLotteryMethod.getAgentNo());
        gameWarnAutoDisableRecord.setAgentName(gameLotteryMethod.getAgentName());
        gameWarnAutoDisableRecord.setDisableLottery(currentLottery);
        gameWarnAutoDisableRecord.setDisableLotteryName(gameLotteryMethod.getGameName());
        gameWarnAutoDisableRecord.setDisableGameplay(null);
        gameWarnAutoDisableRecord.setAutoDisableLossAmount(configInfo.getWarnDisableLossVolume());
        gameWarnAutoDisableRecord.setStatus("1");
        gameWarnAutoDisableRecord.setMeasurementWay("1");
        gameWarnAutoDisableRecord.setDisableTime(currentDate);
        gameWarnAutoDisableRecord.setIsDelete(0);
        gameWarnAutoDisableRecord.setRecoveredTime(currentDate);
        gameWarnAutoDisableRecord.setCreateTime(currentDate);
        gameWarnAutoDisableRecord.setLastUpdateTime(currentDate);
        return gameWarnAutoDisableRecord;
    }

    private static GameWarnRecord getGameWarnRecord(GameLotteryReportVo gameLotteryMethod, GameWarnConfigInfo configInfo, Timestamp currentDate, String currentLottery) {
        GameWarnRecord gameWarnRecord = new GameWarnRecord();
        gameWarnRecord.setConfigId(configInfo.getId());
        gameWarnRecord.setAgentNo(gameLotteryMethod.getAgentNo());
        gameWarnRecord.setAgentName(gameLotteryMethod.getAgentName());
        gameWarnRecord.setLastWarnTime(currentDate);
        gameWarnRecord.setLotteryGameplay(currentLottery);
        gameWarnRecord.setLotteryGameplayName(gameLotteryMethod.getGameName());
        gameWarnRecord.setWarnLossAmount(configInfo.getWarnLossAmount());
        gameWarnRecord.setStatus("1");
        gameWarnRecord.setMeasurementWay("1");
        gameWarnRecord.setIsDelete(0);
        gameWarnRecord.setProcessedBy(null);
        gameWarnRecord.setCreateTime(currentDate);
        gameWarnRecord.setLastUpdateTime(currentDate);
        return gameWarnRecord;
    }


    public Date getDatePart(Date date) {
        // 转为LocalDate（提取年月日）
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        // 转回Date（时间自动置零）
        return Date.from(
                localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()
        );
    }

    private static String getStringSafe(Object value) {
        return null == value ? "" : String.valueOf(value);
    }

    private static BigDecimal getBigDecimalSafe(Object obj) {
        if (null == obj) return BigDecimal.ZERO; // 空值返回0
        if (obj instanceof BigDecimal) return (BigDecimal) obj;
        try {
            return new BigDecimal(obj.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO; // 格式错误兜底
        }
    }
}
