package ph.yckj.admin.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import lottery.utils.open.OpenTime;
import myutil.DateUtils;
import myutil.Moment;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ph.yckj.admin.enums.PromoteConfigType;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.admin.vo.CommissionConfig;
import ph.yckj.admin.vo.RebateConfig;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;
import ph.yckj.common.enums.ExecuteType;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GameJob extends AbstractService {

//	private final static Log log = LogFactory.getLog(GameJob.class);


	@Autowired
	private TotalReportService totalReportService;


//	@Scheduled(cron = "55 1/2 * * * ?")
	public void genernateJob1() {
		try {
			generate2sLottery();
		} catch (Exception e) {
			log.error("生成开奖号码定时任务启动失败", e);
		}
	}

//	@Scheduled(cron = "55 4/5 * * * ?")
	public void genernateJob2() {
		try {
			generate5sLottery();
		} catch (Exception e) {
			log.error("生成开奖号码定时任务启动失败", e);
		}
	}

	private void generate2sLottery() throws InterruptedException {
		List<GameLotteryOpenCode> codes = new ArrayList<>();
		getRedisService().getGameLotteryInfoList().forEach(l -> {
			switch (l.getLottery()) {
			case "dzssc":
				codes.add(generateCode(l, generateNumbers(0, 8, 5, true, false)));
				break;
			case "dzk3":
				codes.add(generateCode(l, generateNumbers(1, 6, 3, true, false)));
				break;
			default:
				break;
			}
		});

		Thread.sleep(2000);
		for (GameLotteryOpenCode code : codes) {
			GameLotteryVideo video = new GameLotteryVideo();
			video.setLottery(code.getLottery());
			video.setOpenCode(code.getOpenCode());
			video.setVideoYunStoreLink("openCode/video/video_1.mp4");
			video.setVideoPrivateStoreLink("openCode/video/video_1.mp4");
			video.setVideoFormat(1);
			video.setVideoDuration(28121);
			video.setVideoSize(24703296);
			video.setUseStatus(0);
			video.setBanStatus(0);
			video.setUseTimes(0);
			video.setRemark("系统生成");
			video.setCreateBy("admin");
			video.setCreateTime(new Moment().toTimestamp());
			video.setUpdateTime(video.getCreateTime());
			video.setIsDelete(0);
			getGameLotteryVideoDao().add(video);
		}
	}

	private void generate5sLottery() throws InterruptedException {
		List<GameLotteryOpenCode> codes = new ArrayList<>();

		getRedisService().getGameLotteryInfoList().forEach(l -> {
			switch (l.getLottery()) {
			case "dzpk10":
				codes.add(generateCode(l, generateNumbers(1, 10, 10, false, false)));
				break;
			case "dzkl8":
				codes.add(generateCode(l, generateNumbers(1, 8, 8, false, false)));
				break;
			case "dzlhc":
				codes.add(generateCode(l, generateNumbers(1, 49, 7, false, true)));
				break;
			default:
				break;
			}
		});

		Thread.sleep(5000);
		for (GameLotteryOpenCode code : codes) {
			GameLotteryVideo video = new GameLotteryVideo();
			video.setLottery(code.getLottery());
			video.setOpenCode(code.getOpenCode());
			video.setVideoYunStoreLink("openCode/video/video_1.mp4");
			video.setVideoPrivateStoreLink("openCode/video/video_1.mp4");
			video.setVideoFormat(1);
			video.setVideoDuration(28121);
			video.setVideoSize(24703296);
			video.setUseStatus(0);
			video.setBanStatus(0);
			video.setUseTimes(0);
			video.setRemark("系统生成");
			video.setCreateBy("admin");
			video.setCreateTime(new Moment().toTimestamp());
			video.setUpdateTime(video.getCreateTime());
			video.setIsDelete(0);
			getGameLotteryVideoDao().add(video);
		}
	}

	private GameLotteryOpenCode generateCode(GameLotteryInfo gameLotteryInfo, List<String> numbers) {
		String time = new Moment().toSimpleTime();
		GameLotteryOpenCode code = new GameLotteryOpenCode();
		OpenTime openTimeByTime = getServiceUtils().getOpenTimeByTime(gameLotteryInfo, time);
		code.setLottery(gameLotteryInfo.getLottery());
		code.setGameIssue(openTimeByTime.getIssue());
		code.setOpenTime(DateUtils.addDate("ss", 3, DateUtils.convert(openTimeByTime.getOpenTime())));
		code.setStopTime(DateUtils.convert(openTimeByTime.getStopTime()));
		code.setOpenCode(String.join(",", numbers));
		code.setSettleStatus(0);
		code.setOpenResult("");
		code.setSettleTime(DateUtils.addDate("ss", 3, new Moment().toDate()));
		return code;
	}

	private List<String> generateNumbers(int min, int max, int count, boolean allowDuplicates, boolean padZero) {
		Random random = new Random();
		Set<Integer> numberSet = new LinkedHashSet<>();
		List<String> numberList = new ArrayList<>();
		while (numberList.size() < count) {
			int number = random.nextInt((max - min) + 1) + min;
			if (allowDuplicates || numberSet.add(number)) {
				String formattedNumber = padZero ? String.format("%02d", number) : String.valueOf(number);
				numberList.add(formattedNumber);
			}
		}
		return numberList;
	}

	// @Scheduled(cron = "15 4/5 * * * ?")
	public void killOpenCodeJob1() {
		List<GameLotteryOpenCode> list = getGameLotteryOpenCodeDao().listBySettleStatus(-1);
		log.info("获取待推送开奖视频流共计条数为：" + list.size());
		for (GameLotteryOpenCode t : list) {
//			 log.info(t.getLottery() + "(" + t.getGameIssue() +")，推送开奖视频：" + t.getOpenResult());
			getGameLotteryService().pushGameLotteryVideo(t.getId());
		}
	}

	@Scheduled(cron = "1 2/2 * * * ?")
	public void job1() {
		List<GameLotteryOpenCode> list = getGameLotteryOpenCodeDao().listBySettleStatus(-1);
		for (GameLotteryOpenCode t : list) {
			String lottery = t.getLottery();
			if("dzpc28".equals(lottery)) {
				continue;
			}
			if ("dzpk10".equals(lottery) || "dzkl8".equals(lottery) || "dzlhc".equals(lottery)) {
				continue;
			}
			getGameLotteryOpenCodeDao().updateSettleStatus(lottery, t.getGameIssue(), GameLotteryOpenCode.SETTLE_STATUS_WAITING);
			getRedisService().delGameOpenCodeList(lottery);
		}
	}

	@Scheduled(cron = "1 5/5 * * * ?")
	public void job2() {
		List<GameLotteryOpenCode> list = getGameLotteryOpenCodeDao().listBySettleStatus(-1);
		for (GameLotteryOpenCode t : list) {
			String lottery = t.getLottery();
			if("dzpc28".equals(lottery)) {
				continue;
			}
			if ("dzssc".equals(lottery) || "dzk3".equals(lottery)) {
				continue;
			}
			getGameLotteryOpenCodeDao().updateSettleStatus(lottery, t.getGameIssue(), GameLotteryOpenCode.SETTLE_STATUS_WAITING);
			getRedisService().delGameOpenCodeList(lottery);
		}
	}


	//	@Scheduled(fixedDelay = 1000 * 60 * 10)
	@Scheduled(cron = "0 5 10 * * ?")
	public void playerRebateJob() {
		try {
			log.info("返水奖励计算开始");
			generateRebate();
			log.info("返水奖励计算完成");
		} catch (Exception e) {
			log.error("返水奖励计算失败", e);
		}
	}

	private List<AgentPromoteConfig> getNeedGeneratePromoteConfig(PromoteConfigType PromoteConfigType) {
		List<AgentPromoteConfig> needGenerateList = new ArrayList<>();
		List<AgentPromoteConfig> agentPromoteConfigs = getAgentPromoteConfigDao().listAll();
		if (CollectionUtils.isEmpty(agentPromoteConfigs)) {
			return needGenerateList;
		}
		needGenerateList = agentPromoteConfigs.stream().filter(a -> a.getPromoteType() == 1).filter(p -> {
			ExecuteType executeType = ExecuteType.DAY;

			if (PromoteConfigType.REBATE.equals(PromoteConfigType)) {
				executeType = ExecuteType.getByCode(p.getRebateExecute());
			} else if (PromoteConfigType.COMMISSION.equals(PromoteConfigType)) {
				executeType = ExecuteType.getByCode(p.getCommissionExecute());
			}
			return isSettlementDay(executeType);
		}).collect(Collectors.toList());

		return needGenerateList;
	}

	public boolean isSettlementDay(ExecuteType type) {
		LocalDate today = LocalDate.now();

		switch (type) {
			case DAY:
				// 日结：每天都是结算日
				return true;

			case WEEK:
				// 周结：每周的周一是结算日
				return today.getDayOfWeek().getValue() == 1;

			case HALF_MONTH:
				// 半月结：每月的 1 号和 16 号是结算日
				return today.getDayOfMonth() == 1 || today.getDayOfMonth() == 16;

			case MONTH:
				// 月结：每月的第一天是结算日
				return today.getDayOfMonth() == 1;

			case QUARTER:
				// 季度结算：每个季度的第一天（1月1日, 4月1日, 7月1日, 10月1日）是结算日
				int month = today.getMonthValue();
				if (month == 1 || month == 4 || month == 7 || month == 10) {
					return today.getDayOfMonth() == 1;
				}
				return false;

			default:
				return false;
		}
	}

	public Date getBeginDate(ExecuteType type) {
		LocalDate today = LocalDate.now();

		LocalDate beginDate;

		switch (type) {
			case DAY:
				// 日结：开始时间是昨天
				beginDate = today.minusDays(1);
				break;

			case WEEK:
				// 周结：开始时间是上周的周一
				beginDate = today.minusWeeks(1).with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
				break;

			case HALF_MONTH:
				// 半月结：如果是 1 号，开始时间是上个月的 16 号；否则是本月的 1 号
				if (today.getDayOfMonth() == 1) {
					beginDate = today.minusMonths(1).withDayOfMonth(16);  // 上个月的 16 号
				} else {
					beginDate = today.withDayOfMonth(1);  // 本月的 1 号
				}
				break;

			case MONTH:
				// 月结：开始时间是上个月的 1 号
				beginDate = today.minusMonths(1).withDayOfMonth(1);
				break;

			case QUARTER:
				// 季度结算：开始时间是上个季度的第一天
				int month = today.getMonthValue();
				if (month <= 3) {
					beginDate = LocalDate.of(today.getYear() - 1, 10, 1);  // 上个季度的第一天（去年10月1日）
				} else if (month <= 6) {
					beginDate = LocalDate.of(today.getYear(), 1, 1);  // 本年度第一季度的开始（1月1日）
				} else if (month <= 9) {
					beginDate = LocalDate.of(today.getYear(), 4, 1);  // 本年度第二季度的开始（4月1日）
				} else {
					beginDate = LocalDate.of(today.getYear(), 7, 1);  // 本年度第三季度的开始（7月1日）
				}
				break;

			default:
				return null;
		}

		// 将 LocalDate 转换为 java.util.Date
		return Date.from(beginDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
	}

	public Date getEndDate(ExecuteType type) {
		LocalDate today = LocalDate.now();

		LocalDate endDate;

		switch (type) {
			case DAY:
				// 日结：结束时间是今天
				endDate = today;
				break;

			case WEEK:
				// 周结：结束时间是本周的周一
				endDate = today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
				break;

			case HALF_MONTH:
				// 半月结：如果是 1 号，结束时间是本月的1号；否则是本月的 16 号
				if (today.getDayOfMonth() == 1) {
					endDate = today.withDayOfMonth(1);  // 本月1号
				} else {
					endDate = today.withDayOfMonth(16);  // 本月的 16 号
				}
				break;

			case MONTH:
				// 月结：结束时间是上个月的最后一天
				endDate = today.withDayOfMonth(1);
				break;

			case QUARTER:
				// 季度结算：结束时间是上个季度的最后一天
				int month = today.getMonthValue();
				if (month <= 3) {
					endDate = LocalDate.of(today.getYear() - 1, 12, 31);  // 上个季度的最后一天（去年12月31日）
				} else if (month <= 6) {
					endDate = LocalDate.of(today.getYear(), 3, 31);  // 本年度第一季度的结束（3月31日）
				} else if (month <= 9) {
					endDate = LocalDate.of(today.getYear(), 6, 30);  // 本年度第二季度的结束（6月30日）
				} else {
					endDate = LocalDate.of(today.getYear(), 9, 30);  // 本年度第三季度的结束（9月30日）
				}
				break;

			default:
				return null;
		}

		// 将 LocalDate 转换为 java.util.Date
		return Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
	}

	private void generateRebate() {
		List<AgentPromoteConfig> needGeneratePromoteConfig = getNeedGeneratePromoteConfig(PromoteConfigType.REBATE);
		if (CollectionUtils.isEmpty(needGeneratePromoteConfig)) {
			log.info("没有需要结算的返水配置");
			return;
		}

		needGeneratePromoteConfig.forEach(this::generateAgentRebate);
	}

	private void generateAgentRebate(AgentPromoteConfig promoteConfig) {
		try {
			ExecuteType executeType = ExecuteType.getByCode(promoteConfig.getRebateExecute());
			if (executeType == null) {
				log.info("未知结算类型，promoteConfig：{}", promoteConfig);
				return;
			}

			Date beginDate = getBeginDate(executeType);
			Date endDate = getEndDate(executeType);

			if (beginDate == null || endDate == null) {
				log.info("未知结算区间，promoteConfig：{}", promoteConfig);
				return;
			}

			List<RebateConfig> rebateConfigList;
			if (StringUtils.isEmpty(promoteConfig.getRebateConfig())) {
				log.warn("代理商商户号：{}, 的返水配置不存在，不进行处理", promoteConfig.getAgentNo());
				return;
			}
			rebateConfigList = JSONArray.parseArray(promoteConfig.getRebateConfig(), RebateConfig.class);


			if (CollectionUtils.isEmpty(rebateConfigList)) {
				log.info("返水配置为空，promoteConfig：{}", promoteConfig);
				return;
			}

			Map<Long, List<PlatformAgentPlayerReport>> groupedReports = getReportMap(promoteConfig, beginDate, endDate);

			if (CollectionUtils.isEmpty(groupedReports)) {
				log.info("没有报表数据,不需要计算佣金，promoteConfig：{}", promoteConfig);
				return;
			}

			groupedReports.forEach((key, value) -> {

				generatePlayerRebate(key, value, rebateConfigList,promoteConfig, beginDate, endDate);

			});
			log.info("代理商商户号：{},的返水处理完成", promoteConfig.getAgentNo());
		} catch (Exception e) {
			log.error("结算代理商：{}下的返水异常", promoteConfig.getAgentNo(), e);
		}
	}

	private void generatePlayerRebate(Long playerId, List<PlatformAgentPlayerReport> reportList, List<RebateConfig> rebateConfigList, AgentPromoteConfig promoteConfig, Date beginDate, Date endDate) {
		BigDecimal totalBetAmount = reportList.stream().map(PlatformAgentPlayerReport::getBetAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalRechargeAmount = reportList.stream().map(PlatformAgentPlayerReport::getTransferInAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		Boolean activityCondition = promoteConfig.getActivityCondition() != 4;
		JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
		BigDecimal betAmount = jsonObject.getBigDecimal("betAmount") == null ? new BigDecimal(10000) : jsonObject.getBigDecimal("betAmount");
		BigDecimal rechargeAmount = jsonObject.getBigDecimal("rechargeAmount") == null ? new BigDecimal(1000) : jsonObject.getBigDecimal("rechargeAmount");

		Integer activePlayerCount = getAgentPlayerService().getActivePlayerCount(playerId, beginDate, endDate, activityCondition, betAmount, rechargeAmount);


		BigDecimal rebateRate = getRebateRate(rebateConfigList, totalBetAmount, totalRechargeAmount, activePlayerCount);
		if (rebateRate.compareTo(BigDecimal.ZERO) <= 0) {
			log.info("玩家ID：{}，返水lv是0,不需要处理", playerId);
			return;
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			log.info("玩家ID：{}，不存在,不需要处理", playerId);
			return;
		}

		AgentPlayerRebateRecord rebateRecord = new AgentPlayerRebateRecord();
		rebateRecord.setAgentNo(playerInfo.getAgentNo());
		rebateRecord.setAgentName(playerInfo.getAgentName());
		rebateRecord.setAgentType(playerInfo.getAgentType());
		rebateRecord.setPlayerId(playerInfo.getPlayerId());
		rebateRecord.setPlayerName(playerInfo.getPlayerName());
		rebateRecord.setPlayerType(playerInfo.getPlayerType());
		rebateRecord.setPid(playerInfo.getPid());
		rebateRecord.setPids(playerInfo.getPids());
		rebateRecord.setTeamRechargeAmount(totalRechargeAmount);
		rebateRecord.setTeamBetAmount(totalBetAmount);
		rebateRecord.setActivePlayerCount(activePlayerCount);
		rebateRecord.setRebateRate(rebateRate);
		rebateRecord.setRebateAmount(totalBetAmount.multiply(rebateRate));
		rebateRecord.setRebateExecute(promoteConfig.getRebateExecute());
		rebateRecord.setExecuteBeginTime(new Timestamp(beginDate.getTime()));
		rebateRecord.setExecuteEndTime(dateToTimestamp(endDate));
		rebateRecord.setDealTime(getRebateDealTime());
		rebateRecord.setDealMethod(1);
		rebateRecord.setRebateType(1);
		rebateRecord.setCreateBy("admin");
		rebateRecord.setCreateTime(new Moment().toTimestamp());
		rebateRecord.setUpdateTime(rebateRecord.getCreateTime());
		rebateRecord.setIsDelete(0);

		boolean b = getPlayerService().dealRebate(playerInfo,1, rebateRecord);
		if (!b) {
			log.info("玩家ID：{}，返水处理失败,rebateRecord:{}", playerId, rebateRecord);
			return;
		}

		// 无效报表移除
		// totalReportService.begin(playerId, ReportTargetDataType.REBATE);

	}

	private Timestamp dateToTimestamp(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.SECOND, -1);
		Date time = calendar.getTime();
		return new Timestamp(time.getTime());
	}

	private Map<Long, List<PlatformAgentPlayerReport>> getReportMap(AgentPromoteConfig promoteConfig, Date beginDate, Date endDate) {
		Map<Long, List<PlatformAgentPlayerReport>> groupedReports = new HashMap<>();
		List<Criterion> criterions = new ArrayList<Criterion>();
		criterions.add(Restrictions.ge("reportDate", beginDate));
		criterions.add(Restrictions.lt("reportDate", endDate));
		criterions.add(Restrictions.eq("agentNo", promoteConfig.getAgentNo()));
		criterions.add(Restrictions.gt("pid", 0L));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		List<PlatformAgentPlayerReport> agentPlayerGameReports = getPlatformAgentPlayerReportDao().find(criterions, orders);

		if (CollectionUtils.isEmpty(agentPlayerGameReports)) {
			return groupedReports;
		}

		return agentPlayerGameReports.stream()
				.collect(Collectors.groupingBy(PlatformAgentPlayerReport::getPid));
	}



	private Timestamp getRebateDealTime() {
		// 获取当天时间并设置为两点
		LocalDateTime todayAtTwo = LocalDateTime.now().withHour(2).withMinute(0).withSecond(0).withNano(0);

		// 转换为北京时间
		ZonedDateTime beijingTimeAtTwo = todayAtTwo.atZone(ZoneId.of("Asia/Shanghai"));

		// 转换为 Timestamp
		return Timestamp.from(beijingTimeAtTwo.toInstant());
	}

	private BigDecimal getRebateRate(List<RebateConfig> rebateConfigList, BigDecimal totalBetAmount,
			BigDecimal totalRechargeAmount, Integer activePlayerCount) {
		return rebateConfigList.stream()
				.filter(config -> config.getTeamRecharge().compareTo(totalRechargeAmount) <= 0
						&& config.getTeamConsumption().compareTo(totalBetAmount) <= 0
						&& config.getActiveMembers() != null && config.getActiveMembers() <= activePlayerCount)
				.max(Comparator.comparing(RebateConfig::getRebateRate)).map(RebateConfig::getRebateRate)
				.orElse(BigDecimal.ZERO);
	}

		@Scheduled(cron = "0 10 10 * * ?")
//	@Scheduled(fixedDelay = 1000 * 60 * 60 * 24)
	public void playerCommissionJob() {
		try {
			log.info("佣金奖励计算开始");
			generateCommission();
			log.info("佣金奖励计算完成");
		} catch (Exception e) {
			log.error("佣金奖励计算失败", e);
		}
	}


	private void generateCommission() {

		List<AgentPromoteConfig> needGeneratePromoteConfig = getNeedGeneratePromoteConfig(PromoteConfigType.COMMISSION);
		if (CollectionUtils.isEmpty(needGeneratePromoteConfig)) {
			log.info("没有需要结算的佣金配置");
			return;
		}

		needGeneratePromoteConfig.forEach(this::generateAgentCommission);
	}

	private void generateAgentCommission(AgentPromoteConfig promoteConfig) {
		try {
			ExecuteType executeType = ExecuteType.getByCode(promoteConfig.getCommissionExecute());
			if (executeType == null) {
				log.info("未知结算类型，promoteConfig：{}", promoteConfig);
				return;
			}

			Date beginDate = getBeginDate(executeType);
			Date endDate = getEndDate(executeType);

			if (beginDate == null || endDate == null) {
				log.info("未知结算区间，promoteConfig：{}", promoteConfig);
				return;
			}

			List<CommissionConfig> commissionConfigList;
			if (StringUtils.isEmpty(promoteConfig.getCommissionConfig())) {
				log.warn("代理商商户号：{}, 的佣金配置不存在，不进行处理", promoteConfig.getAgentNo());
				return;
			}
			commissionConfigList = JSONArray.parseArray(promoteConfig.getCommissionConfig(), CommissionConfig.class);


			if (CollectionUtils.isEmpty(commissionConfigList)) {
				log.info("佣金配置为空，promoteConfig：{}", promoteConfig);
				return;
			}

			Map<Long, List<PlatformAgentPlayerReport>> groupedReports = getReportMap(promoteConfig, beginDate, endDate);

			if (CollectionUtils.isEmpty(groupedReports)) {
				log.info("没有报表数据,不需要计算佣金，promoteConfig：{}", promoteConfig);
				return;
			}

			groupedReports.forEach((key, value) -> {

				generatePlayerCommission(key, value, commissionConfigList, promoteConfig, beginDate, endDate);

			});
			log.info("代理商商户号：{},的佣金处理完成", promoteConfig.getAgentNo());
		} catch (Exception e) {
			log.error("结算代理商：{}下的佣金异常", promoteConfig.getAgentNo(), e);
		}
	}

	private void generatePlayerCommission(Long playerId, List<PlatformAgentPlayerReport> reportList, List<CommissionConfig> commissionConfigList, AgentPromoteConfig promoteConfig, Date beginDate, Date endDate) {
		BigDecimal totalRechargeAmount = reportList.stream().map(PlatformAgentPlayerReport::getTransferInAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalBetAmount = reportList.stream().map(PlatformAgentPlayerReport::getBetAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalBonusAmount = reportList.stream().map(PlatformAgentPlayerReport::getBonusAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalRebateAmount = reportList.stream().map(PlatformAgentPlayerReport::getRebateAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalPumpAmount = reportList.stream().map(PlatformAgentPlayerReport::getPumpAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);


		BigDecimal totalActivityAmount = reportList.stream().map(PlatformAgentPlayerReport::getActivityAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalProfitAmount = totalBonusAmount.add(totalRebateAmount).add(totalPumpAmount).add(totalActivityAmount).subtract(totalBetAmount);

        Boolean activityCondition = promoteConfig.getActivityCondition() != 4;
        JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
        BigDecimal betAmount = jsonObject.getBigDecimal("betAmount") == null ? new BigDecimal(10000) : jsonObject.getBigDecimal("betAmount");
        BigDecimal rechargeAmount = jsonObject.getBigDecimal("rechargeAmount") == null ? new BigDecimal(1000) : jsonObject.getBigDecimal("rechargeAmount");

        Integer activePlayerCount = getAgentPlayerService().getActivePlayerCount(playerId, beginDate, endDate, activityCondition, betAmount, rechargeAmount);

		BigDecimal commissionRate = getCommissionRate(commissionConfigList, activePlayerCount, totalProfitAmount, totalRechargeAmount);

		if (commissionRate.compareTo(BigDecimal.ZERO) <= 0) {
			log.info("玩家ID：{}，佣金率是0,不需要处理", playerId);
			return;
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			log.info("玩家ID：{}，不存在,不需要处理", playerId);
			return;
		}

		AgentPlayerCommissionRecord commissionRecord = new AgentPlayerCommissionRecord();
		commissionRecord.setAgentNo(playerInfo.getAgentNo());
		commissionRecord.setAgentName(playerInfo.getAgentName());
		commissionRecord.setAgentType(playerInfo.getAgentType());
		commissionRecord.setPlayerId(playerInfo.getPlayerId());
		commissionRecord.setPlayerName(playerInfo.getPlayerName());
		commissionRecord.setPlayerType(playerInfo.getPlayerType());
		commissionRecord.setPid(playerInfo.getPid());
		commissionRecord.setPids(playerInfo.getPids());
		commissionRecord.setTeamBetAmount(totalBetAmount);
		commissionRecord.setTeamRechargeAmount(totalRechargeAmount);
		commissionRecord.setTeamProfitAmount(totalProfitAmount);
		commissionRecord.setCommissionRate(commissionRate);
		commissionRecord.setActivePlayerCount(activePlayerCount);
		commissionRecord.setCommissionAmount(totalProfitAmount.negate().multiply(commissionRate));
		commissionRecord.setCommissionExecute(promoteConfig.getCommissionExecute());
		commissionRecord.setExecuteBeginTime(new Timestamp(beginDate.getTime()));
		commissionRecord.setExecuteEndTime(dateToTimestamp(endDate));
		commissionRecord.setDealTime(getCommissionDealTime());
		commissionRecord.setDealMethod(1);
		commissionRecord.setCommissionType(1);
		commissionRecord.setCreateBy("admin");
		commissionRecord.setCreateTime(new Moment().toTimestamp());
		commissionRecord.setUpdateTime(commissionRecord.getCreateTime());
		commissionRecord.setIsDelete(0);


		boolean b = getPlayerService().dealCommission(playerInfo, 1, commissionRecord);
		if (!b) {
			log.info("玩家ID：{}，佣金处理失败,commissionRecord:{}", playerId, commissionRecord);
			return;
		}

		// 无效报表移除
		// totalReportService.begin(playerId, ReportTargetDataType.PUMP);

	}


	private Timestamp getCommissionDealTime() {

		// 获取当天月第一天时间并设置为两点
		LocalDateTime todayAtTwo = LocalDateTime.now().withHour(2).withMinute(0).withSecond(0).withNano(0);

		// 转换为北京时间
		ZonedDateTime beijingTimeAtTwo = todayAtTwo.atZone(ZoneId.of("Asia/Shanghai"));

		// 转换为 Timestamp
		return Timestamp.from(beijingTimeAtTwo.toInstant());
	}

	private BigDecimal getCommissionRate(List<CommissionConfig> commissionConfigList, Integer teamActivePlayerCount,
			BigDecimal totalProfitAmount, BigDecimal totalRechargeAmount) {

		if(totalProfitAmount.compareTo(BigDecimal.ZERO) >= 0) {
			return BigDecimal.ZERO;
		}
		totalProfitAmount = totalProfitAmount.negate();
		BigDecimal highestPumpRate = BigDecimal.ZERO;

		// 遍历泵送规则列表，选择最高的泵送比率
		for (CommissionConfig rule : commissionConfigList) {
			boolean conditionSatisfied = (totalRechargeAmount != null
					&& totalRechargeAmount.compareTo(rule.getTeamRecharge()) >= 0)
					&& (totalProfitAmount.compareTo(rule.getTeamLoss()) >= 0)
					&& (teamActivePlayerCount != null && teamActivePlayerCount >= rule.getActiveMembers());

			if (conditionSatisfied && rule.getCommissionRate().compareTo(highestPumpRate) > 0) {
				highestPumpRate = rule.getCommissionRate();
			}
		}

		// 如果找到符合条件的规则，按最高比率计算泵送金额
		if (highestPumpRate.compareTo(BigDecimal.ZERO) > 0) {
			return highestPumpRate;
		}else {
			return BigDecimal.ZERO;
		}
	}

	private Integer getActivePlayerCount(Long playerId, Date sDate, Date eDate) {
		List<Criterion> criterions = new ArrayList<Criterion>();

		criterions.add(Restrictions.eq("isDelete", 0));
		criterions.add(Restrictions.eq("pid", playerId));
		criterions.remove(Restrictions.between("lastLoginTime", sDate, eDate));
		return getAgentPlayerInfoDao().totalCount(criterions);
	}

}
