package ph.yckj.admin.job;

import lombok.extern.slf4j.Slf4j;
import myutil.DateUtils;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ph.yckj.admin.service.ContractService;
import ph.yckj.admin.thread.ContractLotterySalaryThread;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.common.util.ContractUtils;
import sy.hoodle.base.common.entity.ContractPlatformConfig;

import java.util.Date;
import java.util.List;

/**
 * 契约扫描任务
 */
@Slf4j
@Component
public class ContractScanJob extends AbstractService {

    @Autowired
    private ContractService contractService;
    @Autowired
    private ContractUtils contractUtils;

    @Scheduled(cron = "0 * * * * ?")
    public void start() {
        try {
            Date thisDate = new Date();
            log.info("contractStart开始监控契约任务{}", DateUtils.getFullString(thisDate));
            List<ContractPlatformConfig> configList = getContractPlatformConfigDao().listAllEnable();
            for(ContractPlatformConfig config : configList) {
                try {
                    String cronExpression = config.getCronSchedule();
                    if(!StringUtils.isEmpty(cronExpression)) {
                        CronExpression expression = new CronExpression(cronExpression);
                        boolean isNow = expression.isSatisfiedBy(thisDate);
                        if(isNow) {
                            log.info("contractStart开始执行契约：{}，编号：{}", config.getContractTitle(),
                                    config.getContractCode());
                            execute(config);
                        }
                    }
                }catch(Exception e) {
                    log.error("contractStart契约 {} 任务执行出错", config.getContractTitle(), e);
                }
            }
        }catch(Exception e) {
            log.error("contractStart执行契约监控任务失败", e);
        }
    }

    public void execute(ContractPlatformConfig config) {
        switch(config.getContractType()) {
            case ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY:
                // 彩票工资
            case ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND:
                // 彩票分红
            case ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY:
                // 三方工资
            case ContractPlatformConfig.CONTRACT_TYPE_THIRD_DIVIDEND:
                // 三方分红
                // 彩票工资/彩票分红/三方工资/三方分红，均使用同一个thread
                try {
                    ContractLotterySalaryThread thisThread = new ContractLotterySalaryThread();
                    thisThread.setContractUtils(contractUtils);
                    thisThread.setContractService(contractService);
                    thisThread.setAgentPlayerInfoDao(getAgentPlayerInfoDao());
                    thisThread.setContractPlayerConfigDao(getContractPlayerConfigDao());
                    thisThread.setContractRecordDao(getContractRecordDao());
                    thisThread.setConfig(config);
                    Thread thread = new Thread(thisThread);
                    thread.start();
                }catch(Exception e) {
                    log.error("执行" + config.getContractTitle() + "出错", e);
                }
                break;
        }
    }
}
