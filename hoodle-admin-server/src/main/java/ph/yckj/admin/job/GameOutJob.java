package ph.yckj.admin.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import myutil.Moment;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ph.yckj.admin.enums.PromoteConfigType;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.admin.vo.CommissionConfig;
import ph.yckj.admin.vo.RebateConfig;
import ph.yckj.common.enums.ExecuteType;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GameOutJob extends AbstractService {

	@Autowired
	private TotalReportService totalReportService;


	//	@Scheduled(fixedDelay = 1000 * 60 * 10)
	@Scheduled(cron = "0 0 10 * * ?")
	public void playerOutRebateJob() {
		try {
			log.info("外部三方返水奖励计算开始");
			generateOutRebate();
			log.info("外部三方返水奖励计算完成");
		} catch (Exception e) {
			log.error("外部三方返水奖励计算失败", e);
		}
	}

	private List<AgentPromoteConfig> getNeedGeneratePromoteConfig(PromoteConfigType PromoteConfigType) {
		List<AgentPromoteConfig> needGenerateList = new ArrayList<>();
		List<AgentPromoteConfig> agentPromoteConfigs = getAgentPromoteConfigDao().listAll();
		if (CollectionUtils.isEmpty(agentPromoteConfigs)) {
			return needGenerateList;
		}
		needGenerateList = agentPromoteConfigs.stream().filter(a -> a.getPromoteType() == 2).filter(p -> {
			ExecuteType executeType = ExecuteType.DAY;

			if (PromoteConfigType.REBATE.equals(PromoteConfigType)) {
				executeType = ExecuteType.getByCode(p.getRebateExecute());
			} else if (PromoteConfigType.COMMISSION.equals(PromoteConfigType)) {
				executeType = ExecuteType.getByCode(p.getCommissionExecute());
			}
			return isSettlementDay(executeType);
		}).collect(Collectors.toList());

		return needGenerateList;
	}

	public boolean isSettlementDay(ExecuteType type) {
		LocalDate today = LocalDate.now();

		switch (type) {
			case DAY:
				// 日结：每天都是结算日
				return true;

			case WEEK:
				// 周结：每周的周一是结算日
				return today.getDayOfWeek().getValue() == 1;

			case HALF_MONTH:
				// 半月结：每月的 1 号和 16 号是结算日
				return today.getDayOfMonth() == 1 || today.getDayOfMonth() == 16;

			case MONTH:
				// 月结：每月的第一天是结算日
				return today.getDayOfMonth() == 1;

			case QUARTER:
				// 季度结算：每个季度的第一天（1月1日, 4月1日, 7月1日, 10月1日）是结算日
				int month = today.getMonthValue();
				if (month == 1 || month == 4 || month == 7 || month == 10) {
					return today.getDayOfMonth() == 1;
				}
				return false;

			default:
				return false;
		}
	}

	public Date getBeginDate(ExecuteType type) {
		LocalDate today = LocalDate.now();

		LocalDate beginDate;

		switch (type) {
			case DAY:
				// 日结：开始时间是昨天
				beginDate = today.minusDays(1);
				break;

			case WEEK:
				// 周结：开始时间是上周的周一
				beginDate = today.minusWeeks(1).with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
				break;

			case HALF_MONTH:
				// 半月结：如果是 1 号，开始时间是上个月的 16 号；否则是本月的 1 号
				if (today.getDayOfMonth() == 1) {
					beginDate = today.minusMonths(1).withDayOfMonth(16);  // 上个月的 16 号
				} else {
					beginDate = today.withDayOfMonth(1);  // 本月的 1 号
				}
				break;

			case MONTH:
				// 月结：开始时间是上个月的 1 号
				beginDate = today.minusMonths(1).withDayOfMonth(1);
				break;

			case QUARTER:
				// 季度结算：开始时间是上个季度的第一天
				int month = today.getMonthValue();
				if (month <= 3) {
					beginDate = LocalDate.of(today.getYear() - 1, 10, 1);  // 上个季度的第一天（去年10月1日）
				} else if (month <= 6) {
					beginDate = LocalDate.of(today.getYear(), 1, 1);  // 本年度第一季度的开始（1月1日）
				} else if (month <= 9) {
					beginDate = LocalDate.of(today.getYear(), 4, 1);  // 本年度第二季度的开始（4月1日）
				} else {
					beginDate = LocalDate.of(today.getYear(), 7, 1);  // 本年度第三季度的开始（7月1日）
				}
				break;

			default:
				return null;
		}

		// 将 LocalDate 转换为 java.util.Date
		return Date.from(beginDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
	}

	public Date getEndDate(ExecuteType type) {
		LocalDate today = LocalDate.now();

		LocalDate endDate;

		switch (type) {
			case DAY:
				// 日结：结束时间是今天
				endDate = today;
				break;

			case WEEK:
				// 周结：结束时间是本周的周一
				endDate = today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
				break;

			case HALF_MONTH:
				// 半月结：如果是 1 号，结束时间是本月的1号；否则是本月的 16 号
				if (today.getDayOfMonth() == 1) {
					endDate = today.withDayOfMonth(1);  // 本月1号
				} else {
					endDate = today.withDayOfMonth(16);  // 本月的 16 号
				}
				break;

			case MONTH:
				// 月结：结束时间是上个月的最后一天
				endDate = today.withDayOfMonth(1);
				break;

			case QUARTER:
				// 季度结算：结束时间是上个季度的最后一天
				int month = today.getMonthValue();
				if (month <= 3) {
					endDate = LocalDate.of(today.getYear() - 1, 12, 31);  // 上个季度的最后一天（去年12月31日）
				} else if (month <= 6) {
					endDate = LocalDate.of(today.getYear(), 3, 31);  // 本年度第一季度的结束（3月31日）
				} else if (month <= 9) {
					endDate = LocalDate.of(today.getYear(), 6, 30);  // 本年度第二季度的结束（6月30日）
				} else {
					endDate = LocalDate.of(today.getYear(), 9, 30);  // 本年度第三季度的结束（9月30日）
				}
				break;

			default:
				return null;
		}

		// 将 LocalDate 转换为 java.util.Date
		return Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
	}

	private void generateOutRebate() {
		List<AgentPromoteConfig> needGeneratePromoteConfig = getNeedGeneratePromoteConfig(PromoteConfigType.REBATE);
		if (CollectionUtils.isEmpty(needGeneratePromoteConfig)) {
			log.info("外部三方没有需要结算的返水配置");
			return;
		}

		needGeneratePromoteConfig.forEach(this::generateAgentRebate);
	}

	private void generateAgentRebate(AgentPromoteConfig promoteConfig) {
		try {
			ExecuteType executeType = ExecuteType.getByCode(promoteConfig.getRebateExecute());
			if (executeType == null) {
				log.info("未知结算类型，promoteConfig：{}", promoteConfig);
				return;
			}

			Date beginDate = getBeginDate(executeType);
			Date endDate = getEndDate(executeType);

			if (beginDate == null || endDate == null) {
				log.info("未知结算区间，promoteConfig：{}", promoteConfig);
				return;
			}

			List<RebateConfig> rebateConfigList;
			if (StringUtils.isEmpty(promoteConfig.getRebateConfig())) {
				log.warn("代理商商户号：{}, 的返水配置不存在，不进行处理", promoteConfig.getAgentNo());
				return;
			}
			rebateConfigList = JSONArray.parseArray(promoteConfig.getRebateConfig(), RebateConfig.class);


			if (CollectionUtils.isEmpty(rebateConfigList)) {
				log.info("返水配置为空，promoteConfig：{}", promoteConfig);
				return;
			}

			Map<Long, List<GameSimulationReport>> groupedReports = getReportMap(promoteConfig, beginDate, endDate);

			if (CollectionUtils.isEmpty(groupedReports)) {
				log.info("没有三方报表数据,不需要计算返水，promoteConfig：{}", promoteConfig);
				return;
			}

			groupedReports.forEach((key, value) -> {

				generatePlayerRebate(key, value, rebateConfigList,promoteConfig, beginDate, endDate);

			});
			log.info("代理商商户号：{},的三方返水处理完成", promoteConfig.getAgentNo());
		} catch (Exception e) {
			log.error("结算代理商：{}下的三方返水异常", promoteConfig.getAgentNo(), e);
		}
	}

	private void generatePlayerRebate(Long playerId, List<GameSimulationReport> reportList, List<RebateConfig> rebateConfigList, AgentPromoteConfig promoteConfig, Date beginDate, Date endDate) {
		BigDecimal totalBetAmount = reportList.stream().map(GameSimulationReport::getValidBetAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalRechargeAmount = reportList.stream().map(GameSimulationReport::getDepositAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		Boolean activityCondition = promoteConfig.getActivityCondition() != 4;
		JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
		BigDecimal betAmount = jsonObject.getBigDecimal("betAmount") == null ? new BigDecimal(10000) : jsonObject.getBigDecimal("betAmount");
		BigDecimal rechargeAmount = jsonObject.getBigDecimal("rechargeAmount") == null ? new BigDecimal(1000) : jsonObject.getBigDecimal("rechargeAmount");

		Integer activePlayerCount = getAgentPlayerService().getActivePlayerCount(promoteConfig.getAgentNo(), playerId, beginDate, endDate, 2);


		BigDecimal rebateRate = getRebateRate(rebateConfigList, totalBetAmount, totalRechargeAmount, activePlayerCount);
		if (rebateRate.compareTo(BigDecimal.ZERO) <= 0) {
			log.info("玩家ID：{}，返水lv是0,不需要处理", playerId);
			return;
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			log.info("玩家ID：{}，不存在,不需要处理", playerId);
			return;
		}

		AgentPlayerRebateRecord rebateRecord = new AgentPlayerRebateRecord();
		rebateRecord.setAgentNo(playerInfo.getAgentNo());
		rebateRecord.setAgentName(playerInfo.getAgentName());
		rebateRecord.setAgentType(playerInfo.getAgentType());
		rebateRecord.setPlayerId(playerInfo.getPlayerId());
		rebateRecord.setPlayerName(playerInfo.getPlayerName());
		rebateRecord.setPlayerType(playerInfo.getPlayerType());
		rebateRecord.setPid(playerInfo.getPid());
		rebateRecord.setPids(playerInfo.getPids());
		rebateRecord.setTeamRechargeAmount(totalRechargeAmount);
		rebateRecord.setTeamBetAmount(totalBetAmount);
		rebateRecord.setActivePlayerCount(activePlayerCount);
		rebateRecord.setRebateRate(rebateRate);
		rebateRecord.setRebateAmount(totalBetAmount.multiply(rebateRate));
		rebateRecord.setRebateExecute(promoteConfig.getRebateExecute());
		rebateRecord.setExecuteBeginTime(new Timestamp(beginDate.getTime()));
		rebateRecord.setExecuteEndTime(dateToTimestamp(endDate));
		rebateRecord.setDealTime(getRebateDealTime());
		rebateRecord.setDealMethod(1);
		rebateRecord.setRebateType(2);
		rebateRecord.setCreateBy("admin");
		rebateRecord.setCreateTime(new Moment().toTimestamp());
		rebateRecord.setUpdateTime(rebateRecord.getCreateTime());
		rebateRecord.setIsDelete(0);

		boolean b = getPlayerService().dealRebate(playerInfo,2, rebateRecord);
		if (!b) {
			log.info("玩家ID：{}，返水处理失败,rebateRecord:{}", playerId, rebateRecord);
			return;
		}

		// 无效报表移除
		// totalReportService.begin(playerId, ReportTargetDataType.REBATE);

	}

	private Timestamp dateToTimestamp(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.SECOND, -1);
		Date time = calendar.getTime();
		return new Timestamp(time.getTime());
	}

	private Map<Long, List<GameSimulationReport>> getReportMap(AgentPromoteConfig promoteConfig, Date beginDate, Date endDate) {
		Map<Long, List<GameSimulationReport>> groupedReports = new HashMap<>();
		List<Criterion> criterions = new ArrayList<Criterion>();
		criterions.add(Restrictions.ge("reportDate", beginDate));
		criterions.add(Restrictions.lt("reportDate", endDate));
		criterions.add(Restrictions.eq("agentNo", promoteConfig.getAgentNo()));
		criterions.add(Restrictions.gt("pid", 0L));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		List<GameSimulationReport> gameSimulationReports = getGameSimulationReportDao().find(criterions, orders);

		if (CollectionUtils.isEmpty(gameSimulationReports)) {
			return groupedReports;
		}

		return gameSimulationReports.stream()
				.collect(Collectors.groupingBy(GameSimulationReport::getPid));
	}



	private Timestamp getRebateDealTime() {
		// 获取当天时间并设置为两点
		LocalDateTime todayAtTwo = LocalDateTime.now().withHour(2).withMinute(0).withSecond(0).withNano(0);

		// 转换为北京时间
		ZonedDateTime beijingTimeAtTwo = todayAtTwo.atZone(ZoneId.of("Asia/Shanghai"));

		// 转换为 Timestamp
		return Timestamp.from(beijingTimeAtTwo.toInstant());
	}

	private BigDecimal getRebateRate(List<RebateConfig> rebateConfigList, BigDecimal totalBetAmount,
			BigDecimal totalRechargeAmount, Integer activePlayerCount) {
		return rebateConfigList.stream()
				.filter(config -> config.getTeamRecharge().compareTo(totalRechargeAmount) <= 0
						&& config.getTeamConsumption().compareTo(totalBetAmount) <= 0
						&& config.getActiveMembers() != null && config.getActiveMembers() <= activePlayerCount)
				.max(Comparator.comparing(RebateConfig::getRebateRate)).map(RebateConfig::getRebateRate)
				.orElse(BigDecimal.ZERO);
	}

		@Scheduled(cron = "0 15 10 * * ?")
//	@Scheduled(fixedDelay = 1000 * 60 * 60 * 24)
	public void playerCommissionJob() {
		try {
			log.info("外部三方佣金奖励计算开始");
			generateOutCommission();
			log.info("外部三方佣金奖励计算完成");
		} catch (Exception e) {
			log.error("外部三方佣金奖励计算失败", e);
		}
	}


	private void generateOutCommission() {

		List<AgentPromoteConfig> needGeneratePromoteConfig = getNeedGeneratePromoteConfig(PromoteConfigType.COMMISSION);
		if (CollectionUtils.isEmpty(needGeneratePromoteConfig)) {
			log.info("外部三方没有需要结算的佣金配置");
			return;
		}

		needGeneratePromoteConfig.forEach(this::generateAgentCommission);
	}

	private void generateAgentCommission(AgentPromoteConfig promoteConfig) {
		try {
			ExecuteType executeType = ExecuteType.getByCode(promoteConfig.getCommissionExecute());
			if (executeType == null) {
				log.info("外部三方未知结算类型，promoteConfig：{}", promoteConfig);
				return;
			}

			Date beginDate = getBeginDate(executeType);
			Date endDate = getEndDate(executeType);

			if (beginDate == null || endDate == null) {
				log.info("外部三方未知结算区间，promoteConfig：{}", promoteConfig);
				return;
			}

			List<CommissionConfig> commissionConfigList;
			if (StringUtils.isEmpty(promoteConfig.getCommissionConfig())) {
				log.warn("外部三方代理商商户号：{}, 的佣金配置不存在，不进行处理", promoteConfig.getAgentNo());
				return;
			}
			commissionConfigList = JSONArray.parseArray(promoteConfig.getCommissionConfig(), CommissionConfig.class);


			if (CollectionUtils.isEmpty(commissionConfigList)) {
				log.info("外部三方佣金配置为空，promoteConfig：{}", promoteConfig);
				return;
			}

			Map<Long, List<GameSimulationReport>> groupedReports = getReportMap(promoteConfig, beginDate, endDate);

			if (CollectionUtils.isEmpty(groupedReports)) {
				log.info("外部三方没有报表数据,不需要计算佣金，promoteConfig：{}", promoteConfig);
				return;
			}

			groupedReports.forEach((key, value) -> {

				generatePlayerCommission(key, value, commissionConfigList, promoteConfig, beginDate, endDate);

			});
			log.info("外部三方代理商商户号：{},的佣金处理完成", promoteConfig.getAgentNo());
		} catch (Exception e) {
			log.error("外部三方结算代理商：{}下的佣金异常", promoteConfig.getAgentNo(), e);
		}
	}

	private void generatePlayerCommission(Long playerId, List<GameSimulationReport> reportList, List<CommissionConfig> commissionConfigList, AgentPromoteConfig promoteConfig, Date beginDate, Date endDate) {
		BigDecimal totalRechargeAmount = reportList.stream().map(GameSimulationReport::getDepositAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalBetAmount = reportList.stream().map(GameSimulationReport::getValidBetAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);


		BigDecimal totalRebateAmount = reportList.stream().map(GameSimulationReport::getRebateAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalPumpAmount = reportList.stream().map(GameSimulationReport::getCommissionAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);


		BigDecimal totalActivityAmount = reportList.stream().map(GameSimulationReport::getActivityAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		BigDecimal totalProfitAmount = reportList.stream().map(GameSimulationReport:: getBonusAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
		totalProfitAmount = totalProfitAmount.add(totalRebateAmount).add(totalPumpAmount).add(totalActivityAmount);

		Boolean activityCondition = promoteConfig.getActivityCondition() != 4;
        JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
        BigDecimal betAmount = jsonObject.getBigDecimal("betAmount") == null ? new BigDecimal(10000) : jsonObject.getBigDecimal("betAmount");
        BigDecimal rechargeAmount = jsonObject.getBigDecimal("rechargeAmount") == null ? new BigDecimal(1000) : jsonObject.getBigDecimal("rechargeAmount");

		Integer activePlayerCount = getAgentPlayerService().getActivePlayerCount(promoteConfig.getAgentNo(), playerId, beginDate, endDate, 2);

		BigDecimal commissionRate = getCommissionRate(commissionConfigList, activePlayerCount, totalProfitAmount, totalRechargeAmount);

		if (commissionRate.compareTo(BigDecimal.ZERO) <= 0) {
			log.info("玩家ID：{}，佣金率是0,不需要处理", playerId);
			return;
		}

		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			log.info("玩家ID：{}，不存在,不需要处理", playerId);
			return;
		}

		AgentPlayerCommissionRecord commissionRecord = new AgentPlayerCommissionRecord();
		commissionRecord.setAgentNo(playerInfo.getAgentNo());
		commissionRecord.setAgentName(playerInfo.getAgentName());
		commissionRecord.setAgentType(playerInfo.getAgentType());
		commissionRecord.setPlayerId(playerInfo.getPlayerId());
		commissionRecord.setPlayerName(playerInfo.getPlayerName());
		commissionRecord.setPlayerType(playerInfo.getPlayerType());
		commissionRecord.setPid(playerInfo.getPid());
		commissionRecord.setPids(playerInfo.getPids());
		commissionRecord.setTeamBetAmount(totalBetAmount);
		commissionRecord.setTeamRechargeAmount(totalRechargeAmount);
		commissionRecord.setTeamProfitAmount(totalProfitAmount);
		commissionRecord.setCommissionRate(commissionRate);
		commissionRecord.setActivePlayerCount(activePlayerCount);
		commissionRecord.setCommissionAmount(totalProfitAmount.negate().multiply(commissionRate));
		commissionRecord.setCommissionExecute(promoteConfig.getCommissionExecute());
		commissionRecord.setExecuteBeginTime(new Timestamp(beginDate.getTime()));
		commissionRecord.setExecuteEndTime(dateToTimestamp(endDate));
		commissionRecord.setDealTime(getCommissionDealTime());
		commissionRecord.setDealMethod(1);
		commissionRecord.setCommissionType(2);
		commissionRecord.setCreateBy("admin");
		commissionRecord.setCreateTime(new Moment().toTimestamp());
		commissionRecord.setUpdateTime(commissionRecord.getCreateTime());
		commissionRecord.setIsDelete(0);


		boolean b = getPlayerService().dealCommission(playerInfo,2 , commissionRecord);
		if (!b) {
			log.info("玩家ID：{}，佣金处理失败,commissionRecord:{}", playerId, commissionRecord);
			return;
		}

		// 无效报表移除
		// totalReportService.begin(playerId, ReportTargetDataType.PUMP);

	}


	private Timestamp getCommissionDealTime() {

		// 获取当天月第一天时间并设置为两点
		LocalDateTime todayAtTwo = LocalDateTime.now().withHour(2).withMinute(0).withSecond(0).withNano(0);

		// 转换为北京时间
		ZonedDateTime beijingTimeAtTwo = todayAtTwo.atZone(ZoneId.of("Asia/Shanghai"));

		// 转换为 Timestamp
		return Timestamp.from(beijingTimeAtTwo.toInstant());
	}

	private BigDecimal getCommissionRate(List<CommissionConfig> commissionConfigList, Integer teamActivePlayerCount,
			BigDecimal totalProfitAmount, BigDecimal totalRechargeAmount) {

		if(totalProfitAmount.compareTo(BigDecimal.ZERO) >= 0) {
			return BigDecimal.ZERO;
		}
		totalProfitAmount = totalProfitAmount.negate();
		BigDecimal highestPumpRate = BigDecimal.ZERO;

		// 遍历泵送规则列表，选择最高的泵送比率
		for (CommissionConfig rule : commissionConfigList) {
			boolean conditionSatisfied = (totalRechargeAmount != null
					&& totalRechargeAmount.compareTo(rule.getTeamRecharge()) >= 0)
					&& (totalProfitAmount.compareTo(rule.getTeamLoss()) >= 0)
					&& (teamActivePlayerCount != null && teamActivePlayerCount >= rule.getActiveMembers());

			if (conditionSatisfied && rule.getCommissionRate().compareTo(highestPumpRate) > 0) {
				highestPumpRate = rule.getCommissionRate();
			}
		}

		// 如果找到符合条件的规则，按最高比率计算泵送金额
		if (highestPumpRate.compareTo(BigDecimal.ZERO) > 0) {
			return highestPumpRate;
		}else {
			return BigDecimal.ZERO;
		}
	}

	private Integer getActivePlayerCount(Long playerId, Date sDate, Date eDate) {
		List<Criterion> criterions = new ArrayList<Criterion>();

		criterions.add(Restrictions.eq("isDelete", 0));
		criterions.add(Restrictions.eq("pid", playerId));
		criterions.remove(Restrictions.between("lastLoginTime", sDate, eDate));
		return getAgentPlayerInfoDao().totalCount(criterions);
	}

}
