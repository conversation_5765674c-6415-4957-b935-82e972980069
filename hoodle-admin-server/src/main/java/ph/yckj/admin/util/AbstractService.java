package ph.yckj.admin.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import ph.yckj.admin.pool.DataFactory;
import ph.yckj.admin.service.*;
import ph.yckj.common.service.PlayerService;
import ph.yckj.common.service.RedisService;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.ServiceUtils;
import sy.hoodle.base.common.dao.GameRecommendationDao;
import sy.hoodle.base.common.dao.OutThirdUserOrderDao;
import sy.hoodle.base.common.dao.PlayerWithdrawLimitDao;
import sy.hoodle.base.common.entity.AgentInfo;
import sy.hoodle.base.common.entity.PlatformLine;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Slf4j
public abstract class AbstractService extends AbstractAdminDao {

	@Autowired
	private DataFactory dataFactory;

	@Autowired
	private RedisService redisService;

	@Autowired
	private ServiceUtils serviceUtils;

	@Autowired
	private AdminUtils adminUtils;

	@Autowired
	private BillService billService;

	@Autowired
	private GameLotteryService gameLotteryService;

	@Autowired
	private PaymentService paymentService;

	@Autowired
	private PlatformService platformService;

	@Autowired
	private ToolsService toolsService;

	@Autowired
	private TCChatService tcChatService;

	@Autowired
	private PlayerService playerService;

	@Autowired
	private PlayerBillService playerBillService;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private AgentPlayerService agentPlayerService;

	@Autowired
	private PlatformAgentPlayerReportService platformAgentPlayerReportService;

	@Autowired
	private ThirdAgentPlayerReportService thirdAgentPlayerReportService;

	@Autowired
	private GameMonopolyService gameMonopolyService;

	@Autowired
	private PlayerWithdrawLimitDao playerWithdrawLimitDao;

	@Autowired
	private GameRecommendationDao gameRecommendationDao;
	@Autowired
	private OutThirdUserOrderDao outThirdUserOrderDao;

	@Autowired
	private GameSimulationService gameSimulationService;
	@Autowired
	private SimulationGameSuperService simulationGameSuperService;
	@Autowired
	private PlatformAgentPlayerCommonService platformAgentPlayerCommonService;
	@Autowired
	private AgentPlayerAccountTypeService agentPlayerAccountTypeService;
	@Autowired
	private GameLotteryProbabilityAnalysisService gameLotteryProbabilityAnalysisService;
	@Autowired
	private GameWarnConfigInfoService gameWarnConfigInfoService;
	@Autowired
	private GameWarnRecordService gameWarnRecordService;
	@Autowired
	private GameWarnAutoDisableRecordService gameWarnAutoDisableRecordService;
	/**
	 * 构造函数
	 */
	protected AbstractService() {

	}

	/**
	 * 生成错误
	 *
	 * @param key
	 * @return
	 */
	public ServiceException newException(String key) {
		return ExceptionUtil.newException(key);
	}

	public ServiceException newException(String key, Object obj) {
		String message = dataFactory.getSysMessage(key);
		ServiceException exception = new ServiceException(key, message, obj);
		return exception;
	}

	public ServiceException newException(String key, String message) {
		if (StringUtils.isEmpty(message)) {
			message = dataFactory.getSysMessage(key);
		}
		ServiceException exception = new ServiceException(key, message);
		return exception;
	}

	/**
	 * 生成错误
	 *
	 * @param key
	 * @param prams
	 * @return
	 */
	public ServiceException newException(String key, Map<String, Object> prams) {
		String message = dataFactory.getSysMessage(key);
		if (!StringUtils.isEmpty(message)) {
			for (Object o : prams.keySet()) {
				String name = String.valueOf(o);
				String value = String.valueOf(prams.get(o));
				message = message.replace("{" + name + "}", String.valueOf(value));
			}
		}
		ServiceException exception = new ServiceException(key, message);
		return exception;
	}

	/**
	 * 根据线路获取平台代理商户号
	 *
	 * @param request
	 * @return
	 */
	protected AgentInfo getAgent(final HttpServletRequest request) {
		String serverName = request.getHeader("X-Forwarded-Host");
		if (StringUtils.isEmpty(serverName)) {
			serverName = request.getHeader("Host");
		}
		if (StringUtils.isEmpty(serverName)) {
			serverName = request.getServerName();
		}
		PlatformLine line = getPlatformLineDao().getByServerLineDomainLink(serverName);
		if (null == line) {
			log.error("访问线路{}不存在...", serverName);
			throw newException("-1", "访问线路不存在！");
		}
		if (StringUtils.isEmpty(line.getAgentNo())) {
			return null;
		}
		if (PlatformLine.LINE_TYPE_SERVER != line.getLineType()) {
			log.info(serverName + "，访问线路不存在！");
			throw newException("-1", "访问线路不存在！");
		}
		if (PlatformLine.STATUS_FORBIDDEN == line.getStatus()) {
			throw newException("-1", "该平台已停用！");
		}
		AgentInfo agent = getRedisService().getAgentInfo(line.getAgentNo());
		if (null == agent) {
			throw newException("-1", "平台该访问线路不存在！");
		}
		if (AgentInfo.AGENT_STATUS_FORBIDDEN == agent.getAgentStatus()) {
			throw newException("-1", "平台该访问线路已停用！");
		}
		return agent;
	}

	public DataFactory getDataFactory() {
		return dataFactory;
	}

	public ServiceUtils getServiceUtils() {
		return serviceUtils;
	}

	public AdminUtils getAdminUtils() {
		return adminUtils;
	}

	public BillService getBillService() {
		return billService;
	}

	public GameLotteryService getGameLotteryService() {
		return gameLotteryService;
	}

	public RedisService getRedisService() {
		return redisService;
	}

	public PaymentService getPaymentService() {
		return paymentService;
	}

	public PlatformService getPlatformService() {
		return platformService;
	}

	public ToolsService getToolsService() {
		return toolsService;
	}

	public TCChatService getTCChatService() {
		return tcChatService;
	}

	public PlayerService getPlayerService() {
		return playerService;
	}

	public PlayerBillService getPlayerBillService() {
		return playerBillService;
	}

	public TCChatService getTcChatService() {
		return tcChatService;
	}

	public ActivityService getActivityService() {
		return activityService;
	}

	public AgentPlayerService getAgentPlayerService() {
		return agentPlayerService;
	}

	public GameMonopolyService getGameMonopolyService() {
		return gameMonopolyService;
	}

	public PlatformAgentPlayerReportService getPlatformAgentPlayerReportService() {
		return platformAgentPlayerReportService;
	}

	public ThirdAgentPlayerReportService getThirdAgentPlayerReportService() {
		return thirdAgentPlayerReportService;
	}

	public GameRecommendationDao getGameRecommendationDao() {
		return gameRecommendationDao;
	}

	public OutThirdUserOrderDao getOutThirdUserOrderDao() {
		return outThirdUserOrderDao;
	}

	public GameSimulationService getGameSimulationService() {
		return gameSimulationService;
	}

	public SimulationGameSuperService getSimulationGameSuperService() {
		return simulationGameSuperService;
	}

	public PlatformAgentPlayerCommonService getPlatformAgentPlayerCommonService() {
		return platformAgentPlayerCommonService;
	}

	public PlayerWithdrawLimitDao getPlayerWithdrawLimitDao() {
		return playerWithdrawLimitDao;
	}

	public AgentPlayerAccountTypeService getAgentPlayerAccountTypeService() {
		return agentPlayerAccountTypeService;
	}

	public GameLotteryProbabilityAnalysisService getGameLotteryProbabilityAnalysisService() {
		return gameLotteryProbabilityAnalysisService;
	}
	public GameWarnConfigInfoService getGameWarnConfigInfoService() {
		return gameWarnConfigInfoService;
	}

	public GameWarnRecordService getGameWarnRecordService() {
		return gameWarnRecordService;
	}

	public GameWarnAutoDisableRecordService getGameWarnAutoDisableRecordService() {
		return gameWarnAutoDisableRecordService;
	}
}