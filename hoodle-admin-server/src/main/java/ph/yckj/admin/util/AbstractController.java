package ph.yckj.admin.util;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import ph.yckj.admin.service.*;

@Getter
public abstract class AbstractController extends AbstractService {

	@Autowired
	private AdminService adminService;

	@Autowired
	private AgentService agentService;
	@Autowired
	private GameLotteryService gameLotteryService;

	@Autowired
	private GameMonopolyService gameMonopolyService;

	@Autowired
	private BillService billService;

	@Autowired
	private GiftService giftService;

	@Autowired
	private SystemService systemService;

	@Autowired
	private ActivityService activityService;
	@Autowired
	private GameSimulationPlatformTypeService gameSimulationPlatformTypeService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private LockAccountRecordService lockAccountRecordService;

	@Autowired
	private GamePumpConfigService gamePumpConfigService;

	/**
	 * 构造函数
	 */
	protected AbstractController() {

	}
}