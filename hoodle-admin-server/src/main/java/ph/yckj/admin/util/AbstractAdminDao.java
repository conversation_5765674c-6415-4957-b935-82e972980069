package ph.yckj.admin.util;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import ph.yckj.admin.dao.*;
import sy.hoodle.base.common.dao.*;
import ph.yckj.common.util.AbstractDao;

@Getter
public abstract class AbstractAdminDao extends AbstractDao {

	@Autowired
	private AdminAccessDao adminAccessDao;
	@Autowired
	private AdminAccountDao adminAccountDao;
	@Autowired
	private AdminAccountAccessDao adminAccountAccessDao;
	@Autowired
	private AdminLogDao adminLogDao;
	@Autowired
	private AdminLoginLogDao adminLoginLogDao;
	@Autowired
	private AdminMenuDao adminMenuDao;
	@Autowired
	private AdminRoleDao adminRoleDao;
	@Autowired
	private AdminRoleAccessDao adminRoleAccessDao;

	@Autowired
	private PushStreamEventDao pushStreamEventDao;

	@Autowired
	private PlayerLoginLogDao playerLoginLogDao;

	@Autowired
	private PlayerActionLogDao playerActionLogDao;

	@Autowired
	private LiveInfoDao liveInfoDao;

	@Autowired
	private GameLotteryProfitRankingDao gameLotteryProfitRankingDao;

	@Autowired
	private AgentBillRecordDao agentBillRecordDao;

	@Autowired
	private GameSimulationAccountDao gameSimulationAccountDao;

	@Autowired
	private GameSimulationPlatformTypeDao gameSimulationPlatformTypeDao;

	@Autowired
	private GameSimulationTransferDao gameSimulationTransferDao;

	@Autowired
	private GameSimulationReportRecrawlDao gameSimulationReportRecrawlDao;

	@Autowired
	private GamePlayerRecordDetailDao gamePlayerRecordDetailDao;

	@Autowired
	private GameSportsRecordDetailDao gameSportsRecordDetailDao;

	@Autowired
	private GamePlatformKYDetailOrderDao gamePlatformKYDetailOrderDao;

	@Autowired
	private GamePlatformDGDetailOrderDao gamePlatformDGDetailOrderDao;

	@Autowired
	private GamePlatformGoldenFDetailOrderDao gamePlatformGoldenFDetailOrderDao;
	@Autowired
	private GameSimulationDao gameSimulationDao;
	@Autowired
	private GameSimulationPlatPlatformTypeDao gameSimulationPlatPlatformTypeDao;
	@Autowired
	private ContractPlatformConfigDao contractPlatformConfigDao;
	@Autowired
	private ContractPlayerConfigDao contractPlayerConfigDao;

	@Autowired
	private AgentPlayerRegistLinkDao agentPlayerRegistLinkDao;

	@Autowired
	private PlayerVerifyCodeDao playerVerifyCodeDao;

	@Autowired
	private PaymentBillRecordDao paymentBillRecordDao;

	@Autowired
	private GamePumpConfigDao gamePumpConfigDao;

	/**
	 * 构造函数
	 */
	protected AbstractAdminDao() {

	}
}