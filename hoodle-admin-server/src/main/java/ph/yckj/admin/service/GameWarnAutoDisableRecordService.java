package ph.yckj.admin.service;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import sy.hoodle.base.common.entity.GameWarnAutoDisableRecord;

import java.util.Date;
import java.util.List;

public interface GameWarnAutoDisableRecordService {

    GameWarnAutoDisableRecord getById(String lotteryGameplay, Date createTime);
    boolean save(GameWarnAutoDisableRecord entity);

    void insert(List<GameWarnAutoDisableRecord> records);

    boolean update(String status, String lotteryGameplay, String methodCode, String methodType,  Date createTime);
    List<GameWarnAutoDisableRecord> find(List<Criterion> criterions, List<Order> orders, int firstResult, int maxResults);
    List<GameWarnAutoDisableRecord> find(List<Criterion> criterions);
}
