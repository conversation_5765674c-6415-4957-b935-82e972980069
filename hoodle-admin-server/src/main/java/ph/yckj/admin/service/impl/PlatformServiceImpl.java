package ph.yckj.admin.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import lombok.extern.slf4j.Slf4j;
import myutil.ErrorUtils;
import myutil.MathUtils;
import myutil.ObjectUtils;
import ph.yckj.admin.service.PlatformService;
import ph.yckj.admin.util.AbstractService;
import sy.hoodle.base.common.entity.AgentInfo;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.ContractPlatformConfig;
import sy.hoodle.base.common.entity.GameSimulationPlatPlatformType;
import sy.hoodle.base.common.entity.PaymentThirdPay;
import sy.hoodle.base.common.entity.PaymentThirdRemit;
import sy.hoodle.base.common.entity.PaymentTransfer;
import sy.hoodle.base.common.entity.PlatformActivityConfig;
import sy.hoodle.base.common.entity.PlatformConfig;
import sy.hoodle.base.common.entity.PlayerRecharge;
import sy.hoodle.base.common.entity.PlayerWithdraw;

@Slf4j
@Service
@Transactional
public class PlatformServiceImpl extends AbstractService implements PlatformService {

	@Override
	public void supplementPlatformConfig(AgentInfo agent) {
		List<PlatformConfig> list = new ArrayList<>();
		// 用户配置
		list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "SYS_CODE", "2000", "最高奖级"));
		list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "SYS_POINT", "5", "最高返点"));
		list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "SYS_CODE_MAX", "2000", "最高可投注奖级"));
		list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "SYS_CODE_MIN", "1900", "最低可投注奖级"));
		list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "SYS_UNIT_MONEY", "1", "单注投注金额"));
		list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "METHOD_TYPE_STANDARD", "false", "标准盘玩法总开关"));
        list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "LOCK_ACCOUNT", "false", "卡单总开关"));
        list.add(new PlatformConfig(agent.getAgentNo(), "GAME_LOTTERY", "LOCK_ACCOUNT_IP", "false", "IP卡单开关"));
		list.add(new PlatformConfig(agent.getAgentNo(), "ACCOUNT", "AUTO_EQUAL_CODE", "1956,1954,1952,1950", "自动开启平级"));
		list.add(new PlatformConfig(agent.getAgentNo(), "ACCOUNT", "ALLOW_TRANSFER_TO_DOWN", "true", "是否允许给下级转账"));
		list.add(new PlatformConfig(agent.getAgentNo(), "ACCOUNT", "ALLOW_TRANSFER_TO_UP", "true", "是否允许给上级转账"));

		for (PlatformConfig config : list) {
			PlatformConfig c = getPlatformConfigDao().getPlatformConfigByGroupAndKey(config.getAgentNo(),
					config.getGroup(), config.getKey());
			if (c == null) {
				getPlatformConfigDao().save(config);
			}
		}
	}

	@Override
	public boolean updateConfig(String agentNo, String group, Map<String, String> data) {
		for (Map.Entry<String, String> param : data.entrySet()) {
			PlatformConfig entity = new PlatformConfig();
			entity.setAgentNo(agentNo);
			entity.setGroup(group);
			entity.setKey(param.getKey());
			entity.setValue(param.getValue());
			entity.setDescription("");
			getPlatformConfigDao().upsert(entity);
		}
		return true;
	}



	@Override
	public Map<String, Object> platformRTS(String agentNo, String playerName, Date sDate, Date eDate) {
		Map<String, Object> data = new HashMap<String, Object>();

		Long playerId = 0L;
		if (!StringUtils.isEmpty(playerName)) {
			AgentPlayerInfo player = getRedisService().getAgentPlayerInfoName(agentNo, playerName);
			if (null == player) {
				throw newException("-1", "即时统计该玩家不存在！");
			}
			playerId = player.getPlayerId();
		}

		setRechargRTSMap(data, agentNo, playerId, sDate, eDate);
		setWithdrawRTSMap(data, agentNo, playerId, sDate, eDate);
		setThirdGameRTSMap(data, agentNo, playerId, sDate, eDate);
		setActivityRTSMap(data, agentNo, playerId, sDate, eDate);
		setSalaryAndDividendRTSMap(data, agentNo, playerId, sDate, eDate);

		double LotteryBetAmount = 0;
		double LotteryBounsAmount = 0;
		double LotteryPumpAmount = 0;
		double LotteryPointAmount = 0;
		double LotteryProfitAmount = 0;
		double TransferInAmount = 0;
		double TransferOutAmount = 0;
		double TransferTotalAmount = 0;

		try {
			List<Integer> billTypes = new ArrayList<Integer>();
			billTypes.add(AgentPlayerAccountBill.BILL_TYPE_BOUNS); // 派奖
			billTypes.add(AgentPlayerAccountBill.BILL_TYPE_CONSUME); // 投注
			billTypes.add(AgentPlayerAccountBill.BILL_TYPE_Rebate); // 抽水
			billTypes.add(AgentPlayerAccountBill.BILL_TYPE_REBATE_CONSUME); // 返点
			billTypes.add(AgentPlayerAccountBill.BILL_TYPE_ADMIN_ADD); // 资金修正（正）
			billTypes.add(AgentPlayerAccountBill.BILL_TYPE_ADMIN_MINUS); // 资金修正（负）
			List<?> totalList = getAgentPlayerAccountBillDao().totalRTSAmount(agentNo, playerId, billTypes, sDate, eDate);
			for (Object o : totalList) {
				Object[] info = (Object[]) o;
				Integer billType = ObjectUtils.toInt(info[0]);
				double amount = ObjectUtils.toDouble(info[1]);
				switch (billType) {
				case AgentPlayerAccountBill.BILL_TYPE_BOUNS:
					LotteryBounsAmount = amount;
					break;
				case AgentPlayerAccountBill.BILL_TYPE_CONSUME:
					LotteryBetAmount = amount;
					break;
				case AgentPlayerAccountBill.BILL_TYPE_Rebate:
					LotteryPumpAmount = amount;
					break;
				case AgentPlayerAccountBill.BILL_TYPE_REBATE_CONSUME:
					LotteryPointAmount = amount;
					break;
				case AgentPlayerAccountBill.BILL_TYPE_ADMIN_ADD:
					TransferInAmount = amount;
					TransferTotalAmount = MathUtils.add(TransferTotalAmount, TransferInAmount);
					break;
				case AgentPlayerAccountBill.BILL_TYPE_ADMIN_MINUS:
					TransferOutAmount = amount;
					TransferTotalAmount = MathUtils.add(TransferTotalAmount, TransferOutAmount);
					break;
				default:
					break;
				}
			}
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("自营彩票即时统计查询异常:" + error);
		}

		try {
			LotteryPumpAmount = getAgentPlayerGameOrderDao().totalPumpAmount(agentNo, playerId, sDate, eDate);
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("彩票抽水即时统计查询异常:" + error);
		}

		double ActityTotalAmount = (double) data.getOrDefault("ActityTotalAmount", 0);
		double SalaryTotalAmount = (double) data.getOrDefault("SalaryTotalAmount", 0);
		double DividendTotalAmount = (double) data.getOrDefault("DividendTotalAmount", 0);
		LotteryProfitAmount = MathUtils.add(LotteryProfitAmount, LotteryBounsAmount);
		LotteryProfitAmount = MathUtils.add(LotteryProfitAmount, LotteryPointAmount);
		LotteryProfitAmount = MathUtils.add(LotteryProfitAmount, ActityTotalAmount);
		LotteryProfitAmount = MathUtils.add(LotteryProfitAmount, SalaryTotalAmount);
		LotteryProfitAmount = MathUtils.add(LotteryProfitAmount, DividendTotalAmount);
		LotteryProfitAmount = MathUtils.subtract(LotteryProfitAmount, LotteryBetAmount);
		data.put("LotteryBetAmount", LotteryBetAmount);
		data.put("LotteryBounsAmount", LotteryBounsAmount);
		data.put("LotteryPumpAmount", LotteryPumpAmount);
		data.put("LotteryPointAmount", LotteryPointAmount);
		data.put("LotteryProfitAmount", LotteryProfitAmount);

		data.put("TransferInAmount", TransferInAmount);
		data.put("TransferOutAmount", TransferOutAmount);
		data.put("TransferTotalAmount", TransferTotalAmount);

		double LotteryAmount = 0;
		try {
			LotteryAmount = getAgentPlayerInfoDao().totalRTSAmount(agentNo, playerId);
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("彩票余额即时统计查询异常:" + error);
		}
		data.put("LotteryAmount", LotteryAmount);

		double ThridGameTotalAmount = (double) data.getOrDefault("ThridGameTotalAmount", 0);
		data.put("TotalAmount", MathUtils.add(LotteryAmount, ThridGameTotalAmount));
		return data;
	}

	/**
	 * 获取存款即时统计数据
	 *
	 * @param data
	 */
	protected void setRechargRTSMap(Map<String, Object> data, String agentNo, Long playerId, Date sDate, Date eDate) {
		double RechargeTotalAmount = 0;
		double RechargeTotalFreeAmount = 0;
		double RechargeSystemInAmount = 0;
		double RechargeSystemInFreeAmount = 0;
		double RechargeTransferInAmount = 0;
		double RechargeTransferInFreeAmount = 0;
		List<Object[]> RechargeList = new ArrayList<Object[]>();
		try {
			List<Integer> methods = new ArrayList<Integer>();
			methods.add(PlayerRecharge.METHOD_THRID); // 三方存款
			methods.add(PlayerRecharge.METHOD_SYSTEM); // 人工上分
			methods.add(PlayerRecharge.METHOD_TRANSFER);// 自营存款
			List<?> totalList = getPlayerRechargeDao().totalRTSAmount(agentNo, playerId, methods, sDate, eDate);
			Map<Integer, List<Object[]>> dataMethodMap = new HashMap<Integer, List<Object[]>>();
			for (Object o : totalList) {
				Object[] info = (Object[]) o;
				int method = ObjectUtils.toInt(info[0]);
				List<Object[]> tmpList = dataMethodMap.getOrDefault(method, new ArrayList<Object[]>());
				tmpList.add(info);
				dataMethodMap.put(method, tmpList);
			}

			// 获取三方存款通道
			List<PaymentThirdPay> payList = getPaymentThirdPayDao().getByAgentNo(agentNo);
			List<Object[]> methodThirdList = dataMethodMap.getOrDefault(PlayerRecharge.METHOD_THRID, new ArrayList<Object[]>());
			if(!CollectionUtils.isEmpty(payList)) {
				Map<Long, Object[]> tmpListMap = new HashMap<Long, Object[]>();
				for (Object o : methodThirdList) {
					Object[] info = (Object[]) o;
					Long payId = ObjectUtils.toLong(info[1]);
					double amount = ObjectUtils.toDouble(info[2]);
					double freeAmount = ObjectUtils.toDouble(info[3]);
					Object[] result = { amount, freeAmount };
					tmpListMap.put(payId, result);
				}
				for (PaymentThirdPay t : payList) {
					Long payId = t.getId();
					double amount = 0, freeAmount = 0;
					Object[] result = (Object[]) tmpListMap.getOrDefault(payId, null);
					if (null != result) {
						amount = ObjectUtils.toDouble(result[0]);
						freeAmount = ObjectUtils.toDouble(result[1]);
					}
					result = new Object[] { t.getName(), amount, freeAmount };
					RechargeList.add(result);
					RechargeTotalAmount = MathUtils.add(RechargeTotalAmount, amount);
					RechargeTotalFreeAmount = MathUtils.add(RechargeTotalFreeAmount, freeAmount);
				}
			}

			// 获取人工上分
			List<Object[]> methodSystemList = dataMethodMap.getOrDefault(PlayerRecharge.METHOD_SYSTEM, new ArrayList<Object[]>());
			if(!CollectionUtils.isEmpty(methodSystemList)) {
				Object[] info = methodSystemList.get(0);
				RechargeSystemInAmount = ObjectUtils.toDouble(info[2]);
				RechargeSystemInFreeAmount = ObjectUtils.toDouble(info[3]);
				RechargeTotalAmount = MathUtils.add(RechargeTotalAmount, RechargeSystemInAmount);
				RechargeTotalFreeAmount = MathUtils.add(RechargeTotalFreeAmount, RechargeSystemInFreeAmount);
			}

			// 获取自营存款通道
			List<PaymentTransfer> transferList = getPaymentTransferDao().getByAgentNo(agentNo);
			List<Object[]> methodTransferList = dataMethodMap.getOrDefault(PlayerRecharge.METHOD_TRANSFER, new ArrayList<Object[]>());
			if(!CollectionUtils.isEmpty(transferList)) {
				Map<Long, Object[]> tmpListMap = new HashMap<Long, Object[]>();
				for (Object o : methodTransferList) {
					Object[] info = (Object[]) o;
					Long payId = ObjectUtils.toLong(info[1]);
					double amount = ObjectUtils.toDouble(info[2]);
					double freeAmount = ObjectUtils.toDouble(info[3]);
					Object[] result = { amount, freeAmount };
					tmpListMap.put(payId, result);
				}
				for (PaymentTransfer t : transferList) {
					Long payId = t.getId();
					double amount = 0, freeAmount = 0;
					Object[] result = (Object[]) tmpListMap.getOrDefault(payId, null);
					if (null != result) {
						amount = ObjectUtils.toDouble(result[0]);
						freeAmount = ObjectUtils.toDouble(result[1]);
					}
					result = new Object[] { t.getName(), amount, freeAmount };
					RechargeList.add(result);
					RechargeTotalAmount = MathUtils.add(RechargeTotalAmount, amount);
					RechargeTotalFreeAmount = MathUtils.add(RechargeTotalFreeAmount, freeAmount);
				}
			}
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("存款通道及时统计查询异常:" + error);
		}

		try {
			// 获取上下级转账（转入）数据
			RechargeTransferInAmount = getAgentPlayerAccountBillDao().totalTransferInAmount(agentNo, playerId, sDate, eDate);
			RechargeTotalAmount = MathUtils.add(RechargeTotalAmount, RechargeTransferInAmount);
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("上下级转账（转入）即时统计查询异常:" + error);
		}

		data.put("RechargeList", RechargeList);
		data.put("RechargeTotalAmount", RechargeTotalAmount);
		data.put("RechargeTotalFreeAmount", RechargeTotalFreeAmount);
		data.put("RechargeSystemInAmount", RechargeSystemInAmount);
		data.put("RechargeSystemInFreeAmount", RechargeSystemInFreeAmount);
		data.put("RechargeTransferInAmount", RechargeTransferInAmount);
		data.put("RechargeTransferInFreeAmount", RechargeTransferInFreeAmount);
	}

	/**
	 * 获取取款即时统计数据
	 *
	 * @param data
	 */
	protected void setWithdrawRTSMap(Map<String, Object> data, String agentNo, Long playerId, Date sDate, Date eDate) {
		double WithdrawTotalAmount = 0;
		double WithdrawTotalFreeAmount = 0;
		double WithdrawSystemOutAmount = 0;
		double WithdrawSystemOutFreeAmount = 0;
		double WithdrawTransferOutAmount = 0;
		double WithdrawTransferOutFreeAmount = 0;
		List<Object[]> WithdrawList = new ArrayList<Object[]>();
		try {
			List<Integer> payMethods = new ArrayList<Integer>();
			payMethods.add(PlayerWithdraw.PAY_METHOD_AUTO); // 三方出款
			payMethods.add(PlayerWithdraw.PAY_METHOD_MANUAL); // 人工出款
			List<?> totalList = getPlayerWithdrawDao().totalRTSAmount(agentNo, playerId, payMethods, sDate, eDate);
			Map<Integer, List<Object[]>> dataMethodMap = new HashMap<Integer, List<Object[]>>();
			for (Object o : totalList) {
				Object[] info = (Object[]) o;
				int method = ObjectUtils.toInt(info[0]);
				List<Object[]> tmpList = dataMethodMap.getOrDefault(method, new ArrayList<Object[]>());
				tmpList.add(info);
				dataMethodMap.put(method, tmpList);
			}

			// 获取三方存款通道
			List<PaymentThirdRemit> remitList = getPaymentThirdRemitDao().getByAgentNo(agentNo);
			List<Object[]> methodThirdList = dataMethodMap.getOrDefault(PlayerWithdraw.PAY_METHOD_AUTO, new ArrayList<Object[]>());
			if(!CollectionUtils.isEmpty(remitList)) {
				Map<Long, Object[]> tmpListMap = new HashMap<Long, Object[]>();
				for (Object o : methodThirdList) {
					Object[] info = (Object[]) o;
					Long payId = ObjectUtils.toLong(info[1]);
					double amount = ObjectUtils.toDouble(info[2]);
					double freeAmount = ObjectUtils.toDouble(info[3]);
					Object[] result = { amount, freeAmount };
					tmpListMap.put(payId, result);
				}
				for (PaymentThirdRemit t : remitList) {
					Long payId = t.getId();
					double amount = 0, freeAmount = 0;
					Object[] result = (Object[]) tmpListMap.getOrDefault(payId, null);
					if (null != result) {
						amount = ObjectUtils.toDouble(result[0]);
						freeAmount = ObjectUtils.toDouble(result[1]);
					}
					result = new Object[] { t.getName(), amount, freeAmount };
					WithdrawList.add(result);
					WithdrawTotalAmount = MathUtils.add(WithdrawTotalAmount, amount);
					WithdrawTotalFreeAmount = MathUtils.add(WithdrawTotalFreeAmount, freeAmount);
				}
			}

			// 获取人工上分
			List<Object[]> methodSystemList = dataMethodMap.getOrDefault(PlayerWithdraw.PAY_METHOD_MANUAL, new ArrayList<Object[]>());
			if(!CollectionUtils.isEmpty(methodSystemList)) {
				Object[] info = methodSystemList.get(0);
				WithdrawSystemOutAmount = ObjectUtils.toDouble(info[2]);
				WithdrawSystemOutFreeAmount = ObjectUtils.toDouble(info[3]);
				WithdrawTotalAmount = MathUtils.add(WithdrawTotalAmount, WithdrawSystemOutAmount);
				WithdrawTotalFreeAmount = MathUtils.add(WithdrawTotalFreeAmount, WithdrawSystemOutFreeAmount);
			}
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("取款通道及时统计查询异常:" + error);
		}

		try {
			// 获取上下级转账（转出）数据
			WithdrawTransferOutAmount = getAgentPlayerAccountBillDao().totalTransferOutAmount(agentNo, playerId, sDate, eDate);
			WithdrawTotalAmount = MathUtils.add(WithdrawTotalAmount, WithdrawTransferOutAmount);
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("上下级转账（转出）即时统计查询异常:" + error);
		}

		data.put("WithdrawList", WithdrawList);
		data.put("WithdrawTotalAmount", WithdrawTotalAmount);
		data.put("WithdrawTotalFreeAmount", WithdrawTotalFreeAmount);
		data.put("WithdrawSystemOutAmount", WithdrawSystemOutAmount);
		data.put("WithdrawSystemOutFreeAmount", WithdrawSystemOutFreeAmount);
		data.put("WithdrawTransferOutAmount", WithdrawTransferOutAmount);
		data.put("WithdrawTransferOutFreeAmount", WithdrawTransferOutFreeAmount);
	}

	protected void setThirdGameRTSMap(Map<String, Object> data, String agentNo, Long playerId, Date sDate, Date eDate) {
		double ThridGameTotalAmount = 0;
		List<Object[]> ThridGameList = new ArrayList<Object[]>();
		try {
			List<?> totalList = getGameSimulationAccountDao().totalRTSAmount(agentNo, playerId);
			Map<String, Double> gameAmountMap = new HashMap<String, Double>(totalList.size());
			for (Object o : totalList) {
				Object[] info = (Object[]) o;
				String code = ObjectUtils.toString(info[0]);
				double balance = ObjectUtils.toDouble(info[2]);
				gameAmountMap.put(code, balance);
			}
			List<GameSimulationPlatPlatformType> gameList = getGameSimulationPlatPlatformTypeDao().listByAgentNo(agentNo);
			for (GameSimulationPlatPlatformType t : gameList) {
				String code = t.getCode();
				String name = t.getName();
				double balance = gameAmountMap.getOrDefault(code, (double) 0);
				Object[] result = { code, name, balance };
				ThridGameList.add(result);
				ThridGameTotalAmount = MathUtils.add(ThridGameTotalAmount, balance);
			}
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("三方游戏即时统计查询异常:" + error);
		}
		data.put("ThridGameList", ThridGameList);
		data.put("ThridGameTotalAmount", ThridGameTotalAmount);
	}



	protected void setActivityRTSMap(Map<String, Object> data, String agentNo, Long playerId, Date sDate, Date eDate) {
		double ActityTotalAmount = 0;
		List<Object[]> ActivityList = new ArrayList<Object[]>();
		try {
			// 活动-即时统计
			List<PlatformActivityConfig> list = getPlatformActivityConfigDao().listAllByAgentNo(agentNo);
			List<Long> activityIds = list.stream().map(PlatformActivityConfig::getId).collect(Collectors.toList());
			Map<Long, Double> dataMap = new HashMap<Long, Double>();
			if (!CollectionUtils.isEmpty(activityIds)) {
				List<?> totalList = getPlatformActivityRewardRecordDao().totalRTSAmount(agentNo, playerId, activityIds, sDate, eDate);
				for (Object o : totalList) {
					Object[] info = (Object[]) o;
					Long activityId = ObjectUtils.toLong(info[0]);
					double amount = ObjectUtils.toDouble(info[1]);
					dataMap.put(activityId, amount);
				}
			}
			for (PlatformActivityConfig t : list) {
				Long activityId = t.getId();
				double amount = dataMap.getOrDefault(activityId, (double) 0);
				Object[] result = { t.getActivityTitle(), amount };
				ActivityList.add(result);
				ActityTotalAmount = MathUtils.add(ActityTotalAmount, amount);
			}
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("活动即时统计出现异常：" + error);
		}
		data.put("ActivityList", ActivityList);
		data.put("ActityTotalAmount", ActityTotalAmount);
	}

	protected void setSalaryAndDividendRTSMap(Map<String, Object> data, String agentNo, Long playerId, Date sDate, Date eDate){
		double SalaryTotalAmount = 0;
		List<Object[]> SalaryList = new ArrayList<Object[]>();
		double DividendTotalAmount = 0;
		List<Object[]> DividendList = new ArrayList<Object[]>();
		try {
			List<ContractPlatformConfig> list = getContractPlatformConfigDao().listAll(agentNo);
			List<String> contractCodes = list.stream().map(ContractPlatformConfig::getContractCode).collect(Collectors.toList());
			List<?> totalList = getContractRecordDao().totalRTSAmount(agentNo, playerId, contractCodes, sDate, eDate);
			Map<String, Double> dataMap = new HashMap<String, Double>();
			for (Object o : totalList) {
				Object[] info = (Object[]) o;
				String contractCode = ObjectUtils.toString(info[1]);
				double amount = ObjectUtils.toDouble(info[2]);
				dataMap.put(contractCode, amount);
			}
			for (ContractPlatformConfig t : list) {
				String contractCode = t.getContractCode();
				double amount = dataMap.getOrDefault(contractCode, (double) 0);
				Object[] result = { t.getContractTitle(), amount };
				Integer contractType = t.getContractType();
				switch (contractType) {
				case ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY:
				case ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY:
					SalaryList.add(result);
					SalaryTotalAmount = MathUtils.add(SalaryTotalAmount, amount);
					break;
				case ContractPlatformConfig.CONTRACT_TYPE_THIRD_DIVIDEND:
				case ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND:
					DividendList.add(result);
					DividendTotalAmount = MathUtils.add(DividendTotalAmount, amount);
					break;
				default:
					break;
				}
			}
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("工资、分红即时统计出现异常：" + error);
		}
		data.put("SalaryList", SalaryList);
		data.put("SalaryTotalAmount", SalaryTotalAmount);
		data.put("DividendList", DividendList);
		data.put("DividendTotalAmount", DividendTotalAmount);
	}

}
