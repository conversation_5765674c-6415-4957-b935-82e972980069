package ph.yckj.admin.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import myutil.ErrorUtils;
import myutil.Moment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import ph.yckj.admin.service.ActivityService;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.common.service.S3VideoUploadService;
import ph.yckj.common.util.ActivityUtils;
import ph.yckj.common.util.ServiceException;
import sy.hoodle.base.common.dto.PlayerAmount;
import sy.hoodle.base.common.dto.activity.AcitvitySalaryConditions;
import sy.hoodle.base.common.dto.activity.AcitvitySalaryDailyRules;
import sy.hoodle.base.common.dto.activity.AcitvitySalaryPeriodRules;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Service
@Transactional
public class ActivityServiceImpl extends AbstractService implements ActivityService {

    @Autowired
    private S3VideoUploadService s3VideoUploadService;
    @Autowired
    private ActivityUtils activityUtils;

    @Override
    public boolean addActivityConfig(String agentNo, String agentName, String code, String activityTitle,
            Integer activityType, boolean mutex, String playerType, Integer targetType, Integer cycleScope, Integer drawType,
            String schedule, Timestamp startTime, Timestamp finishTime, Timestamp createTime, Integer activityStatus) {
        PlatformActivityConfig entity = new PlatformActivityConfig(agentNo, agentName, code, activityTitle,
                activityType, mutex, null,playerType, targetType, cycleScope, drawType, schedule, startTime, finishTime, "",
                createTime, activityStatus);
        return getPlatformActivityConfigDao().save(entity);
    }

    @Override
    public PlatformActivityConfig updateActivityConfig(String code, int activityType, boolean mutex, String playerType,
            String activityTitle, int drawType, int targetType, int cycleScope, String schedule, Timestamp startTime,
            Timestamp finishTime, Timestamp createTime) {
        PlatformActivityConfig entity = getPlatformActivityConfigDao().getByCode(code);
        if(null == entity) {
            return null;
        }
        entity.setActivityType(activityType);
        entity.setMutex(mutex);
        entity.setPlayerType(playerType);
        entity.setActivityTitle(activityTitle);
        entity.setDrawType(drawType);
        entity.setTargetType(targetType);
        entity.setCycleScope(cycleScope);
        entity.setSchedule(schedule);
        entity.setStartTime(startTime);
        entity.setFinishTime(finishTime);
        entity.setCreateTime(createTime);
        getPlatformActivityConfigDao().update(entity);
        return entity;
    }

    @Override
    public boolean updateActivityStatus(String code, int activityStatus) {
        PlatformActivityConfig entity = getPlatformActivityConfigDao().getByCode(code);
        if(entity == null) {
            throw newException("-1", "该活动已取消");
        }
        if(PlatformActivityConfig.ACTIVITY_STATUS_NORMAL == activityStatus) {
            // 工资/分红活动时，不需要上传图片
            if(entity.getActivityType() != PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY &&
                    entity.getActivityType() != PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND &&
                    entity.getActivityType() != PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY &&
                    entity.getActivityType() != PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
                if(StringUtils.isEmpty(entity.getBannerSrc())) {
                    throw newException("-1", "PC版活动Banner图未上传");
                }
                if(StringUtils.isEmpty(entity.getBannerListSrc())) {
                    throw newException("-1", "PC版活动列表图未上传");
                }
                if(StringUtils.isEmpty(entity.getH5BannerSrc())) {
                    throw newException("-1", "H5版活动Banner图未上传");
                }
                if(StringUtils.isEmpty(entity.getH5BannerListSrc())) {
                    throw newException("-1", "H5版活动列表图未上传");
                }
                if(StringUtils.isEmpty(entity.getH5BannerListDetailSrc())) {
                    throw newException("-1", "H5版活动列表详情图未上传");
                }
            }
        }
        return getPlatformActivityConfigDao().updateActivityStatus(code, activityStatus);
    }

    @Override
    public boolean deleteActivity(String code) {
        PlatformActivityConfig entity = getPlatformActivityConfigDao().getByCode(code);
        if(entity == null) {
            throw newException("-1", "该活动已删除");
        }
        getPlatformActivityConfigDao().delete(entity);
        return true;
    }

    @Override
    public boolean updateActivityRules(String code, String activityRules) {
        PlatformActivityConfig entity = getPlatformActivityConfigDao().getByCode(code);
        if(entity == null) {
            throw newException("-1", "该活动已取消");
        }
        return getPlatformActivityConfigDao().updateActivityRules(code, activityRules);
    }

    @Override
    public boolean uploadActivityImage(String code, int imageType, String base64Code) {
        PlatformActivityConfig entity = getPlatformActivityConfigDao().getByCode(code);
        if(entity == null) {
            throw newException("-1", "该活动已取消");
        }
        String imageSrc = "";
        String meditType = StrUtil.subBefore(base64Code, ";", false).split(":")[1];
        String fileName = IdUtil.fastSimpleUUID().concat(".").concat(StrUtil.subAfter(meditType, "/", false));
        String base64 = StrUtil.subAfter(base64Code, ",", true);
        try {
            // Step 1: 解码Base64字符串
            byte[] decodedBytes = Base64.getDecoder().decode(base64);
            // Step 2: 转换为InputStream
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(decodedBytes);
            // Step 3: 创建MultipartFile
            MultipartFile file = new MockMultipartFile(fileName, fileName, meditType, byteArrayInputStream);
            if(file != null) {
                String decodedFileName = null;
                try {
                    decodedFileName = URLDecoder.decode(Objects.requireNonNull(file.getOriginalFilename()),
                            StandardCharsets.UTF_8.name());
                }catch(UnsupportedEncodingException e) {
                    log.error("活动图文件名获取失败", e);
                    throw new ServiceException("1", "活动图上传失败");
                }
                String type = (imageType == 1 || imageType == 2) ? "pc" : "h5";
                imageSrc = "activity/icon/" + code + "/" + type + "/" + decodedFileName;
                URL giftBackgroundUrl = s3VideoUploadService.uploadBackground(file, imageSrc);
                if(giftBackgroundUrl == null) {
                    throw new ServiceException("1", "上传活动图失败");
                }
            }
        }catch(IOException e) {
            log.info("error:" + ErrorUtils.getStackTraceInfo(e));
            throw new ServiceException("1", "活动图上传失败");
        }
        switch(imageType) {
            case 1:
                entity.setBannerSrc(imageSrc);
                break;
            case 2:
                entity.setBannerListSrc(imageSrc);
                break;
            case 3:
                entity.setH5BannerSrc(imageSrc);
                break;
            case 4:
                entity.setH5BannerListSrc(imageSrc);
                break;
            case 5:
                entity.setH5BannerListDetailSrc(imageSrc);
                break;
            default:
                throw new ServiceException("1", "上传活动图失败");
        }
        getPlatformActivityConfigDao().update(entity);
        return true;
    }

    @Override
    public void inActivities(AgentPlayerInfo player, PlayerRecharge recharge) {
        String agentNo = player.getAgentNo();
        int activityType = PlatformActivityConfig.ACTIVITY_TYPE_USDT_REGISTER_GiVEING;
        List<PlatformActivityConfig> list = getPlatformActivityConfigDao().listEnableActivity(agentNo, activityType);
        for(PlatformActivityConfig activity : list) {
            Moment orderMoment = new Moment().fromDate(recharge.getOrderTime());
            Moment startTimeMoment = new Moment().fromTime(activity.getStartTime());
            Moment finishTimeMoment = new Moment().fromTime(activity.getFinishTime());
            boolean isBetween = orderMoment.between(startTimeMoment, finishTimeMoment);
            if(!isBetween) {
                return;
            }
            JSONArray array = JSONArray.parseArray(activity.getActivityRules());
            if(null == array) {
                continue;
            }
            int rewardType = 0;
            boolean isConform = false;
            BigDecimal rewardAmount = BigDecimal.ZERO;
            for(int i = 0; i < array.size(); i++) {
                JSONObject object = (JSONObject) array.get(i);
                int rewardTypeTmp = object.getIntValue("rewardType");
                BigDecimal rewardAmountTmp = object.getBigDecimal("rewardMoney");
                BigDecimal rechargeAmountTmp = object.getBigDecimal("rechargeAmount");
                if(recharge.getAmount().compareTo(rechargeAmountTmp) >= 0) {
                    isConform = true;
                    rewardType = rewardTypeTmp;
                    rewardAmount = rewardAmountTmp;
                }
            }
            Long playerId = player.getPlayerId();
            Long activityId = activity.getId();
            Timestamp activityDate = new Moment().fromDate(orderMoment.toSimpleDate()).toTimestamp();
            if(isConform) {
                PlatformActivityRewardRecord record = getPlatformActivityRewardRecordDao().getByPlayerIdAndActivityDate(
                        playerId, activityId, activityDate);
                if(null != record) {
                    return;
                }
                record = new PlatformActivityRewardRecord();
                record.setAgentNo(recharge.getAgentNo());
                record.setAgentName(recharge.getAgentName());
                record.setPlayerId(recharge.getPlayerId());
                record.setPid(player.getPid());
                record.setPids(player.getPids());
                record.setPlayerName(player.getPlayerName());
                BigDecimal amount = BigDecimal.ZERO;
                if(rewardType == 1) {
                    amount = rewardAmount;
                }else {
                    amount = rewardAmount.multiply(recharge.getAmount()).multiply(new BigDecimal("0.01"));
                }
                record.setAmount(amount);
                record.setDrawData("");
                record.setDrawStatus(1);
                record.setPid(player.getPid() != null ? player.getPid() : 0L);
                record.setActivityDate(activityDate);
                record.setActivityId(activity.getId());
                record.setActivityType(activityType);
                String playerIp = recharge.getOrderIp();
                record.setPlayerIp(playerIp);
                record.setRemarks(activity.getActivityTitle());
                record.setRewardTime(new Moment().toTimestamp());
                // 判断玩家参与IP
                Timestamp startTime = new Moment().fromDate(activityDate).toTimestamp();
                Timestamp finishTime = new Moment().fromDate(activityDate).tomorrow().toTimestamp();
                boolean isCheck = getPlatformActivityRewardRecordDao().checkAlreadyPartIn(activityId, playerIp,
                        startTime, finishTime);
                if(isCheck) {
                    return;
                }
                boolean updateAmount = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerId, amount,
                        BigDecimal.ZERO);
                if(!updateAmount) {
                    log.info(activity.getActivityTitle() + "活动达标奖励金额发放失败。");
                    return;
                }
                getBillService().addActivityBill(player, record);
                boolean bool = getPlatformActivityRewardRecordDao().save(record);
            }
        }
    }

    @Override
    public void dealInActivities(AgentPlayerInfo player, PlayerRecharge recharge) {
        final Long playerId = player.getPlayerId();
        final String agentNo = recharge.getAgentNo();
        int activityType = PlatformActivityConfig.ACTIVITY_TYPE_NEWCOMER_REGISTER;
        if(0 != player.getPid()) {
            activityType = PlatformActivityConfig.ACTIVITY_TYPE_INVITATION_REGISTER;
        }
        List<PlatformActivityConfig> activitys = getPlatformActivityConfigDao().listEnableActivity(agentNo,
                activityType);
        for(PlatformActivityConfig activity : activitys) {
            Moment orderMoment = new Moment().fromDate(recharge.getOrderTime());
            Moment startTimeMoment = new Moment().fromTime(activity.getStartTime());
            Moment finishTimeMoment = new Moment().fromTime(activity.getFinishTime());
            boolean isBetween = orderMoment.between(startTimeMoment, finishTimeMoment);
            if(!isBetween) {
                return;
            }
            Long activityId = activity.getId();
            PlatformActivityInviteRecord record = getPlatformActivityInviteRecordDao().getActivityInviteRecord(playerId,
                    activityId, activityType);
            if(null == record || PlatformActivityInviteRecord.STATUS_NORMAL == record.getStatus()) {
                return;
            }
            BigDecimal rechargeAmount = recharge.getActualAmount();
            getPlatformActivityInviteRecordDao().updateRechargeAmount(record.getId(), rechargeAmount);
        }
    }

    @Override
    @Transactional
    public boolean doDraw(PlatformActivityConfig config, AgentPlayerInfo player, Date sDate, Date eDate,
            String calculateCycle, Integer grandTotalType, Date grandTotalStartDate) {
        try {
//            log.info("doDraw活动===编码：{}，活动标题：{}，用户：{}，计算周期：{}", config.getCode(),
//                    config.getActivityTitle(), player.getPlayerName(), calculateCycle);
            // 验证活动公共配置
            activityUtils.verifyActivityConfig(player, config, new Date());
            int activityType = config.getActivityType();
            int cycleScope = config.getCycleScope();
            // 计算量
            PlayerAmount playerAmount;
            // 活动记录
            PlatformActivityRewardRecord salaryReward = null;
            // 上级活动记录（部分活动会同时给上级发放奖励）
            PlatformActivityRewardRecord upSalaryReward = null;
            // 活动规则
            switch(activityType) {
                case PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_SALARY:
                    // 彩票工资
                case PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND:
                    // 彩票分红
                case PlatformActivityConfig.ACTIVITY_TYPE_THIRD_SALARY:
                    // 三方工资
                case PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND:
                    // 三方分红
                    // 彩票工资/彩票分红/三方工资/三方分红，只有获取周期量和计算活动金额不一样
                    if(activityUtils.hasReceiveReward(config, player.getPlayerId(), sDate, eDate)) {
//                        log.info("doDraw活动==={}==={}===已生成活动记录", config.getCode(), player.getPlayerName());
                        throw newException("120-04");
                    }
                    // 查询投注量等
                    playerAmount = activityUtils.getPlayerAmount(config, player, sDate, eDate);
                    playerAmount.setLastLossAmount(playerAmount.getLossAmount());
                    if(activityType == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND ||
                            activityType == PlatformActivityConfig.ACTIVITY_TYPE_THIRD_DIVIDEND) {
                        // 分红才累计亏损
                        playerAmount.setGrandTotalType(grandTotalType);
                        if(grandTotalType != AcitvitySalaryConditions.GRAND_TOTAL_TYPE_NO &&
                                playerAmount.getLossAmount().compareTo(BigDecimal.ZERO) < 0) {
                            // 分红活动，累计配置非不累计，且有亏损时，查询以前的累计盈亏金额
                            BigDecimal grandTotalLossAmount = activityUtils.getLossAmount(config, player,
                                    grandTotalStartDate, sDate);
                            playerAmount.setGrandTotalLossAmount(grandTotalLossAmount);
                            if(grandTotalLossAmount.compareTo(BigDecimal.ZERO) > 0) {
                                // 当累计盈亏金额为正数，表示用户盈利了，需要累计到上期盈亏里
                                playerAmount.setLossAmount(playerAmount.getLossAmount().add(grandTotalLossAmount));
                            }
                        }
                    }
                    log.info("doDraw活动==={}==={}==={}", config.getCode(), player.getPlayerName(), playerAmount);

                    if(cycleScope == PlatformActivityConfig.CYCLE_SCOPE_DAY) {
                        // 日
                        AcitvitySalaryDailyRules salaryPerfectRules = activityUtils.getSalaryDailyPerfectRules(config,
                                playerAmount);
                        if(salaryPerfectRules == null) {
//                            log.info("doDraw活动==={}==={}===奖励规则不存在", config.getCode(), player.getPlayerName());
                            return false;
                        }
                        salaryReward = activityUtils.getSalaryDailyRewardRecord(salaryPerfectRules, player, config,
                                playerAmount, sDate, eDate);
                        if(activityType == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND) {
                            // 彩票分红生成上级奖励活动记录
                            upSalaryReward = activityUtils.getUpSalaryDailyRewardRecord(salaryPerfectRules, player,
                                    config, playerAmount, sDate, eDate);
                        }
                    }else if(cycleScope == PlatformActivityConfig.CYCLE_SCOPE_WEEK ||
                            cycleScope == PlatformActivityConfig.CYCLE_SCOPE_HALF_MONTH ||
                            cycleScope == PlatformActivityConfig.CYCLE_SCOPE_MONTH ||
                            cycleScope == PlatformActivityConfig.CYCLE_SCOPE_MIN_10 ||
                            cycleScope == PlatformActivityConfig.CYCLE_SCOPE_MIN_20 ||
                            cycleScope == PlatformActivityConfig.CYCLE_SCOPE_MIN_30 ||
                            cycleScope == PlatformActivityConfig.CYCLE_SCOPE_HOUR_1) {
                        // 周/半月/月
                        AcitvitySalaryPeriodRules salaryPerfectRules = activityUtils.getSalaryPeriodPerfectRules(config,
                                playerAmount);
                        if(salaryPerfectRules == null) {
//                            log.info("doDraw活动==={}==={}===奖励规则不存在", config.getCode(), player.getPlayerName());
                            return false;
                        }
                        salaryReward = activityUtils.getSalaryPeriodRewardRecord(salaryPerfectRules, player, config,
                                playerAmount, sDate, eDate);
                        if(activityType == PlatformActivityConfig.ACTIVITY_TYPE_LOTTERY_DIVIDEND) {
                            // 彩票分红生成上级奖励活动记录
                            upSalaryReward = activityUtils.getUpSalaryPeriodRewardRecord(salaryPerfectRules, player,
                                    config, playerAmount, sDate, eDate);
                        }
                    }
                    if(salaryReward != null) {
                        if(config.getDrawType() == PlatformActivityConfig.DRAW_TYPE_AUTO) {
                            // 自动派发奖励
                            activityUtils.payMoneyAndRecord(salaryReward, config);
                            if(upSalaryReward != null) {
                                // 自动派发奖励
                                activityUtils.payMoneyAndRecord(upSalaryReward, config);
                            }
                        }else {
//                            log.info("doDraw活动==={}==={}===手动派发", config.getCode(), player.getPlayerName());
                            // TODO 目前活动只有自动派发方式。如果后续增加手动派发，取消下方代码的注释。并且修改统计报表代码，保证未派发的不会统计到报表。
                            // // 非自动派发，保存活动记录
                            // boolean result = getPlatformActivityRewardRecordDao().save(salaryReward);
                            // getPlatformActivityRewardRecordDao().save(upSalaryReward);
                            // if(!result) {
                            //     log.info("doDraw活动==={}==={}===保存活动记录失败", config.getCode(),
                            //             salaryReward.getPlayerName());
                            //     throw new IllegalArgumentException();
                            // }
                        }
                    }
                    break;
            }
        }catch(ServiceException e) {
            return false;
        }catch(IllegalArgumentException e) {
            return false;
        }
        return true;
    }

    @Override
    public List<String> getMutualExclusionCodes(String code) {
        PlatformActivityConfig entity = getPlatformActivityConfigDao().getByCode(code);
        if (entity == null || entity.getMutexActivity() == null || entity.getMutexActivity().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(entity.getMutexActivity().split(","));
    }

    @Override
    public boolean updateMutualExclusion(String code, String mutualExclusionCodes) {
        return getPlatformActivityConfigDao().updateMutexActivity(code,mutualExclusionCodes);
    }
}
