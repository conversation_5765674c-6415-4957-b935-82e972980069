package ph.yckj.admin.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import myutil.RandomUtils;
import myutil.StringIdUtils;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import ph.yckj.admin.service.ContractService;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.contract.ContractPlatformConfigVo;
import ph.yckj.common.util.ContractUtils;
import ph.yckj.common.util.ServiceException;
import sy.hoodle.base.common.dto.PlayerAmount;
import sy.hoodle.base.common.dto.contract.*;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class ContractServiceImpl extends AbstractService implements ContractService {

    @Autowired
    private ContractUtils contractUtils;

    @Override
    @Transactional
    public boolean saveContractPlatformConfig(ContractPlatformConfigVo vo) {
        if(StringUtils.isEmpty(vo.getAgentNo())) {
            throw newException("500-092");
        }
        AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(vo.getAgentNo());
        if(agentInfo == null) {
            throw newException("500-093");
        }
        if(StringUtils.isEmpty(vo.getContractCode())) {
            throw newException("500-094");
        }
        ContractPlatformConfig contractConfig = getContractPlatformConfigDao().getByContractCode(vo.getAgentNo(),
                vo.getContractCode());
        if(contractConfig != null) {
            throw newException("500-095");
        }
        if(StringUtils.isEmpty(vo.getContractTitle())) {
            throw newException("500-096");
        }
        if(vo.getContractType() == null) {
            throw newException("500-097");
        }
        if(vo.getAccountType() == null) {
            throw newException("500-098");
        }
        if(vo.getTargetType() == null) {
            throw newException("500-099");
        }
        if(vo.getCycleType() == null) {
            throw newException("500-100");
        }
        if(vo.getDrawType() == null) {
            throw newException("500-101");
        }
        if(StringUtils.isEmpty(vo.getCronSchedule())) {
            throw newException("500-102");
        }
        if(!CronExpression.isValidExpression(vo.getCronSchedule())) {
            throw newException("500-111");
        }

        String contractRules = testSysContractRules(vo.getCycleType(), vo.getContractRules());
        String downRules = testSysDownRules(vo.getCycleType(), vo.getDownRules());

        ContractPlatformConfig config = new ContractPlatformConfig(agentInfo.getAgentNo(), agentInfo.getAgentName(),
                vo.getContractCode(), vo.getContractTitle(), vo.getContractType(), vo.getAccountType(),
                vo.getTargetType(), vo.getCycleType(), vo.getComputeRange(), vo.getDrawType(), vo.getCronSchedule(),
                contractRules, downRules, ContractPlatformConfig.CONTRACT_STATUS_ENABLE);
        return getContractPlatformConfigDao().save(config);
    }

    @Override
    @Transactional
    public void updateContractPlatformConfig(ContractPlatformConfigVo vo) {
        if(vo.getId() == null) {
            throw newException("500-091");
        }
        ContractPlatformConfig config = getContractPlatformConfigDao().getById(vo.getId());
        if(config == null) {
            throw newException("500-091");
        }
        if(StringUtils.isEmpty(vo.getContractTitle())) {
            throw newException("500-096");
        }
        if(vo.getContractType() == null) {
            throw newException("500-097");
        }
        if(vo.getAccountType() == null) {
            throw newException("500-098");
        }
        if(vo.getTargetType() == null) {
            throw newException("500-099");
        }
        if(vo.getDrawType() == null) {
            throw newException("500-101");
        }
        if(StringUtils.isEmpty(vo.getCronSchedule())) {
            throw newException("500-102");
        }
        if(!CronExpression.isValidExpression(vo.getCronSchedule())) {
            throw newException("500-111");
        }

        String contractRules = testSysContractRules(vo.getCycleType(), vo.getContractRules());
        String downRules = testSysDownRules(vo.getCycleType(), vo.getDownRules());

        // 如果 契约标题 有改动，需要更改 ContractPlayerConfig 以及 ContractRecord
        if(!config.getContractTitle().equals(vo.getContractTitle())) {
            getContractPlayerConfigDao().updateContractTitle(config.getAgentNo(), config.getContractCode(),
                    vo.getContractTitle());
            // 先不改契约发放记录的契约标题
            // getContractRecordDao().updateContractTitle(config.getAgentNo(), config.getContractCode(),
            //         vo.getContractTitle());
        }

        // 更新契约
        config.setContractTitle(vo.getContractTitle());
        config.setContractType(vo.getContractType());
        config.setAccountType(vo.getAccountType());
        config.setTargetType(vo.getTargetType());
        config.setComputeRange(vo.getComputeRange());
        config.setDrawType(vo.getDrawType());
        config.setCronSchedule(vo.getCronSchedule());
        config.setContractRules(contractRules);
        config.setDownRules(downRules);
        getContractPlatformConfigDao().update(config);

        // 保存操作日志
        String content = "编辑平台契约，agentNo：" + config.getAgentNo() + "，契约编号：" + config.getContractCode() +
                "，契约标题：" + config.getContractTitle() + "，契约类型：" + config.getContractType() + "，计算对象：" +
                config.getTargetType() + "，计算周期：" + config.getCycleType() + "，计算范围：" + vo.getComputeRange() +
                "，发放方式：" + config.getDrawType() + "，执行时间：" + config.getCronSchedule() + "，契约规则：" +
                config.getContractRules() + "，下级契约规则：" + vo.getDownRules();
        // 设置 ThreadLocal 变量
        ThreadLocalUtil.setAdminLogContent(content);
    }

    /**
     * 验证平台契约规则
     *
     * @param cycleType 计算周期
     * @param contractRules 契约规则
     * @return 返回顺序排序且正确的契约规则
     */
    private String testSysContractRules(int cycleType, String contractRules) {
        if(StringUtils.isEmpty(contractRules)) {
            throw newException("500-073");
        }
        if(cycleType == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 计算周期为日
            ContractDailyConditions conditions = JSON.parseObject(contractRules, ContractDailyConditions.class);
            if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                throw newException("500-073");
            }
            if(conditions.getActiveMinRecharge().doubleValue() < 0) {
                throw newException("500-105");
            }
            if(conditions.getActiveMinConsume().doubleValue() < 0) {
                throw newException("500-106");
            }
            // 契约规则顺序排序
            contractUtils.reorderDaily(conditions.getRules(), true);

            for(int i = 0; i < conditions.getRules().size(); i++) {
                ContractDailyRules thisBean = conditions.getRules().get(i);
                if(thisBean.getDailyRecharge().doubleValue() < 0 || thisBean.getDailyConsume().doubleValue() < 0 ||
                        thisBean.getDailyLoss().doubleValue() < 0 || thisBean.getActiveUser() < 0) {
                    throw newException("500-076");
                }
                if(thisBean.getScalePoint() <= 0) {
                    throw newException("500-080");
                }
                // 先取消限制
                // if(i > 0) {
                //     ContractDailyRules lastBean = conditions.getRules().get(i - 1);
                //     if(thisBean.getScalePoint() <= lastBean.getScalePoint()) {
                //         throw newException("500-077");
                //     }
                //     if(thisBean.getDailyRecharge().doubleValue() < lastBean.getDailyRecharge().doubleValue() ||
                //             thisBean.getDailyConsume().doubleValue() < lastBean.getDailyConsume().doubleValue() ||
                //             thisBean.getDailyLoss().doubleValue() < lastBean.getDailyLoss().doubleValue() ||
                //             thisBean.getActiveUser() < lastBean.getActiveUser()) {
                //         throw newException("500-078");
                //     }
                //     // 不能有两条全部一样的规则
                //     if(thisBean.getDailyRecharge().equals(lastBean.getDailyRecharge()) &&
                //             thisBean.getDailyConsume().equals(lastBean.getDailyConsume()) &&
                //             thisBean.getDailyLoss().equals(lastBean.getDailyLoss()) && thisBean.getActiveUser().equals(
                //             lastBean.getActiveUser())) {
                //         throw newException("500-079");
                //     }
                // }
            }

            // 设置周期，用户契约需要
            conditions.setCycleType(cycleType);
            return JSON.toJSONString(conditions);
        }else {
            // 计算周期为周/半月/月
            ContractPeriodConditions conditions = JSON.parseObject(contractRules, ContractPeriodConditions.class);
            if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                throw newException("500-073");
            }
            if(conditions.getActiveMinRecharge().doubleValue() < 0) {
                throw newException("500-105");
            }
            if(conditions.getActiveMinConsume().doubleValue() < 0) {
                throw newException("500-106");
            }
            if(conditions.getStandardDays().doubleValue() < 0) {
                throw newException("500-107");
            }
            // 契约规则顺序排序
            contractUtils.reorderPeriod(conditions.getRules(), true);

            for(int i = 0; i < conditions.getRules().size(); i++) {
                ContractPeriodRules thisBean = conditions.getRules().get(i);
                if(thisBean.getTotalRecharge().doubleValue() < 0 || thisBean.getTotalConsume().doubleValue() < 0 ||
                        thisBean.getTotalLoss().doubleValue() < 0 || thisBean.getActiveUser() < 0) {
                    throw newException("500-076");
                }
                if(thisBean.getScalePoint() <= 0) {
                    throw newException("500-080");
                }
                // 先取消限制
                // if(i > 0) {
                //     ContractPeriodRules lastBean = conditions.getRules().get(i - 1);
                //     if(thisBean.getScalePoint() <= lastBean.getScalePoint()) {
                //         throw newException("500-077");
                //     }
                //     if(thisBean.getTotalRecharge().doubleValue() < lastBean.getTotalRecharge().doubleValue() ||
                //             thisBean.getTotalConsume().doubleValue() < lastBean.getTotalConsume().doubleValue() ||
                //             thisBean.getTotalLoss().doubleValue() < lastBean.getTotalLoss().doubleValue() ||
                //             thisBean.getActiveUser() < lastBean.getActiveUser()) {
                //         throw newException("500-078");
                //     }
                //     // 不能有两条全部一样的规则
                //     if(thisBean.getTotalRecharge().equals(lastBean.getTotalRecharge()) &&
                //             thisBean.getTotalConsume().equals(lastBean.getTotalConsume()) &&
                //             thisBean.getTotalLoss().equals(lastBean.getTotalLoss()) && thisBean.getActiveUser().equals(
                //             lastBean.getActiveUser())) {
                //         throw newException("500-079");
                //     }
                // }
            }

            // 设置周期，用户契约需要
            conditions.setCycleType(cycleType);
            return JSON.toJSONString(conditions);
        }
    }

    /**
     * 验证平台下级契约规则
     *
     * @param cycleType 计算周期
     * @param downRules 下级契约规则
     * @return 返回顺序排序且正确的契约规则
     */
    private String testSysDownRules(int cycleType, String downRules) {
        if(StringUtils.isEmpty(downRules)) {
            return "";
        }
        if(cycleType == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 计算周期为日
            List<ContractDailyRulesDown> downRulesList = JSON.parseArray(downRules, ContractDailyRulesDown.class);
            if(downRulesList == null || downRulesList.isEmpty()) {
                return "";
            }

            // 下级契约规则排序
            // 先对层级顺序排序
            Collections.sort(downRulesList);
            // 再对每个层级的契约规则排序
            for(ContractDailyRulesDown rulesDown : downRulesList) {
                contractUtils.reorderDaily(rulesDown.getDownRuleList(), true);
            }

            return JSON.toJSONString(downRulesList);
        }else {
            // 计算周期为周/半月/月
            List<ContractPeriodRulesDown> downRulesList = JSON.parseArray(downRules, ContractPeriodRulesDown.class);
            if(downRulesList == null || downRulesList.isEmpty()) {
                return "";
            }

            // 下级契约规则排序
            // 先对层级顺序排序
            Collections.sort(downRulesList);
            // 再对每个层级的契约规则排序
            for(ContractPeriodRulesDown rulesDown : downRulesList) {
                contractUtils.reorderPeriod(rulesDown.getDownRuleList(), true);
            }

            return JSON.toJSONString(downRulesList);
        }
    }

    /**
     * 验证契约规则
     *
     * @param cycleType 计算周期
     * @param config 待修改用户契约
     * @param contractRules 新的契约规则
     * @return 返回顺序排序且正确的契约规则
     */
    private String testPlayerContractRules(int cycleType, ContractPlayerConfig config, String contractRules) {
        // 是否有上级契约
        boolean hasUp = config.getFromPlayerId() != 0;
        // 查询上级契约
        ContractPlayerConfig upConfig = null;
        if(hasUp) {
            upConfig = getContractPlayerConfigDao().getMyContract(config.getAgentNo(), config.getFromPlayerId(),
                    config.getContractCode());
            if(upConfig == null) {
                throw newException("500-103");
            }
            // 上级契约规则
            if(StringUtils.isEmpty(upConfig.getExtraRules())) {
                throw newException("500-074");
            }
        }
        if(StringUtils.isEmpty(contractRules)) {
            throw newException("500-073");
        }
        // 直属下级契约
        List<ContractPlayerConfig> downConfigs = getContractPlayerConfigDao().listDownContract(config.getAgentNo(),
                config.getContractCode(), config.getToPlayerId());

        if(cycleType == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 计算周期为日
            // 用户自己的契约规则
            ContractDailyConditions conditions = JSON.parseObject(contractRules, ContractDailyConditions.class);
            if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                throw newException("500-073");
            }
            // 递增排序契约规则
            contractUtils.reorderDaily(conditions.getRules(), true);
            if(hasUp) {
                // 上级契约规则
                ContractDailyConditions upConditions = JSON.parseObject(upConfig.getExtraRules(),
                        ContractDailyConditions.class);
                if(upConditions == null || upConditions.getRules() == null || upConditions.getRules().isEmpty()) {
                    throw newException("500-074");
                }
                // 校验上级契约规则
                for(ContractDailyRules rule : conditions.getRules()) {
                    if(!ContractDailyRules.isSubRuleValid(upConditions.getRules(), rule)) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("playerName", upConfig.getToPlayerName());
                        throw newException("500-108", params);
                    }
                }
            }

            if(downConfigs != null && !downConfigs.isEmpty()) {
                // 校验其下级契约规则
                for(ContractPlayerConfig downConfig : downConfigs) {
                    ContractDailyConditions downConditions = JSON.parseObject(downConfig.getExtraRules(),
                            ContractDailyConditions.class);
                    if(downConditions != null && downConditions.getRules() != null &&
                            !downConditions.getRules().isEmpty()) {
                        for(ContractDailyRules downRule : downConditions.getRules()) {
                            if(!ContractDailyRules.isSubRuleValid(conditions.getRules(), downRule)) {
                                Map<String, Object> params = new HashMap<>();
                                params.put("playerName", downConfig.getToPlayerName());
                                throw newException("500-109", params);
                            }
                        }
                    }
                }
            }

            // 用户自己的旧契约规则
            ContractDailyConditions oldConditions = JSON.parseObject(config.getExtraRules(),
                    ContractDailyConditions.class);
            if(oldConditions == null) {
                conditions.setCycleType(cycleType);
                return JSON.toJSONString(conditions);
            }else {
                // 只更新契约规则，不改动活跃用户条件
                oldConditions.setRules(conditions.getRules());
                return JSON.toJSONString(oldConditions);
            }
        }else {
            // 计算周期为周/半月/月
            // 用户自己的契约规则
            ContractPeriodConditions conditions = JSON.parseObject(contractRules, ContractPeriodConditions.class);
            if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                throw newException("500-073");
            }
            // 递增排序契约规则
            contractUtils.reorderPeriod(conditions.getRules(), true);
            if(hasUp) {
                // 上级契约规则
                ContractPeriodConditions upConditions = JSON.parseObject(upConfig.getExtraRules(),
                        ContractPeriodConditions.class);
                if(upConditions == null || upConditions.getRules() == null || upConditions.getRules().isEmpty()) {
                    throw newException("500-074");
                }
                // 校验上级契约规则
                for(ContractPeriodRules rule : conditions.getRules()) {
                    if(!ContractPeriodRules.isSubRuleValid(upConditions.getRules(), rule)) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("playerName", upConfig.getToPlayerName());
                        throw newException("500-108", params);
                    }
                }
            }

            if(downConfigs != null && !downConfigs.isEmpty()) {
                // 校验其下级契约规则
                for(ContractPlayerConfig downConfig : downConfigs) {
                    ContractPeriodConditions downConditions = JSON.parseObject(downConfig.getExtraRules(),
                            ContractPeriodConditions.class);
                    if(downConditions != null && downConditions.getRules() != null &&
                            !downConditions.getRules().isEmpty()) {
                        for(ContractPeriodRules downRule : downConditions.getRules()) {
                            if(!ContractPeriodRules.isSubRuleValid(conditions.getRules(), downRule)) {
                                Map<String, Object> params = new HashMap<>();
                                params.put("playerName", downConfig.getToPlayerName());
                                throw newException("500-109", params);
                            }
                        }
                    }
                }
            }

            // 用户自己的旧契约规则
            ContractPeriodConditions oldConditions = JSON.parseObject(config.getExtraRules(),
                    ContractPeriodConditions.class);
            if(oldConditions == null) {
                conditions.setCycleType(cycleType);
                return JSON.toJSONString(conditions);
            }else {
                // 只更新契约规则，不改动活跃用户条件
                oldConditions.setRules(conditions.getRules());
                return JSON.toJSONString(oldConditions);
            }
        }
    }

    /**
     * 验证契约规则（旧判断）
     *
     * @param cycleType 计算周期
     * @param config 待修改用户契约
     * @param contractRules 新的契约规则
     * @return 返回顺序排序且正确的契约规则
     */
    @Deprecated
    private String testPlayerContractRulesOld(int cycleType, ContractPlayerConfig config, String contractRules) {
        // 是否有上级契约
        boolean hasUp = config.getFromPlayerId() != 0;
        // 查询上级契约
        ContractPlayerConfig upConfig = null;
        if(hasUp) {
            upConfig = getContractPlayerConfigDao().getMyContract(config.getAgentNo(), config.getFromPlayerId(),
                    config.getContractCode());
            if(upConfig == null) {
                throw newException("500-103");
            }
            // 上级契约规则
            if(StringUtils.isEmpty(upConfig.getExtraRules())) {
                throw newException("500-074");
            }
        }
        if(StringUtils.isEmpty(contractRules)) {
            throw newException("500-073");
        }
        // 直属下级契约
        List<ContractPlayerConfig> downConfigs = getContractPlayerConfigDao().listDownContract(config.getAgentNo(),
                config.getContractCode(), config.getToPlayerId());

        if(cycleType == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 计算周期为日
            // 上级的最大发放比例
            double upMaxScalePoint = 0D;
            if(hasUp) {
                // 上级契约规则
                ContractDailyConditions upConditions = JSON.parseObject(upConfig.getExtraRules(),
                        ContractDailyConditions.class);
                if(upConditions == null || upConditions.getRules() == null || upConditions.getRules().isEmpty()) {
                    throw newException("500-074");
                }
                // 上级的最大发放比例
                upMaxScalePoint = upConditions.getRules().get(0).getScalePoint();
                for(int i = 1; i < upConditions.getRules().size(); i++) {
                    if(upMaxScalePoint < upConditions.getRules().get(i).getScalePoint()) {
                        upMaxScalePoint = upConditions.getRules().get(i).getScalePoint();
                    }
                }
            }
            // 用户自己的契约规则
            ContractDailyConditions conditions = JSON.parseObject(contractRules, ContractDailyConditions.class);
            if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                throw newException("500-073");
            }
            // 递增排序契约规则
            contractUtils.reorderDaily(conditions.getRules(), true);

            // 先取消此限制
            // // 用户所有下级契约的最大发放比例
            // double downMaxScalePoint = 0D;
            // if(downConfigs != null && !downConfigs.isEmpty()) {
            //     for(ContractPlayerConfig downConfig : downConfigs) {
            //         ContractDailyConditions downConditions = JSON.parseObject(downConfig.getExtraRules(),
            //                 ContractDailyConditions.class);
            //         if(downConditions != null && downConditions.getRules() != null &&
            //                 !downConditions.getRules().isEmpty()) {
            //             for(ContractDailyRules downRuls : downConditions.getRules()) {
            //                 if(downMaxScalePoint < downRuls.getScalePoint()) {
            //                     downMaxScalePoint = downRuls.getScalePoint();
            //                 }
            //             }
            //         }
            //     }
            // }

            for(int i = 0; i < conditions.getRules().size(); i++) {
                ContractDailyRules thisBean = conditions.getRules().get(i);
                if(hasUp && thisBean.getScalePoint() >= upMaxScalePoint) {
                    Map<String, Object> prams = new HashMap<>();
                    prams.put("scalePoint", upMaxScalePoint);
                    throw newException("500-075", prams);
                }
                // 先取消此限制
                // if(thisBean.getScalePoint() < downMaxScalePoint) {
                //     Map<String, Object> prams = new HashMap<>();
                //     prams.put("scalePoint", downMaxScalePoint);
                //     throw newException("500-104", prams);
                // }
                if(thisBean.getDailyRecharge().doubleValue() < 0 || thisBean.getDailyConsume().doubleValue() < 0 ||
                        thisBean.getDailyLoss().doubleValue() < 0 || thisBean.getActiveUser() < 0) {
                    throw newException("500-076");
                }
                if(thisBean.getScalePoint() <= 0) {
                    throw newException("500-080");
                }
                if(i > 0) {
                    ContractDailyRules lastBean = conditions.getRules().get(i - 1);
                    if(thisBean.getScalePoint() <= lastBean.getScalePoint()) {
                        throw newException("500-077");
                    }
                    if(thisBean.getDailyRecharge().doubleValue() < lastBean.getDailyRecharge().doubleValue() ||
                            thisBean.getDailyConsume().doubleValue() < lastBean.getDailyConsume().doubleValue() ||
                            thisBean.getDailyLoss().doubleValue() < lastBean.getDailyLoss().doubleValue() ||
                            thisBean.getActiveUser() < lastBean.getActiveUser()) {
                        throw newException("500-078");
                    }
                    // 不能有两条全部一样的规则
                    if(thisBean.getDailyRecharge().equals(lastBean.getDailyRecharge()) &&
                            thisBean.getDailyConsume().equals(lastBean.getDailyConsume()) &&
                            thisBean.getDailyLoss().equals(lastBean.getDailyLoss()) && thisBean.getActiveUser().equals(
                            lastBean.getActiveUser())) {
                        throw newException("500-079");
                    }
                }
            }

            // 用户自己的旧契约规则
            ContractDailyConditions oldConditions = JSON.parseObject(config.getExtraRules(),
                    ContractDailyConditions.class);
            if(oldConditions == null) {
                conditions.setCycleType(cycleType);
                return JSON.toJSONString(conditions);
            }else {
                // 只更新契约规则，不改动活跃用户条件
                oldConditions.setRules(conditions.getRules());
                return JSON.toJSONString(oldConditions);
            }
        }else {
            // 计算周期为周/半月/月
            // 用户自己的契约规则
            ContractPeriodConditions conditions = JSON.parseObject(contractRules, ContractPeriodConditions.class);
            if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                throw newException("500-073");
            }
            // 上级的最大发放比例
            double upMaxScalePoint = 0D;
            if(hasUp) {
                // 上级契约规则
                ContractPeriodConditions upConditions = JSON.parseObject(upConfig.getExtraRules(),
                        ContractPeriodConditions.class);
                if(upConditions == null || upConditions.getRules() == null || upConditions.getRules().isEmpty()) {
                    throw newException("500-074");
                }
                // 上级的最大发放比例
                upMaxScalePoint = upConditions.getRules().get(0).getScalePoint();
                for(int i = 1; i < upConditions.getRules().size(); i++) {
                    if(upMaxScalePoint < upConditions.getRules().get(i).getScalePoint()) {
                        upMaxScalePoint = upConditions.getRules().get(i).getScalePoint();
                    }
                }
            }
            // 递增排序契约规则
            contractUtils.reorderPeriod(conditions.getRules(), true);

            // 先取消此限制
            // // 用户所有下级契约的最大发放比例
            // double downMaxScalePoint = 0D;
            // if(downConfigs != null && !downConfigs.isEmpty()) {
            //     for(ContractPlayerConfig downConfig : downConfigs) {
            //         ContractPeriodConditions downConditions = JSON.parseObject(downConfig.getExtraRules(),
            //                 ContractPeriodConditions.class);
            //         if(downConditions != null && downConditions.getRules() != null &&
            //                 !downConditions.getRules().isEmpty()) {
            //             for(ContractPeriodRules downRuls : downConditions.getRules()) {
            //                 if(downMaxScalePoint < downRuls.getScalePoint()) {
            //                     downMaxScalePoint = downRuls.getScalePoint();
            //                 }
            //             }
            //         }
            //     }
            // }

            for(int i = 0; i < conditions.getRules().size(); i++) {
                ContractPeriodRules thisBean = conditions.getRules().get(i);
                if(hasUp && thisBean.getScalePoint() >= upMaxScalePoint) {
                    Map<String, Object> prams = new HashMap<>();
                    prams.put("scalePoint", upMaxScalePoint);
                    throw newException("500-075", prams);
                }
                // 先取消此限制
                // if(thisBean.getScalePoint() < downMaxScalePoint) {
                //     Map<String, Object> prams = new HashMap<>();
                //     prams.put("scalePoint", downMaxScalePoint);
                //     throw newException("500-104", prams);
                // }
                if(thisBean.getTotalRecharge().doubleValue() < 0 || thisBean.getTotalConsume().doubleValue() < 0 ||
                        thisBean.getTotalLoss().doubleValue() < 0 || thisBean.getActiveUser() < 0) {
                    throw newException("500-076");
                }
                if(thisBean.getScalePoint() <= 0) {
                    throw newException("500-080");
                }
                if(i > 0) {
                    ContractPeriodRules lastBean = conditions.getRules().get(i - 1);
                    if(thisBean.getScalePoint() <= lastBean.getScalePoint()) {
                        throw newException("500-077");
                    }
                    if(thisBean.getTotalRecharge().doubleValue() < lastBean.getTotalRecharge().doubleValue() ||
                            thisBean.getTotalConsume().doubleValue() < lastBean.getTotalConsume().doubleValue() ||
                            thisBean.getTotalLoss().doubleValue() < lastBean.getTotalLoss().doubleValue() ||
                            thisBean.getActiveUser() < lastBean.getActiveUser()) {
                        throw newException("500-078");
                    }
                    // 不能有两条全部一样的规则
                    if(thisBean.getTotalRecharge().equals(lastBean.getTotalRecharge()) &&
                            thisBean.getTotalConsume().equals(lastBean.getTotalConsume()) &&
                            thisBean.getTotalLoss().equals(lastBean.getTotalLoss()) && thisBean.getActiveUser().equals(
                            lastBean.getActiveUser())) {
                        throw newException("500-079");
                    }
                }
            }

            // 用户自己的旧契约规则
            ContractPeriodConditions oldConditions = JSON.parseObject(config.getExtraRules(),
                    ContractPeriodConditions.class);
            if(oldConditions == null) {
                conditions.setCycleType(cycleType);
                return JSON.toJSONString(conditions);
            }else {
                // 只更新契约规则，不改动活跃用户条件
                oldConditions.setRules(conditions.getRules());
                return JSON.toJSONString(oldConditions);
            }
        }
    }

    @Override
    @Transactional
    public synchronized boolean refreshPlatformContract(String agentNo, String contractCode) {
        ContractPlatformConfig config = getContractPlatformConfigDao().getByContractCode(agentNo, contractCode);
        if(config == null) {
            throw newException("500-091");
        }
        // 查询是否有不同用户类型的平台契约，如果有则说明更改了用户类型，需要删除旧的用户契约
        int count = getContractPlayerConfigDao().otherAccountTypeSystemContract(config.getAgentNo(),
                config.getContractCode(), config.getAccountType());
        if(count > 0) {
            // 删除旧的用户契约
            getContractPlayerConfigDao().deleteContract(config.getAgentNo(), config.getContractCode());
        }

        // 下级契约规则，key为层级差，value为extraRules
        Map<String, String> downRulesMap = getDownRulesMap(config);

        List<AgentPlayerInfo> list = getAgentPlayerInfoDao().listByAccountType(config.getAgentNo(),
                config.getAccountType());
        if(list == null || list.isEmpty()) {
            return true;
        }
        Date now = new Date();
        for(AgentPlayerInfo player : list) {
            ContractPlayerConfig contract = getContractPlayerConfigDao().getMyContract(player.getAgentNo(),
                    player.getPlayerId(), config.getContractCode());
            if(contract == null) {
                contract = new ContractPlayerConfig();
            }
            contract.setAgentNo(player.getAgentNo());
            contract.setAgentName(player.getAgentName());
            contract.setPlatformContractId(config.getId());
            contract.setContractCode(config.getContractCode());
            contract.setContractTitle(config.getContractTitle());
            contract.setContractType(config.getContractType());
            contract.setToPlayerId(player.getPlayerId());
            contract.setToPlayerName(player.getPlayerName());
            contract.setFromPlayerId(0L);
            contract.setFromPlayerName("");
            contract.setExtraRules(config.getContractRules());
            contract.setContractTime(now);

            if(contract.getId() == null) {
                getContractPlayerConfigDao().save(contract);
            }else {
                getContractPlayerConfigDao().update(contract);
            }
            refreshPlatformContractDown(player, 1, config, downRulesMap, now);
        }

        return true;
    }

    /**
     * 下级契约规则，key为层级差，value为extraRules
     *
     * @param config
     * @return
     */
    private Map<String, String> getDownRulesMap(ContractPlatformConfig config) {
        Map<String, String> downRulesMap = new HashMap<>();
        if(StringUtils.isEmpty(config.getDownRules())) {
            return downRulesMap;
        }
        if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_DAY) {
            // 上级契约规则
            ContractDailyConditions conditions = JSON.parseObject(config.getContractRules(),
                    ContractDailyConditions.class);
            // 下级契约规则
            List<ContractDailyRulesDown> downRulesList = JSON.parseArray(config.getDownRules(),
                    ContractDailyRulesDown.class);
            for(ContractDailyRulesDown downRules : downRulesList) {
                conditions.setRules(downRules.getDownRuleList());
                downRulesMap.put(String.valueOf(downRules.getDownTiers()), JSON.toJSONString(conditions));
            }
        }else {
            // 上级契约规则
            ContractPeriodConditions conditions = JSON.parseObject(config.getContractRules(),
                    ContractPeriodConditions.class);
            List<ContractPeriodRulesDown> downRulesList = JSON.parseArray(config.getDownRules(),
                    ContractPeriodRulesDown.class);
            for(ContractPeriodRulesDown downRules : downRulesList) {
                conditions.setRules(downRules.getDownRuleList());
                downRulesMap.put(String.valueOf(downRules.getDownTiers()), JSON.toJSONString(conditions));
            }
        }
        return downRulesMap;
    }

    /**
     * 同步下级契约规则
     *
     * @param player 用户
     * @param downTiers 下级层级
     * @param config 平台契约配置
     * @param downRulesMap 下级契约规则
     * @param now 当前时间
     */
    public void refreshPlatformContractDown(AgentPlayerInfo player, int downTiers, ContractPlatformConfig config,
            Map<String, String> downRulesMap, Date now) {
        String key = String.valueOf(downTiers);
        if(!downRulesMap.containsKey(key)) {
            // 防止配置的下级契约规则，层级不是从1开始，或不连续（因为契约是一级级往下签订的，不能出现断层）
            return;
        }
        // 查询直接下级代理用户
        List<AgentPlayerInfo> downList = getAgentPlayerInfoDao().findDirectAgent(player.getPlayerId(), -1, -1);
        for(AgentPlayerInfo downPlayer : downList) {
            ContractPlayerConfig downContract = getContractPlayerConfigDao().getMyContract(downPlayer.getAgentNo(),
                    downPlayer.getPlayerId(), config.getContractCode());
            if(downContract == null) {
                downContract = new ContractPlayerConfig();
            }
            downContract.setAgentNo(downPlayer.getAgentNo());
            downContract.setAgentName(downPlayer.getAgentName());
            downContract.setPlatformContractId(config.getId());
            downContract.setContractCode(config.getContractCode());
            downContract.setContractTitle(config.getContractTitle());
            downContract.setContractType(config.getContractType());
            downContract.setToPlayerId(downPlayer.getPlayerId());
            downContract.setToPlayerName(downPlayer.getPlayerName());
            downContract.setFromPlayerId(player.getPlayerId());
            downContract.setFromPlayerName(player.getPlayerName());
            downContract.setExtraRules(downRulesMap.get(key));
            downContract.setContractTime(now);
            if(downContract.getId() == null) {
                getContractPlayerConfigDao().save(downContract);
            }else {
                getContractPlayerConfigDao().update(downContract);
            }
            // 递归调用
            refreshPlatformContractDown(downPlayer, downTiers + 1, config, downRulesMap, now);
        }
    }

    @Override
    @Transactional
    public boolean deletePlatformContract(String agentNo, String contractCode) {
        ContractPlatformConfig config = getContractPlatformConfigDao().getByContractCode(agentNo, contractCode);
        if(config == null) {
            throw newException("500-091");
        }

        // 删除平台契约
        boolean result = getContractPlatformConfigDao().deleteById(config.getId());

        if(result) {
            // 删除用户契约
            getContractPlayerConfigDao().deleteContract(agentNo, contractCode);
        }
        return result;
    }

    @Override
    @Transactional
    public void createContractPlayerConfig(AgentPlayerInfo player) {
        if(AgentPlayerInfo.PLAYER_TYPE_AGENT != player.getPlayerType()) {
            // 非代理用户不需要创建契约
            return;
        }
        // 签订平台契约
        List<ContractPlatformConfig> list = getContractPlatformConfigDao().listByAccountType(player.getAgentNo(),
                player.getAccountType());
        Date now = new Date();
        if(list != null && !list.isEmpty()) {
            for(ContractPlatformConfig config : list) {
                ContractPlayerConfig contract = new ContractPlayerConfig();
                contract.setAgentNo(player.getAgentNo());
                contract.setAgentName(player.getAgentName());
                contract.setPlatformContractId(config.getId());
                contract.setContractCode(config.getContractCode());
                contract.setContractTitle(config.getContractTitle());
                contract.setContractType(config.getContractType());
                contract.setToPlayerId(player.getPlayerId());
                contract.setToPlayerName(player.getPlayerName());
                contract.setFromPlayerId(0L);
                contract.setFromPlayerName("");
                contract.setExtraRules(config.getContractRules());
                contract.setContractTime(now);
                getContractPlayerConfigDao().save(contract);
            }
        }
        // 签订上级契约
        if(player.getPid() == null || player.getPid() == 0L) {
            return;
        }
        // 查询上级的契约
        List<ContractPlayerConfig> upContractList = getContractPlayerConfigDao().listMyContract(player.getAgentNo(),
                player.getPid());
        if(upContractList == null || upContractList.isEmpty()) {
            return;
        }
        // 计算当前用户的层级
        int myUserLevel = StringIdUtils.toArray(player.getPids()).length;
        for(ContractPlayerConfig contract : upContractList) {
            // 查询平台契约
            ContractPlatformConfig platformConfig = getContractPlatformConfigDao().getById(
                    contract.getPlatformContractId());
            if(platformConfig == null || StringUtils.isEmpty(platformConfig.getDownRules())) {
                // 平台契约不存在或者没有配置下级契约规则
                continue;
            }
            // 查询平台契约的用户类型
            AgentPlayerAccountType accountType = getAgentPlayerAccountTypeDao().getByCode(platformConfig.getAgentNo(),
                    platformConfig.getAccountType());
            // 得到平台契约的用户层级
            int platformConfigUserLevel = accountType.getUserLevel();
            // 计算出层级差
            String key = String.valueOf(myUserLevel - platformConfigUserLevel);
            // 下级契约规则，key为层级差，value为extraRules
            Map<String, String> downRulesMap = getDownRulesMap(platformConfig);
            if(!downRulesMap.containsKey(key)) {
                continue;
            }
            ContractPlayerConfig myContract = new ContractPlayerConfig();
            myContract.setAgentNo(player.getAgentNo());
            myContract.setAgentName(player.getAgentName());
            myContract.setPlatformContractId(platformConfig.getId());
            myContract.setContractCode(platformConfig.getContractCode());
            myContract.setContractTitle(platformConfig.getContractTitle());
            myContract.setContractType(platformConfig.getContractType());
            myContract.setToPlayerId(player.getPlayerId());
            myContract.setToPlayerName(player.getPlayerName());
            myContract.setFromPlayerId(contract.getToPlayerId());
            myContract.setFromPlayerName(contract.getToPlayerName());
            myContract.setExtraRules(downRulesMap.get(key));
            myContract.setContractTime(now);
            getContractPlayerConfigDao().save(myContract);
        }
    }

    @Override
    @Transactional
    public void updateContractPlayerConfig(ContractPlayerConfig newConfig) {
        if(newConfig.getId() == null) {
            throw newException("500-090");
        }
        ContractPlayerConfig config = getContractPlayerConfigDao().getById(newConfig.getId());
        if(config == null) {
            throw newException("500-090");
        }

        // 查询平台契约
        ContractPlatformConfig platformConfig = getContractPlatformConfigDao().getById(config.getPlatformContractId());
        if(platformConfig == null) {
            throw newException("500-091");
        }
        // 验证契约规则
        String extraRules = testPlayerContractRules(platformConfig.getCycleType(), config, newConfig.getExtraRules());
        // 更新契约
        config.setExtraRules(extraRules);
        config.setContractTime(new Date());
        getContractPlayerConfigDao().update(config);

        // 保存操作日志
        String content = "编辑用户契约，agentNo：" + config.getAgentNo() + "，契约编号：" + config.getContractCode() +
                "，契约标题：" + config.getContractTitle() + "，契约类型：" + config.getContractType() + "，契约规则：" +
                config.getExtraRules();
        // 设置 ThreadLocal 变量
        ThreadLocalUtil.setAdminLogContent(content);
    }

    @Override
    @Transactional
    public boolean deleteContractPlayerConfig(ContractPlayerConfig config, boolean deleteMyContract) {
        if(config == null) {
            return true;
        }
        // 查询下级契约
        List<ContractPlayerConfig> downConfig = getContractPlayerConfigDao().listDownContract(config.getAgentNo(),
                config.getContractCode(), config.getToPlayerId());
        if(downConfig != null && !downConfig.isEmpty()) {
            for(ContractPlayerConfig downc : downConfig) {
                // 递归删除所有下级的该编码契约
                deleteContractPlayerConfig(downc, true);
            }
        }

        if(deleteMyContract) {
            // 删除用户契约
            return getContractPlayerConfigDao().deleteContract(config.getId());
        }
        return true;
    }

    @Override
    @Transactional
    public boolean deleteContractPlayerConfig(String agentNo, Long playerId, boolean deleteMyContract) {
        List<ContractPlayerConfig> list = getContractPlayerConfigDao().listMyContract(agentNo, playerId);
        if(list == null || list.isEmpty()) {
            return true;
        }
        for(ContractPlayerConfig config : list) {
            deleteContractPlayerConfig(config, deleteMyContract);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean drawContractRecord(ContractRecord record) {
        log.info("发放契约开始==={}==={}==={}", record.getContractCode(), record.getToPlayerName(),
                record.getCalculateCycle());
        if(record.getDrawStatus() != ContractRecord.DRAW_STATUS_WAITING) {
            log.info("发放契约==={}==={}===状态不对", record.getContractCode(), record.getToPlayerName());
            throw newException("500-082");
        }
        BigDecimal amount = record.getDrawAmount();
        if(amount.compareTo(BigDecimal.ZERO) <= 0) {
            // 金额小于等于0，直接更新状态为已发放
            log.info("发放契约==={}==={}===金额等于0", record.getContractCode(), record.getToPlayerName());
            boolean setDone = getContractRecordDao().setDone(record.getId());
            if(!setDone) {
                throw newException("500-086");
            }
            return true;
        }

        AgentPlayerInfo toPlayer = getAgentPlayerInfoDao().getById(record.getToPlayerId());
        if(toPlayer == null) {
            log.info("发放契约==={}==={}===用户不存在", record.getContractCode(), record.getToPlayerName());
            throw newException("100-01");
        }
        // 备注
        String remark = record.getContractTitle() + " " + record.getCalculateCycle();
        // 订单号
        String billno = RandomUtils.fromTime16();
        int billType = AgentPlayerAccountBill.BILL_TYPE_DIVIDEND;
        if(record.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY ||
                record.getContractType() == ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY) {
            billType = AgentPlayerAccountBill.BILL_TYPE_SALARY;
        }

        // 契约生成单子的时候，契约发放记录单子里的发放金额已经扣除了下级的，所以此处不用再从上级扣钱
        // if(record.getFromPlayerId() != null && record.getFromPlayerId() != 0L) {
        //     AgentPlayerInfo fromPlayer = getAgentPlayerInfoDao().getById(record.getFromPlayerId());
        //     if(fromPlayer == null) {
        //         log.info("发放契约==={}==={}===契约上级不存在", record.getContractCode(), record.getToPlayerName());
        //         throw newException("500-013");
        //     }
        //     // 验证账户锁定时间
        //     Date accountLockTime = fromPlayer.getLockTime();
        //     if(accountLockTime != null) {
        //         Moment lockMoment = new Moment().fromDate(accountLockTime);
        //         boolean isNotTime = new Moment().le(lockMoment);
        //         if(isNotTime) {
        //             // ERR:账户已经锁定
        //             String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
        //             Map<String, Object> prams = new HashMap<>();
        //             prams.put("time", time);
        //             throw newException("500-083", prams);
        //         }
        //     }
        //     // 验证中心账户余额
        //     if(fromPlayer.getPlayerAvailableBalance().doubleValue() < amount.doubleValue()) {
        //         // ERR:账户余额不足
        //         log.info("发放契约==={}==={}===账户余额不足", record.getContractCode(), record.getToPlayerName());
        //         throw newException("500-084");
        //     }
        //     // 备注
        //     String fromRemark = record.getContractTitle() + " " + toPlayer.getPlayerName() + " " +
        //             record.getCalculateCycle();
        //     // 扣除我的中心账户余额
        //     boolean updateFromBalance = getPlatformAgentPlayerCommonService().updateAvailableBalanceForGameOut(
        //             fromPlayer.getPlayerId(), amount.negate(), amount, billType, billno, fromPlayer.getPlayerName(),
        //             fromRemark);
        //     if(!updateFromBalance) {
        //         // ERR:账户余额不足
        //         log.info("发放契约==={}==={}===契约上级扣钱失败", record.getContractCode(), record.getToPlayerName());
        //         throw newException("500-084");
        //     }
        // }
        // 给目标中心账户加钱
        boolean updateToBalance = getPlatformAgentPlayerCommonService().updateAvailableBalanceForGameIn(
                toPlayer.getPlayerId(), amount, amount.negate(), billType, billno, toPlayer.getPlayerName(), remark);
        if(!updateToBalance) {
            log.info("发放契约==={}==={}===加钱失败", record.getContractCode(), record.getToPlayerName());
            throw newException("500-085");
        }
        // 更新状态为已发放
        boolean setDone = getContractRecordDao().setDone(record.getId());
        if(!setDone) {
            log.info("发放契约==={}==={}===更新状态失败", record.getContractCode(), record.getToPlayerName());
            throw newException("500-086");
        }
        log.info("发放契约结束==={}==={}==={}", record.getContractCode(), record.getToPlayerName(),
                record.getCalculateCycle());
        return true;
    }

    @Override
    @Transactional
    public boolean doDraw(ContractPlatformConfig config, ContractPlayerConfig playerConfig, AgentPlayerInfo player,
            Date sDate, Date eDate, String calculateCycle) {
        try {
            log.info("doDraw契约===契约编码：{}，契约标题：{}，用户：{}，计算周期：{}", config.getContractCode(),
                    config.getContractTitle(), player.getPlayerName(), calculateCycle);
            int contractType = config.getContractType();
            // 计算量
            PlayerAmount playerAmount;
            // 契约记录
            ContractRecord contractRecord = null;
            // 契约规则
            switch(contractType) {
                case ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_SALARY:
                    // 彩票工资
                case ContractPlatformConfig.CONTRACT_TYPE_LOTTERY_DIVIDEND:
                    // 彩票分红
                case ContractPlatformConfig.CONTRACT_TYPE_THIRD_SALARY:
                    // 三方工资
                case ContractPlatformConfig.CONTRACT_TYPE_THIRD_DIVIDEND:
                    // 三方分红
                    // 彩票工资/彩票分红/三方工资/三方分红，只有获取周期量和计算契约金额不一样
                    if(contractUtils.hasReceiveReward(playerConfig, player.getPlayerId(), calculateCycle)) {
                        log.info("doDraw契约==={}==={}===已生成契约记录", config.getContractCode(),
                                player.getPlayerName());
                        throw newException("120-04");
                    }
                    playerAmount = contractUtils.getPlayerAmount(config, playerConfig, player, sDate, eDate);
                    log.info("doDraw契约==={}==={}==={}", config.getContractCode(), player.getPlayerName(),
                            playerAmount.toString());

                    if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_DAY) {
                        // 日
                        // 获取奖励规则
                        ContractDailyConditions conditions = JSON.parseObject(playerConfig.getExtraRules(),
                                ContractDailyConditions.class);
                        if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                            log.info("doDraw契约==={}==={}===没有满足条件的契约规则", config.getContractCode(),
                                    player.getPlayerName());
                            throw new IllegalArgumentException("120-01");
                        }
                        // 奖励规则倒序排序
                        contractUtils.reorderDaily(conditions.getRules(), false);
                        ContractDailyRules perfectRules = contractUtils.getDailyPerfectRules(conditions.getRules(),
                                playerAmount);
                        // 达标状态
                        int achieveStatus = ContractRecord.ACHIEVE_STATUS_YES;
                        if(perfectRules == null) {
                            log.info("doDraw契约==={}==={}===未达标", config.getContractCode(), player.getPlayerName());
                            // 未达标
                            achieveStatus = ContractRecord.ACHIEVE_STATUS_NO;
                            // 获取最低比例规则
                            perfectRules = conditions.getRules().get(conditions.getRules().size() - 1);
                        }
                        contractRecord = contractUtils.getDailyRewardRecord(perfectRules, player, playerConfig,
                                playerAmount, calculateCycle, achieveStatus);
                    }else if(config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_WEEK ||
                            config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_HALF_MONTH ||
                            config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MONTH ||
                            config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MIN_10 ||
                            config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MIN_20 ||
                            config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_MIN_30 ||
                            config.getCycleType() == ContractPlatformConfig.CYCLE_TYPE_HOUR_1) {
                        // 周/半月/月
                        // 获取奖励规则
                        ContractPeriodConditions conditions = JSON.parseObject(playerConfig.getExtraRules(),
                                ContractPeriodConditions.class);
                        if(conditions == null || conditions.getRules() == null || conditions.getRules().isEmpty()) {
                            log.info("doDraw契约==={}==={}===没有满足条件的契约规则", config.getContractCode(),
                                    player.getPlayerName());
                            throw new IllegalArgumentException("120-01");
                        }
                        // 奖励规则倒序排序
                        contractUtils.reorderPeriod(conditions.getRules(), false);
                        ContractPeriodRules perfectRules = contractUtils.getPeriodPerfectRules(config,
                                conditions.getRules(), playerAmount);
                        // 达标状态
                        int achieveStatus = ContractRecord.ACHIEVE_STATUS_YES;
                        if(perfectRules == null) {
                            log.info("doDraw契约==={}==={}===未达标", config.getContractCode(), player.getPlayerName());
                            // 未达标
                            achieveStatus = ContractRecord.ACHIEVE_STATUS_NO;
                            // 获取最低比例规则
                            perfectRules = conditions.getRules().get(conditions.getRules().size() - 1);
                        }
                        contractRecord = contractUtils.getPeriodRewardRecord(perfectRules, player, playerConfig,
                                playerAmount, calculateCycle, achieveStatus);
                    }
                    if(contractRecord != null) {
                        if(contractRecord.getDrawAmount().compareTo(BigDecimal.ZERO) > 0) {
                            // 给上级扣掉金额
                            if(contractRecord.getFromPlayerId() != null && contractRecord.getFromPlayerId() != 0L) {
                                // 查询上级契约发放记录
                                ContractRecord fromRecord = getContractRecordDao().getByToPlayer(
                                        contractRecord.getAgentNo(), contractRecord.getContractCode(),
                                        contractRecord.getCalculateCycle(), contractRecord.getFromPlayerId());
                                if(fromRecord != null) {
                                    // 上级契约发放金额需要减去下级的
                                    BigDecimal fromDrawAmount = fromRecord.getDrawAmount().subtract(
                                            contractRecord.getDrawAmount());
                                    if(fromDrawAmount.compareTo(BigDecimal.ZERO) < 0) {
                                        // 上级契约发放金额，不足以改给下级时，上级的全部给下级
                                        fromDrawAmount = BigDecimal.ZERO;
                                        contractRecord.setRemarks(
                                                String.format("上级契约发放金额不足，理论发放%.2f，实际发放%.2f",
                                                        contractRecord.getDrawAmount(), fromRecord.getDrawAmount()));
                                        contractRecord.setDrawAmount(fromRecord.getDrawAmount());
                                    }
                                    // 更新上级的发放金额
                                    fromRecord.setDrawAmount(fromDrawAmount);
                                    // 更新上级的下级发放金额
                                    fromRecord.setDownDrawAmount(
                                            fromRecord.getDownDrawAmount().add(contractRecord.getDrawAmount()));
                                    getContractRecordDao().update(fromRecord);
                                }
                            }
                        }
                        // 扣除下级发放金额
                        // 查询下级契约发放记录
                        List<ContractRecord> downRecordList = getContractRecordDao().getByFromPlayer(
                                contractRecord.getAgentNo(), contractRecord.getContractCode(),
                                contractRecord.getCalculateCycle(), contractRecord.getToPlayerId());
                        if(downRecordList != null && !downRecordList.isEmpty()) {
                            // 发放金额
                            BigDecimal drawAmount = contractRecord.getDrawAmount();
                            // 下级发放金额
                            BigDecimal downDrawAmount = BigDecimal.ZERO;
                            for(ContractRecord downRecord : downRecordList) {
                                if(downRecord.getDrawAmount().compareTo(BigDecimal.ZERO) <= 0) {
                                    // 下级的发放金额小于等于0，不处理
                                    continue;
                                }
                                if(drawAmount.compareTo(downDrawAmount) > 0) {
                                    // 减去下级的发放金额
                                    drawAmount = drawAmount.subtract(downRecord.getDrawAmount());
                                }else {
                                    // 发放金额小于下级的发放金额，将全部发放金额给下级
                                    downRecord.setRemarks(
                                            String.format("上级契约发放金额不足，理论发放%.2f，实际发放%.2f",
                                                    downRecord.getDrawAmount(), drawAmount));
                                    downRecord.setDrawAmount(drawAmount);
                                    getContractRecordDao().update(downRecord);
                                    drawAmount = BigDecimal.ZERO;
                                }
                                // 加上下级
                                downDrawAmount = downDrawAmount.add(downRecord.getDrawAmount());
                            }
                            contractRecord.setDrawAmount(drawAmount);
                        }

                        // 保存记录
                        boolean result = getContractRecordDao().save(contractRecord);
                        if(!result) {
                            log.info("doDraw契约==={}==={}===保存契约发放记录失败", config.getContractCode(),
                                    player.getPlayerName());
                            throw new IllegalArgumentException();
                        }
                    }
                    break;
            }
        }catch(ServiceException e) {
            return false;
        }catch(IllegalArgumentException e) {
            return false;
        }
        return true;
    }
}
