package ph.yckj.admin.service.impl;

import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.admin.service.LockAccountRecordService;
import ph.yckj.admin.util.AbstractService;
import sy.hoodle.base.common.entity.LockAccountRecord;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class LockAccountRecordServiceImpl extends AbstractService implements LockAccountRecordService {

    @Override
    @Transactional
    public boolean save(String agentNo, String agentName, Integer lockType, String playerNameList, String lottery,
            String issue, Integer lockNumber, String lockRemark, BigDecimal amountMix, BigDecimal amountMax,
            String creater) {
        LockAccountRecord entity = new LockAccountRecord();
        entity.setAgentNo(agentNo);
        entity.setAgentName(agentName);
        entity.setLockType(lockType);
        if(StringUtils.isNotBlank(playerNameList)) {
            entity.setPlayerNameList(playerNameList);
        }else {
            entity.setPlayerNameList("");
        }
        if(StringUtils.isNotBlank(lottery)) {
            entity.setLottery(lottery);
        }else {
            entity.setLottery("");
        }
        if(StringUtils.isNotBlank(lockRemark)) {
            entity.setLockRemark(lockRemark);
        }else {
            entity.setLockRemark("");
        }
        if(lockNumber != null) {
            entity.setLockNumber(lockNumber);
        }
        if(StringUtils.isNotBlank(issue)) {
            entity.setIssue(issue);
        }else {
            entity.setIssue("");
        }
        if(amountMix != null) {
            entity.setAmountMix(amountMix);
        }
        if(amountMax != null) {
            entity.setAmountMax(amountMax);
        }
        if(lockType == LockAccountRecord.LOCK_TYPE_ALL) {
            entity.setPlayerNameList("");
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setLockStatus(LockAccountRecord.LOCK_STATUS_DISABLE);
        entity.setCreater(creater);
        entity.setIsDelete(0);
        return getLockAccountRecordDao().save(entity);
    }

    @Override
    public LockAccountRecord getById(int id) {
        return getLockAccountRecordDao().getById(id);
    }

    @Override
    @Transactional
    public void update(LockAccountRecord entity) {
        getLockAccountRecordDao().update(entity);
    }

    @Override
    @Transactional
    public boolean delete(int id) {
        return getLockAccountRecordDao().delete(id);
    }

    @Override
    public List<LockAccountRecord> getLockRecordByUsername(String agentNo, String agentName, String playerName,
            Integer lockType, String lottery, String issue, BigDecimal amountMix, BigDecimal amountMax) {
        List<Order> orders = new ArrayList<>();
        orders.add(Order.desc("id"));
        List<Criterion> criterions = new ArrayList<>();
        if(StringUtils.isNotBlank(agentNo)) {
            criterions.add(Restrictions.eq("agentNo", agentNo));
        }
        if(StringUtils.isNotBlank(agentName)) {
            criterions.add(Restrictions.eq("agentName", agentName));
        }
        if(StringUtils.isNotBlank(playerName)) {
            criterions.add(Restrictions.like("playerNameList", "%" + playerName + "%"));
        }
        if(StringUtils.isNotBlank(lottery)) {
            criterions.add(Restrictions.eq("lottery", lottery));
        }
        if(StringUtils.isNotBlank(issue)) {
            criterions.add(Restrictions.eq("issue", issue));
        }
        if(lockType != null) {
            criterions.add(Restrictions.eq("lockType", lockType));
        }
        if(amountMix != null && amountMix.compareTo(BigDecimal.ZERO) > 0) {
            criterions.add(Restrictions.ge("amountMix", amountMix));
        }
        if(amountMax != null && amountMax.compareTo(BigDecimal.ZERO) > 0) {
            criterions.add(Restrictions.le("amountMax", amountMax));
        }
        criterions.add(Restrictions.eq("lockStatus", 1));
        criterions.add(Restrictions.eq("isDelete", 0));
        List<LockAccountRecord> list = getLockAccountRecordDao().find(criterions, orders, -1, -1);
        if(null != list && !list.isEmpty()) {
            return list;
        }
        return null;
    }

    @Override
    public List<LockAccountRecord> find(String agentNo, String agentName, String playerName, String lottery,
            String issue, Integer lockType, Integer lockStatus, int firstResult, int maxResults) {
        List<Order> orders = new ArrayList<>();
        orders.add(Order.desc("id"));
        List<Criterion> criterions = new ArrayList<>();
        if(StringUtils.isNotBlank(agentNo)) {
            criterions.add(Restrictions.eq("agentNo", agentNo));
        }
        if(StringUtils.isNotBlank(agentName)) {
            criterions.add(Restrictions.eq("agentName", agentName));
        }
        if(StringUtils.isNotBlank(playerName)) {
            criterions.add(Restrictions.like("playerNameList", "%" + playerName + "%"));
        }
        if(StringUtils.isNotBlank(lottery)) {
            criterions.add(Restrictions.eq("lottery", lottery));
        }
        if(StringUtils.isNotBlank(issue)) {
            criterions.add(Restrictions.eq("issue", issue));
        }
        if(lockType != null) {
            criterions.add(Restrictions.eq("lockType", lockType));
        }
        if(lockStatus != null) {
            criterions.add(Restrictions.eq("lockStatus", lockStatus));
        }
        criterions.add(Restrictions.eq("isDelete", 0));
        List<LockAccountRecord> list = getLockAccountRecordDao().find(criterions, orders, firstResult, maxResults);
        if(null != list && !list.isEmpty()) {
            return list;
        }
        return null;
    }

    @Override
    public int totalRecord(String agentNo, String agentName, String playerName, String lottery, String issue,
            Integer lockType, Integer lockStatus) {
        List<Criterion> criterions = new ArrayList<>();
        if(StringUtils.isNotBlank(agentNo)) {
            criterions.add(Restrictions.eq("agentNo", agentNo));
        }
        if(StringUtils.isNotBlank(agentName)) {
            criterions.add(Restrictions.eq("agentName", agentName));
        }
        if(StringUtils.isNotBlank(playerName)) {
            criterions.add(Restrictions.like("playerNameList", "%" + playerName + "%"));
        }
        if(StringUtils.isNotBlank(lottery)) {
            criterions.add(Restrictions.eq("lottery", lottery));
        }
        if(StringUtils.isNotBlank(issue)) {
            criterions.add(Restrictions.eq("issue", issue));
        }
        if(lockType != null) {
            criterions.add(Restrictions.eq("lockType", lockType));
        }
        if(lockStatus != null) {
            criterions.add(Restrictions.eq("lockStatus", lockStatus));
        }
        criterions.add(Restrictions.eq("isDelete", 0));

        return getLockAccountRecordDao().totalCount(criterions);
    }
}
