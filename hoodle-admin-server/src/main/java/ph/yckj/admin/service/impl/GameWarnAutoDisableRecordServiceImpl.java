package ph.yckj.admin.service.impl;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.admin.service.GameWarnAutoDisableRecordService;
import sy.hoodle.base.common.dao.GameWarnAutoDisableRecordDao;
import sy.hoodle.base.common.entity.GameWarnAutoDisableRecord;

import java.util.Date;
import java.util.List;
@Service
@Transactional
public class GameWarnAutoDisableRecordServiceImpl implements GameWarnAutoDisableRecordService {

    @Autowired
    private GameWarnAutoDisableRecordDao gameWarnAutoDisableRecordDao;

    @Override
    public GameWarnAutoDisableRecord getById(String lotteryGameplay, Date createTime) {
        return gameWarnAutoDisableRecordDao.getById(lotteryGameplay, createTime);
    }

    @Override
    public boolean save(GameWarnAutoDisableRecord entity) {
        return gameWarnAutoDisableRecordDao.save(entity);
    }

    @Override
    public void insert(List<GameWarnAutoDisableRecord> records) {
        gameWarnAutoDisableRecordDao.insert(records);
    }

    @Override
    public boolean update(String status, String lotteryGameplay, String methodCode, String methodType, Date createTime) {
        return gameWarnAutoDisableRecordDao.update(status, lotteryGameplay,methodCode, methodType, createTime);
    }

    @Override
    public List<GameWarnAutoDisableRecord> find(List<Criterion> criterions, List<Order> orders, int firstResult, int maxResults) {
        return gameWarnAutoDisableRecordDao.find(criterions, orders, firstResult, maxResults);
    }

    @Override
    public List<GameWarnAutoDisableRecord> find(List<Criterion> criterions) {
        return gameWarnAutoDisableRecordDao.find(criterions);
    }
}
