package ph.yckj.admin.service;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import sy.hoodle.base.common.entity.GameWarnConfigInfo;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public interface GameWarnConfigInfoService {

    GameWarnConfigInfo getById(Long id);

    boolean save(String agentNo, String agentName, BigDecimal warnLossAmount, BigDecimal warnDisableLossVolume, String measurementWay, String warnWay,
                 String automaticDisableWay, String warnClear, String warnStatus, String warnRemark, int isDelete);

    void update(String agentNo, String agentName, BigDecimal warnLossAmount, BigDecimal warnDisableLossVolume, String measurementWay,
                String automaticDisableWay, Long id, GameWarnConfigInfo entity);

    boolean updateStatus(Long id, String warnStatus);

    /**
     * 设置预警配置关联的彩种
     */
    boolean setLotteryList(Long id, String lotteryList);

    List<GameWarnConfigInfo> find(List<Criterion> criterions, List<Order> orders, int firstResult, int maxResults);

}
