package ph.yckj.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import myutil.ChineseNicknameGenerator;
import myutil.MathUtils;
import myutil.Moment;
import myutil.StringIdUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ph.yckj.admin.service.AgentPlayerService;
import ph.yckj.admin.service.ContractService;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.common.util.ServiceException;
import sy.hoodle.base.common.dao.AgentPlayerInfoDao;
import sy.hoodle.base.common.entity.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@Transactional
public class AgentPlayerServiceImpl extends AbstractService implements AgentPlayerService {
    @Autowired
    private ContractService contractService;
    @Autowired
    private AgentPlayerInfoDao agentPlayerInfoDao;

    private static final ExecutorService executor = Executors.newFixedThreadPool(10);

    @Autowired
    private PlatformTransactionManager transactionManager;


    public AgentPlayerInfo initPlayer(String agentNo, Integer agentType, String agentName, Long pid, String pids,
            String playerName, String playerPassword, Integer playerType, Double point) {
        AgentPlayerInfo player = new AgentPlayerInfo();
        player.setAgentNo(agentNo);
        player.setAgentType(agentType);
        player.setAgentName(agentName);
        player.setPlayerName(playerName);
        player.setPlayerPassword(playerPassword);
        player.setPlayerTelegramId(0L);
        player.setPlayerBlockedBalance(BigDecimal.ZERO);
        player.setPlayerAvailableBalance(BigDecimal.ZERO);
        player.setPid(pid);
        player.setPids(pids);
        player.setPlayerType(playerType);
        player.setPlayerNick(ChineseNicknameGenerator.generateNickname());
        player.setWithdrawName("");
        player.setWithdrawLimit(0);
        player.setWithdrawPassword("");
        player.setWithdrawLimitBalance(BigDecimal.ZERO);
        player.setDevice("");
        player.setPlayerStatus(0);
        player.setOnlineStatus(0);
        player.setMarkersStatus(0);
        player.setSkinId(0);
        player.setMusicId(0);
        player.setLanguageId(0);
        player.setUrlAuthKey("");
        player.setPlayerWealth(0);
        player.setPlayerLevelId(0);
        player.setGoogleKey("");
        player.setGoogleBind(0);
        player.setGoogleLogin(0);
        player.setLockTime(null);
        player.setSessionId("");
        player.setLastLoginTime(null);
        player.setSex(0);
        player.setEmail("");
        player.setBirthday("");
        player.setTelephone("");
        player.setOutThirdAutoTransfer(AgentPlayerInfo.AUTO_TRANSFER_ON);
        player.setCreateBy(player.getPlayerName());
        player.setCreateTime(new Moment().toTimestamp());
        player.setUpdateTime(player.getCreateTime());
        player.setIsDelete(0);
        player.setAllowTransfer(AgentPlayerInfo.ALLOW_TRANSFER_ON);
        player.setPoint(point);
        player.setLotteryCode(getLotteryCode(agentNo, point));
        AgentPlayerAccountType accountType = getAccountType(agentNo, playerType, pids, player.getLotteryCode());
        player.setAccountType(accountType.getCode());
        player.setEqualLevel(testEqualCode(player, accountType));
        return player;
    }

    @Override
    public AgentPlayerAccountType getAccountType(String agentNo, int playerType, String pids, int lotteryCode) {
        AgentPlayerAccountType accountType;
        if(AgentPlayerInfo.PLAYER_TYPE_PLAYER == playerType) {
            // 获取玩家的用户类型
            return getAgentPlayerAccountTypeDao().getByCode(agentNo, AgentPlayerAccountType.CODE_PLAYER);
        }
        // 用户层级
        int level = StringIdUtils.toArray(pids).length;
        accountType = getAgentPlayerAccountTypeDao().getAccountType(agentNo, lotteryCode, level);
        if(accountType == null) {
            // 查询默认用户类型
            accountType = getAgentPlayerAccountTypeDao().getByCode(agentNo, AgentPlayerAccountType.CODE_AGENT);
        }
        return accountType;
    }

    @Override
    public String getUpPlayerNames(String pids) {
        List<AgentPlayerInfo> upPlayers = getAgentPlayerInfoDao().listAllParent(pids);
        if (upPlayers == null || upPlayers.isEmpty()) {
            return "";
        }
        String[] upPlayerNameArry = new String[upPlayers.size()];
        for (int i = 0; i < upPlayers.size(); i++) {
            upPlayerNameArry[i] = upPlayers.get(i).getPlayerName();
        }
        return String.join(" - ", upPlayerNameArry);
    }

    /**
     * 验证同级开号
     *
     * @param newPlayer   新用户
     * @param accountType 用户类型
     * @return
     */
    private Integer testEqualCode(AgentPlayerInfo newPlayer, AgentPlayerAccountType accountType) {
        // 计算奖级
        int lotteryCode = getLotteryCode(newPlayer.getAgentNo(), newPlayer.getPoint());

        if (newPlayer.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_AGENT) {
            // 获取系统最高奖级
            PlatformConfig autoEqualCodeConfig =
                    getPlatformConfigDao().getPlatformConfigByGroupAndKey(newPlayer.getAgentNo(), "ACCOUNT",
                            "AUTO_EQUAL_CODE");
            if (autoEqualCodeConfig != null) {
                String autoEqualCode = autoEqualCodeConfig.getValue();
                if (!StringUtils.isEmpty(autoEqualCode)) {
                    String[] array = autoEqualCode.split(",");
                    for (String tmpCode : array) {
                        if (Integer.parseInt(tmpCode) == lotteryCode) {
                            return AgentPlayerInfo.EQUAL_LEVEL_ON;
                        }
                    }
                }
            }
            if (AgentPlayerAccountType.EQUAL_LEVLEL_TRUE == accountType.getEqualLevel()) {
                return AgentPlayerInfo.EQUAL_LEVEL_ON;
            }
        }

        return AgentPlayerInfo.EQUAL_LEVEL_OFF;
    }

    @Override
    public Integer getActivePlayerCount(Long playerId, Date first, Date last, boolean isAnd, BigDecimal betAmount, BigDecimal rechargeAmount) {
        Integer activePlayerCount = 0;

        activePlayerCount = fromReportData(playerId, first, last, isAnd, betAmount, rechargeAmount);

        return activePlayerCount;
    }

    private Integer fromSourcesData(Long playerId) {
        Integer activePlayerCount = 0;
        try {
            LocalDate firstDay = LocalDate.now().withDayOfMonth(1);
            ZonedDateTime zonedDateTimeFirst = firstDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
            Date first = Date.from(zonedDateTimeFirst.toInstant());

            LocalDate lastDay = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
            ZonedDateTime zonedDateTimeLast = lastDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
            Date last = Date.from(zonedDateTimeLast.toInstant());

            List<Criterion> criterionOrders = new ArrayList<Criterion>();
            criterionOrders.add(Restrictions.eq("pid", playerId));
            criterionOrders.add(Restrictions.ge("createTime", first));
            criterionOrders.add(Restrictions.lt("createTime", last));
            criterionOrders.add(Restrictions.in("orderStatus", Arrays.asList(AgentPlayerGameOrder.GAME_STATUS_WAITING,
                    AgentPlayerGameOrder.GAME_STATUS_LOSE, AgentPlayerGameOrder.GAME_STATUS_WIN)));

            List<AgentPlayerGameOrder> agentPlayerGameOrders = getAgentPlayerGameOrderDao().find(criterionOrders,
                    new ArrayList<>());
            // 按 playerId 分组，计算每个 playerId 的 betAmount 总和，过滤掉总和小于 10000 的 playerId
            List<Long> playerOrderIds = agentPlayerGameOrders.stream()
                    .collect(Collectors.groupingBy(AgentPlayerGameOrder::getPlayerId,
                            Collectors.reducing(BigDecimal.ZERO, AgentPlayerGameOrder::getBetAmount, BigDecimal::add)))
                    .entrySet().stream().filter(entry -> entry.getValue().compareTo(new BigDecimal("10000")) >= 0) // 过滤总和大于等于
                    // 10000
                    // 的玩家
                    .map(Map.Entry::getKey).collect(Collectors.toList());

            List<Criterion> criterionRecharge = new ArrayList<Criterion>();
            criterionRecharge.add(Restrictions.eq("pid", playerId));
            criterionRecharge.add(Restrictions.ge("orderTime", first));
            criterionRecharge.add(Restrictions.lt("orderTime", last));
            criterionRecharge.add(Restrictions.eq("orderStatus", PlayerRecharge.ORDER_STATUS_COMPLETED));

            List<PlayerRecharge> playerRecharges = getPlayerRechargeDao().find(criterionRecharge, new ArrayList<>());
            List<Long> playerRechargeIds = playerRecharges.stream()
                    .collect(Collectors.groupingBy(PlayerRecharge::getPlayerId,
                            Collectors.reducing(BigDecimal.ZERO, PlayerRecharge::getActualAmount, BigDecimal::add)))
                    .entrySet().stream().filter(entry -> entry.getValue().compareTo(new BigDecimal("1000")) >= 0) // 过滤总和大于等于
                    // 10000
                    // 的玩家
                    .map(Map.Entry::getKey).collect(Collectors.toList());
            List<Long> mergedAndDistinct = Stream.concat(playerOrderIds.stream(), playerRechargeIds.stream()).distinct()
                    .collect(Collectors.toList());
            activePlayerCount = mergedAndDistinct.size();
        } catch (Exception e) {
            log.error("获取活跃用户失败，pid：{}", playerId, e);
        }

        return activePlayerCount;
    }

    private Integer fromReportData(Long playerId, Date first, Date last, boolean isAnd, BigDecimal betAmount, BigDecimal rechargeAmount) {
        Integer activePlayerCount = 0;
        try {
//			LocalDate firstDay = LocalDate.now().withDayOfMonth(1);
//			ZonedDateTime zonedDateTimeFirst = firstDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
//			Date first = Date.from(zonedDateTimeFirst.toInstant());
//
//			LocalDate lastDay = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
//			ZonedDateTime zonedDateTimeLast = lastDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
//			Date last = Date.from(zonedDateTimeLast.toInstant());

            List<Criterion> criterionOrders = new ArrayList<Criterion>();
            criterionOrders.add(Restrictions.eq("pid", playerId));
            criterionOrders.add(Restrictions.ge("createTime", first));
            criterionOrders.add(Restrictions.lt("createTime", last));

            List<PlatformAgentPlayerReport> platformAgentPlayerReports = getPlatformAgentPlayerReportDao()
                    .find(criterionOrders, new ArrayList<>());


            List<Long> ids = platformAgentPlayerReports.stream().filter(report -> {
                if (isAnd) {
                    if (report.getTransferInAmount().compareTo(rechargeAmount) >= 0
                            && report.getBetAmount().compareTo(betAmount) >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    if (report.getTransferInAmount().compareTo(rechargeAmount) >= 0
                            || report.getBetAmount().compareTo(betAmount) >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());

            activePlayerCount = ids.size();
        } catch (Exception e) {
            log.error("获取活跃用户失败，pid：{}", playerId, e);
        }
        return activePlayerCount;
    }

    @Override
    public Integer getActivePlayerCount(String agentNo, Long playerId, Date first, Date last, int promoteType) {
        Integer activePlayerCount = 0;

        activePlayerCount = fromReportData(agentNo, playerId, first, last, promoteType);

        return activePlayerCount;
    }

    private Integer fromReportData(String agentNo, Long playerId, Date first, Date last, int promoteType) {
        Integer activePlayerCount = 0;
        try {
//			LocalDate firstDay = LocalDate.now().withDayOfMonth(1);
//			ZonedDateTime zonedDateTimeFirst = firstDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
//			Date first = Date.from(zonedDateTimeFirst.toInstant());
//
//			LocalDate lastDay = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
//			ZonedDateTime zonedDateTimeLast = lastDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
//			Date last = Date.from(zonedDateTimeLast.toInstant());

            List<Criterion> criterionOrders = new ArrayList<Criterion>();
            criterionOrders.add(Restrictions.eq("pid", playerId));
            criterionOrders.add(Restrictions.ge("reportDate", first));
            criterionOrders.add(Restrictions.lt("reportDate", last));

            List<PlatformAgentPlayerReport> platformAgentPlayerReports = new ArrayList<>();
            if (promoteType == 1) {
                platformAgentPlayerReports = getPlatformAgentPlayerReportDao()
                        .find(criterionOrders, new ArrayList<>());
            } else {
                List<GameSimulationReport> gameSimulationReports = getGameSimulationReportDao().listGroupByPlayer(playerId, first, last, "", 0, 10000);
                for (GameSimulationReport gameSimulationReport : gameSimulationReports) {
                    PlatformAgentPlayerReport platformAgentPlayerReport = new PlatformAgentPlayerReport();
                    platformAgentPlayerReport.setAgentNo(gameSimulationReport.getAgentNo());
                    platformAgentPlayerReport.setPlayerId(gameSimulationReport.getAccountId());
                    platformAgentPlayerReport.setBetAmount(gameSimulationReport.getValidBetAmount());
                    platformAgentPlayerReport.setTransferInAmount(gameSimulationReport.getDepositAmount());
                    platformAgentPlayerReports.add(platformAgentPlayerReport);
                }
            }

            AgentPromoteConfig promoteConfig = getAgentPromoteConfigDao().getByAgentNoAndPromoteType(agentNo, promoteType);
            if (promoteConfig == null) {
                return activePlayerCount;
            }
            int activityCondition = promoteConfig.getActivityCondition();
            BigDecimal betAmount;
            BigDecimal rechargeAmount;

            JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
            betAmount = jsonObject.getBigDecimal("betAmount");
            rechargeAmount = jsonObject.getBigDecimal("rechargeAmount");

            if (betAmount == null && rechargeAmount == null) {
                betAmount = new BigDecimal(10000);
                rechargeAmount = new BigDecimal(1000);
                activityCondition = 4;
            }

            List<Long> ids = new ArrayList<>();
            BigDecimal finalBetAmount = betAmount;
            BigDecimal finalRechargeAmount = rechargeAmount;
            switch (activityCondition) {
                case 1:
                    ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
                    break;
                case 2:
                    ids = platformAgentPlayerReports.stream().filter(report -> report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
                    break;
                case 3:
                    ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0 && report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
                    break;
                case 4:
                    ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0 || report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
                    break;
            }
            activePlayerCount = ids.size();
        } catch (Exception e) {
            log.error("获取活跃用户失败，pid：{}", playerId, e);
        }
        return activePlayerCount;
    }

    @Override
    public boolean updatePoint(AgentPlayerInfo player, double point) {
        int lotteryCode = getLotteryCode(player.getAgentNo(), point);
        AgentPlayerAccountType accountType = getAccountType(player.getAgentNo(), player.getPlayerType(),
                player.getPids(), lotteryCode);
        return getAgentPlayerInfoDao().editLotteryPoint(player.getPlayerId(), point, lotteryCode,
                accountType.getCode());
    }

    @Override
    public boolean downLinePoint(AgentPlayerInfo player) {
        List<AgentPlayerInfo> list = getAgentPlayerInfoDao().listTeamPath(player.getAgentNo(), player.getPlayerId());
        list.add(0, player); // 加入自己
        for (AgentPlayerInfo p : list) {
            double point = p.getPoint();
            if (point > 0) {
                point = MathUtils.subtract(point, 0.1);
            }
            updatePoint(p, point);
        }
        return true;
    }

    @Override
    public boolean increaseLinePoint(AgentPlayerInfo player) {
        // 检查升点后的奖级是否符合约束（不能比上级高，不能高于最高奖级），只需要判断首账号就可以
        double tmpPoint = player.getPoint();
        tmpPoint = MathUtils.add(tmpPoint, 0.1);
        int tmpCode = getLotteryCode(player.getAgentNo(), tmpPoint);
        long parentAccountId = player.getPid();
        if (parentAccountId == 0) {
            AgentPlayerAccountType accountType =
                    getAgentPlayerAccountTypeDao().getByCode(player.getAgentNo(), player.getAccountType());
            if (tmpCode > accountType.getLotteryMaxCode()) {
                Map<String, Object> prams = new HashMap<>();
                prams.put("code", accountType.getLotteryMaxCode());
                throw newException("500-032", prams);
            }
        } else {
            AgentPlayerInfo upPlayer = getAgentPlayerInfoDao().getById(parentAccountId);
            if (null == upPlayer) {
                // ERR:理论上不会出现父account不存在的情况，否则线路数据肯定有问题
                throw newException("500-030");
            }
            double upPoint = upPlayer.getPoint();
            int upCode = getLotteryCode(upPlayer.getAgentNo(), upPoint);
            if (tmpCode > upCode) {
                Map<String, Object> prams = new HashMap<>();
                prams.put("code", upCode);
                throw newException("500-031", prams);
            }
        }

        List<AgentPlayerInfo> list = getAgentPlayerInfoDao().listTeamPath(player.getAgentNo(), player.getPlayerId());
        list.add(0, player); // 加入自己
        for (AgentPlayerInfo p : list) {
            double point = p.getPoint();
            if (point > 0) {
                point = MathUtils.add(point, 0.1);
            }
            updatePoint(p, point);
        }
        return true;
    }

    @Override
    public boolean updatePlayerType(AgentPlayerInfo player, int playerType) {
        AgentPlayerAccountType accountType = getAccountType(player.getAgentNo(), playerType, player.getPids(),
                player.getLotteryCode());
        return getAgentPlayerInfoDao().editPlayerType(player.getPlayerId(), playerType, accountType.getCode());
    }

    @Override
    public double getLotteryPoint(String agentNo, int code) throws ServiceException {
        // 获取系统最高奖级
        PlatformConfig sysCodeConfig =
                getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_CODE");
        if (sysCodeConfig == null) {
            throw newException("500-069");
        }
        double sysCode;
        try {
            sysCode = Double.parseDouble(sysCodeConfig.getValue());
        } catch (NumberFormatException e) {
            throw newException("500-070");
        }
        // 获取系统最高返点
        PlatformConfig sysPointConfig =
                getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_POINT");
        if (sysPointConfig == null) {
            throw newException("500-071");
        }
        double sysPoint;
        try {
            sysPoint = Double.parseDouble(sysPointConfig.getValue());
        } catch (NumberFormatException e) {
            throw newException("500-072");
        }

        return MathUtils.subtract(sysPoint, MathUtils.divide((MathUtils.subtract(sysCode, code)), 20, 1));
    }

    @Override
    public int getLotteryCode(String agentNo, double point) throws ServiceException {
        // 获取系统最高奖级
        PlatformConfig sysCodeConfig =
                getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_CODE");
        if (sysCodeConfig == null) {
            throw newException("500-069");
        }
        double sysCode;
        try {
            sysCode = Double.parseDouble(sysCodeConfig.getValue());
        } catch (NumberFormatException e) {
            throw newException("500-070");
        }
        // 获取系统最高返点
        PlatformConfig sysPointConfig =
                getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_POINT");
        if (sysPointConfig == null) {
            throw newException("500-071");
        }
        double sysPoint;
        try {
            sysPoint = Double.parseDouble(sysPointConfig.getValue());
        } catch (NumberFormatException e) {
            throw newException("500-072");
        }
        return (int) MathUtils.subtract(sysCode, MathUtils.multiply(MathUtils.subtract(sysPoint, point), 20));
    }

    @Override
    public boolean updateEqualLevel(long playerId, int equalLevel) {
        return getAgentPlayerInfoDao().updateEqualLevel(playerId, equalLevel);
    }

    @Override
    public boolean updateAllowTransfer(long playerId, int allowTransfer) {
        return getAgentPlayerInfoDao().updateAllowTransfer(playerId, allowTransfer);
    }

    /**
     * 修改玩家上级关系
     *
     * @param playerId   需要修改的玩家ID
     * @param playerName 新的上级用户名
     * @param type       类型，1表示整链转移，0表示只转移下级
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public boolean changePlayerRelation(Long playerId, String playerName, Integer type) {
        AgentPlayerInfo player = agentPlayerInfoDao.getById(playerId);
        if (player == null) {
            log.info("玩家不存在, playerId={}", playerId);
            throw newException("100-01");
        }

        AgentPlayerInfo newParent = agentPlayerInfoDao.getByPlayerName(player.getAgentNo(), playerName);
        if (newParent == null) {
            log.info("新上级不存在, playerName={}", playerName);
            throw newException("500-115");
        }

        if (playerId.equals(newParent.getPlayerId())) {
            log.info("不能将玩家自己设为上级, playerId={}", playerId);
            throw newException("500-116");
        }

        if(type == 1) {
            if(player.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_AGENT &&
                    newParent.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_PLAYER) {
                // 如果新上级是玩家时，则被转移用户不能有代理
                throw newException("500-136");
            }
            if(player.getPoint() > newParent.getPoint()) {
                // 下级返点不能高于上级
                throw newException("500-137");
            }
        }else {
            List<AgentPlayerInfo> list = getAgentPlayerInfoDao().getByPid(player.getPlayerId());
            for(AgentPlayerInfo p : list) {
                if(p.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_AGENT &&
                        newParent.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_PLAYER) {
                    // 如果新上级是玩家时，则被转移用户不能有代理
                    throw newException("500-136");
                }
                if(p.getPoint() > newParent.getPoint()) {
                    // 下级返点不能高于上级
                    throw newException("500-137");
                }
            }
        }

        List<AgentPlayerInfo> allSubPlayers = agentPlayerInfoDao.findAllSubPlayers(playerId);
        if (allSubPlayers != null && !allSubPlayers.isEmpty()) {
            for (AgentPlayerInfo subPlayer : allSubPlayers) {
                if (subPlayer.getPlayerId().equals(newParent.getPlayerId())) {
                    log.info("不能将下级设为上级, playerId={}, newPid={}", playerId, newParent.getPlayerId());
                    throw newException("500-117");
                }
            }
        }

        // 8. 更新玩家的pid和pids
        // 是否解除用户自己的契约
        boolean deleteMyContract = false;
        // 转移整个链路
        if (type == 1) {
            deleteMyContract = true;
        }
        boolean updateResult = contractService.deleteContractPlayerConfig(player.getAgentNo(), player.getPlayerId(),
                deleteMyContract);
        if(!updateResult) {
            throw newException("500-112");
        }

        boolean updateSubResult = changeLine(player, newParent, type);
        if (!updateSubResult) {
            log.info("更新下级玩家pids可能失败, playerId={}", playerId);
            throw newException("500-114");
        }

        log.info("成功修改玩家上级关系, playerId={}, oldPid={}, newPid={}", playerId, player.getPid(), newParent.getPlayerId());
        return true;
    }

    @Override
    @Transactional
    public void updatePlayerAccountType(AgentPlayerAccountType accountType, String changeType) {
        List<AgentPlayerInfo> list;
        // 查询条件
        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.eq("agentNo", accountType.getAgentNo()));
        if("add".equals(changeType)) {
            // 查询代理用户
            criterions.add(Restrictions.eq("playerType", AgentPlayerInfo.PLAYER_TYPE_AGENT));
        }else if("update".equals(changeType)) {
            // 查询该用户类型的用户和代理用户
            criterions.add(Restrictions.or(Restrictions.eq("accountType", accountType.getCode()),
                    Restrictions.eq("playerType", AgentPlayerInfo.PLAYER_TYPE_AGENT)));
        }else if("delete".equals(changeType)) {
            // 查询该用户类型的用户
            criterions.add(Restrictions.eq("accountType", accountType.getCode()));
        }else {
            throw newException("500-121");
        }
        list = getAgentPlayerInfoDao().find(criterions, null, -1, -1);
        if (list == null || list.isEmpty()) {
            return;
        }
        for(AgentPlayerInfo player : list) {
            AgentPlayerAccountType newAccountType = getAccountType(player.getAgentNo(), player.getPlayerType(),
                    player.getPids(), player.getLotteryCode());
            getAgentPlayerInfoDao().editAccountType(player.getPlayerId(), newAccountType.getCode());
        }
    }

    /**
     * 线路转移
     *
     * @param player 用户
     * @param newParent 新上级
     * @param type 转移类型，0-只转移下级，1-转移整个线路
     * @return
     */
    public boolean changeLine(AgentPlayerInfo player, AgentPlayerInfo newParent, Integer type) {
        // 只转移下级用户
        if(type == 0) {
            List<AgentPlayerInfo> list = getAgentPlayerInfoDao().getByPid(player.getPlayerId());
            updateTeamLine(list, newParent);
        }
        // 转移整条线路
        if(type == 1) {
            List<AgentPlayerInfo> list = new ArrayList<>();
            list.add(player);
            updateTeamLine(list, newParent);
        }
        return true;
    }

    /*
     * 然后更新整个下级
     */
    protected void updateTeamLine(List<AgentPlayerInfo> list, AgentPlayerInfo target) {
        for (AgentPlayerInfo tmpBean : list) {
            Long pid = target.getPlayerId();
            String pids = target.getPids();
            if (pid != 0) {
                pids = addFirst(pids, pid);
            }
            log.info("事务状态: {}", TransactionSynchronizationManager.isActualTransactionActive());
            //修改用户上级
            boolean updateResult = getAgentPlayerInfoDao().updatePids(tmpBean.getPlayerId(), pid, pids);
            //修改用户其他信息表，使用线程池处理
            submitAfterCommit(() -> {
                TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
                transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                transactionTemplate.execute(status -> {
                    try {
                        updatePlayerAllPidAndPids(tmpBean.getPlayerId());
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        log.error("异步任务执行失败", e);
                    }
                    return null;
                });
            });
            if (!updateResult) {
                throw new IllegalArgumentException();
            }
            tmpBean.setPid(pid); // 刷新内存
            tmpBean.setPids(pids); // 刷新内存
            List<AgentPlayerInfo> subPlayers = getAgentPlayerInfoDao().getByPid(tmpBean.getPlayerId());
            if (!CollectionUtils.isEmpty(subPlayers)) {
                updateTeamLine(subPlayers, tmpBean);
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
    public void updatePlayerAllPidAndPids(Long playerId) {
        AgentPlayerInfo player = getAgentPlayerInfoDao().getByPlayerId(playerId);
        if(player == null) {
            return;
        }
        AgentPlayerAccountType agentPlayerAccountType = getAccountType(player.getAgentNo(), player.getPlayerType(),
                player.getPids(), player.getLotteryCode());
        // 修改账号类型
        player.setAccountType(agentPlayerAccountType.getCode());
        getAgentPlayerInfoDao().update(player);
        // 修改佣金表上级代理
        getAgentPlayerCommissionRecordDao().updatePidAndPids(player);
        // 修改返点明细上级代理
        getAgentPlayerRebateRecordDao().updatePidAndPids(player);
        // 修改注册链接上级代理
        getAgentPlayerRegistLinkDao().updatePidAndPids(player);
        // 修改游戏虚拟账户上级代理
        getGameSimulationAccountDao().updatePidAndPids(player);
        // 修改游戏虚拟转账上级代理
        getGameSimulationTransferDao().updatePidAndPids(player);
        // 修改平台邀请明细上级代理
        getPlatformActivityInviteRecordDao().updatePid(player);
        // 修改平台代理玩家报表上级代理
        getPlatformAgentPlayerReportDao().updatePidAndPids(player);
        // 修改玩家充值上级代理
        getPlayerRechargeDao().updatePidAndPids(player);
        // 修改代理玩家账户账单上级代理
        getAgentPlayerAccountBillDao().updatePidAndPids(player);
        // 修改代理玩家游戏订单上级代理
        getAgentPlayerGameOrderDao().updatePidAndPids(player);
        // 修改游戏虚拟报表上级代理
        getGameSimulationReportDao().updatePidAndPids(player);
        // 修改平台代理玩家奖金上级代理
        getPlatformActivityRewardRecordDao().updatePid(player);
        // 修改玩家提现上级代理
        getPlayerWithdrawDao().updatePidAndPids(player);
    }

    /**
     * 添加到头
     *
     * @param ids
     * @param id
     * @return
     */
    public static String addFirst(String ids, Long id) {
        StringBuffer sb = new StringBuffer();
        sb.append("[" + id + "]");
        if (!StringUtils.isEmpty(ids)) {
            sb.append("," + ids);
        }
        return sb.toString();
    }

    /**
     * 提交事务提交后的任务
     */
    public static void submitAfterCommit(Runnable task) {
        // 如果当前有事务
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            executor.submit(task);
                        }
                    }
            );
        } else {
            // 无事务直接提交
            executor.submit(task);
        }
    }

}
