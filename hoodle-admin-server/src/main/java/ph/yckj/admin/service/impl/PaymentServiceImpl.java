package ph.yckj.admin.service.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import lombok.extern.slf4j.Slf4j;
import myutil.Moment;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.service.PaymentService;
import ph.yckj.admin.util.AbstractService;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.PaymentThirdPay;
import sy.hoodle.base.common.entity.PaymentThirdRemit;
import sy.hoodle.base.common.entity.PaymentTransfer;
import sy.hoodle.base.common.entity.PlayerWithdraw;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;
import ph.yckj.common.util.ServiceException;

@Slf4j
@Service
@Transactional
public class PaymentServiceImpl extends AbstractService implements PaymentService {

	@Override
	public boolean updateThird(Long id, String payUrl, String payBackUrl, String whiteIps) {
		return getPaymentThirdDao().update(id, payUrl, payBackUrl, whiteIps);
	}

	@Override
	public boolean addThirdPay(String agentNo, String agentName, String name, Long thirdId, String merId,
			String merSecretKey, double totalCredits, double minAmount, double maxAmount, double minUnitAmount,
			double maxUnitAmount, double feeRate, String payType, String payWayCode, String paySpecific, int sort) {
		int status = 0;
		double usedCredits = 0;
		Timestamp createTime = new Moment().toTimestamp();
		PaymentThirdPay entity = new PaymentThirdPay(agentNo, agentName, name, thirdId, merId, merSecretKey,
				usedCredits, totalCredits, minAmount, maxAmount, minUnitAmount, maxUnitAmount, feeRate, payType,
				payWayCode, paySpecific, sort, createTime, createTime, status);
		return getPaymentThirdPayDao().save(entity);
	}

	@Override
	public boolean deleteThirdPay(Long id) {
		return getPaymentThirdPayDao().deleteById(id);
	}

	@Override
	public boolean clearThirdPayCredits(Long id) {
		return getPaymentThirdPayDao().clearUsedCredits(id);
	}

	@Override
	public boolean updateThirdPay(Long id, String name, Long thirdId, double totalCredits, double minAmount,
			double maxAmount, double minUnitAmount, double maxUnitAmount, double feeRate, String payType,
			String payWayCode, String paySpecific, int sort) {
		PaymentThirdPay entity = getPaymentThirdPayDao().getById(id);
		if (entity != null) {
			entity.setName(name);
			entity.setThirdId(thirdId);
			entity.setTotalCredits(totalCredits);
			entity.setMinAmount(minAmount);
			entity.setMaxAmount(maxAmount);
			entity.setMinUnitAmount(minUnitAmount);
			entity.setMaxUnitAmount(maxUnitAmount);
			entity.setFeeRate(feeRate);
			entity.setPayType(payType);
			entity.setPayWayCode(payWayCode);
			entity.setPaySpecific(paySpecific);
			entity.setSort(sort);
			entity.setLastUpdateTime(new Moment().toTimestamp());
			getPaymentThirdPayDao().update(entity);
			return true;
		}
		return false;
	}

	@Override
	public boolean updateThirdPayStatus(Long id, int status) {
		return getPaymentThirdPayDao().updateStatus(id, status);
	}

	@Override
	public boolean addThirdRemit(String agentNo, String agentName, String name, Long thirdId, String merId,
			String merSecretKey, String remitType, double feeRate, double minUnitAmount, double maxUnitAmount) {
		int status = 0;
		Timestamp createTime = new Moment().toTimestamp();
		PaymentThirdRemit entity = new PaymentThirdRemit(agentNo, agentName, name, thirdId, merId, merSecretKey,
				remitType, feeRate, minUnitAmount, maxUnitAmount, createTime, createTime, status);
		return getPaymentThirdRemitDao().save(entity);
	}

	@Override
	public boolean updateThirdRemit(Long id, String name, Long thirdId, String remitType, double feeRate,
			double minUnitAmount, double maxUnitAmount) {
		PaymentThirdRemit entity = getPaymentThirdRemitDao().getById(id);
		if (null != entity) {
			entity.setName(name);
			entity.setThirdId(thirdId);
			entity.setRemitType(remitType);
			entity.setFeeRate(feeRate);
			entity.setMinUnitAmount(minUnitAmount);
			entity.setMaxUnitAmount(maxUnitAmount);
			entity.setLastUpdateTime(new Moment().toTimestamp());
			getPaymentThirdRemitDao().update(entity);
			return true;
		}
		return false;
	}

	@Override
	public boolean deleteThirdRemit(Long id) {
		return getPaymentThirdRemitDao().deleteById(id);
	}

	@Override
	public boolean updateThirdRemitStatus(Long id, int status) {
		return getPaymentThirdRemitDao().updateStatus(id, status);
	}

	@Override
	public boolean addTransfer(String agentNo, String agentName, int type, String name, String payType, String bankName,
			String bankBranch, String bankCardName, String bankCardAddress, String base64Code, double minAmount,
			double maxAmount, double minUnitAmount, double maxUnitAmount, double feeRate, double totalCredits,
			int sort) {
		int status = 0;
		double usedCredits = 0;
		Timestamp createTime = new Moment().toTimestamp();
		PaymentTransfer entity = new PaymentTransfer(agentNo, agentName, type, name, payType, bankName, bankBranch,
				bankCardName, bankCardAddress, base64Code, usedCredits, totalCredits, minAmount, maxAmount,
				minUnitAmount, maxUnitAmount, feeRate, sort, status, createTime, createTime);
		return getPaymentTransferDao().save(entity);
	}

	@Override
	public boolean updateTransfer(Long id, int type, String name, String payType, String bankName, String bankBranch,
			String bankCardName, String bankCardAddress, String base64Code, double minAmount, double maxAmount,
			double minUnitAmount, double maxUnitAmount, double feeRate, double totalCredits, int sort) {
		PaymentTransfer entity = getPaymentTransferDao().getById(id);
		if (null != entity) {
			entity.setName(name);
			entity.setPayType(payType);
			entity.setBankName(bankName);
			entity.setBankBranch(bankBranch);
			entity.setBankCardName(bankCardName);
			entity.setBankCardAddress(bankCardAddress);
			entity.setBase64Code(base64Code);
			entity.setFeeRate(feeRate);
			entity.setMinAmount(minAmount);
			entity.setMaxAmount(maxAmount);
			entity.setMinUnitAmount(minUnitAmount);
			entity.setMaxUnitAmount(maxUnitAmount);
			entity.setTotalCredits(totalCredits);
			entity.setSort(sort);
			entity.setLastUpdateTime(new Moment().toTimestamp());
			getPaymentTransferDao().update(entity);
			return true;
		}
		return false;
	}

	@Override
	public boolean deleteTransfer(Long id) {
		return getPaymentTransferDao().deleteById(id);
	}

	@Override
	public boolean clearTransferCredits(Long id) {
		return getPaymentTransferDao().clearUsedCredits(id);
	}

	@Override
	public boolean updateTransferStatus(Long id, int status) {
		return getPaymentTransferDao().updateStatus(id, status);
	}

	@Override
	public boolean updateThirdPayKey(Long id, String merId, String merSecretKey) {
		return getPaymentThirdPayDao().update(id, merId, merSecretKey);
	}

	@Override
	public boolean updateThirdRemitKey(Long id, String merId, String merSecretKey) {
		return getPaymentThirdRemitDao().update(id, merId, merSecretKey);
	}

	@Override
	public boolean updateWithdrawCheckStatus(AdminAccount adminAccount, Long id, int status) {
		String checkUser = adminAccount.getUsername();
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw new ServiceException("-1", "订单数据不存在");
		}
		if (entity.getCheckStatus() != PlayerWithdraw.CHECK_STATUS_WAITING) {
			throw new ServiceException("-1", "该订单已经审核完成，无法再次修改审核状态");
		}
		return getPlayerWithdrawDao().updateCheckStatus(id, status, checkUser);
//		String billno = entity.getBillno();
//		String agentNo = entity.getAgentNo();
//		double actualAmount = entity.getActualAmount().doubleValue();
//		String remitType = RemitType.indexOf(entity.getRemitType()).getValue();
//		// 检查渠道是否有符合扫单范围,则直接审核走默认渠道,不需额外设定
//		List<PaymentThirdRemit> remitList = getPaymentThirdRemitDao().listAvailableInRangeByRemitType(agentNo,
//				actualAmount, remitType);
//		if (remitList.size() == 1) {
//			Long remitId = remitList.get(0).getId();
//			log.info("审核自动符合渠道设定billno:" + billno + ",渠道类型： " + remitType + " ,remitid:" + remitId);
//			return getPlayerWithdrawDao().updateCheckStatus(id, status, checkUser, remitId.intValue());
//		} else {
//			return getPlayerWithdrawDao().updateCheckStatus(id, status, checkUser);
//		}
	}

	@Override
	public boolean updateWithdrawRemitId(AdminAccount adminAccount, Long id, int remitId) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (entity.getPayStatus() != PlayerWithdraw.PAY_STATUS_WAITING) {
			throw newException("-1", "该订单已处理完成或其他用户正在处理该订单");
		}
		if (entity.getCheckStatus() == PlayerWithdraw.CHECK_STATUS_FAIL) {
			throw newException("-1", "审核不通过的订单无法设置支付渠道");
		}
		if (entity.getOrderStatus() != PlayerWithdraw.ORDER_STATUS_WAITING) {
			throw newException("-1", "只能对未处理的订单设置支付渠道");
		}
		String lockUser = adminAccount.getUsername();
		return getPlayerWithdrawDao().updateWithdrawRemitId(id, remitId, lockUser);
	}

	@Override
	public boolean lockWithdraw(AdminAccount adminAccount, Long id) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (entity.getOrderStatus() != PlayerWithdraw.ORDER_STATUS_WAITING) {
			throw newException("-1", "只能对未处理的订单进行锁定操作");
		}
		if (entity.getLockStatus() != PlayerWithdraw.LOCK_STATUS_FALSE) {
			throw newException("-1", "该订单已被其他用户锁定");
		}
		String lockUser = adminAccount.getUsername();
		return getPlayerWithdrawDao().lock(id, lockUser);
	}

	@Override
	public boolean unlockWithdraw(AdminAccount adminAccount, Long id) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (entity.getLockStatus() != PlayerWithdraw.LOCK_STATUS_TRUE) {
			throw newException("-1", "该订单未被锁定");
		}
		String lockUser = adminAccount.getUsername();
		if (!lockUser.equals(entity.getLockUser())) {
			throw newException("-1", "该订单已被其他用户锁定");
		}
		return getPlayerWithdrawDao().unlock(id, lockUser);
	}

	@Override
	public boolean refuseWithdraw(AdminAccount adminAccount, Long id, String reason) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (entity.getLockStatus() != PlayerWithdraw.LOCK_STATUS_TRUE) {
			throw newException("-1", "该订单未被锁定");
		}
		String lockUser = adminAccount.getUsername();
		if (!lockUser.equals(entity.getLockUser())) {
			throw newException("-1", "该订单已被其他用户锁定");
		}
		Long playerId = entity.getPlayerId();
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
		String remarks = "您的提现处理失败，资金已返还。";
		if (!StringUtils.isEmpty(reason)) {
			remarks += "原因：" + reason;
		}
		String payUser = adminAccount.getUsername();
		int payMethod = PlayerWithdraw.PAY_METHOD_MANUAL;
		String oldRemarks = entity.getRemarks();
		if (StringUtils.isEmpty(oldRemarks)) {
			oldRemarks = "";
		}
		String newRemarks = oldRemarks + "<br>" + remarks;
		boolean updateCompleted = getPlayerWithdrawDao().setPayFail(id, payMethod, payUser, newRemarks);
		if (!updateCompleted) {
			throw new IllegalArgumentException();
		}
		// 增加用户金额
		boolean updateBalance = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerId, entity.getAmount(),
				entity.getAmount(), entity.getAmount().negate());
		if (!updateBalance) {
			throw new IllegalArgumentException();
		}
		remarks += "，返还金额：" + entity.getAmount().toString();
		getPlayerBillService().addBillWithdrawReturn(adminAccount, player, entity, remarks);
		return true;
	}

	@Override
	public boolean completedWithdraw(AdminAccount adminAccount, Long id, String payBillno, String remarks) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (entity.getCheckStatus() != PlayerWithdraw.CHECK_STATUS_PASS) {
			throw newException("-1", "该订单审核未通过");
		}
		if (entity.getLockStatus() != PlayerWithdraw.LOCK_STATUS_TRUE) {
			throw newException("-1", "该订单未被锁定");
		}
		String lockUser = adminAccount.getUsername();
		if (!lockUser.equals(entity.getLockUser())) {
			throw newException("-1", "该订单已被其他用户锁定");
		}
		String oldRemarks = entity.getRemarks();
		if (StringUtils.isEmpty(oldRemarks)) {
			oldRemarks = "";
		}
		String newRemarks = oldRemarks + "<br>" + remarks;
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(entity.getPlayerId());
		String infos = "您的提现已处理，请您注意查收";
		int payMethod = PlayerWithdraw.PAY_METHOD_MANUAL;
		String payUser = adminAccount.getUsername();
		boolean updateCompleted = getPlayerWithdrawDao().setPayCompleted(id, payMethod, payUser, payBillno, newRemarks,
				infos);
		return true;
	}

	@Override
	public boolean autoCanceledRecharge(int minute) {
		return getPlayerRechargeDao().autoCanceledOrder(minute);
	}

	@Override
	public boolean updateWithdrawRepay(AdminAccount adminAccount, Long id, Integer status) {
		return getPlayerWithdrawDao().repay(id);
	}

	@Override
	@Transactional
	public boolean manualWithdrawDeduction(AdminAccount adminAccount, Long id, String remarks) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (entity.getCheckStatus() != PlayerWithdraw.CHECK_STATUS_PASS) {
			throw newException("-1", "该订单审核未通过");
		}
		if (entity.getLockStatus() != PlayerWithdraw.LOCK_STATUS_TRUE) {
			throw newException("-1", "该订单未被锁定");
		}
		String lockUser = adminAccount.getUsername();
		if (!lockUser.equals(entity.getLockUser())) {
			throw newException("-1", "该订单已被其他用户锁定");
		}
		String oldRemarks = entity.getRemarks();
		if (StringUtils.isEmpty(oldRemarks)) {
			oldRemarks = "";
		}
		String newRemarks = oldRemarks + "<br>" + remarks;

		// 准备完成订单的参数
		String infos = "您的提现已通过人工下分处理完成，请您注意查收";
		int payMethod = PlayerWithdraw.PAY_METHOD_MANUAL;
		String payUser = adminAccount.getUsername();
		String payBillno = "";

		// 使用现有的setPayCompleted方法完成订单
		boolean updateCompleted = getPlayerWithdrawDao().setPayCompleted(id, payMethod, payUser, payBillno, newRemarks, infos);
		if (!updateCompleted) {
			throw new ServiceException("-1", "完成订单失败");
		}

		// 单独更新remitType字段，避免实体更新冲突
		log.info("开始设置remitType为人工下分: {}", PlayerWithdraw.REMIT_TYPE_MANUAL_DEDUCTION);
		boolean remitTypeUpdated = getPlayerWithdrawDao().updateRemitType(id, PlayerWithdraw.REMIT_TYPE_MANUAL_DEDUCTION);

		if (!remitTypeUpdated) {
			log.error("设置remitType失败");
			throw new ServiceException("-1", "设置提现方式失败");
		}

		return true;
	}

}
