package ph.yckj.admin.service;

import sy.hoodle.base.common.entity.AgentInfo;

import java.util.Date;
import java.util.Map;

public interface PlatformService {

    /**
     * 补全配置
     */
    void supplementPlatformConfig(AgentInfo agent);

    boolean updateConfig(String agentNo, String group, Map<String, String> data);

	Map<String, Object> platformRTS(String agentNo, String playerName, Date sDate, Date eDate);

}
