package ph.yckj.admin.service;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import sy.hoodle.base.common.entity.GameWarnAutoDisableRecord;
import sy.hoodle.base.common.entity.GameWarnRecord;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GameWarnRecordService {
    /**
     * 亏损量预警记录
     * @param lotteryGameplay
     * @param createTime
     * @return
     */

    GameWarnRecord getById(String lotteryGameplay, Date createTime);
    boolean save(GameWarnRecord entity);

    void insert(List<GameWarnRecord> records);

    boolean update(String status, String lotteryGameplay, Date createTime);
    List<GameWarnRecord> find(List<Criterion> criterions, List<Order> orders, int firstResult, int maxResults);

    /**
     * 查询预警记录列表
     */
    Map<String, Object> searchRecords(int page, int size, String warnStatus);

    /**
     * 查询自动禁用记录列表
     */
    Map<String, Object> searchAutoDisableRecords(int page, int size);

    /**
     * 获取预警记录详情
     */
    GameWarnRecord getById(Long id);

    /**
     * 清除预警记录
     */
    boolean clearWarn(Long id);

    int totalCount();

}
