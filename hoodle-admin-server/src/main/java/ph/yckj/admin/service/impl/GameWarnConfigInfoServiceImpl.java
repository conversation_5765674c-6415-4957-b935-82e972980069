package ph.yckj.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import myutil.Moment;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.admin.service.GameWarnConfigInfoService;
import ph.yckj.admin.util.AbstractService;
import sy.hoodle.base.common.entity.AgentInfo;
import sy.hoodle.base.common.entity.GameMonopolyConfigInfo;
import sy.hoodle.base.common.entity.GameWarnConfigInfo;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class GameWarnConfigInfoServiceImpl extends AbstractService implements GameWarnConfigInfoService {

    @Override
    public GameWarnConfigInfo getById(Long id) {
        return getGameWranConfigInfoDao().getById(id);
    }

    @Override
    public boolean save(String agentNo, String agentName, BigDecimal warnLossAmount, BigDecimal warnDisableLossVolume, String measurementWay, String warnWay,
                        String automaticDisableWay, String warnClear, String warnStatus, String warnRemark, int isDelete) {
        GameWarnConfigInfo entity = new GameWarnConfigInfo(agentNo, agentName, warnLossAmount, warnDisableLossVolume, measurementWay, warnWay,
                automaticDisableWay, warnClear, warnStatus, warnRemark, isDelete);
        Timestamp timestamp = new Moment().toTimestamp();
        entity.setCreateTime(timestamp);
        entity.setLastUpdateTime(timestamp);
        return getGameWranConfigInfoDao().save(entity);
    }



    @Override
    public void update(String agentNo, String agentName, BigDecimal warnLossAmount, BigDecimal warnDisableLossVolume, String measurementWay,
                       String automaticDisableWay, Long id, GameWarnConfigInfo oldEntity) {
        GameWarnConfigInfo entity = new GameWarnConfigInfo();
        BeanUtils.copyProperties(oldEntity, entity);
        entity.setAgentNo(agentNo);
        entity.setAgentName(agentName);
        entity.setWarnLossAmount(warnLossAmount);
        entity.setWarnDisableLossVolume(warnDisableLossVolume);
        entity.setMeasurementWay(measurementWay);
        entity.setAutomaticDisableWay(automaticDisableWay);
        entity.setId(id);
        entity.setLastUpdateTime(new Moment().toTimestamp());
        getGameWranConfigInfoDao().update(entity);
    }

    @Override
    public boolean updateStatus(Long id, String warnStatus) {
        return getGameWranConfigInfoDao().updateStatus(id, warnStatus);
    }

    @Override
    public boolean setLotteryList(Long id, String lotteryList) {
        try {
            GameWarnConfigInfo entity = getGameWranConfigInfoDao().getById(id);
            if (entity == null) {
                return false;
            }
            // 将逗号分隔的彩种列表转换为JSON数组格式
            String jsonLotteryList = convertToJsonArray(lotteryList);
            // 设置彩种列表
            entity.setLotteryList(jsonLotteryList);
            entity.setLastUpdateTime(new Moment().toTimestamp());
            getGameWranConfigInfoDao().update(entity);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将逗号分隔的彩种列表转换为JSON数组格式
     */
    private String convertToJsonArray(String lotteryList) {
        if (lotteryList == null || lotteryList.trim().isEmpty()) {
            return "[]";
        }

        StringBuilder json = new StringBuilder("[");
        String[] lotteries = lotteryList.split(",");
        for (String s : lotteries) {
            String lottery = s.trim();
            if (!lottery.isEmpty()) {
                if (json.length() > 1) {
                    json.append(",");
                }
                json.append("\"").append(lottery).append("\"");
            }
        }
        json.append("]");
        return json.toString();
    }

    @Override
    public List<GameWarnConfigInfo> find(List<Criterion> criterions, List<Order> orders, int firstResult, int maxResults) {
        return getGameWranConfigInfoDao().find(criterions, orders, firstResult, maxResults);
    }

}
