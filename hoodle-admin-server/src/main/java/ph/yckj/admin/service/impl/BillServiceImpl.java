package ph.yckj.admin.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import myutil.Moment;
import myutil.RandomUtils;
import ph.yckj.admin.service.BillService;
import ph.yckj.admin.util.AbstractService;
import sy.hoodle.base.common.entity.AgentAccountBill;
import sy.hoodle.base.common.entity.AgentInfo;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerGameOrder;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.AgentPlayerTransferRecord;
import sy.hoodle.base.common.entity.AgentRechargeRecord;
import sy.hoodle.base.common.entity.AgentWithdrawRecord;
import sy.hoodle.base.common.entity.PlatformActivityRewardRecord;
import sy.hoodle.base.common.entity.PlayerRecharge;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;

@Slf4j
@Service
@Transactional
public class BillServiceImpl extends AbstractService implements BillService {

	@Override
	public boolean addAgentAccountBill(AgentInfo agent, BigDecimal amount, BigDecimal balanceBeforeAmount,
			BigDecimal balanceAfterAmount, int billType, String referenceBillNo, String createBy, String billRemark) {
		AgentAccountBill entity = new AgentAccountBill();
		entity.setAgentNo(agent.getAgentNo());
		entity.setAgentName(agent.getAgentName());
		entity.setAgentBillNo(RandomUtils.fromTime24());
		entity.setTransferAmount(amount);
		entity.setAgentBalanceAfter(balanceAfterAmount);
		entity.setAgentBalanceBefore(balanceBeforeAmount);
		entity.setBillType(billType);
		entity.setBillRemark(billRemark);
		entity.setReferenceBillNo(referenceBillNo);
		entity.setIsDelete(0);
		entity.setCreateBy(createBy);
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		return getAgentAccountBillDao().save(entity);
	}

	@Override
	public boolean addAgentRechargeRecord(AgentInfo agent, String billno, BigDecimal amount,
			BigDecimal balanceBeforeAmount, BigDecimal balanceAfterAmount, int rechargeType, String createBy,
			String rechargeRemarke, BigDecimal rate, Integer currency, BigDecimal actualAmount, BigDecimal postedAmount) {
		AgentRechargeRecord entity = new AgentRechargeRecord();
		entity.setAgentNo(agent.getAgentNo());
		entity.setAgentName(agent.getAgentName());
		entity.setRechargeAmount(amount);
		entity.setAgentBalanceAfter(balanceAfterAmount);
		entity.setAgentBalanceBefore(balanceBeforeAmount);
		entity.setRechargeBillNo(billno);
		entity.setRechargeType(rechargeType);
		entity.setRechargeRemarke(rechargeRemarke);
		entity.setIsDelete(0);
		entity.setRate(rate);
		entity.setPostedAmount(postedAmount);
		entity.setCurrency(currency);
		entity.setActualAmount(actualAmount);
		entity.setCreateBy(createBy);
		entity.setAgentLevelCode(agent.getAgentLevelCode() != null ? agent.getAgentLevelCode() : 0);
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		return getAgentRechargeRecordDao().save(entity);
	}

	@Override
	public boolean addAgentWithdrawRecord(AgentInfo agent, String billno, BigDecimal amount,
			BigDecimal balanceBeforeAmount, BigDecimal balanceAfterAmount, int withdrawType, String createBy,
			String withdrawRemarke) {
		AgentWithdrawRecord entity = new AgentWithdrawRecord();
		entity.setAgentNo(agent.getAgentNo());
		entity.setAgentName(agent.getAgentName());
		entity.setWithdrawAmount(amount);
		entity.setAgentBalanceAfter(balanceAfterAmount);
		entity.setAgentBalanceBefore(balanceBeforeAmount);
		entity.setWithdrawBillNo(billno);
		entity.setWithdrawType(withdrawType);
		entity.setWithdrawRemarke(withdrawRemarke);
		entity.setIsDelete(0);
		entity.setCreateBy(createBy);
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		return getAgentWithdrawRecordDao().save(entity);
	}

	@Override
	public boolean addAgentPlayerAccountBill(AgentPlayerInfo playerInfo, BigDecimal amount,
			BigDecimal balanceBeforeAmount, BigDecimal balanceAfterAmount, int billType, String referenceBillNo,
			String createBy, String billRemark) {
		AgentPlayerAccountBill entity = new AgentPlayerAccountBill();
		entity.setAgentNo(playerInfo.getAgentNo());
		entity.setAgentName(playerInfo.getAgentName());
		entity.setAgentType(playerInfo.getAgentType());
		entity.setPlayerId(playerInfo.getPlayerId());
		entity.setPlayerName(playerInfo.getPlayerName());
		String billNo = RandomUtils.fromTime24();
		entity.setPlayerBillNo(billNo);
		entity.setBillType(billType);
		entity.setBillRemark(billRemark);
		entity.setPlayerBalanceBefore(balanceBeforeAmount);
		entity.setBillAmount(amount);
		entity.setPlayerBalanceAfter(balanceAfterAmount);
		entity.setIsDelete(0);
		entity.setCreateBy(createBy);
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		Long pid = playerInfo.getPid();
		entity.setPid(pid != null ? pid : 0L);
		entity.setPids(playerInfo.getPids());
		return getAgentPlayerAccountBillDao().save(entity);
	}

	@Override
	public boolean addAgentPlayerTransferRecord(AgentInfo agent, AgentPlayerInfo player) {
		AgentPlayerTransferRecord entity = new AgentPlayerTransferRecord();
		return getAgentPlayerTransferRecordDao().save(entity);
	}

	@Override
	public boolean addRechargeBill(AgentPlayerInfo player, PlayerRecharge recharge, String remarks) {
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		Integer agentType = player.getAgentType();
		Long playerId = player.getPlayerId();
		Long pid = player.getPid();
		pid = pid != null ? pid : 0L;
		String playerName = player.getPlayerName();
		String playerBillNo = recharge.getBillno();
		BigDecimal billAmount = recharge.getActualAmount();
		BigDecimal playerBalanceBefore = player.getPlayerAvailableBalance();
		BigDecimal playerBalanceAfter = playerBalanceBefore.add(billAmount);
		int billType = AgentPlayerAccountBill.BILL_TYPE_RECHARGE;
		String billRemark = remarks;
		int isDelete = 0;
		String createBy = player.getPlayerName();
		Timestamp createTime = new Moment().toTimestamp();
		Timestamp updateTime = createTime;
		AgentPlayerAccountBill entity = new AgentPlayerAccountBill(agentNo, agentType, agentName, playerId, playerName,
				playerBillNo, playerBalanceBefore, billAmount, playerBalanceAfter, billType, billRemark, isDelete,
				createBy, createTime, updateTime,pid);
		entity.setPids(player.getPids());
		return getAgentPlayerAccountBillDao().save(entity);
	}

	@Override
	public boolean addAgentPlayerOrderCancelBill(AgentPlayerInfo player, AgentPlayerGameOrder order) {
		int billType = AgentPlayerAccountBill.BILL_TYPE_SYSTEM_CHANCELED;
		AgentPlayerAccountBill entity = new AgentPlayerAccountBill();
		entity.setAgentNo(player.getAgentNo());
		entity.setAgentName(player.getAgentName());
		entity.setAgentType(player.getAgentType());
		entity.setPlayerId(player.getPlayerId());
		entity.setPlayerName(player.getPlayerName());
		String billNo = RandomUtils.fromTime24();
		entity.setPlayerBillNo(billNo);
		entity.setBillType(billType);
		entity.setBillRemark(order.getGameOrderNo());
		BigDecimal billAmount = order.getBetAmount();
		BigDecimal playerBalanceBefore = player.getPlayerAvailableBalance();
		BigDecimal playerBalanceAfter = playerBalanceBefore.add(billAmount);
		entity.setPlayerBalanceBefore(playerBalanceBefore);
		entity.setBillAmount(billAmount);
		entity.setPlayerBalanceAfter(playerBalanceAfter);
		entity.setIsDelete(0);
		entity.setCreateBy("0");
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		Long pid = player.getPid();
		entity.setPid(pid != null ? pid : 0L);
		entity.setPids(player.getPids());
		return getAgentPlayerAccountBillDao().save(entity);
	}

	@Override
	public boolean addActivityBill(AgentPlayerInfo player, PlatformActivityRewardRecord record) {
		int billType = AgentPlayerAccountBill.BILL_TYPE_ACTIVITY;
		AgentPlayerAccountBill entity = new AgentPlayerAccountBill();
		entity.setAgentNo(record.getAgentNo());
		entity.setAgentName(record.getAgentName());
		entity.setAgentType(player.getAgentType());
		entity.setPlayerId(player.getPlayerId());
		entity.setPlayerName(player.getPlayerName());
		String billNo = RandomUtils.fromTime24();
		entity.setPlayerBillNo(billNo);
		entity.setBillType(billType);
		entity.setBillRemark("活动奖励发放");
		BigDecimal billAmount = record.getAmount();
		BigDecimal playerBalanceBefore = player.getPlayerAvailableBalance();
		BigDecimal playerBalanceAfter = playerBalanceBefore.add(billAmount);
		entity.setPlayerBalanceBefore(playerBalanceBefore);
		entity.setBillAmount(billAmount);
		entity.setPlayerBalanceAfter(playerBalanceAfter);
		entity.setIsDelete(0);
		entity.setCreateBy("0");
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		Long pid = player.getPid();
		entity.setPid(pid != null ? pid : 0L);
		entity.setPids(player.getPids());
		return getAgentPlayerAccountBillDao().save(entity);
	}

}
