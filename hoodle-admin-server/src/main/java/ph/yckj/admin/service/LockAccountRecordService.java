package ph.yckj.admin.service;

import sy.hoodle.base.common.entity.LockAccountRecord;

import java.math.BigDecimal;
import java.util.List;

public interface LockAccountRecordService {

    /**
     * 保存卡单记录
     *
     * @return
     */
    boolean save(String agentNo, String agentName, Integer lockType, String playerNameList, String lottery,
            String issue, Integer lockNumber, String lockRemark, BigDecimal amountMix, BigDecimal amountMax,
            String creater);

    /**
     * 根据用户id查询卡单记录
     *
     * @param id
     * @return
     */
    LockAccountRecord getById(int id);

    /**
     * 修改卡单记录
     *
     * @param entity
     * @return
     */
    void update(LockAccountRecord entity);

    /**
     * 删除卡单记录
     *
     * @param id
     * @return
     */
    boolean delete(int id);

    /**
     * 查询符合条件的卡单人员
     *
     * @return
     */
    List<LockAccountRecord> getLockRecordByUsername(String agentNo, String agentName, String playerName,
            Integer lockType, String lottery, String issue, BigDecimal amountMix, BigDecimal amountMax);

    /**
     * 查询分页记录
     *
     * @param firstResult
     * @param maxResults
     * @return
     */
    List<LockAccountRecord> find(String agentNo, String agentName, String playerName, String lottery, String issue,
            Integer lockType, Integer lockStatus, int firstResult, int maxResults);

    /**
     * 查询分页记录
     *
     * @return
     */
    int totalRecord(String agentNo, String agentName, String playerName, String lottery, String issue, Integer lockType,
            Integer lockStatus);
}
