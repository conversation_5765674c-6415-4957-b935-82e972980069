package ph.yckj.admin.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyPair;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.codec.binary.Base64;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.util.IdUtil;
import cryptix.jce.provider.MD5;
import lombok.extern.slf4j.Slf4j;
import myutil.DateUtils;
import myutil.HttpUtils;
import myutil.Moment;
import myutil.RSAUtils;
import myutil.RandomUtils;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.service.AgentService;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.admin.vo.PlatformLineVo;
import ph.yckj.common.util.ActivityUtils;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.StaticUtils;
import sy.hoodle.base.common.dto.activity.AcitvitySalaryConditions;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;

@Slf4j
@Service
@Transactional
public class AgentServiceImpl extends AbstractService implements AgentService {

	private static SecureRandom random = new SecureRandom();

	@Autowired
	private ActivityUtils activityUtils;

	@Override
	public boolean updateAgentStatus(int id, int status) {
		return getAgentInfoDao().updateStatus(id, status);
	}

	@Override
	public boolean updateAgentInfo(String agentNo, int agentSettleMode, double agentRatePercent,
			double agentPumpPercent, BigDecimal serviceCharge, Integer language, Integer currency
			,double rewardRatio,double thirdGameRatio) {
		return getAgentInfoDao().update(agentNo, agentSettleMode, agentRatePercent, agentPumpPercent,
				serviceCharge, language, currency,rewardRatio,thirdGameRatio);
	}

	@Override
	public AgentInfo addAgentInfo(AdminAccount adminAccount, Integer agentType, String agentName, Integer gentLevelCode,
			Integer agentPid, String agentPids, int agentSettleMode, double agentRatePercent,
			double agentPumpPercent, BigDecimal serviceCharge, Integer language, Integer currency, Integer branching,
			String agentUsername, String agentPassword,double rewardRatio,double thirdGameRatio) {
		String agentNo = null;
		AgentInfo entity = null;
		boolean isExist = false;
		while (!isExist) {
			agentNo = String.valueOf(DateUtils.dateToShortString(new Date()) + (random.nextInt(9000) + 1000));
			entity = getAgentInfoDao().getByAgentNo(agentNo);
			if (null == entity) {
				isExist = true;
				entity = new AgentInfo();
			}
		}
		entity.setAgentNo(agentNo);
		entity.setAgentType(agentType);
		entity.setAgentName(agentName);
		if (null == gentLevelCode) {
			gentLevelCode = 0;
		}
		entity.setAgentPid(agentPid);
		entity.setAgentPids(agentPids);
		entity.setAgentLevelCode(gentLevelCode);
		entity.setAgentApiUrl("");
		entity.setAgentApiWhiteIps("");
		entity.setAgentBlockedBalance(new BigDecimal(0));
		entity.setAgentAvailableBalance(new BigDecimal(0));
		KeyPair keyPair = RSAUtils.createKey();
		entity.setAgentMd5Key(IdUtil.simpleUUID().toUpperCase());
		entity.setAgentPublicKey(new Base64().encodeToString(keyPair.getPublic().getEncoded()));
		entity.setAgentPrivateKey(new Base64().encodeToString(keyPair.getPrivate().getEncoded()));
		entity.setAgentStatus(AgentInfo.AGENT_STATUS_NORMAL);
		entity.setAgentAccountId(0);
		entity.setAgentUsername(agentUsername);
		entity.setAgentSettleMode(agentSettleMode);
		entity.setAgentRatePercent(agentRatePercent);
		entity.setAgentPumpPercent(agentPumpPercent);
		entity.setServiceCharge(serviceCharge);
		entity.setLanguage(language);
		entity.setAgentUid("");
		entity.setCurrency(currency);
		entity.setBranching(branching);
		entity.setIsDelete(0);
		entity.setCreateBy(ThreadLocalUtil.getSessionUser().getUsername());
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setUpdateTime(entity.getCreateTime());
		entity.setRewardRatio(rewardRatio);
		entity.setThirdGameRatio(thirdGameRatio);
		return getAgentInfoDao().addSave(entity);
	}

	@Override
	@Transactional
	public boolean updateAgentWhiteIps(int id, String whitelist) {
		return getAgentInfoDao().updateAgentWhiteIps(id, whitelist);
	}

	@Override
	@Transactional
	public boolean updateAgentSecretKey(int id, String publicKey, String privateKey) {
		return getAgentInfoDao().updateAgentSecretKey(id, publicKey, privateKey);
	}

	@Override
	@Transactional
	public boolean enableOrDisableAgentPlayer(Long playerId, int playerStatus) {
		return getAgentPlayerInfoDao().updateAgentPlayerInfoPlayerStatus(playerId, playerStatus);
	}

	@Override
	@Transactional
	public boolean markAgentPlayer(Long playerId, int markersStatus) {
		return getAgentPlayerInfoDao().updateAgentPlayerInfoMarkersStatus(playerId, markersStatus);
	}

	//增加额度
	@Override
	public boolean maualAgentAddBalance(AdminAccount adminAccount, AgentInfo agent, BigDecimal amount, String remarks) {
		// 检查上级代理商余额是否足够
		if (agent.getAgentPid() != 0) {
			AgentInfo parentAgent = getAgentInfoDao().getById(agent.getAgentPid());
			if (parentAgent == null) {
				throw newException("-1", "上级代理商不存在");
			}
			if (parentAgent.getAgentAvailableBalance().compareTo(amount) < 0) {
				throw newException("-1", "上级代理商余额不足");
			}
		}

		boolean result = getAgentInfoDao().updateAvailableBalance(agent.getAgentId(), amount);
		if (result) {
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = agent.getAgentAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(amount);
			// 插入订单
			getBillService().addAgentRechargeRecord(agent, billno, amount, balanceBefore, balanceAfter, 1,
					adminAccount.getUsername(), remarks, BigDecimal.valueOf(1), agent.getCurrency(), BigDecimal.ZERO, amount);
			// 插入账单，判断金额正负来设置billType
			int billType = amount.compareTo(BigDecimal.ZERO) < 0 ? 7 : 6;
			getBillService().addAgentAccountBill(agent, amount, balanceBefore, balanceAfter, billType, billno,
					adminAccount.getUsername(), remarks);

			// 扣除上级代理商余额
			if (agent.getAgentPid() > 0) {
				AgentInfo parentAgent = getAgentInfoDao().getById(agent.getAgentPid());
				BigDecimal parentBalanceBefore = parentAgent.getAgentAvailableBalance();
				BigDecimal parentBalanceAfter = parentBalanceBefore.subtract(amount);

				// 更新上级代理商余额
				getAgentInfoDao().updateAvailableBalance(parentAgent.getAgentId(), amount.negate());

				// 记录上级代理商账单
				String parentBillno = RandomUtils.fromTime24();
				int sjBillType = amount.compareTo(BigDecimal.ZERO) < 0 ? 6 : 7;
				getBillService().addAgentAccountBill(parentAgent, amount.negate(), parentBalanceBefore, parentBalanceAfter,
						sjBillType, parentBillno, adminAccount.getUsername(),
						"下级代理商" + agent.getAgentName() + "增加额度");
			}
			return true;
		}
		return false;
	}

	//玩家上分
	@Override
	public boolean maualAgentRecharge(AdminAccount adminAccount, AgentInfo agent, BigDecimal amount, BigDecimal rate,
			Integer currency, String remarks, BigDecimal actualAmount, BigDecimal postedAmount) {
		// 检查上级代理商余额是否足够
		if (agent.getAgentPid() != 0) {
			AgentInfo parentAgent = getAgentInfoDao().getById(agent.getAgentPid());
			if (parentAgent == null) {
				throw newException("-1", "上级代理商不存在");
			}
			if (parentAgent.getAgentAvailableBalance().compareTo(postedAmount) < 0) {
				throw newException("-1", "上级代理商余额不足");
			}
		}

		boolean result = getAgentInfoDao().updateAvailableBalance(agent.getAgentId(), postedAmount);
		if (result) {
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = agent.getAgentAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(postedAmount);
			// 插入订单
			getBillService().addAgentRechargeRecord(agent, billno, amount, balanceBefore, balanceAfter, 1,
					adminAccount.getUsername(), remarks, rate, currency, actualAmount, postedAmount);
			// 插入账单
			int billType = amount.compareTo(BigDecimal.ZERO) < 0 ? 2 : 1;
			getBillService().addAgentAccountBill(agent, postedAmount, balanceBefore, balanceAfter, billType, billno,
					adminAccount.getUsername(), remarks);

			// 扣除上级代理商余额
			if (agent.getAgentPid() > 0) {
				AgentInfo parentAgent = getAgentInfoDao().getById(agent.getAgentPid());
				BigDecimal parentBalanceBefore = parentAgent.getAgentAvailableBalance();
				BigDecimal parentBalanceAfter = parentBalanceBefore.subtract(postedAmount);

				// 更新上级代理商余额
				getAgentInfoDao().updateAvailableBalance(parentAgent.getAgentId(), postedAmount.negate());

				// 记录上级代理商账单
				String parentBillno = RandomUtils.fromTime24();
				int sjBillType = amount.compareTo(BigDecimal.ZERO) < 0 ? 1 : 2;
				getBillService().addAgentAccountBill(parentAgent, postedAmount.negate(), parentBalanceBefore, parentBalanceAfter,
						sjBillType, parentBillno, adminAccount.getUsername(),
						"下级代理商" + agent.getAgentName() + "上分");
			}
			return true;
		}
		return false;
	}

	public static void main(String[] args) {
		System.out.print(IdUtil.simpleUUID().toUpperCase().length());
	}

	@Override
	public boolean maualAgentWithdraw(AdminAccount adminAccount, AgentInfo agent, BigDecimal amount, String remarks) {
		boolean result = getAgentInfoDao().updateAvailableBalance(agent.getAgentId(), amount);
		if (result) {
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = agent.getAgentAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(amount);
			// 插入订单
			getBillService().addAgentWithdrawRecord(agent, billno, amount, balanceBefore, balanceAfter, 0,
					adminAccount.getUsername(), remarks);
			// 插入账单
			getBillService().addAgentAccountBill(agent, amount, balanceBefore, balanceAfter, 7, billno,
					adminAccount.getUsername(), remarks);
			return true;
		}
		return false;
	}

	@Override
	public boolean manualPlayerRecharge(HttpServletRequest request, AdminAccount adminAccount,
			AgentPlayerInfo playerInfo, BigDecimal amount, String remarks) {
		Moment moment = new Moment();
		boolean result = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerInfo.getPlayerId(), amount,
				BigDecimal.ZERO);
		if (result) {
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = playerInfo.getPlayerAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(amount);
			int billType = AgentPlayerAccountBill.BILL_TYPE_RECHARGE;
			int transferType = AgentPlayerTransferRecord.TRANSFER_IN;
			if (amount.compareTo(BigDecimal.ZERO) < 0) {
				billType = AgentPlayerAccountBill.BILL_TYPE_WITHDRAW;
				transferType = AgentPlayerTransferRecord.TRANSFER_OUT;
			}

			if (amount.compareTo(BigDecimal.ZERO) > 0) {
				// 插入充值记录
				savePlayerRecharge(request, playerInfo, amount, remarks);
				getRedisService().delAgentPlayerInfo(playerInfo.getPlayerId());
			}else {
				// 插入提现记录
				savePlayerWithdraw(request, playerInfo, amount, remarks);
				getRedisService().delAgentPlayerInfo(playerInfo.getPlayerId());
			}

			// 插入账单
			getBillService().addAgentPlayerAccountBill(playerInfo, amount, balanceBefore, balanceAfter, billType,
					billno, adminAccount.getUsername(), remarks);

			AgentPlayerTransferRecord transferRecord = new AgentPlayerTransferRecord();
			transferRecord.setAgentNo(playerInfo.getAgentNo());
			transferRecord.setAgentName(playerInfo.getAgentName());
			transferRecord.setAgentType(playerInfo.getAgentType());
			transferRecord.setCreateBy(adminAccount.getUsername());
			transferRecord.setCreateTime(moment.toTimestamp());
			transferRecord.setPlayerId(playerInfo.getPlayerId());
			transferRecord.setPlayerName(playerInfo.getPlayerName());
			transferRecord.setPlayerBillNo(billno);
			transferRecord.setPlayerBalanceAfter(balanceAfter);
			transferRecord.setPlayerBalanceBefore(balanceBefore);
			transferRecord.setTransferType(transferType);
			transferRecord.setTransferAmount(amount);
			transferRecord.setIsDelete(0);
			transferRecord.setUpdateTime(moment.toTimestamp());
			getAgentPlayerTransferRecordDao().save(transferRecord);
			// 更新提现金额限制
			boolean increaseTotalBalance = getPlayerWithdrawLimitDao().increaseLotteryBalance(playerInfo.getPlayerId(), amount);
			if (!increaseTotalBalance) {
				log.error("更新提现金额限制失败，playerID:{}, amount:{}", playerInfo.getPlayerId(), amount);
				throw new IllegalArgumentException();
			}
			return true;
		}
		return false;
	}

	private boolean savePlayerRecharge(HttpServletRequest request, AgentPlayerInfo player, BigDecimal amount,
			String remarks) {
		Moment moment = new Moment();
		Long playerId = player.getPlayerId();
		String agentName = player.getAgentName();
		String billno = RandomUtils.fromTime16();
		BigDecimal balanceAfterTmp = player.getPlayerAvailableBalance().add(amount);
		BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
		BigDecimal feeAmountTmp = BigDecimal.ZERO;
		BigDecimal actualAmountTmp = amount;
		Long payId = 0L;
		Timestamp orderTime = moment.toTimestamp();
		int payMethod = PlayerRecharge.PAY_METHOD_MANUAL;
		int payType = 0;
		int method = PlayerRecharge.METHOD_SYSTEM;
		int orderStatus = PlayerRecharge.ORDER_STATUS_COMPLETED;
		String infos = "";
		String payAttach = "";
		String postscript = "";
		String orderIp = HttpUtils.getRemoteAddr(request);
		PlayerRecharge entity = new PlayerRecharge(null, player.getAgentNo(), agentName, billno, playerId,
				player.getPid(), player.getPids(), amount, feeAmountTmp, actualAmountTmp, balanceAfterTmp, balanceBeforeTmp, infos,
				method, orderIp, orderTime, orderStatus, payId, payType, orderTime, payAttach, payMethod, postscript,
				remarks);
		return getPlayerRechargeDao().save(entity);
	}

	private boolean savePlayerWithdraw(HttpServletRequest request, AgentPlayerInfo player, BigDecimal amount,
									   String remarks) {
		Moment moment = new Moment();
		String agentNo = player.getAgentNo();
		Long playerId = player.getPlayerId();
		String agentName = player.getAgentName();
		String billno = RandomUtils.fromTime16();
		BigDecimal balanceAfterTmp = player.getPlayerAvailableBalance().add(amount);
		BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
		BigDecimal feeAmountTmp = BigDecimal.ZERO;
		BigDecimal amountTmp = amount.negate();
		BigDecimal actualAmountTmp = amount.negate();
		Long payId = 0L;
		int payMethod = PlayerRecharge.PAY_METHOD_MANUAL;
		int payType = 0;
		int method = PlayerRecharge.METHOD_SYSTEM;
		int orderStatus = PlayerRecharge.ORDER_STATUS_COMPLETED;
		String infos = "";
		String payAttach = "";
		String postscript = "";
		String orderIp = HttpUtils.getRemoteAddr(request);
		int checkStatus = PlayerWithdraw.CHECK_STATUS_PASS;
		String checkUser = null;
		int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
		String lockUser = null;
		int payStatus = PlayerWithdraw.PAY_STATUS_COMPLETED;
		String payUser = null;
		String payBillno = null;
		Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
		Integer remitId = 0;
		Integer remitType = PlayerWithdraw.REMIT_TYPE_MANUAL_DEDUCTION;
		PlayerWithdraw entity = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
				balanceAfterTmp, balanceBeforeTmp, "", "", "", "",
				0, billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser,
				orderStatus, orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks,
				player.getPid());
		entity.setPids(player.getPids());
		return getPlayerWithdrawDao().save(entity);
	}

	@Override
	public boolean manualPlayerWithdraw(AdminAccount adminAccount, AgentPlayerInfo playerInfo, BigDecimal amount,
			String remarks) {
		boolean result = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerInfo.getPlayerId(), amount,
				BigDecimal.ZERO);
		if (result) {
			getRedisService().delAgentPlayerInfo(playerInfo.getPlayerId());
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = playerInfo.getPlayerAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(amount);
			// 插入账单
			getBillService().addAgentPlayerAccountBill(playerInfo, amount, balanceBefore, balanceAfter,
					AgentPlayerAccountBill.BILL_TYPE_ADMIN_MINUS, billno, adminAccount.getUsername(), remarks);
			return true;
		}
		return false;
	}

	@Override
	public boolean manualFixBalance(AdminAccount adminAccount, AgentPlayerInfo playerInfo, BigDecimal amount, String remarks) {
		boolean result = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerInfo.getPlayerId(), amount,
				BigDecimal.ZERO);
		if (result) {
			getRedisService().delAgentPlayerInfo(playerInfo.getPlayerId());
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = playerInfo.getPlayerAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(amount);
			int billType = AgentPlayerAccountBill.BILL_TYPE_ADMIN_ADD;
			if (amount.compareTo(BigDecimal.ZERO) < 0) {
				billType = AgentPlayerAccountBill.BILL_TYPE_ADMIN_MINUS;
			}
			// 插入账单
			getBillService().addAgentPlayerAccountBill(playerInfo, amount, balanceBefore, balanceAfter,
					billType, billno, adminAccount.getUsername(), remarks);
			return true;
		}
		return false;
	}

	@Override
	@Transactional
	public boolean manualPlayerActivity(AdminAccount adminAccount, AgentPlayerInfo player, BigDecimal amount, String remarks) {
		// 插入活动放发记录
		PlatformActivityRewardRecord record = new PlatformActivityRewardRecord();
		record.setAgentNo(player.getAgentNo());
		record.setAgentName(player.getAgentName());
		record.setPlayerId(player.getPlayerId());
		record.setPlayerName(player.getPlayerName());
		record.setActivityId(0L);
		record.setActivityType(0);
		record.setPid(player.getPid());
		record.setPids(player.getPids());
		record.setAmount(amount);
		record.setDrawStatus(PlatformActivityRewardRecord.DRAW_STATUS_ALREADY);
		record.setRewardTime(new Timestamp(System.currentTimeMillis()));
		record.setActivityDate(new Timestamp(System.currentTimeMillis())); // 记录周期里的开始时间
		record.setDrawData("");
		String ip = activityUtils.getAccountLoginIp(player.getPlayerId());
		record.setPlayerIp(ip);
		record.setRemarks(remarks);
		boolean result = getPlatformActivityRewardRecordDao().save(record);
		if(!result) {
			log.warn("活动补贴，插入活动放发记录失败");
			return false;
		}

		result = getAgentPlayerInfoDao().updatePlayerAvailableBalance(player.getPlayerId(), amount,
				BigDecimal.ZERO);
		if (result) {
			getRedisService().delAgentPlayerInfo(player.getPlayerId());
			String billno = RandomUtils.fromTime24();
			BigDecimal balanceBefore = player.getPlayerAvailableBalance();
			BigDecimal balanceAfter = balanceBefore.add(amount);
			int billType = AgentPlayerAccountBill.BILL_TYPE_ACTIVITY;

			// 插入账单
			getBillService().addAgentPlayerAccountBill(player, amount, balanceBefore, balanceAfter,
					billType, billno, adminAccount.getUsername(), remarks);
			return true;
		}
		return false;
	}


	@Override
	public boolean savePlateFormLine(AgentInfo agent, List<PlatformLineVo> lines) {
		String agentNo = agent.getAgentNo();
		String agentName = agent.getAgentName();
		Integer agentType = agent.getAgentType();

		Set<String> domainSet = new HashSet<String>();
		for (PlatformLineVo line : lines) {
			String lineDomainLink = line.getLineDomainLink();
			String str = lineDomainLink.substring(lineDomainLink.length() - 1);
			if ("/".equals(str)) {
				lineDomainLink = lineDomainLink.substring(0, lineDomainLink.length() - 1);
			}
			domainSet.add(lineDomainLink);
			line.setLineDomainLink(lineDomainLink);
		}

		if (domainSet.size() < lines.size()) {
			throw newException("-1", "绑定线路域名不能重复！");
		}

		List<Criterion> criterions = new ArrayList<Criterion>();
		criterions.add(Restrictions.eq("agentNo", agentNo));
		List<Order> orders = new ArrayList<Order>();
		orders.add(Order.desc("id"));
		List<PlatformLine> collectOld = getPlatformLineDao().find(criterions, orders);
		// 新增、更新和删除的集合
		List<PlatformLine> deleteList = new ArrayList<>();
		List<PlatformLine> updateList = new ArrayList<>();
		List<PlatformLine> addList = new ArrayList<>();

		// 转换成 Map，方便后续操作 (key 为 lineDomainLink + lineType 的组合)
		Map<String, PlatformLine> oldMap = collectOld.stream()
				.collect(Collectors.toMap(line -> line.getLineDomainLink() + line.getLineType(), line -> line));

		Map<String, PlatformLineVo> newMap = lines.stream()
				.collect(Collectors.toMap(line -> line.getLineDomainLink() + line.getLineType(), line -> line));

		// 1. 找出删除的项：collectOld 有，但 lines 没有
		for (PlatformLine oldLine : collectOld) {
			String key = oldLine.getLineDomainLink() + oldLine.getLineType();
			if (!newMap.containsKey(key)) {
				deleteList.add(oldLine); // 加入删除集合
			}
		}

		// 2. 找出新增的项：lines 有，但 collectOld 没有
		for (PlatformLineVo newLine : lines) {
			String key = newLine.getLineDomainLink() + newLine.getLineType();
			if (!oldMap.containsKey(key)) {
				PlatformLine platformLine = new PlatformLine();
				platformLine.setLineDomainLink(newLine.getLineDomainLink());
				platformLine.setLineType(newLine.getLineType());
				platformLine.setStatus(newLine.getStatus());
				platformLine.setAgentNo(agentNo);
				platformLine.setAgentName(agentName);
				platformLine.setAgentType(agentType);
				addList.add(platformLine); // 加入新增集合
			}
		}

		// 3. 找出需要更新的项：collectOld 和 lines 都有，但 status 不同
		for (PlatformLine oldLine : collectOld) {
			String key = oldLine.getLineDomainLink() + oldLine.getLineType();
			if (newMap.containsKey(key)) {
				PlatformLineVo newLine = newMap.get(key);
				if (!Objects.equals(oldLine.getStatus(), newLine.getStatus())) {
					oldLine.setStatus(newLine.getStatus());
					updateList.add(oldLine); // 加入更新集合
				}
			}
		}

		// 打印结果
		System.out.println("删除集合: " + deleteList);
		System.out.println("新增集合: " + addList);
		System.out.println("更新集合: " + updateList);

		for (PlatformLine oldLine : deleteList) {
			getPlatformLineDao().delete(oldLine);
		}

		for (PlatformLine oldLine : addList) {
			List<Criterion> queryCriterions = new ArrayList<Criterion>();
			queryCriterions.add(Restrictions.eq("lineDomainLink", oldLine.getLineDomainLink()));
			int count = getPlatformLineDao().totalCount(queryCriterions);
			if (count > 0) {
				throw newException("-1", "绑定线路域名已被占用，请检查");
			}
			getPlatformLineDao().save(oldLine);
			//更新域名值
			if (oldLine.getLineType() == 3 ) {
				getAgentInfoDao().updateAgentApiUrl(agent.getAgentId(), oldLine.getLineDomainLink());
			}
		}

		for (PlatformLine oldLine : updateList) {
			List<Criterion> queryCriterions = new ArrayList<Criterion>();
			queryCriterions.add(Restrictions.eq("lineDomainLink", oldLine.getLineDomainLink()));
			int count = getPlatformLineDao().totalCount(queryCriterions);
			if (count > 1) {
				throw newException("-1", "绑定线路域名已被占用，请检查");
			}
			getPlatformLineDao().update(oldLine);
			//更新域名值
			if (oldLine.getLineType() == 3 ) {
				getAgentInfoDao().updateAgentApiUrl(agent.getAgentId(), oldLine.getLineDomainLink());
			}

		}

		return true;
	}

	@Override
	@Transactional
	public boolean updatePassword(Long playerId, String password) {
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		boolean testPassword = StaticUtils.testPassword(password);
		if (!testPassword) {
			throw newException("102-02");
		}
		String md5Pwd = new MD5().toMD5(password);
		boolean result = getAgentPlayerInfoDao().updatePassword(playerId, md5Pwd);
		if (!result) {
			throw new IllegalArgumentException();
		}
		getRedisService().delAgentPlayerInfo(playerId);
//		getServiceUtils().updateAccountLockTime(account, "ADMIN_LOCK_WITHDRAW_FOR_LOGIN");
		getServiceUtils().updateAccountLockTime(playerInfo, 1);
		return true;
	}

	@Override
	@Transactional
	public boolean updateWithdrawPassword(Long playerId, String password) {
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		boolean testPassword = StaticUtils.testPassword(password);
		if (!testPassword) {
			throw newException("104-02");
		}
		String md5Pwd = new MD5().toMD5(password);
		boolean result = getAgentPlayerInfoDao().updateWithdrawPassword(playerId, md5Pwd);
		if (!result) {
			throw new IllegalArgumentException();
		}
//		getServiceUtils().updateAccountLockTime(account, "ADMIN_LOCK_WITHDRAW_FOR_PASSWORD");
		getServiceUtils().updateAccountLockTime(playerInfo, 1);
		return true;
	}

	@Override
	@Transactional
	public boolean updateWithdrawName(Long playerId, String name) {
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		boolean updateWithdrawName = getAgentPlayerInfoDao().updateWithdrawName(playerId, name);
		if (!updateWithdrawName) {
			throw new IllegalArgumentException();
		}
		getServiceUtils().updateAccountLockTime(playerInfo, 1);
		return true;
	}

	@Override
	@Transactional
	public boolean resetAccountLockTime(Long playerId) {
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		return getAgentPlayerInfoDao().updateLockTime(playerId, null);
	}

	@Override
	@Transactional
	public boolean resetAccountSecurity(Long playerId) {
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		// 这里不用等待回复结果
//		getAccountSecurityDao().deleteByAccount(playerId);
//		getServiceUtils().updateAccountLockTime(account, "ADMIN_LOCK_WITHDRAW_FOR_SECURITY");
		return true;
	}

	@Override
	@Transactional
	public boolean clearWithdrawLimitBalance(Long playerId) {
		AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(playerId);
		if (playerInfo == null) {
			throw newException("100-01");
		}
		return getPlayerWithdrawLimitDao().clear(playerId);
	}

	@Override
	@Transactional
	public boolean updateAccountGoogleLogin(Long playerId, int flag) {
		boolean b = getAgentPlayerInfoDao().updateGoogleLogin(playerId, flag);
		if (b) {
			boolean b1 = getRedisService().delAgentPlayerInfo(playerId);
			if (!b1) {
				log.warn("删除用户redis缓存失败, playerId:" + playerId);
			}
		}
		return b;
	}

	@Override
	@Transactional
	public boolean resetAccountGoogleBind(Long playerId) {
		boolean result = getAgentPlayerInfoDao().resetGoogleBind(playerId);
		if (result) {
			getRedisService().delAgentPlayerInfo(playerId);
		}

		return result;
	}

	@Override
	public boolean cancelRecharge(String billno) {
		return getPlayerRechargeDao().cancelRecharge(billno);
	}

	@Override
	public boolean patchRecharge(String billno, BigDecimal changeAmount, String payBillno, String remarks) {
		PlayerRecharge recharge = getPlayerRechargeDao().getByBillno(billno);
		if (recharge == null) {
			throw new ServiceException("-1", "订单数据不存在");
		}

		if (recharge.getOrderStatus() != PlayerRecharge.ORDER_STATUS_REVIEW
				&& PlayerRecharge.ORDER_STATUS_WAITING != recharge.getOrderStatus()) {
			throw new ServiceException("-1", "只能对未处理的订单进行补单操作");
		}

		Long playerId = recharge.getPlayerId();
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
		if (player == null) {
			throw new ServiceException("-1", "充值订单对应的玩家不存在");
		}
		BigDecimal amount = recharge.getAmount();
		BigDecimal actualAmount = recharge.getActualAmount();

		// 判断金额是否被修改
		if (PlayerRecharge.METHOD_TRANSFER == recharge.getMethod()) {
			// 是USDT支付
			if (amount.compareTo(changeAmount) != 0) {
				// USDT支付可以修改金额,此处说明金额发生了修改
				recharge.setAmount(changeAmount);
				// 手续费（百分比）
				BigDecimal feeRate = actualAmount.divide(amount, 5, RoundingMode.DOWN);
				// 实际到账金额
				BigDecimal changeActualAmount = changeAmount.multiply(feeRate);
				recharge.setActualAmount(changeActualAmount);
				// 手续费金额
				recharge.setFeeAmount(changeAmount.subtract(changeActualAmount));
				String changeRemarks = recharge.getRemarks();
				recharge.setRemarks(changeRemarks + String.format(",漏单补单,修改金额%s为%s", amount, changeAmount));
				getPlayerRechargeDao().update(recharge);
				// 更新金额
				amount = changeAmount;
				actualAmount = changeActualAmount;
			}
			// 判断是否需要绑定新的交易记录
			PaymentBillRecord paymentBillRecord = getPaymentBillRecordDao()
					.getPaymentBillRecordByTransferHash(payBillno);
			if (paymentBillRecord != null) {
				getPaymentBillRecordDao().updateStatus(paymentBillRecord.getId(), (byte) 2);
			}
		}

		BigDecimal balanceBefore = player.getPlayerAvailableBalance();
		BigDecimal balanceAfter = balanceBefore.add(actualAmount);
		// 更新账户余额
		boolean updateAmount = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerId, actualAmount,
				BigDecimal.ZERO);
		if (!updateAmount) {
			throw new ServiceException("-1", "更新账户余额失败");
		}
		// 更新提现金额限制
		boolean increaseTotalBalance = getPlayerWithdrawLimitDao().increaseLotteryBalance(playerId, actualAmount);
		if (!increaseTotalBalance) {
			throw new IllegalArgumentException("更新充值累计金额失败");
		}
		getRedisService().delAgentPlayerInfo(playerId);
		// 更新订单状态
		remarks = "#漏单补单#" + remarks + "，#付款单号：" + payBillno;
		int payMethod = PlayerRecharge.PAY_METHOD_MANUAL;
		String infos = "";
		int method = recharge.getMethod();
		if (PlayerRecharge.METHOD_THRID == method) {
			infos = "在线支付" + String.format("%.2f", recharge.getAmount()) + "元";
		} else if (PlayerRecharge.METHOD_TRANSFER == method) {
			infos = "USDT充值" + String.format("%.2f", recharge.getAmount()) + "元";
		}
		boolean updateOrder = getPlayerRechargeDao().completedOrder(billno, payMethod, balanceBefore, balanceAfter,
				remarks, infos, recharge.getOrderStatus());
		if (!updateOrder) {
			throw new IllegalArgumentException("更新订单失败");
		}
		// 添加充值账单
		getBillService().addRechargeBill(player, recharge, remarks);
		// 处理支付通道已用额度
		Long payId = recharge.getPayId();

		if (PlayerRecharge.METHOD_THRID == method) {
			getPaymentThirdPayDao().updateUsedCredits(payId, amount);
			getPaymentThirdPayDao().checkUpdateStatus(payId);
		} else if (PlayerRecharge.METHOD_TRANSFER == method) {
			getPaymentTransferDao().updateUsedCredits(payId, amount);
			getPaymentTransferDao().checkUpdateStatus(payId);
			player.addPlayerAvailableBalance(actualAmount);
			getActivityService().inActivities(player, recharge);
		}
		getActivityService().dealInActivities(player, recharge);
		return true;
	}

	@Override
	public boolean syncCountry(String agentNo) {
		try {
			AgentInfo byAgentNo = getAgentInfoDao().getByAgentNo(agentNo);

			if (byAgentNo == null) {
				log.warn("代理商不存在，agentNo：{}", agentNo);
				return false;
			}

			List<Country> countries = getCountryDao().listAll();

			List<PlatformCountry> platformCountries = getPlatformCountryDao().listAll(agentNo);
			Map<String, PlatformCountry> map = platformCountries.stream().collect(Collectors.toMap(PlatformCountry::getCountryCode, p -> p));


			countries.forEach(country -> {
				PlatformCountry platformCountry = map.get(country.getCountryCode());
                if (platformCountry == null) {
					PlatformCountry pl = new PlatformCountry();
					BeanUtils.copyProperties(country, pl);
					pl.setAgentNo(byAgentNo.getAgentNo());
					pl.setAgentName(byAgentNo.getAgentName());
					getPlatformCountryDao().save(pl);
                }
			});

			return true;
		}catch (Exception e){
			log.error("同步代理商服务地区失败， agentNo：{}", agentNo);
			return false;
		}
	}

	@Override
	public boolean sortCountry(PlatformCountry platformCountry, int sort) {

		List<Criterion> criterions = new ArrayList<Criterion>();
		criterions.add(Restrictions.eq("agentNo", platformCountry.getAgentNo()));

		int i = getPlatformCountryDao().totalCount(criterions);
		if (sort > i) {
			sort = i;
		}

		if (platformCountry.getSort().equals(sort)) {
			return true;
		}


		if (platformCountry.getSort() > sort) {
			List<Order> orders = new ArrayList<Order>();
			orders.add(Order.desc("sort"));
			criterions.add(Restrictions.between("sort", sort, platformCountry.getSort() - 1));
			List<PlatformCountry> platformCountries = getPlatformCountryDao().find(criterions, orders);
			for (PlatformCountry platformCountry1 : platformCountries) {
				platformCountry1.setSort(platformCountry1.getSort() + 1);
				getPlatformCountryDao().update(platformCountry1);
			}
		} else {
			List<Order> orders = new ArrayList<Order>();
			orders.add(Order.asc("sort"));
			criterions.add(Restrictions.between("sort", platformCountry.getSort() + 1, sort));
			List<PlatformCountry> platformCountries = getPlatformCountryDao().find(criterions, orders);
			for (PlatformCountry platformCountry1 : platformCountries) {
				platformCountry1.setSort(platformCountry1.getSort() - 1);
				getPlatformCountryDao().update(platformCountry1);
			}
		}
		platformCountry.setSort(sort);
		getPlatformCountryDao().update(platformCountry);
		return true;
	}

	@Override
	public boolean sortThesaurus(PlatformThesaurus platformThesaurus, int sort) {

		List<Criterion> criterions = new ArrayList<Criterion>();
		criterions.add(Restrictions.eq("agentNo", platformThesaurus.getAgentNo()));

		int i = getPlatformThesaurusDao().totalCount(criterions);
		if (sort > i) {
			sort = i;
		}

		if (platformThesaurus.getSort().equals(sort)) {
			return true;
		}


		if (platformThesaurus.getSort() > sort) {
			List<Order> orders = new ArrayList<Order>();
			orders.add(Order.desc("sort"));
			criterions.add(Restrictions.between("sort", sort, platformThesaurus.getSort() - 1));
			List<PlatformThesaurus> platformThesauruses = getPlatformThesaurusDao().find(criterions, orders);
			for (PlatformThesaurus platformThesaurus1 : platformThesauruses) {
				platformThesaurus1.setSort(platformThesaurus1.getSort() + 1);
				getPlatformThesaurusDao().update(platformThesaurus1);
			}
		} else {
			List<Order> orders = new ArrayList<Order>();
			orders.add(Order.asc("sort"));
			criterions.add(Restrictions.between("sort", platformThesaurus.getSort() + 1, sort));
			List<PlatformThesaurus> platformThesauruses = getPlatformThesaurusDao().find(criterions, orders);
			for (PlatformThesaurus platformThesaurus1 : platformThesauruses) {
				platformThesaurus1.setSort(platformThesaurus1.getSort() - 1);
				getPlatformThesaurusDao().update(platformThesaurus1);
			}
		}
		platformThesaurus.setSort(sort);
		getPlatformThesaurusDao().update(platformThesaurus);
		return true;
	}

	@Override
	@Transactional
	public boolean changeLine(int type, String lineA, String lineB,String agentNo) {
		AgentPlayerInfo aBean = getAgentPlayerInfoDao().getByPlayerName(agentNo,lineA);
		AgentPlayerInfo bBean = getAgentPlayerInfoDao().getByPlayerName(agentNo,lineB);
		if (aBean == null) {
			throw newException("100-01");
		}
		if (bBean == null) {
			throw newException("100-01");
		}
		// 只转移下级用户
		if (type == 0) {
			List<AgentPlayerInfo> list = getAgentPlayerInfoDao().findAllSubPlayers(aBean.getPlayerId());
//			updateTeamLine(list, bBean);
		}
		// 转移整条线路
		if (type == 1) {
			List<AgentPlayerInfo> list = new ArrayList<AgentPlayerInfo>();
			list.add(aBean);
//			updateTeamLine(list, bBean);
		}
		return true;
	}

}