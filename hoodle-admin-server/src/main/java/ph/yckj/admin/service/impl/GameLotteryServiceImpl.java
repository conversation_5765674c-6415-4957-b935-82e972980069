package ph.yckj.admin.service.impl;

import myutil.DateUtils;
import myutil.Moment;
import myutil.RandomUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import ph.yckj.admin.entity.AdminAccount;
import ph.yckj.admin.service.GameLotteryService;
import ph.yckj.admin.service.SystemService;
import ph.yckj.admin.service.TencentLiveStreamService;
import ph.yckj.admin.util.AbstractService;
import ph.yckj.admin.util.LotteryValidator;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.common.VideoFormat;
import ph.yckj.common.service.S3VideoUploadService;
import ph.yckj.common.util.ServiceException;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.ReportTargetDataType;
import sy.hoodle.base.common.service.report.TotalReportService;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class GameLotteryServiceImpl extends AbstractService implements GameLotteryService {

    private static final String rtmpUrlTemplate = "rtmp://%s/%s/%s?%s";

    private static final char[] DIGITS_LOWER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
            'e', 'f'};
    private static final Log log = LogFactory.getLog(GameLotteryServiceImpl.class);

    /**
     * 线程安全的结算任务
     */
    protected static final int poolSize = 100;

    @Autowired
    private SystemService systemService;

    @Autowired
    private S3VideoUploadService s3VideoUploadService;

    @Autowired
    private TencentLiveStreamService tencentLiveStreamService;

    @Autowired
    @Qualifier("gameLotteryPushVideoExecutor")
    private Executor gameLotteryPushVideoExecutor;

    @Override
    public boolean updateLotteryTypeStatus(Long id, int status) {
        return getGameLotteryTypeDao().updateLotteryTypeStatus(id, status);
    }

    @Override
    public boolean editLotteryInfo(int gameId, String gameRoomName, String gameCompereName, String telegramAccount,
            String gameNotice, Integer stopDelay, Integer downCode, Integer fenDownCode, Integer liDownCode,
            Integer floatBonus, BigDecimal maxBonus) {
        GameLotteryInfo entity = getGameLotteryInfoDao().getById(Long.valueOf(gameId));
        if (entity != null) {
            entity.setGameRoomName(gameRoomName);
            entity.setGameCompereName(gameCompereName);
            entity.setTelegramAccount(telegramAccount);
            entity.setGameNotice(gameNotice);
            entity.setStopDelay(stopDelay);
            entity.setDownCode(downCode);
            entity.setFenDownCode(fenDownCode);
            entity.setLiDownCode(liDownCode);
            entity.setFloatBonus(floatBonus);
            entity.setMaxBonus(maxBonus);
            entity.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            getGameLotteryInfoDao().update(entity);
            if(!floatBonus.equals(entity.getFloatBonus())) {
                // 修改了浮动奖级后，需要刷新玩法赔率奖金
                systemService.updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_METHOD);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean editAgentGameLotteryInfo(int gameId, String gameRoomName, String gameCompereName,
            String telegramAccount, String gameNotice) {
        GameLotteryInfo entity = getGameLotteryInfoDao().getById(Long.valueOf(gameId));
        if (entity != null) {
            return getAgentGameLotteryInfoDao().updateAgentGameLotteryInfo(entity.getLottery(), gameRoomName,
                    gameCompereName, telegramAccount, gameNotice);
        }
        return false;
    }

    @Override
    public boolean editLotteryInfo(Long gameId, String gameGroupCode, Integer stopDelay, Integer downCode,
            Integer fenDownCode, Integer liDownCode, Integer floatBonus, BigDecimal maxBonus, BigDecimal gameMaxLimit,
            Integer sort) {
        GameLotteryInfo entity = getGameLotteryInfoDao().getById(gameId);
        if(entity != null) {
            boolean result = getGameLotteryInfoDao().update(gameId, gameGroupCode, stopDelay, downCode, fenDownCode,
                    liDownCode, floatBonus, maxBonus, gameMaxLimit, sort);
            if(result) {
                if(!sort.equals(entity.getSort())) {
                    // 变更了排序，更新代理商的排序字段
                    getAgentGameLotteryInfoDao().updateAllAgentLotterySort(entity.getLottery(), sort);
                }
                if(!floatBonus.equals(entity.getFloatBonus())) {
                    // 修改了浮动奖级后，需要刷新玩法赔率奖金
                    systemService.updateSystemCacheVersion(SystemCache.Key.GAME_LOTTERY_METHOD);
                }
            }
            return result;
        }
        return false;
    }

    @Override
    public boolean editAgentGameLotteryInfo(Long gameId, String gameGroupCode, Integer stopDelay, Integer downCode,
            Integer fenDownCode, Integer liDownCode, Integer floatBonus, BigDecimal maxBonus, BigDecimal gameMaxLimit,
            Integer sort) {
        return getAgentGameLotteryInfoDao().updateAgentGameLotteryInfo(gameId, gameGroupCode, stopDelay, downCode,
                fenDownCode, liDownCode, floatBonus, maxBonus, gameMaxLimit, sort);
    }

    @Override
    public boolean preStepSortLotteryType(int sortValue) {
        List<GameLotteryType> list = getGameLotteryTypeDao().listPreLimit2(sortValue);
        if (list.size() == 2) {
            for (int i = 0; i < list.size(); i++) {
                int sort = 0;
                Long typeId = list.get(i).getTypeId();
                String gameTypeCode = list.get(i).getGameTypeCode();

                if (i == 0) {
                    sort = list.get(i + 1).getSort();
                } else {
                    sort = list.get(i - 1).getSort();
                }
                getGameLotteryTypeDao().updateLotteryTypeSort(typeId, gameTypeCode, sort);
            }
            return true;
        } else {
            throw newException("已是最高序列，无法升序");
        }
    }

    @Override
    public boolean nextStepSortLotteryType(int sortValue) {
        List<GameLotteryType> list = getGameLotteryTypeDao().listNextLimit2(sortValue);
        if (list.size() == 2) {
            for (int i = 0; i < list.size(); i++) {
                int sort = 0;
                Long typeId = list.get(i).getTypeId();
                String gameTypeCode = list.get(i).getGameTypeCode();

                if (i == 0) {
                    sort = list.get(i + 1).getSort();
                } else {
                    sort = list.get(i - 1).getSort();
                }
                getGameLotteryTypeDao().updateLotteryTypeSort(typeId, gameTypeCode, sort);
            }
            return true;
        } else {
            throw newException("已是最低序列，无法降序");
        }
    }

    @Override
    public boolean preStepSortLotteryInfo(int sortValue) {
        List<GameLotteryInfo> list = getGameLotteryInfoDao().listPreLimit2(sortValue);
        if (list.size() == 2) {
            for (int i = 0; i < list.size(); i++) {
                int sort = 0;
                Long gameId = list.get(i).getGameId();

                if (i == 0) {
                    sort = list.get(i + 1).getSort();
                } else {
                    sort = list.get(i - 1).getSort();
                }
                getGameLotteryInfoDao().updateLotteryInfoSort(gameId, sort);
            }
            return true;
        } else {
            throw newException("已是最高序列，无法升序");
        }
    }

    @Override
    public boolean nextStepSortLotteryInfo(int sortValue) {
        List<GameLotteryInfo> list = getGameLotteryInfoDao().listNextLimit2(sortValue);
        if (list.size() == 2) {
            for (int i = 0; i < list.size(); i++) {
                int sort = 0;
                Long gameId = list.get(i).getGameId();

                if (i == 0) {
                    sort = list.get(i + 1).getSort();
                } else {
                    sort = list.get(i - 1).getSort();
                }

                getGameLotteryInfoDao().updateLotteryInfoSort(gameId, sort);
            }
            return true;
        } else {
            throw newException("已是最低序列，无法降序");
        }
    }

    @Override
    public boolean enableOrDisableLotteryMethod(String agentNo, String lottery, String gameTypeCode, String methodCode,
            int methodStatus) {
        // 查询总配置
        GameLotteryMethod method = getGameLotteryMethodDao().getByGameTypeCodeAndMethodCode(gameTypeCode, methodCode);
        if(method == null){
            throw newException("500-134");
        }
        if(StringUtils.isEmpty(agentNo) || StringUtils.isEmpty(lottery)) {
            // 修改总配置
            method.setMethodStatus(methodStatus);
            getGameLotteryMethodDao().update(method);
            if(methodStatus == GameLotteryMethod.GAME_METHOD_STATUS_FORBIDDEN) {
                // 禁用总配置时，将全部代理商该玩法禁用
                getGameLotteryMethodCustomizeDao().updateAllAgentMethodStatus(gameTypeCode, methodCode, methodStatus);
            }

            return true;
        }else {
            // 修改代理商玩法配置
            if(methodStatus == GameLotteryMethod.GAME_METHOD_STATUS_NORMAL &&
                    method.getMethodStatus() != GameLotteryMethod.GAME_METHOD_STATUS_NORMAL) {
                // 代理商启用玩法时，如果总配置为禁用状，则禁止启用
                throw newException("500-135");
            }
            GameLotteryMethodCustomize entity = getGameLotteryMethodCustomizeDao().getByInfo(agentNo, lottery,
                    methodCode);
            if(entity != null) {
                // 代理商有该玩法配置，直接修改
                entity.setMethodStatus(methodStatus);
                getGameLotteryMethodCustomizeDao().update(entity);
                return true;
            }else {
                // 代理商没有该玩法配置，查询总配置
                AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
                entity = new GameLotteryMethodCustomize(lottery, method);
                entity.setMethodId(null);
                entity.setMethodStatus(methodStatus);
                entity.setAgentNo(agent.getAgentNo());
                entity.setAgentName(agent.getAgentName());
                return getGameLotteryMethodCustomizeDao().save(entity);
            }
        }
    }

    @Override
    public boolean editLotteryMethod(String agentNo, String lottery, String gameTypeCode, String methodCode,
            BigDecimal betMinLimit, BigDecimal betMaxLimit, BigDecimal bonusMaxLimit, Integer minRecord,
            Integer maxRecord, Integer totalRecord, Integer oooNums, BigDecimal oooBonus, Integer floatBonus) {
        if(StringUtils.isEmpty(agentNo) || StringUtils.isEmpty(lottery)) {
            // 修改总配置
            GameLotteryMethod entity = getGameLotteryMethodDao().getByGameTypeCodeAndMethodCode(gameTypeCode,
                    methodCode);
            if(entity != null) {
                entity.setBetMinLimit(betMinLimit);
                entity.setBetMaxLimit(betMaxLimit);
                entity.setBonusMaxLimit(bonusMaxLimit);
                entity.setMinRecord(minRecord);
                entity.setMaxRecord(maxRecord);
                entity.setTotalRecord(totalRecord);
                entity.setOooNums(oooNums);
                entity.setOooBonus(oooBonus);
                entity.setFloatBonus(floatBonus);
                getGameLotteryMethodDao().update(entity);
                return true;
            }
        }else {
            // 修改代理商玩法配置
            GameLotteryMethodCustomize entity = getGameLotteryMethodCustomizeDao().getByInfo(agentNo, lottery,
                    methodCode);
            if(entity != null) {
                // 代理商有该玩法配置，直接修改
                entity.setBetMinLimit(betMinLimit);
                entity.setBetMaxLimit(betMaxLimit);
                entity.setBonusMaxLimit(bonusMaxLimit);
                entity.setMinRecord(minRecord);
                entity.setMaxRecord(maxRecord);
                entity.setTotalRecord(totalRecord);
                entity.setOooNums(oooNums);
                entity.setOooBonus(oooBonus);
                entity.setFloatBonus(floatBonus);
                getGameLotteryMethodCustomizeDao().update(entity);
                return true;
            }else {
                // 代理商没有该玩法配置，查询总配置
                GameLotteryMethod method = getGameLotteryMethodDao().getByGameTypeCodeAndMethodCode(gameTypeCode,
                        methodCode);
                AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
                entity = new GameLotteryMethodCustomize(lottery, method);
                entity.setMethodId(null);
                entity.setBetMinLimit(betMinLimit);
                entity.setBetMaxLimit(betMaxLimit);
                entity.setBonusMaxLimit(bonusMaxLimit);
                entity.setMinRecord(minRecord);
                entity.setMaxRecord(maxRecord);
                entity.setTotalRecord(totalRecord);
                entity.setOooNums(oooNums);
                entity.setOooBonus(oooBonus);
                entity.setFloatBonus(floatBonus);
                entity.setAgentNo(agent.getAgentNo());
                entity.setAgentName(agent.getAgentName());
                return getGameLotteryMethodCustomizeDao().save(entity);
            }
        }
        return false;
    }

    @Override
    public boolean setLotteryMethodBonus(String agentNo, String lottery, String gameTypeCode, String methodCode,
            String bonus) {
        if(StringUtils.isEmpty(agentNo) || StringUtils.isEmpty(lottery)) {
            // 修改总配置
            GameLotteryMethod entity = getGameLotteryMethodDao().getByGameTypeCodeAndMethodCode(gameTypeCode,
                    methodCode);
            if(entity != null) {
                entity.setBonus(bonus);
                getGameLotteryMethodDao().update(entity);
                return true;
            }
        }else {
            // 修改代理商玩法配置
            GameLotteryMethodCustomize entity = getGameLotteryMethodCustomizeDao().getByInfo(agentNo, lottery,
                    methodCode);
            if(entity != null) {
                // 代理商有该玩法配置，直接修改
                entity.setBonus(bonus);
                getGameLotteryMethodCustomizeDao().update(entity);
                return true;
            }else {
                // 代理商没有该玩法配置，查询总配置
                GameLotteryMethod method = getGameLotteryMethodDao().getByGameTypeCodeAndMethodCode(gameTypeCode,
                        methodCode);

                AgentInfo agent = getAgentInfoDao().getByAgentNo(agentNo);
                entity = new GameLotteryMethodCustomize(lottery, method);
                entity.setMethodId(null);
                entity.setBonus(bonus);
                entity.setAgentNo(agent.getAgentNo());
                entity.setAgentName(agent.getAgentName());
                return getGameLotteryMethodCustomizeDao().save(entity);
            }
        }
        return false;
    }

    @Override
    public boolean updateKillConfigLotteryInfo(Long id, String killConfig) {
        return getGameLotteryInfoDao().updateKillConfig(id, killConfig);
    }

    @Override
    public boolean importGameLotteryVideo(String lottery, String openCode, MultipartFile video, String remark) {

        checkOpenCode(lottery, openCode);

        String videoKey = getVideoKey(lottery, openCode, video);
        GameLotteryVideo gameLotteryVideo = new GameLotteryVideo();
        gameLotteryVideo.setOpenCode(openCode);
        gameLotteryVideo.setLottery(lottery);
        gameLotteryVideo.setVideoYunStoreLink(videoKey);
        gameLotteryVideo.setVideoPrivateStoreLink(videoKey);
        gameLotteryVideo.setVideoFormat(getVideoFormat(video));
        gameLotteryVideo.setVideoDuration(getVideoDuration(video));
        gameLotteryVideo.setVideoSize(Math.toIntExact(video.getSize()));
        gameLotteryVideo.setRemark(remark);
        gameLotteryVideo.setCreateBy(String.valueOf(Objects.requireNonNull(ThreadLocalUtil.getSessionUser()).getId()));
        gameLotteryVideo.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        gameLotteryVideo.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        gameLotteryVideo.setUseStatus(0);
        gameLotteryVideo.setBanStatus(1);
        gameLotteryVideo.setUseTimes(0);
        return getGameLotteryVideoDao().add(gameLotteryVideo);
    }

    private void checkOpenCode(String lottery, String openCode) {
        if (StringUtils.isEmpty(openCode) || !openCode.contains(",")) {
            String text = String.format("该开奖号码格式不对，应以英文半角逗号分隔开奖号码, 开奖号码: %s", openCode);
            log.warn(text);
            throw new ServiceException("1", text);
        }

        GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(lottery);
        if (gameLotteryInfo == null) {
            String text = String.format("该游戏不存在, 游戏简称: %s", lottery);
            log.warn(text);
            throw new ServiceException("1", text);
        }

        boolean b = LotteryValidator.validateLotteryNumbers(openCode, lottery);
        if (!b) {
            String text = String.format("该开奖号码不符合对应游戏的规则, 游戏名称: %s，开奖号码: %s", gameLotteryInfo.getGameName(), openCode);
            log.warn(text);
            throw new ServiceException("1", text);
        }

        // 判断openCode是否存在
        List<Criterion> criterions = new ArrayList<Criterion>();
        if (!StringUtils.isEmpty(lottery)) {
            criterions.add(Restrictions.eq("lottery", lottery));
        }
        if (!StringUtils.isEmpty(openCode)) {
            criterions.add(Restrictions.eq("openCode", openCode));
        }

        criterions.add(Restrictions.eq("isDelete", 0));
        int i = getGameLotteryVideoDao().totalCount(criterions);
        if (i > 0) {
            String text = String.format("该开奖号码存在，请更换，开奖号码: %s", openCode);
            log.warn(text);
            throw new ServiceException("1", text);
        }
    }

    private String getVideoKey(String lottery, String openCode, MultipartFile video) {
        String videoKey = "";
        try {
            String decodedFileName = URLDecoder.decode(Objects.requireNonNull(video.getOriginalFilename()),
                    StandardCharsets.UTF_8.name());

            String keyName = "openCode/video/" + lottery + "/" + openCode + "/" + decodedFileName;
            URL url = s3VideoUploadService.uploadVideo(video, keyName);
            if (url != null) {
                videoKey = keyName;
            }
        } catch (Exception e) {
            log.error("getVideoKey error ", e);
            throw new ServiceException("1", "视频上传云服务器失败");
        }

        return videoKey;
    }

    @SuppressWarnings("resource")
    private Integer getVideoDuration(MultipartFile video) {
        Integer videoDuration = 0;
        try {
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(video.getInputStream());
            grabber.start();
            videoDuration = Math.toIntExact(grabber.getLengthInTime() / 1000);
        } catch (Exception e) {
            log.error("getVideoDuration error ", e);
            throw new ServiceException("1", "获取视频时长失败");
        }

        return videoDuration;
    }

    private Integer getVideoFormat(MultipartFile video) {
        Integer videoFormat = 0;
        try {
            String extension = "";
            // 获取文件名
            String fileName = video.getOriginalFilename();

            // 获取文件名中最后一个"."的位置
            int dotIndex = Objects.requireNonNull(fileName).lastIndexOf(".");

            // 如果文件名中包含"."并且不在起始位置，就截取文件后缀名
            if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                extension = fileName.substring(dotIndex + 1);
            }
            videoFormat = Objects.requireNonNull(VideoFormat.getVideoFormat(extension)).getId();
        } catch (Exception e) {
            log.error("getVideoFormat error ", e);
            throw new ServiceException("1", "获取视频格式失败");
        }

        return videoFormat;
    }

    /*
     * KEY+ streamName + txTime
     */
    private static String getSafeUrl(String streamName, Long txTime) {

        String key = "ddb8ac45e6c4847689d8d898c587ad1c";
        String input = new StringBuilder().append(key).append(streamName).append(Long.toHexString(txTime).toUpperCase())
                .toString();

        String txSecret = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            txSecret = byteArrayToHexString(messageDigest.digest(input.getBytes("UTF-8")));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return txSecret == null ? ""
                : new StringBuilder().append("txSecret=").append(txSecret).append("&").append("txTime=")
                .append(Long.toHexString(txTime).toUpperCase()).toString();
    }

    private static String byteArrayToHexString(byte[] data) {
        char[] out = new char[data.length << 1];

        for (int i = 0, j = 0; i < data.length; i++) {
            out[j++] = DIGITS_LOWER[(0xF0 & data[i]) >>> 4];
            out[j++] = DIGITS_LOWER[0x0F & data[i]];
        }
        return new String(out);
    }

    @Override
    public boolean pushGameLotteryVideo(Long id) {

        GameLotteryVideo byId = getGameLotteryVideoDao().getById(id);
        if (byId == null || byId.getBanStatus() == 1 || byId.getIsDelete() == 1) {
            return false;
        }

        gameLotteryPushVideoExecutor.execute(() -> {
            // 创建一个CompletableFuture对象
            String openCodeStreamName = byId.getLottery() + "_openCode";
            try {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    log.info("下载 视频 开始");
                    InputStream inputStream = s3VideoUploadService.downloadVideoFromS3(byId.getVideoYunStoreLink());
                    log.info("下载 视频 结束");
                    long expireTime = Instant.now().plus(7, ChronoUnit.DAYS).toEpochMilli();
                    String rtmpPushUrl = getRTMPPushUrl(openCodeStreamName, expireTime / 1000);
                    log.info(rtmpPushUrl);
                    log.info("推送 视频 开始");
                    tencentLiveStreamService.pushStreamToTencent(inputStream, rtmpPushUrl);
                    log.info("视频 推送 结束");
                    log.info("混流 取消 开始");
                    boolean b = tencentLiveStreamService.cancelCommonMixStream(openCodeStreamName);
                    log.info("混流 取消 结束：" + b);
                    if (byId.getUseStatus() == 0) {
                        byId.setUseStatus(1);
                    }
                    int useTimes = byId.getUseTimes();
                    useTimes++;
                    byId.setUseTimes(useTimes);
                    getGameLotteryVideoDao().update(byId);
                }, gameLotteryPushVideoExecutor);

                // 主线程等待异步任务完成
                log.info("等待开始");
                Thread.sleep(30000);
                log.info("等待结束");
                log.info("混流 创建 开始");
                boolean commonMixStream = tencentLiveStreamService.createCommonMixStream(openCodeStreamName, 20L,
                        Arrays.asList(openCodeStreamName, "default"), "default");
                log.info("混流 创建 结束：" + commonMixStream);
                future.get();

            } catch (Exception e) {
                log.error(" 推送视频失败 ", e);
            }
        });

        return true;
    }

    private String getRTMPPushUrl(String streamName, Long expireTime) {
        String domain = "201431.push.tlivecloud.com";
        String appName = "live";
        String txSecret = getSafeUrl(streamName, expireTime);
        return String.format(rtmpUrlTemplate, domain, appName, streamName, txSecret);
    }

    @Override
    public boolean editGameLotteryVideo(Long id, String openCode, String remark) {
        GameLotteryVideo byId = getGameLotteryVideoDao().getById(id);
        if (byId == null) {
            return false;
        }
        checkOpenCode(byId.getLottery(), openCode);
        byId.setOpenCode(openCode);
        byId.setRemark(remark);
        getGameLotteryVideoDao().update(byId);
        return true;
    }

    @Override
    public boolean banOrNotBnaGameLotteryVideo(Long id, Integer banStatus) {
        GameLotteryVideo byId = getGameLotteryVideoDao().getById(id);
        if (byId == null) {
            return false;
        }
        byId.setBanStatus(banStatus);
        getGameLotteryVideoDao().update(byId);
        return true;
    }

    @Override
    public boolean deleteGameLotteryVideo(Long id) {
        GameLotteryVideo byId = getGameLotteryVideoDao().getById(id);
        if (byId == null) {
            return false;
        }
        byId.setIsDelete(1);
        getGameLotteryVideoDao().update(byId);
        return true;
    }

    @Override
    public boolean updateLotteryGameStatus(Long id, int status) {
        return getGameLotteryInfoDao().updateLotteryStatus(id, status);
    }

    @Override
    public boolean updateLotteryGameMaintainStatus(Long id, int status, Timestamp maintainEndTime) {
        return getGameLotteryInfoDao().updateLotteryGameMaintainStatus(id, status, maintainEndTime);
    }

    @Override
    public boolean updateAgentGameLotteryMaintainStatus(String lottery, int status, Timestamp maintainEndTime) {
        return getAgentGameLotteryInfoDao().updateAgentGameLotteryMaintainStatus(lottery, status, maintainEndTime);
    }

    @Override
    public boolean updateLotteryGameKillStatus(Long id, int status) {
        return getGameLotteryInfoDao().updateLotteryKillStatus(id, status);
    }

    @Override
    public boolean updateLotteryOpenCode(Long id, String openCode) {
        return getGameLotteryOpenCodeDao().updateOpenCode(id, openCode);
    }

    @Override
    public boolean addLotteryOpenCode(String lottery, String gameIssue, String openCode) {
        gameIssue = gameIssue.trim();
        GameLotteryOpenCode gameLotteryOpenCode = new GameLotteryOpenCode();
        gameLotteryOpenCode.setGameIssue(gameIssue);
        gameLotteryOpenCode.setLottery(lottery);
        gameLotteryOpenCode.setOpenCode(openCode);
        gameLotteryOpenCode.setSettleStatus(0);
        gameLotteryOpenCode.setIsDelete(0);
        String openVideoLink = lottery.concat("/").concat(gameIssue.substring(2, gameIssue.length()).replace("-", ""))
                .concat("_").concat(openCode.replace(",", "_")).concat("_no.mp4");
        gameLotteryOpenCode.setOpenVideoLink(openVideoLink);
        gameLotteryOpenCode.setOpenResult("0");
        gameLotteryOpenCode.setOpenTime(Timestamp.valueOf(LocalDateTime.now()));
        gameLotteryOpenCode.setSettleTime(Timestamp.valueOf(LocalDateTime.now()));
        gameLotteryOpenCode.setStopTime(Timestamp.valueOf(LocalDateTime.now()));
        return getGameLotteryOpenCodeDao().save(gameLotteryOpenCode);
    }

    @Override
    public boolean addLotteryOpenCode(String lottery, String gameIssue, String openCode, int settleStatus ) {
        gameIssue = gameIssue.trim();
        GameLotteryOpenCode gameLotteryOpenCode = new GameLotteryOpenCode();
        gameLotteryOpenCode.setGameIssue(gameIssue);
        gameLotteryOpenCode.setLottery(lottery);
        gameLotteryOpenCode.setOpenCode(openCode);
        gameLotteryOpenCode.setSettleStatus(settleStatus);
        gameLotteryOpenCode.setIsDelete(0);
        String openVideoLink = lottery.concat("/").concat(gameIssue.substring(2, gameIssue.length()).replace("-", ""))
                .concat("_").concat(openCode.replace(",", "_")).concat("_no.mp4");
        gameLotteryOpenCode.setOpenVideoLink(openVideoLink);
        gameLotteryOpenCode.setOpenResult("0");
        gameLotteryOpenCode.setOpenTime(Timestamp.valueOf(LocalDateTime.now()));
        gameLotteryOpenCode.setSettleTime(Timestamp.valueOf(LocalDateTime.now()));
        gameLotteryOpenCode.setStopTime(Timestamp.valueOf(LocalDateTime.now()));
        return getGameLotteryOpenCodeDao().save(gameLotteryOpenCode);
    }

    @Override
    public boolean cancelGeneralOrder(String gameOrderNo) {
        AgentPlayerGameOrder order = getAgentPlayerGameOrderDao().getByGameOrderNo(gameOrderNo);
        if (null == order) {
            throw newException("500-009");
        }
        if (AgentPlayerGameOrder.GAME_STATUS_WAITING != order.getOrderStatus()) {
            throw newException("-1", "该订单已完成结算，无法撤单");
        }
        if (0 != order.getLockId()) {
            // 卡单订单，直接删除
            getAgentPlayerGameOrderDao().deleteById(order.getOrderId());

            getRedisService().delGameRecordDetail(order.getOrderId());
            return true;
        }
        if ("dfw".equals(order.getGameMethod())) {
            boolean bool = getGameMonopolyService().cancelDfwOrder(order);
            if (!bool) {
                log.error("撤单大富翁奖池金额变动失败。");
                throw newException("-1", "撤单失败！");
            }
        }
        Long playerId = order.getPlayerId();
        AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
        BigDecimal betAmount = order.getBetAmount();
        boolean updateBalance = getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerId, betAmount,
                betAmount.negate());
        if (!updateBalance) {
            throw newException("-1", "撤单失败！");
        }
        boolean bool = getAgentPlayerGameOrderDao().updateOrderStatus(order.getOrderId(),
                AgentPlayerGameOrder.GAME_STATUS_CANCELED);
        if (!bool) {
            throw newException("-1", "撤单失败！");
        }
        getRedisService().delGameRecordDetail(order.getOrderId());
        getBillService().addAgentPlayerOrderCancelBill(player, order);
        return true;
    }

    @Override
    public boolean updatePlayerWithdrawStatus(AdminAccount adminAccount, Long id, Integer status) {
        PlayerWithdraw entity = getPlayerWithdrawDao().getById(id);
        if (entity == null) {
            throw new ServiceException("-1", "订单数据不存在");
        }
        if (status == 1) {
            entity.setCheckStatus(1);
            entity.setCheckUser(adminAccount.getUsername());
        } else if (status == 2) {
            entity.setPayStatus(-1);
            entity.setCheckStatus(-1);
            entity.setOrderStatus(-1);
            entity.setCheckUser(adminAccount.getUsername());
        } else if (status == 3) {
            entity.setPayStatus(2);
            entity.setOrderStatus(1);
            entity.setCheckUser(adminAccount.getUsername());
        } else if (status == 4) {
            entity.setPayStatus(0);
            entity.setCheckStatus(0);
            entity.setCheckUser("");
            entity.setOrderStatus(0);
        }
        getPlayerWithdrawDao().update(entity);
        if (status == 2) {
            AgentPlayerInfo player = getAgentPlayerInfoDao().getById(entity.getPlayerId());
            boolean updateAmount = getPlayerService().updateAvailableBalance(player.getPlayerId(), entity.getAmount(),
                    entity.getAmount().negate());
            if (!updateAmount) {
                throw newException("-1", "");
            }
            String remark = "提现驳回，返还金额：" + entity.getAmount().toString();
            getPlayerBillService().addBillWithdrawReturn(adminAccount, player, entity, remark);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean cancelLotteryOpenCode(String lottery, String issue, String reason) {
        try {
            GameLotteryOpenCode gameLotteryOpenCode = getGameLotteryOpenCodeDao().getByLotteryAndIssue(lottery, issue);
            if (gameLotteryOpenCode == null) {
                throw new ServiceException("1", "期号不存在或已被修改");
            }
            if (gameLotteryOpenCode.getSettleStatus() == GameLotteryOpenCode.SETTLE_STATUS_CANCEL) {
                throw new ServiceException("1", "期号已撤销不能重复撤销处理");
            }
            //撤销开奖号码·
            getGameLotteryOpenCodeDao().updateSettleStatus(lottery, issue,reason, GameLotteryOpenCode.SETTLE_STATUS_CANCEL);
            // 获取订单
            List<AgentPlayerGameOrder> waitingList = getAgentPlayerGameOrderDao().listNotCancel(lottery, issue);
            if (CollectionUtils.isEmpty(waitingList)) {
                log.info("开奖彩种:" + lottery + ",期号:" + issue + ",未下注，注单为空");
                return true;
            }
            // 先删除卡单订单
            for(AgentPlayerGameOrder order : new ArrayList<>(waitingList)) {
                if(order.getLockId() != 0) {
                    if(order.getOrderStatus() == AgentPlayerGameOrder.GAME_STATUS_WAITING) {
                        // 作废时，未结算的卡单，直接删除
                        getAgentPlayerGameOrderDao().deleteById(order.getOrderId());
                    }
                    // 不处理卡单订单
                    waitingList.remove(order);
                }
            }

            // 初始化多线程来结算
            ExecutorService executors = Executors.newFixedThreadPool(poolSize);
            // 任务列表
            List<Callable<Boolean>> tasks = new ArrayList<>();

            Map<Long, List<AgentPlayerGameOrder>> listByPayerId = waitingList.stream()
                    .collect(Collectors.groupingBy(AgentPlayerGameOrder::getPlayerId));
            log.info("根据playerId进行订单分组，总分组=" + listByPayerId.size());
            listByPayerId.forEach((playerId, orders) -> {
                tasks.add(() -> {
                    log.info("开始撤销 playerId=" + playerId + " 的注单，数量=" + orders.size());
                    doCancelThreadsafe(listByPayerId.get(playerId), playerId, gameLotteryOpenCode);
                    return true;
                });
            });
            // 执行所有
            List<Future<Boolean>> futures = executors.invokeAll(tasks);
            // 阻塞得到结果
            for (Future<Boolean> future : futures) {
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("获取撤销结果出错", e);
                }
            }
            // 关闭所有线程
            executors.shutdown();
            // 等待终结任务
            boolean done = executors.awaitTermination(10, TimeUnit.SECONDS);
            if (done) {
                int threadLength = Thread.getAllStackTraces().size();
                log.info("撤销线程已完成，当前线程数：" + threadLength);
            }
        } catch (InterruptedException e) {
            log.error("撤销开奖结果失败", e);
        }
        return true;
    }

    public void doCancelThreadsafe(List<AgentPlayerGameOrder> orderList, Long playerId, GameLotteryOpenCode gameLotteryOpenCode) {
        log.info("开始撤销下注订单");
        StopWatch watch = new StopWatch();
        watch.start();
        AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
        if (player == null) {
            log.error("玩家账户不存在");
            throw new IllegalArgumentException();
        }
        BigDecimal totalReviseMoney = BigDecimal.ZERO; // 本期累计修正消费
        // 需要插入账单列表
        List<AgentPlayerAccountBill> insertBillList = new ArrayList<>();
        for (AgentPlayerGameOrder order : orderList) {
            if (0 != order.getLockId()) {
                // 卡单订单不能撤单
                continue;
            }
            // 已结算并且中奖处理
            if (gameLotteryOpenCode.getSettleStatus() == GameLotteryOpenCode.SETTLE_STATUS_COMPLETED && order.getOrderStatus() == AgentPlayerGameOrder.GAME_STATUS_WIN) {
                BigDecimal amount = order.getBonusAmount().negate();
                AgentPlayerAccountBill entity = addAgentPlayerGameOrderCancelBill(player, amount, AgentPlayerAccountBill.BILL_TYPE_REVISE_BONUS);
                insertBillList.add(entity);
                totalReviseMoney = totalReviseMoney.add(amount);
                player.addPlayerAvailableBalance(amount);
            }
            // 返回投注金额
            insertBillList.add(addAgentPlayerGameOrderCancelBill(player, order.getBetAmount(), AgentPlayerAccountBill.BILL_TYPE_SYSTEM_CHANCELED));
            totalReviseMoney = totalReviseMoney.add(order.getBetAmount());
            player.addPlayerAvailableBalance(order.getBetAmount());
            // 修改订单状态
            order.setBonusAmount(BigDecimal.ZERO);
            order.setProfitAmount(BigDecimal.ZERO);
            order.setSettleTime(new Moment().toTimestamp());
            order.setOrderStatus(AgentPlayerGameOrder.GAME_STATUS_CANCELED);
        }
        try {
            //更新金额
            getAgentPlayerInfoDao().updatePlayerAvailableBalance(playerId, totalReviseMoney, BigDecimal.ZERO);
        } catch (Exception e) {
            log.error("更新用户金额失败", e);
            throw new IllegalArgumentException();
        }
        try {
            for (AgentPlayerGameOrder order : orderList) {
                getAgentPlayerGameOrderDao().update(order);
            }
        } catch (Exception e) {
            log.error("更新用户订单", e);
            throw new IllegalArgumentException();
        }
        try {
            //批量插入账单
            getAgentPlayerAccountBillDao().saveList(insertBillList);
        } catch (Exception e) {
            log.error("插入账单失败", e);
            throw new IllegalArgumentException();
        }
        try {
            //删除期汇总
            cancelCalculateGameIssue(player, gameLotteryOpenCode);
        } catch (Exception e) {
            log.error("删除期汇总失败", e);
            throw new IllegalArgumentException();
        }
        log.info("撤销下注订单完成");
        watch.stop();
    }

    //期汇总和大富翁期汇总删除
    private void cancelCalculateGameIssue(AgentPlayerInfo player, GameLotteryOpenCode gameLotteryOpenCode) {
        GameOrderIssueRecord gameOrderIssueRecord = getGameOrderIssueRecordDao().getRecord(gameLotteryOpenCode.getLottery(), gameLotteryOpenCode.getGameIssue());
        if (gameOrderIssueRecord != null) {
            //修改状态逻辑删除
            gameOrderIssueRecord.setIsDelete(1);
            getGameOrderIssueRecordDao().update(gameOrderIssueRecord);
        }
        GameMonopolyBetRecord gameMonopolyBetRecord = getGameMonopolyBetRecordDao().getByLotteryAndIssue(player.getAgentNo(), gameLotteryOpenCode.getLottery(), gameLotteryOpenCode.getGameIssue());
        if (gameMonopolyBetRecord != null) {
            //修改状态逻辑删除
            gameMonopolyBetRecord.setIsDelete(1);
            getGameMonopolyBetRecordDao().update(gameMonopolyBetRecord);
        }
        GameMonopolyPlayerBetDetail gameMonopolyPlayerBetDetail = getGameMonopolyPlayerBetDetailDao().getByLotteryAndIssue(player.getPlayerId(), gameLotteryOpenCode.getLottery(), gameLotteryOpenCode.getGameIssue());
        if (gameMonopolyPlayerBetDetail != null) {
            //修改状态逻辑删除
            gameMonopolyPlayerBetDetail.setIsDelete(1);
            getGameMonopolyPlayerBetDetailDao().update(gameMonopolyPlayerBetDetail);
        }
    }

    // 构建账单信息
    public static AgentPlayerAccountBill addAgentPlayerGameOrderCancelBill(AgentPlayerInfo player, BigDecimal billAmount, int billType) {
        String agentNo = player.getAgentNo();
        String agentName = player.getAgentName();
        Integer agentType = player.getAgentType();
        Long playerId = player.getPlayerId();
        String playerName = player.getPlayerName();
        String playerBillNo = RandomUtils.fromTime24();
        BigDecimal playerBalanceBefore = player.getPlayerAvailableBalance();
        BigDecimal playerBalanceAfter = playerBalanceBefore.add(billAmount);
        String billRemark = "";
        if(AgentPlayerAccountBill.BILL_TYPE_SYSTEM_CHANCELED == billType) {
            billRemark = "游戏投注作废撤单";
        } else if(AgentPlayerAccountBill.BILL_TYPE_REVISE_BONUS == billType) {
            billRemark = "游戏投注作废修正奖金";
        }
        int isDelete = AgentPlayerAccountBill.DELETE_STATUS_NORMAL;
        String createBy = playerName;
        Timestamp createTime = new Moment().toTimestamp();
        Timestamp updateTime = createTime;
        Long pid = player.getPid();
        String pids = player.getPids();
        AgentPlayerAccountBill bill = new AgentPlayerAccountBill(agentNo, agentType, agentName, playerId, playerName,
                playerBillNo, playerBalanceBefore, billAmount, playerBalanceAfter, billType, billRemark, isDelete,
                createBy, createTime, updateTime, pid);
        bill.setPids(pids);
        return bill;
    }
}
