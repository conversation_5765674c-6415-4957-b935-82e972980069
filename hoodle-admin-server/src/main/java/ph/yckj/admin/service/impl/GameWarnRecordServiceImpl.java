package ph.yckj.admin.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.admin.service.GameWarnRecordService;
import ph.yckj.admin.util.ThreadLocalUtil;
import ph.yckj.common.util.ServiceException;
import sy.hoodle.base.common.dao.GameWarnAutoDisableRecordDao;
import sy.hoodle.base.common.dao.GameWarnRecordDao;
import sy.hoodle.base.common.entity.GameWarnAutoDisableRecord;
import sy.hoodle.base.common.entity.GameWarnRecord;

import java.time.LocalDate;
import java.util.*;
@Slf4j
@Service
@Transactional
public class GameWarnRecordServiceImpl implements GameWarnRecordService {

    @Autowired
    private GameWarnRecordDao gameWarnRecordDao;

    @Autowired
    private GameWarnAutoDisableRecordDao gameWarnAutoDisableRecordDao;

    @Override
    public GameWarnRecord getById(String lotteryGameplay, Date createTime) {
        return gameWarnRecordDao.getById(lotteryGameplay, createTime);
    }

    @Override
    public boolean save(GameWarnRecord entity) {
        return gameWarnRecordDao.save(entity);
    }

    @Override
    public void insert(List<GameWarnRecord> records) {
        gameWarnRecordDao.insert(records);
    }

    @Override
    public boolean update(String status, String lotteryGameplay, Date createTime) {
        return gameWarnRecordDao.update(status, lotteryGameplay, createTime);
    }

    @Override
    public List<GameWarnRecord> find(List<Criterion> criterions, List<Order> orders, int firstResult, int maxResults) {
        criterions.add(Restrictions.eq("isDelete", 0));
        return gameWarnRecordDao.find(criterions, orders, firstResult, maxResults);
    }

    @Override
    public Map<String, Object> searchRecords(int page, int size, String warnStatus) {
        try {
            // 构建查询条件
            List<Criterion> criterions = new ArrayList<>();
            criterions.add(Restrictions.eq("isDelete", 0));
            if (warnStatus != null && !warnStatus.trim().isEmpty()) {
                // 根据预警状态查询
                criterions.add(Restrictions.eq("status", warnStatus));
            }

            // 构建排序
            List<Order> orders = new ArrayList<>();
            orders.add(Order.desc("lastWarnTime"));
            int firstResult = page * size;
            List<GameWarnRecord> records = gameWarnRecordDao.find(criterions, orders, firstResult, size);
            int totalCount = gameWarnRecordDao.totalCount(criterions);
            int totalPages = (int) Math.ceil((double) totalCount / size);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", records);
            result.put("totalCount", totalCount);
            result.put("totalPages", totalPages);

            return result;
        } catch (Exception e) {
            log.error("查询预警记录失败", e);
            throw new ServiceException("9999", "查询预警记录失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> searchAutoDisableRecords(int page, int size) {
        try {
            // 构建查询条件
            List<Criterion> criterions = new ArrayList<>();
            criterions.add(Restrictions.eq("isDelete", 0));
            // 构建排序
            List<Order> orders = new ArrayList<>();
            orders.add(Order.desc("disableTime"));
            int firstResult = page * size;
            // 查询数据
            List<GameWarnAutoDisableRecord> records = gameWarnAutoDisableRecordDao.find(criterions, orders, firstResult, size);
            int totalCount = gameWarnAutoDisableRecordDao.totalCount(criterions);
            int totalPages = (int) Math.ceil((double) totalCount / size);
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", records);
            result.put("totalCount", totalCount);
            result.put("totalPages", totalPages);
            return result;
        } catch (Exception e) {
            log.error("查询自动禁用记录失败", e);
            throw new ServiceException("9999", "查询自动禁用记录失败: " + e.getMessage());
        }
    }

    @Override
    public GameWarnRecord getById(Long id) {
        try {
            return gameWarnRecordDao.getById(id);
        } catch (Exception e) {
            log.error("获取预警记录详情失败", e);
            throw new ServiceException("9999", "获取预警记录详情失败: " + e.getMessage());
        }
    }

    @Override
    public boolean clearWarn(Long id) {
        try {
            GameWarnRecord record = gameWarnRecordDao.getById(id);
            if (record == null) {
                throw new ServiceException("9999", "预警记录不存在");
            }
            // 更新预警状态为已清除
            record.setStatus("2"); // 2表示已清除
            record.setLastUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
            // 设置处理人为当前登录的管理员用户名
            ph.yckj.admin.entity.AdminAccount currentUser = ThreadLocalUtil.getSessionUser();
            if (currentUser != null) {
                record.setProcessedBy(currentUser.getUsername());
            } else {
                record.setProcessedBy("system");
            }
            return gameWarnRecordDao.update(record);
        } catch (Exception e) {
            log.error("清除预警记录失败", e);
            throw new ServiceException("9999", "清除预警记录失败: " + e.getMessage());
        }
    }

    @Override
    public int totalCount() {
        List<Criterion> criterions = new ArrayList<>();
        criterions.add(Restrictions.eq("isDelete", 0));
        return gameWarnRecordDao.totalCount(criterions);
    }

}
