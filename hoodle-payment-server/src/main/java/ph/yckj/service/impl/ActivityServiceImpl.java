package ph.yckj.service.impl;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import myutil.Moment;
import ph.yckj.service.ActivityService;
import ph.yckj.util.AbstractService;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.PlatformActivityConfig;
import sy.hoodle.base.common.entity.PlatformActivityInviteRecord;
import sy.hoodle.base.common.entity.PlatformActivityRewardRecord;
import sy.hoodle.base.common.entity.PlayerRecharge;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.sql.Timestamp;

@Slf4j
@Service
@Transactional
public class ActivityServiceImpl extends AbstractService implements ActivityService {

	@Autowired
	private BillService billService;

	@Autowired
	private AgentPlayerInfoLockService agentPlayerInfoLockService;

	@Override
	public void dealInActivities(AgentPlayerInfo player, PlayerRecharge recharge) {
		final Long playerId = player.getPlayerId();
		final String agentNo = recharge.getAgentNo();
		int activityType = PlatformActivityConfig.ACTIVITY_TYPE_NEWCOMER_REGISTER;
		if (0 != player.getPid()) {
			activityType = PlatformActivityConfig.ACTIVITY_TYPE_INVITATION_REGISTER;
		}
		List<PlatformActivityConfig> activitys = getPlatformActivityConfigDao().listEnableActivity(agentNo,
				activityType);
		for (PlatformActivityConfig activity : activitys) {
			Moment orderMoment = new Moment().fromDate(recharge.getOrderTime());
			Moment startTimeMoment = new Moment().fromTime(activity.getStartTime());
			Moment finishTimeMoment = new Moment().fromTime(activity.getFinishTime());
			boolean isBetween = orderMoment.between(startTimeMoment, finishTimeMoment);
			if (!isBetween) {
				return;
			}
			Long activityId = activity.getId();
			PlatformActivityInviteRecord record = getPlatformActivityInviteRecordDao().getActivityInviteRecord(playerId,
					activityId, activityType);
			if (null == record || PlatformActivityInviteRecord.STATUS_NORMAL == record.getStatus()) {
				return;
			}
			BigDecimal rechargeAmount = recharge.getAmount();
			getPlatformActivityInviteRecordDao().updateRechargeAmount(record.getId(), rechargeAmount);
		}
	}

	@Override
	public void inActivities(AgentPlayerInfo player, PlayerRecharge recharge) {
		String agentNo = player.getAgentNo();
		int activityType = PlatformActivityConfig.ACTIVITY_TYPE_USDT_REGISTER_GiVEING;
		List<PlatformActivityConfig> list = getPlatformActivityConfigDao().listEnableActivity(agentNo, activityType);
		for (PlatformActivityConfig activity : list) {
			Moment orderMoment = new Moment().fromDate(recharge.getOrderTime());
			Moment startTimeMoment = new Moment().fromTime(activity.getStartTime());
			Moment finishTimeMoment = new Moment().fromTime(activity.getFinishTime());
			boolean isBetween = orderMoment.between(startTimeMoment, finishTimeMoment);
			if (!isBetween) {
				return;
			}
			JSONArray array = JSONArray.parseArray(activity.getActivityRules());
			if (null == array) {
				continue;
			}
			int rewardType = 0;
			boolean isConform = false;
			BigDecimal rewardAmount = BigDecimal.ZERO;
			for (int i = 0; i < array.size(); i++) {
				JSONObject object = (JSONObject) array.get(i);
				int rewardTypeTmp = object.getIntValue("rewardType");
				BigDecimal rewardAmountTmp = object.getBigDecimal("rewardMoney");
				BigDecimal rechargeAmountTmp = object.getBigDecimal("rechargeAmount");
				if (recharge.getAmount().compareTo(rechargeAmountTmp) >= 0) {
					isConform = true;
					rewardType = rewardTypeTmp;
					rewardAmount = rewardAmountTmp;
				}
			}
			Long playerId = player.getPlayerId();
			Long activityId = activity.getId();
			Timestamp activityDate = new Moment().fromDate(orderMoment.toSimpleDate()).toTimestamp();
			if (isConform) {
				PlatformActivityRewardRecord record = getPlatformActivityRewardRecordDao()
						.getByPlayerIdAndActivityDate(playerId, activityId, activityDate);
				if (null != record) {
					return;
				}
				record = new PlatformActivityRewardRecord();
				record.setAgentNo(recharge.getAgentNo());
				record.setAgentName(recharge.getAgentName());
				record.setPlayerId(recharge.getPlayerId());
				record.setPid(player.getPid());
				record.setPids(player.getPids());
				record.setPlayerName(player.getPlayerName());
				BigDecimal amount = BigDecimal.ZERO;
				if (rewardType == 1) {
					amount = rewardAmount;
				} else {
					amount = rewardAmount.multiply(recharge.getAmount()).multiply(new BigDecimal("0.01"));
				}
				record.setAmount(amount);
				record.setDrawData("");
				record.setDrawStatus(1);
				record.setPid(player.getPid() != null ? player.getPid() : 0L);
				record.setActivityDate(activityDate);
				record.setActivityId(activity.getId());
				record.setActivityType(activityType);
				String playerIp = recharge.getOrderIp();
				record.setPlayerIp(playerIp);
				record.setRemarks(activity.getActivityTitle());
				record.setRewardTime(new Moment().toTimestamp());
				// 判断玩家参与IP
				Timestamp startTime = new Moment().fromDate(activityDate).toTimestamp();
				Timestamp finishTime = new Moment().fromDate(activityDate).tomorrow().toTimestamp();
				boolean isCheck = getPlatformActivityRewardRecordDao().checkAlreadyPartIn(activityId, playerIp,
						startTime, finishTime);
				if (isCheck) {
					return;
				}

				final BigDecimal finalAmount = amount;
				agentPlayerInfoLockService.incr(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_ACTIVITY,
						activity.getActivityTitle(), player.getPlayerId(), finalAmount, BigDecimal.ZERO,
						(beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
							// 账变完成
						});

				billService.addActivityBill(player, record);
				getPlatformActivityRewardRecordDao().save(record);
			}
		}
	}

}
