package ph.yckj.service.impl;

import java.math.BigDecimal;

import cn.hutool.core.thread.ThreadUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import ph.yckj.exception.ServiceException;
import ph.yckj.service.PaymentService;
import ph.yckj.util.AbstractService;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.PaymentBillRecord;
import sy.hoodle.base.common.entity.PlayerRecharge;
import sy.hoodle.base.common.entity.PlayerWithdraw;
import sy.hoodle.base.common.redis.RedisDelService;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;

@Slf4j
@Service
@Transactional
public class PaymentServiceImpl extends AbstractService implements PaymentService {

	@Autowired
	private RedisDelService redisDelService;

	@Autowired
	private BillService billService;

	@Autowired
	private AgentPlayerInfoLockService agentPlayerInfoLockService;

	@Override
	public boolean autoCompleted(String billno, int payMethod, String payUser, String payBillno, String remarks) {
		PlayerWithdraw entity = getPlayerWithdrawDao().getByBillno(billno);
		if (entity == null) {
			throw new RuntimeException("提现订单不存在");
		}
		Long playerId = entity.getPlayerId();
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
		if (player == null) {
			throw new RuntimeException("玩家用户不存在");
		}
		String infos = "您的提现已处理，请您注意查收";
		boolean setPayCompleted = getPlayerWithdrawDao().setPayCompleted(billno, payMethod, payUser, payBillno, remarks,
				infos);
		if (!setPayCompleted) {
			throw new RuntimeException("更新完成订单失败");
		}
		return true;
	}

	@Override
	public boolean thirdPayAutoReceived(Long pid, String billno, BigDecimal amount, String payBillno, String remarks) {
		PlayerRecharge recharge = getPlayerRechargeDao().getByBillno(billno);
		if (recharge == null) {
			log.error("订单不存在，单号：" + billno);
			return false;
		}
		Long playerId = recharge.getPlayerId();
		int orderStatus = recharge.getOrderStatus();
		if (!(orderStatus == PlayerRecharge.ORDER_STATUS_WAITING
				|| orderStatus == PlayerRecharge.ORDER_STATUS_CANCELED)) {
			log.error("订单已经支付完成，单号：" + billno);
			return false;
		}
		if (amount.compareTo(recharge.getAmount()) != 0) {
			log.error("支付金额与订单金额不匹配，单号：" + billno);
			return false;
		}
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
		if (player == null) {
			log.error("用户主账户不存在");
			return false;
		}
		final BigDecimal actualAmount = recharge.getActualAmount();

		agentPlayerInfoLockService.incr(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_RECHARGE,
				"第三方支付到账", player.getPlayerId(), actualAmount, BigDecimal.ZERO,
				(beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
					// 账变完成
				});

		BigDecimal balanceBefore = player.getPlayerAvailableBalance();
		BigDecimal balanceAfter = balanceBefore.add(actualAmount);
		int payMethod = PlayerRecharge.PAY_METHOD_AUTO;
		// 更新订单
		boolean updateOrder = getPlayerRechargeDao().completedOrder(billno, payMethod, balanceBefore, balanceAfter,
				remarks);
		if (!updateOrder) {
			log.error("更新订单状态失败，单号：" + billno);
			throw new IllegalArgumentException();
		}

		// 更新彩票账户提现金额累计
		boolean increaseTotalBalance = getPlayerWithdrawLimitDao().increaseLotteryBalance(playerId, actualAmount);
		if (!increaseTotalBalance) {
			log.error("更新提现金额累计失败");
			throw new IllegalArgumentException();
		}

		getActivityService().dealInActivities(player, recharge);
		// 添加充值账单
		getPlayerBillService().addRechargeBill(player, recharge, "充值到账");

		// 处理支付通道已用额度
		Long payId = recharge.getPayId();
		Integer method = recharge.getMethod();
		if (PlayerRecharge.METHOD_THRID == method) {
			getPaymentThirdPayDao().updateUsedCredits(payId, amount);
		}
		return true;
	}

	@Override
	public boolean payUsdt(PaymentBillRecord paymentBillRecord, Long inUserId, String billno, BigDecimal amount,
			String transferHash, String remarks) {
		PlayerRecharge recharge = getPlayerRechargeDao().getByBillno(billno);
		if (recharge == null) {
			throw new ServiceException(-1, "订单数据不存在");
		}

		if (recharge.getOrderStatus() != PlayerRecharge.ORDER_STATUS_REVIEW
				&& PlayerRecharge.ORDER_STATUS_WAITING != recharge.getOrderStatus()) {
			throw new ServiceException(-1, "只能对未处理的订单进行补单操作");
		}

		String payBillno = paymentBillRecord.getTransferHash();
		Long playerId = recharge.getPlayerId();
		AgentPlayerInfo player = getAgentPlayerInfoDao().getById(playerId);
		if (player == null) {
			throw new ServiceException(-1, "充值订单对应的玩家不存在");
		}
		final BigDecimal actualAmount = recharge.getActualAmount();

		agentPlayerInfoLockService.incr(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_RECHARGE,
				"USDT充值", player.getPlayerId(), actualAmount, BigDecimal.ZERO,
				(beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
					// 账变完成
				});

		BigDecimal balanceBefore = player.getPlayerAvailableBalance();
		BigDecimal balanceAfter = balanceBefore.add(actualAmount);
		// 更新提现金额限制
		boolean increaseTotalBalance = getPlayerWithdrawLimitDao().increaseLotteryBalance(playerId, actualAmount);
		if (!increaseTotalBalance) {
			throw new IllegalArgumentException("更新充值累计金额失败");
		}
		redisDelService.delAgentPlayerInfo(playerId);
		// 更新订单状态
		String temp_remark = "#付款单号：" + payBillno;
		remarks += temp_remark;
		int payMethod = PlayerRecharge.PAY_METHOD_MANUAL;
		String infos = "";
		int method = recharge.getMethod();
		if (PlayerRecharge.METHOD_THRID == method) {
			infos = "在线支付" + String.format("%.2f", recharge.getAmount()) + "元";
		} else if (PlayerRecharge.METHOD_TRANSFER == method) {
			infos = "USDT充值" + String.format("%.2f", recharge.getAmount()) + "元";
		}

		// 此处的修改,因为代码在很多地方使用,为了不破坏原有的结构,所以并没有放在一个事务中,将已经匹配的地址交易订单,标记为已使用
		getPaymentBillRecordDao().updateStatus(paymentBillRecord.getId(), (byte) 2);

		boolean updateOrder = getPlayerRechargeDao().completedOrder(billno, payMethod, balanceBefore, balanceAfter,
				remarks, infos, recharge.getOrderStatus());
		if (!updateOrder) {
			throw new IllegalArgumentException("更新订单失败");
		}
		// 添加充值账单
		billService.addRechargeBill(player, recharge, "USDT充值" + temp_remark);
		// 处理支付通道已用额度
		Long payId = recharge.getPayId();
		getPaymentTransferDao().updateAddUsedCredits(payId, amount);
		getPaymentTransferDao().checkUpdateStatus(payId);
		player.setPlayerAvailableBalance(player.getPlayerAvailableBalance().add(actualAmount));
		// 活动配置检查
		getActivityService().inActivities(player, recharge);
		getActivityService().dealInActivities(player, recharge);
		return true;
	}

}
