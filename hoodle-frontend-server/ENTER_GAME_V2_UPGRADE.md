# Enter Game V2 升级说明

## 📋 概述

参考 `apply-up-ded-transfer` 功能的改动模式，对 `enter-game` 进入三方游戏功能进行架构升级，引入批量余额更新服务，提高系统的原子性、并发安全性和可维护性。

## 🔄 改动对比

### 原有架构 vs 新架构

| 方面 | 原有架构 | 新架构 (V2) |
|------|---------|-------------|
| **余额更新方式** | 分步操作 | 批量原子操作 |
| **事务管理** | 外部事务 | 内部统一事务 |
| **并发安全** | 依赖外部锁 | 内置锁机制 |
| **账变记录** | 手动创建 | 自动集成 |
| **错误处理** | 分散处理 | 统一处理 |

## 🚀 核心改动

### 1. 新增服务接口

#### GameSimulationService.java
```java
// 新增：批量余额转账方法（参考apply-up-ded-transfer模式）
int balanceLottery2GameV2(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, 
    BigDecimal amount, boolean isTransferAll);
```

### 2. 新增依赖注入

#### GameSimulationServiceImpl.java
```java
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.base.common.service.balance.vo.BalanceChangeDirection;
import sy.hoodle.base.common.service.balance.vo.PlayerBalanceUpdateParams;

@Autowired
private AgentPlayerInfoLockService agentPlayerInfoLockService;
```

### 3. 核心实现：批量余额更新

#### 原有方式（分步操作）
```java
// 1. 扣除中心账户余额
boolean updateFromBalance = getPlatformAgentPlayerCommonService()
    .updateAvailableBalanceForGameOut(player.getPlayerId(), amount.negate(), amount, 
        AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_RECHARGE, billno, player.getPlayerName(), remark);

// 2. 生成转账记录
GameSimulationTransfer gameSimulationTransfer = ...
boolean saveGameTransfer = getGameSimulationTransferDao().save(gameSimulationTransfer);

// 3. 调用游戏平台API
gameSimulationTransfer = getSimulationGameSuperService().deposit(gameSimulationTransfer);

// 4. 添加报表
GameSimulationReport gameSimulationReport = ...
getGameSimulationReportDao().upsertTransfer(gameSimulationReport);
```

#### 新方式（批量原子操作）
```java
// 🚀 使用新的批量余额更新服务
List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
    new PlayerBalanceUpdateParams(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_RECHARGE,
        remark, player.getPlayerId(), amount.negate(), amount, BalanceChangeDirection.DECR,
        (beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
            // 所有业务逻辑在回调中统一处理
            String billno = agentPlayerAccountBill.getPlayerBillNo();
            // 生成转账记录、调用API、添加报表等
        })
);

agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
```

### 4. 新增API接口

#### Route.java
```java
// 进入游戏V2版本（使用批量余额更新）
public static final String ENTER_GAME_V2 = "/enter-game-v2";

// 游戏转账V2版本（中心账户到游戏账户，使用批量余额更新）
public static final String TRANSFER_LOTTERY_TO_GAME_V2 = "/transfer-lottery-to-game-v2";
```

#### GameSimulationController.java
```java
@RequestMapping(Route.Third.ENTER_GAME_V2)
public WebJson enterGameV2(HttpServletRequest request, ...)

@RequestMapping(Route.Third.TRANSFER_LOTTERY_TO_GAME_V2)
public WebJson TRANSFER_LOTTERY_TO_GAME_V2(HttpServletRequest request, ...)
```

## 🎯 架构优势

### 1. **原子性提升**
- 从多个分离操作变为单个原子操作
- 避免部分成功导致的数据不一致

### 2. **并发安全性增强**
- 引入专门的锁服务 `AgentPlayerInfoLockService`
- 避免并发转账时的数据竞争

### 3. **性能优化**
- 减少数据库交互次数
- 批量处理提高效率

### 4. **代码维护性**
- 统一的余额管理逻辑
- 减少重复代码
- 更清晰的职责分离

### 5. **事务一致性**
- 使用 `@Transactional(propagation = Propagation.NOT_SUPPORTED)`
- 避免与底层服务事务冲突
- 让专业的余额服务处理事务

## 📊 使用方式

### 前端调用
```javascript
// 原有方式
POST /api/out/enter-game

// 新V2方式
POST /api/out/enter-game-v2

// 转账原有方式
POST /api/out/transfer-lottery-to-game

// 转账V2方式
POST /api/out/transfer-lottery-to-game-v2
```

### 参数保持不变
- `platformCode`: 游戏平台代码
- `gameCode`: 游戏代码（可选）
- `amount`: 转账金额（转账接口）

## ⚠️ 注意事项

### 1. **向后兼容**
- 原有接口保持不变
- 新增V2接口作为升级版本
- 可以逐步迁移

### 2. **依赖要求**
- 需要确保 `AgentPlayerInfoLockService` 服务可用
- 需要相关的余额更新参数类

### 3. **监控建议**
- 监控V2接口的性能表现
- 对比V1和V2的成功率
- 关注错误日志中的异常情况

## 🔮 未来规划

1. **逐步迁移**: 在V2版本稳定后，逐步将流量从V1迁移到V2
2. **性能监控**: 持续监控V2版本的性能指标
3. **功能扩展**: 将此模式应用到其他转账相关功能
4. **最终替换**: 在V2版本完全稳定后，考虑废弃V1版本

## 📝 总结

这次升级参考了 `apply-up-ded-transfer` 的成功改动模式，将传统的分步操作升级为现代化的批量原子操作，不仅提高了系统的可靠性和性能，也为未来的功能扩展奠定了良好的基础。
