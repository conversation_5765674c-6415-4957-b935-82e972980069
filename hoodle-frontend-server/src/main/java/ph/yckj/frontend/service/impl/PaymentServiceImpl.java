package ph.yckj.frontend.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.net.util.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cryptix.jce.provider.MD5;
import lombok.extern.slf4j.Slf4j;
import myutil.AmountUtils;
import myutil.CipherUtils;
import myutil.DESUtils;
import myutil.ErrorUtils;
import myutil.HttpUtils;
import myutil.JacksonUtils;
import myutil.MathUtils;
import myutil.Moment;
import myutil.RandomUtils;
import ph.yckj.common.enums.PayTypeEnum;
import ph.yckj.common.util.ServiceException;
import ph.yckj.frontend.service.AgentPlayerService;
import ph.yckj.frontend.service.PaymentService;
import ph.yckj.frontend.util.AbstractService;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.PaymentBank;
import sy.hoodle.base.common.entity.PaymentThird;
import sy.hoodle.base.common.entity.PaymentThirdPay;
import sy.hoodle.base.common.entity.PaymentTransfer;
import sy.hoodle.base.common.entity.PlatformConfig;
import sy.hoodle.base.common.entity.PlayerCardAlipay;
import sy.hoodle.base.common.entity.PlayerCardBank;
import sy.hoodle.base.common.entity.PlayerCardHh5;
import sy.hoodle.base.common.entity.PlayerCardM;
import sy.hoodle.base.common.entity.PlayerCardOkg;
import sy.hoodle.base.common.entity.PlayerCardUsdt;
import sy.hoodle.base.common.entity.PlayerRecharge;
import sy.hoodle.base.common.entity.PlayerWithdraw;
import sy.hoodle.base.common.function.FourConsumer;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.base.common.service.balance.vo.BalanceChangeDirection;
import sy.hoodle.base.common.service.balance.vo.PlayerBalanceUpdateParams;
import sy.hoodle.base.common.util.ProJPAQueryFactoryUtil;
import sy.hoodle.base.common.util.UsdtRateUtil;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;

@Slf4j
@Service
@Transactional
public class PaymentServiceImpl extends AbstractService implements PaymentService {

	@Autowired
	private PlatformAgentPlayerCommonService platformAgentPlayerCommonService;

	@Autowired
	private AgentPlayerService agentPlayerService;

	@Autowired
	private UsdtRateUtil usdtRateUtil;

	@Autowired
	private ProJPAQueryFactoryUtil proJPAQueryFactoryUtil;

	@Autowired
	private AgentPlayerInfoLockService agentPlayerInfoLockService;

	private final static String DES_KEY = "DES_KEY_PAYMENT";

	@Override
	public double sellUsdtRate(String agentNo) {
		List<PlatformConfig> list = getPlatformConfigDao().listByGroup(agentNo, "USDT_EXCHANGE");
		if (CollectionUtils.isEmpty(list)) {
			return 0;
		}
		double usdtRechargeRate = 0;
		double usdtWithdrawRate = 0;
		double followRechargeAddRate = 0;
		boolean isUsdtRateFollowRecharge = false;
		for (PlatformConfig config : list) {
			String key = config.getKey();
			String value = config.getValue();
			if (key.equals("USDT_RECHARGE_RATE")) {
				usdtRechargeRate = Double.parseDouble(value);
			} else if (key.equals("USDT_WITHDRAW_RATE")) {
				usdtWithdrawRate = Double.parseDouble(value);
			} else if (key.equals("FOLLOW_RECHARGE_ADD_RATE")) {
				followRechargeAddRate = Double.parseDouble(value);
			} else if (key.equals("IS_USDT_RATE_FOLLOW_RECHARGE")) {
				isUsdtRateFollowRecharge = Boolean.parseBoolean(value);
			}
		}
		double sellUsdtRate = 0;
		if (!isUsdtRateFollowRecharge) {
			sellUsdtRate = usdtWithdrawRate;
		} else {
			sellUsdtRate = MathUtils.add(usdtRechargeRate, followRechargeAddRate);
		}
		return sellUsdtRate;
	}

	@Override
	public boolean addCardUsdt(AgentPlayerInfo player, String name, String usdtTrc20Address, String usdtErc20Address) {
		String agentNo = player.getAgentNo();
		Long playerId = player.getPlayerId();
		String playerName = player.getPlayerName();
		int status = 0;
		int usdtType = 0;
		boolean isDefault = false;
		boolean lockStatus = false;
		Date lockedTime = null;
		String createBy = playerName;
		Date createTime = new Moment().toDate();
		String encryptedUsdtTrc20Address = getToolsService().encrypt(usdtTrc20Address);
		String encryptedUsdtErc20Address = getToolsService().encrypt(usdtErc20Address);
		PlayerCardUsdt entity = new PlayerCardUsdt(null, agentNo, playerId, usdtType, name, encryptedUsdtTrc20Address,
				encryptedUsdtErc20Address, isDefault, lockStatus, lockedTime, status, createBy, createTime);
		return getPlayerCardUsdtDao().save(entity);
	}

	@Override
	public boolean updateCardUsdt(AgentPlayerInfo player, Long cardId, String usdtTrc20Address, String usdtErc20Address) {
		Date lockedTime  = new Moment().add(8, "hours").toDate();
		String encryptedUsdtTrc20Address = getToolsService().encrypt(usdtTrc20Address);
		String encryptedUsdtErc20Address = getToolsService().encrypt(usdtErc20Address);
		return getPlayerCardUsdtDao().updateUsdtTrc20AddressById(cardId, encryptedUsdtTrc20Address, encryptedUsdtErc20Address, lockedTime);
	}

	@Override
	public boolean setDefaultCardUsdt(Long playerId, Long cardId) {
		getPlayerCardBankDao().updateByPlayerId(playerId, false);
		getPlayerCardAlipayDao().updateByPlayerId(playerId, false);
		getPlayerCardUsdtDao().updateByPlayerId(playerId, false);
		return getPlayerCardUsdtDao().updateById(cardId, true);
	}
	
	@Override
	public boolean addCardM(AgentPlayerInfo player, String name, String address) {
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		Long playerId = player.getPlayerId();
		String playerName = player.getPlayerName();
		int status = 0;
		boolean isDefault = false;
		boolean lockStatus = false;
		Date lockedTime = null;
		String createBy = playerName;
		Date createTime = new Moment().toDate();
		String encryptedCardAddress = getToolsService().encrypt(address);
		PlayerCardM entity = new PlayerCardM(null, agentNo, agentName, playerId, name, encryptedCardAddress, createBy,
				createTime, lockStatus, lockedTime, status, isDefault);
		return getPlayerCardMDao().save(entity);
	}

	@Override
	public boolean deleteCardMById(Long cardId) {
		return getPlayerCardMDao().deleteById(cardId);
	}

	@Override
	public boolean setDefaultCardM(Long playerId, Long cardId) {
		getPlayerCardMDao().updateByPlayerId(playerId, false);
		getPlayerCardHh5Dao().updateByPlayerId(playerId, false);
		getPlayerCardOkgDao().updateByPlayerId(playerId, false);
		getPlayerCardUsdtDao().updateByPlayerId(playerId, false);
		getPlayerCardBankDao().updateByPlayerId(playerId, false);
		getPlayerCardAlipayDao().updateByPlayerId(playerId, false);
		return getPlayerCardMDao().updateById(cardId, true);
	}

	@Override
	public boolean addCardHh5(AgentPlayerInfo player, String name, String address) {
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		Long playerId = player.getPlayerId();
		String playerName = player.getPlayerName();
		int status = 0;
		boolean isDefault = false;
		boolean lockStatus = false;
		Date lockedTime = null;
		String createBy = playerName;
		Date createTime = new Moment().toDate();
		String encryptedCardAddress = getToolsService().encrypt(address);
		PlayerCardHh5 entity = new PlayerCardHh5(null, agentNo, agentName, playerId, name, encryptedCardAddress,
				createBy, createTime, lockStatus, lockedTime, status, isDefault);
		return getPlayerCardHh5Dao().save(entity);
	}

	@Override
	public boolean deleteCardHh5ById(Long cardId) {
		return getPlayerCardHh5Dao().deleteById(cardId);
	}

	@Override
	public boolean setDefaultCardHh5(Long playerId, Long cardId) {
		getPlayerCardMDao().updateByPlayerId(playerId, false);
		getPlayerCardHh5Dao().updateByPlayerId(playerId, false);
		getPlayerCardOkgDao().updateByPlayerId(playerId, false);
		getPlayerCardUsdtDao().updateByPlayerId(playerId, false);
		getPlayerCardBankDao().updateByPlayerId(playerId, false);
		getPlayerCardAlipayDao().updateByPlayerId(playerId, false);
		return getPlayerCardHh5Dao().updateById(cardId, true);
	}

	@Override
	public boolean addCardOkg(AgentPlayerInfo player, String name, String address) {
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		Long playerId = player.getPlayerId();
		String playerName = player.getPlayerName();
		int status = 0;
		boolean isDefault = false;
		boolean lockStatus = false;
		Date lockedTime = null;
		String createBy = playerName;
		Date createTime = new Moment().toDate();
		String encryptedCardAddress = getToolsService().encrypt(address);
		PlayerCardOkg entity = new PlayerCardOkg(null, agentNo, agentName, playerId, name, encryptedCardAddress,
				createBy, createTime, lockStatus, lockedTime, status, isDefault);
		return getPlayerCardOkgDao().save(entity);
	}

	@Override
	public boolean deleteCardOkgById(Long cardId) {
		return getPlayerCardOkgDao().deleteById(cardId);
	}

	@Override
	public boolean setDefaultCardOkg(Long playerId, Long cardId) {
		getPlayerCardMDao().updateByPlayerId(playerId, false);
		getPlayerCardHh5Dao().updateByPlayerId(playerId, false);
		getPlayerCardOkgDao().updateByPlayerId(playerId, false);
		getPlayerCardUsdtDao().updateByPlayerId(playerId, false);
		getPlayerCardBankDao().updateByPlayerId(playerId, false);
		getPlayerCardAlipayDao().updateByPlayerId(playerId, false);
		return getPlayerCardOkgDao().updateById(cardId, true);
	}

	@Override
	public boolean setDefaultCardBank(Long playerId, Long cardId) {
		getPlayerCardBankDao().updateByPlayerId(playerId, false);
		getPlayerCardAlipayDao().updateByPlayerId(playerId, false);
		getPlayerCardUsdtDao().updateByPlayerId(playerId, false);
		return getPlayerCardBankDao().updateById(cardId, true);
	}

	@Override
	public boolean addCardAlipay(AgentPlayerInfo player, String alipayName, String alipayAccount) {
		if (StringUtils.isEmpty(player.getWithdrawName())) {
			throw newException("-1", "提现用户名未绑定");
		}
		String agentNo = player.getAgentNo();
		Long playerId = player.getPlayerId();
		String playerName = player.getPlayerName();
		int status = 0;
		boolean isDefault = false;
		int lockStatus = 0;
		String createBy = playerName;
		Date createTime = new Moment().toDate();
		String encryptedBankCardNo = getToolsService().encrypt(alipayAccount);
		Date lockedTime = null;
		PlayerCardAlipay entity = new PlayerCardAlipay(null, agentNo,player.getAgentName(), playerId,playerName, alipayName, encryptedBankCardNo, isDefault, lockStatus, lockedTime, status, createBy, createTime);
		return getPlayerCardAlipayDao().save(entity);
	}

	@Override
	public boolean deleteCardAlipayById(Long cardId) {
		return getPlayerCardAlipayDao().deleteById(cardId);
	}

	@Override
	public boolean setDefaultCardAlipay(Long playerId, Long cardId) {
		getPlayerCardBankDao().updateByPlayerId(playerId, false);
		getPlayerCardAlipayDao().updateByPlayerId(playerId, false);
		getPlayerCardUsdtDao().updateByPlayerId(playerId, false);
		return getPlayerCardAlipayDao().updateById(cardId, true);
	}

	@Override
	public boolean updateCardAlipay(AgentPlayerInfo player, Long cardId, String alipayName, String alipayAccount) {
		Date lockedTime = new Moment().add(8, "hours").toDate();
		PlayerCardAlipay playerCardAlipay = new PlayerCardAlipay();
		playerCardAlipay.setId(cardId);
		playerCardAlipay.setAlipayName(alipayName);
		playerCardAlipay.setLockedTime(lockedTime);
		if (null != alipayAccount) {
			playerCardAlipay.setAlipayAccount(getToolsService().encrypt(alipayAccount));
		}
		getPlayerCardAlipayDao().update(playerCardAlipay);
		return true;
	}


	@Override
	public boolean addCardBank(AgentPlayerInfo player, Long bankId, String bankCardBranch, String bankCardNo) {
		if (StringUtils.isEmpty(player.getWithdrawName())) {
			throw newException("-1", "提现用户名未绑定");
		}
		String agentNo = player.getAgentNo();
		Long playerId = player.getPlayerId();
		String playerName = player.getPlayerName();
		String bankCardName = player.getWithdrawName();
		int status = 0;
		boolean isDefault = false;
		boolean lockStatus = false;
		String createBy = playerName;
		Date createTime = new Moment().toDate();
		String encryptedBankCardNo = getToolsService().encrypt(bankCardNo);
		Date lockedTime = null;
		PlayerCardBank entity = new PlayerCardBank(null, agentNo, playerId, bankId, bankCardBranch, bankCardName,
				encryptedBankCardNo, isDefault, lockStatus, lockedTime, status, createBy, createTime);
		return getPlayerCardBankDao().save(entity);
	}

	@Override
	public Map<String, Object> applyThirdPay(HttpServletRequest request, AgentPlayerInfo player, Long pid,
			double amount) {
		String agentNo = player.getAgentNo();
		PaymentThirdPay thirdPay = getPaymentThirdPayDao().getAvailableById(pid, agentNo);
		if (null == thirdPay) {
			throw newException("-1", "该充值通道暂时已停用");
		}
		// 检测充值金额
		if (amount <= 0) {
			throw newException("-1", "充值金额输入不正确");
		}
		double minUnitAmount = thirdPay.getMinUnitAmount();
		if (amount < minUnitAmount) {
			throw newException("-1", "单次充值金额不能低于" + minUnitAmount + "元");
		}
		double maxUnitAmount = thirdPay.getMaxUnitAmount();
		if (amount > maxUnitAmount) {
			throw newException("-1", "单次充值金额不能高于" + maxUnitAmount + "元");
		}
		// 判断下三方支付的支付类型，如果是USDT-TRC20，就不需要绑定提款人
		if (!"USDT_TRC20_PAY".equals(thirdPay.getPayType())) {
			// 验证是否绑定了提现姓名
			boolean isBindWithdrawName = StringUtils.isEmpty(player.getWithdrawName()) ? false : true;
			if (!isBindWithdrawName) {
				throw newException("-1", "请先绑定取款人");
			}
		}

		double feeAmount = 0;
		double feeRate = thirdPay.getFeeRate();
		if (feeRate > 0) {
			feeAmount = amount * feeRate / 100;
		}
		Moment moment = new Moment();
		Long playerId = player.getPlayerId();
		String agentName = player.getAgentName();
		String billno = RandomUtils.fromTime16();
		BigDecimal amountTmp = BigDecimal.valueOf(amount);
		BigDecimal balanceAfterTmp = BigDecimal.ZERO;
		BigDecimal balanceBeforeTmp = BigDecimal.ZERO;
		BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
		BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);
		Long payId = thirdPay.getId();
		Timestamp orderTime = moment.toTimestamp();
		int payMethod = 1;
		Timestamp payTime = orderTime;
		int payType = PayTypeEnum.getByPayTypeCode(thirdPay.getPayType()).getCode();
		String infos = "", remarks = "", payAttach = "", postscript = "";
		int method = PlayerRecharge.METHOD_THRID;
		String orderIp = HttpUtils.getRemoteAddr(request);
		int orderStatus = PlayerRecharge.ORDER_STATUS_WAITING;
		PlayerRecharge entity = new PlayerRecharge(null, agentNo, agentName, billno, playerId, player.getPid(), player.getPids(),
				amountTmp, feeAmountTmp, actualAmountTmp, balanceAfterTmp, balanceBeforeTmp, infos, method, orderIp,
				orderTime, orderStatus, payId, payType, payTime, payAttach, payMethod, postscript, remarks);
		entity.setPids(player.getPids());
		boolean result = getPlayerRechargeDao().save(entity);
		if (!result) {
			throw newException("-1", "充值通道拉单失败");
		}
		PaymentThird third = getPaymentThirdDao().getById(thirdPay.getThirdId());
		String link = third.getPayUrl() + "/pay";
		String MerKey = thirdPay.getMerSecretKey().trim();
//		if ("USDT_TRC20_PAY".equals(thirdPay.getPayType())) {
//			amount = czAmount;
//		}
		String text = encodePay(pid, billno, amount, thirdPay.getPayWayCode(), null, MerKey);
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("link", link);
		data.put("billno", billno);
		data.put("amount", amount);
		data.put("feeRate", feeRate);
		data.put("feeAmount", feeAmount);
		data.put("actualAmount", actualAmountTmp);
		data.put("text", text);
		return data;
	}

	private static String encodePay(Long pid, String billno, double amount, String method, String bankco, String key) {
		try {
			String signature = new MD5().toMD5("pid=" + pid + "&billno=" + billno + "&amount=" + amount + "&method="
					+ method + "&bankco=" + bankco + "|" + key);
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("pid", pid);
			data.put("billno", billno);
			data.put("amount", amount);
			data.put("method", method);
			data.put("bankco", bankco);
			data.put("signature", signature);
			String jsonValue = JacksonUtils.toJsonString(data);
			String desValue = DESUtils.encryptStr(jsonValue, DES_KEY);
			byte[] b64bytes = desValue.getBytes("UTF8");
			byte[] result = Base64.encodeBase64(b64bytes);
			String text = new String(result);
			return text;
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.error("error:" + error);
		}
		return null;
	}

	@Override
	public Map<String, Object> applyTransferPay(HttpServletRequest request, AgentPlayerInfo player, Long id,
			double amount, String postscript) {
		PaymentTransfer transfer = getPaymentTransferDao().getById(id);
		if (null == transfer || PaymentTransfer.STATUS_NORMAL != transfer.getStatus()) {
			throw newException("-1", "该充值通道已停用！");
		}
		BigDecimal amountBigDecimal = BigDecimal.valueOf(amount);
		// 检测充值金额
		if (amount <= 0) {
			throw newException("-1", "充值金额输入不正确");
		}
		double minUnitAmount = transfer.getMinUnitAmount();
		if (amount < minUnitAmount) {
			throw newException("-1", "单次充值金额不能低于" + minUnitAmount + "元");
		}
		double maxUnitAmount = transfer.getMaxUnitAmount();
		if (amount > maxUnitAmount) {
			throw newException("-1", "单次充值金额不能高于" + maxUnitAmount + "元");
		}
		double feeRate = transfer.getFeeRate();
		double feeAmount = 0;
		if (feeRate > 0) {
			feeAmount = amount * feeRate / 100;
		}
		BigDecimal feeAmountBigDecimal = BigDecimal.valueOf(feeAmount);
		Long playerId = player.getPlayerId();
		String billno = RandomUtils.fromTime16();
		double actualAmount = amount - feeAmount;
		Date payTime = null;
		String infos = "", remarks = "", payAttach = "";
		int method = PlayerRecharge.METHOD_TRANSFER;
		Date orderTime = new Moment().toDate();
		int orderStatus = PlayerRecharge.ORDER_STATUS_WAITING;
		Long payId = transfer.getId();
		int payMethod = 1;
		double czAmount = 0;
		int payType = transfer.getType();
		String bankName = transfer.getBankName();
		String bankBranch = transfer.getBankBranch();
		String bankCardName = transfer.getBankCardName();
		String bankCardAddress = transfer.getBankCardAddress();
		String base64Code = transfer.getBase64Code();
		method = PlayerRecharge.METHOD_TRANSFER;
		payAttach = "USDT-TRC20收款地址：" + bankCardAddress;
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		double usdtRechargeRate = 0;
		PlatformConfig config = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "USDT_EXCHANGE",
				"USDT_RECHARGE_RATE");
		if (config != null) {
			usdtRechargeRate = Double.parseDouble(config.getValue());
		}
		if (usdtRechargeRate <= 0) {
			throw newException("-1", "充值USDT-TRC20汇率异常！");
		}
		czAmount = amount / Double.valueOf(usdtRechargeRate);
		czAmount = Double.valueOf(AmountUtils.toDecimalUp(czAmount));
		BigDecimal usdtAmount = getUsdtRechargeAmount(new BigDecimal(czAmount + ""));
		if (null == usdtAmount) {
			throw newException("110-05");
		}
		czAmount = Double.parseDouble(AmountUtils.toDecimalDown(Double.parseDouble(usdtAmount.toString())));
		BigDecimal amountNew = usdtAmount.multiply(new BigDecimal(usdtRechargeRate));
		actualAmount = Double.parseDouble(
				AmountUtils.toDecimalDown(Double.parseDouble(amountNew.subtract(feeAmountBigDecimal).toString())));
		remarks = "汇率:" + usdtRechargeRate + ",USDT充值金额:" + czAmount + ",到账金额:" + actualAmount;
		postscript = Double.toString(czAmount);
		BigDecimal balanceAfterTmp = BigDecimal.ZERO;
		BigDecimal balanceBeforeTmp = BigDecimal.ZERO;
		String orderIp = HttpUtils.getRemoteAddr(request);
		BigDecimal actualAmountBigDecimal = new BigDecimal(actualAmount);
		PlayerRecharge entity = new PlayerRecharge(null, agentNo, agentName, billno, playerId, player.getPid(), player.getPids(),
				amountBigDecimal, feeAmountBigDecimal, actualAmountBigDecimal, balanceAfterTmp, balanceBeforeTmp, infos,
				method, orderIp, orderTime, orderStatus, payId, payType, payTime, payAttach, payMethod, postscript,
				remarks);
		entity.setPids(player.getPids());
		boolean result = getPlayerRechargeDao().save(entity);
		if (!result) {
			throw newException("-1", "充值通道拉单失败");
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("billno", billno);
		data.put("amount", actualAmount);
		data.put("transferType", transfer.getType());
		data.put("usdtRate", usdtRechargeRate);
		data.put("usdtAmount", czAmount + "");
		data.put("bankName", bankName);
		data.put("bankBranch", bankBranch);
		data.put("bankCardName", bankCardName);
		data.put("bankCardAddress", bankCardAddress);
		data.put("base64Code", base64Code);
		data.put("remarks", remarks);
		return data;
	}

	private BigDecimal getUsdtRechargeAmount(BigDecimal amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date(System.currentTimeMillis()));
		calendar.add(Calendar.MINUTE, -15);
		Date startTime = calendar.getTime();
		BigDecimal upAmount = amount;
		List<PlayerRecharge> rechargeList = new ArrayList<PlayerRecharge>();
		// 先查看金额有没有重复的
		rechargeList = getPlayerRechargeDao().getRechargeOrderByAddressAndStatus(upAmount.doubleValue(), startTime);
		if (null == rechargeList || rechargeList.size() == 0) {
			return upAmount;
		}
		// 随机取98次看有没有重复的
		for (int i = 0; i < 98; i++) {
			Random random = new Random();
			double addAount = Double.valueOf(random.nextInt(99)) / 100;
			upAmount = upAmount.add(new BigDecimal(AmountUtils.toDecimal(addAount)));
			rechargeList = getPlayerRechargeDao().getRechargeOrderByAddressAndStatus(upAmount.doubleValue(), startTime);
			if (null == rechargeList || rechargeList.size() == 0) {
				return upAmount;
			}
		}
		// 遍历取1~99看有没有重复的
		for (int i = 1; i < 100; i++) {
			upAmount = upAmount.add(new BigDecimal("0.01"));
			rechargeList = getPlayerRechargeDao().getRechargeOrderByAddressAndStatus(upAmount.doubleValue(), startTime);
			if (null == rechargeList || rechargeList.size() == 0) {
				return upAmount;
			}
		}
		return null;
	}

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyWithdraw(HttpServletRequest request, AgentPlayerInfo player, double amount,
			String withdrawPassword, int remitType, Long cardId) {


		PlatformConfig platformConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(), "WITHDRAW","FIRST_WITHDRAW_WAIT_HOUR");

		if(platformConfig != null ){
			int waitHour = 0;
			try {
				waitHour = Integer.parseInt(platformConfig.getValue());
			}catch (Exception e){
				log.warn("新用户首次提现等待时间配置转化数字失败，platformConfig:{}", platformConfig, e);
			}

			LocalDateTime canTime = player.getCreateTime().toLocalDateTime().plusHours(waitHour);
			if (LocalDateTime.now().isBefore(player.getCreateTime().toLocalDateTime().plusHours(waitHour))) {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH点mm分ss秒");

				String msg = String.format("还未到首次可提现时间 %s", canTime.format(formatter));
				throw  new ServiceException("1", msg);
			}
		}

		// 检测充值金额
		if (amount <= 0) {
			throw newException("-1", "提现金额输入不正确");
		}
		if (!CipherUtils.verify(player.getWithdrawPassword(), withdrawPassword)) {
			throw newException("-1", "资金密码输入错误");
		}
		// 验证玩家提现锁定时间
		Timestamp withdrawLockTime = player.getLockTime();
		if (player.getLockTime() != null) {
			Moment lockMoment = new Moment().fromTime(withdrawLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				throw newException("-1", "该玩家提现已被锁定，解锁时间为：" + time);
			}
		}

		// 获取消费量限制
		BigDecimal availableBalance = getServiceUtils().getAvailableWithdrawAmount(player);
		// 验证可用余额
		if (availableBalance.compareTo(new BigDecimal(amount)) < 0) {
			// ERR:账户可用余额不足
			throw newException("-1", "账户可提现余额不足");
		}

		// 当天可提现次数、当天可提现额度限制判断（这个包括收取手续费和免手续费的）
		Map<String, String> dataMap = new HashMap<String, String>();
		List<PlatformConfig> list = getPlatformConfigDao().listByGroup(player.getAgentNo(), "WITHDRAW");
		if (!CollectionUtils.isEmpty(list)) {
			for (PlatformConfig config : list) {
				String key = config.getKey();
				String value = config.getValue();
				dataMap.put(key, value);
			}
		}
		// 当天可提现次数、当天可提现额度限制判断（这个包括收取手续费和免手续费的）
		List<PlayerWithdraw> withdraws = getPlayerWithdrawDao().getNoRefuseToDay(player.getPlayerId());
		if (!CollectionUtils.isEmpty(withdraws)) {
			String MAX_DAILY_COUNT = dataMap.get("MAX_DAILY_COUNT");
			if (!StringUtils.isEmpty(MAX_DAILY_COUNT)) {
				int maxDailyCount = Integer.parseInt(MAX_DAILY_COUNT) - withdraws.size();
				if (maxDailyCount <= 0) {
					throw newException("-1", "今日可提现次数不足！");
				}
			}
			String MAX_DAILY_AMOUNT = dataMap.get("MAX_DAILY_AMOUNT");
			if (!StringUtils.isEmpty(MAX_DAILY_AMOUNT)) {
				double maxDailyAmount = Double.parseDouble(MAX_DAILY_AMOUNT);
				BigDecimal totalWithdrawAmount = withdraws.stream().map(PlayerWithdraw::getAmount)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				maxDailyAmount = (new BigDecimal(maxDailyAmount).subtract(totalWithdrawAmount)).doubleValue();
				if (amount > maxDailyAmount) {
					throw newException("-1", "今日可提现额度不足！");
				}
			}
		}
		// 这个是withdraws只比较免手续次数的
		withdraws = getPlayerWithdrawDao().getNoRefuseToDayCount(player.getPlayerId());
		boolean result = false;
		if (0 == remitType) {
			result = applyWithdrawBank(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (1 == remitType) {
//			result = applyWithdrawUsdt(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
			result = applyWithdrawThirdUsdt(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (2 == remitType) {
			result = applyWithdrawAlipay(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (3 == remitType) {
			result = applyWithdrawM(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (4 == remitType) {
			result = applyWithdrawHh5(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (5 == remitType) {
			result = applyWithdrawOkg(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		}
		return result;
	}


	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyWithdrawV2(HttpServletRequest request, AgentPlayerInfo player, double amount,
								 String withdrawPassword, int remitType, Long cardId) {


		PlatformConfig platformConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(), "WITHDRAW","FIRST_WITHDRAW_WAIT_HOUR");

		if(platformConfig != null ){
			int waitHour = 0;
			try {
				waitHour = Integer.parseInt(platformConfig.getValue());
			}catch (Exception e){
				log.warn("新用户首次提现等待时间配置转化数字失败，platformConfig:{}", platformConfig, e);
			}

			LocalDateTime canTime = player.getCreateTime().toLocalDateTime().plusHours(waitHour);
			if (LocalDateTime.now().isBefore(player.getCreateTime().toLocalDateTime().plusHours(waitHour))) {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH点mm分ss秒");

				String msg = String.format("还未到首次可提现时间 %s", canTime.format(formatter));
				throw  new ServiceException("1", msg);
			}
		}

		// 检测充值金额
		if (amount <= 0) {
			throw newException("-1", "提现金额输入不正确");
		}
		agentPlayerService.verifyPaymentPassword(request, player, withdrawPassword);

		// 验证玩家提现锁定时间
		Timestamp withdrawLockTime = player.getLockTime();
		if (player.getLockTime() != null) {
			Moment lockMoment = new Moment().fromTime(withdrawLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				throw newException("-1", "该玩家提现已被锁定，解锁时间为：" + time);
			}
		}

		// 获取消费量限制
		BigDecimal availableBalance = getServiceUtils().getAvailableWithdrawAmount(player);
		// 验证可用余额
		if (availableBalance.compareTo(new BigDecimal(amount)) < 0) {
			// ERR:账户可用余额不足
			throw newException("-1", "账户可提现余额不足");
		}

		// 当天可提现次数、当天可提现额度限制判断（这个包括收取手续费和免手续费的）
		Map<String, String> dataMap = new HashMap<String, String>();
		List<PlatformConfig> list = getPlatformConfigDao().listByGroup(player.getAgentNo(), "WITHDRAW");
		if (!CollectionUtils.isEmpty(list)) {
			for (PlatformConfig config : list) {
				String key = config.getKey();
				String value = config.getValue();
				dataMap.put(key, value);
			}
		}
		// 当天可提现次数、当天可提现额度限制判断（这个包括收取手续费和免手续费的）
		List<PlayerWithdraw> withdraws = getPlayerWithdrawDao().getNoRefuseToDay(player.getPlayerId());
		if (!CollectionUtils.isEmpty(withdraws)) {
			String MAX_DAILY_COUNT = dataMap.get("MAX_DAILY_COUNT");
			if (!StringUtils.isEmpty(MAX_DAILY_COUNT)) {
				int maxDailyCount = Integer.parseInt(MAX_DAILY_COUNT) - withdraws.size();
				if (maxDailyCount <= 0) {
					throw newException("-1", "今日可提现次数不足！");
				}
			}
			String MAX_DAILY_AMOUNT = dataMap.get("MAX_DAILY_AMOUNT");
			if (!StringUtils.isEmpty(MAX_DAILY_AMOUNT)) {
				double maxDailyAmount = Double.parseDouble(MAX_DAILY_AMOUNT);
				BigDecimal totalWithdrawAmount = withdraws.stream().map(PlayerWithdraw::getAmount)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				maxDailyAmount = (new BigDecimal(maxDailyAmount).subtract(totalWithdrawAmount)).doubleValue();
				if (amount > maxDailyAmount) {
					throw newException("-1", "今日可提现额度不足！");
				}
			}
		}
		// 这个是withdraws只比较免手续次数的
		withdraws = getPlayerWithdrawDao().getNoRefuseToDayCount(player.getPlayerId());
		boolean result = false;
		if (0 == remitType) {
			result = applyWithdrawBank(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (1 == remitType) {
//			result = applyWithdrawUsdt(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
			result = applyWithdrawThirdUsdt(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (2 == remitType) {
			result = applyWithdrawAlipay(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (3 == remitType) {
			result = applyWithdrawM(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (4 == remitType) {
			result = applyWithdrawHh5(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		} else if (5 == remitType) {
			result = applyWithdrawOkg(request, player, amount, Long.valueOf(cardId), dataMap, withdraws);
		}
		return result;
	}

	private boolean applyWithdrawBank(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId,
			Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		Long playerId = player.getPlayerId();
		Long pid = player.getPid();
		pid = pid == null ? 0L : pid;
		PlayerCardBank card = getPlayerCardBankDao().getById(cardId);
		if (null == card) {
			throw newException("-1", "该提现地址已取消绑定");
		}
		if (0 != card.getStatus()) {
			throw newException("-1", "该提现地址已禁用");
		}
		if (!playerId.equals(card.getPlayerId())) {
			throw newException("-1", "该提现地址玩家未绑定");
		}
		// 验证锁定时间
		Date cardLockTime = card.getLockedTime();
		if (cardLockTime != null) {
			Moment lockMoment = new Moment().fromDate(cardLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				// ERR:提现银行卡已被锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
			}
		}
		Long bankId = card.getBankId();
		PaymentBank bank = getPaymentBankDao().getById(bankId);
		if (null == bank) {
			throw newException("-1", "该提款银行暂不支持提款");
		}
		if (PaymentBank.WITHDRAW_STATUS_FORBIDDEN == bank.getWithdrawStatus()) {
			throw newException("-1", "该提款银行暂不支持提款");
		}

		double feeRate = 0;
		Long dailyCount = 0L;
		int freeDailyCount = 0;
		double minUnitAmount = 0;
		double maxUnitAmount = 0;
		Moment moment = new Moment();
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		// 判断是否还有免体现次数，如果没有才收取提现手续费
		String BANK_FEE_RATE = dataMap.get("BANK_FEE_RATE");
		String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
		String BANK_MIN_UNIT_AMOUNT = dataMap.get("BANK_MIN_UNIT_AMOUNT");
		String BANK_MAX_UNIT_AMOUNT = dataMap.get("BANK_MAX_UNIT_AMOUNT");
		String BANK_FREE_DAILY_COUNT = dataMap.get("BANK_FREE_DAILY_COUNT");
		if (!StringUtils.isEmpty(BANK_FREE_DAILY_COUNT)) {
			freeDailyCount = Integer.parseInt(BANK_FREE_DAILY_COUNT);
		} else {
			freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
		}
		// 比较当前总提现次数与免提现次数
		// 当前提现的总次数
		Map<Integer, Long> remitTypeCountMap = withdrawList.stream()
				.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
		if (!StringUtils.isEmpty(remitTypeCountMap.get(0))) {
			dailyCount = remitTypeCountMap.get(0); // 0是银行卡
		}
		// 如果次数超过免提现次数就要收取手续费
		if (dailyCount >= freeDailyCount) {
			if (!StringUtils.isEmpty(BANK_FEE_RATE)) {
				feeRate = Double.parseDouble(BANK_FEE_RATE);
			}
		}
		if (!StringUtils.isEmpty(BANK_MIN_UNIT_AMOUNT)) {
			minUnitAmount = Double.parseDouble(BANK_MIN_UNIT_AMOUNT);
		}
		if (amount < minUnitAmount) {
			throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
		}
		if (!StringUtils.isEmpty(BANK_MAX_UNIT_AMOUNT)) {
			maxUnitAmount = Double.parseDouble(BANK_MAX_UNIT_AMOUNT);
		}
		if (amount > maxUnitAmount) {
			throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
		}
		BigDecimal amountTmp = new BigDecimal(amount);

		String billno = RandomUtils.fromTime24();

		String remark = "申请提现，提现金额：" + amountTmp.toString();

		try{
			List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
					// 出账
					new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remark, player.getPlayerId(), amountTmp.negate(), amountTmp,
							BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
														  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {}));
			agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		}catch (Exception e){
			throw newException("-1", e.getMessage());
		}
		
		double feeAmount = 0;
		if (feeRate > 0) {
			feeAmount = amount * feeRate / 100;
		}
		BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
		BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);
		BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
		BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
		String bankCardBranch = card.getBankCardBranch();
		String bankCardAddress = card.getBankCardNo();
		String bankCardName = card.getBankCardName();
		String bankCardId = card.getId().toString();
		int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;
		String remarks = null;
		int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
		String checkUser = null;
		int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
		String lockUser = null;
		int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
		int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
		String payUser = null;
		String payBillno = null;
		Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
		String infos = null;
		Integer remitId = 0;
		Integer remitType = PlayerWithdraw.REMIT_TYPE_BANK;
		PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
				balanceAfterTmp, balanceBeforeTmp, bankCardBranch, bankCardAddress, bankCardName, bankCardId,
				bankId.intValue(), billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser,
				orderStatus, orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks,
				pid);
		withdraw.setPids(player.getPids());

		// 开启事务
		proJPAQueryFactoryUtil.txUpdate(() -> {
			boolean result = getPlayerWithdrawDao().save(withdraw);
			if (!result) {
				throw new IllegalArgumentException("插入提现数据失败");
			}
			// 如果用户已经完成任务了，清空彩票消费量限制
			BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
			if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
				boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
				if (!clearLotteryLimit) {
					throw new IllegalArgumentException();
				}
			}
		});
		return true;
	};

	@SuppressWarnings("unused")
	private boolean applyWithdrawUsdt(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId,
			Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		Long playerId = player.getPlayerId();
		Long pid = player.getPid();
		pid = pid != null ? pid : 0L;
		PlayerCardUsdt card = getPlayerCardUsdtDao().getById(cardId);
		if (null == card) {
			throw newException("-1", "该提现地址已取消绑定");
		}
		if (0 != card.getStatus()) {
			throw newException("-1", "该提现地址已禁用");
		}
		if (!playerId.equals(card.getPlayerId())) {
			throw newException("-1", "该提现地址玩家未绑定");
		}
		// 验证锁定时间
		Date cardLockTime = card.getLockedTime();
		if (cardLockTime != null) {
			Moment lockMoment = new Moment().fromDate(cardLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				// ERR:提现银行卡已被锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
			}
		}
		Moment moment = new Moment();
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		double feeRate = 0;
		Long dailyCount = 0L;
		int freeDailyCount = 0;
		double minUnitAmount = 0;
		double maxUnitAmount = 0;
		double usdtRate = sellUsdtRate(agentNo);
		if (usdtRate == 0) {
			throw newException("-1", "提现USDT费率异常，提现失败。");
		}

		String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
		String USDT_FREE_DAILY_COUNT = dataMap.get("USDT_FREE_DAILY_COUNT");
		String USDT_FEE_RATE = dataMap.get("USDT_FEE_RATE");
		String USDT_MIN_UNIT_AMOUNT = dataMap.get("USDT_MIN_UNIT_AMOUNT");
		String USDT_MAX_UNIT_AMOUNT = dataMap.get("USDT_MAX_UNIT_AMOUNT");

		if (!StringUtils.isEmpty(USDT_FREE_DAILY_COUNT)) {
			freeDailyCount = Integer.parseInt(USDT_FREE_DAILY_COUNT);
		} else {
			freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
		}
		// 比较当前总提现次数与免提现次数
		// 当前提现的总次数
		Map<Integer, Long> remitTypeCount = withdrawList.stream()
				.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
		if (!StringUtils.isEmpty(remitTypeCount.get(1))) {
			dailyCount = remitTypeCount.get(1); // 1是USDT提现
		}
		// 如果次数超过免提现次数就要收取手续费
		if (dailyCount >= freeDailyCount) {
			if (!StringUtils.isEmpty(USDT_FEE_RATE)) {
				feeRate = Double.parseDouble(USDT_FEE_RATE);
			}
		}

		if (!StringUtils.isEmpty(USDT_MIN_UNIT_AMOUNT)) {
			minUnitAmount = Double.parseDouble(USDT_MIN_UNIT_AMOUNT);
		}
		if (amount < minUnitAmount) {
			throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
		}
		if (!StringUtils.isEmpty(USDT_MAX_UNIT_AMOUNT)) {
			maxUnitAmount = Double.parseDouble(USDT_MAX_UNIT_AMOUNT);
		}
		if (amount > maxUnitAmount) {
			throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
		}
		BigDecimal amountTmp = BigDecimal.valueOf(amount);

		String billno = RandomUtils.fromTime24();
		double feeAmount = 0;
		if (feeRate > 0) {
			feeAmount = amount * feeRate / 100;
		}
		BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
		BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);

		BigDecimal usdtAmount = actualAmountTmp.divide(new BigDecimal(usdtRate), 2, BigDecimal.ROUND_DOWN);
		
		String remarks = "申请提现，提现金额：" + amountTmp.toString() + "， USDT提现汇率:" + usdtRate + "，USDT提现数量：" + usdtAmount.toString();
		boolean updateAmount = platformAgentPlayerCommonService.updateAvailableBalanceForGameOut(playerId, amountTmp.negate(), amountTmp, AgentPlayerAccountBill.BILL_TYPE_WITHDRAW, billno, player.getPlayerName(), remarks);
		if(!updateAmount) {
			throw newException("-1", "可提现余额不足!");
		}



		try{
			List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
					// 出账
					new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remarks, player.getPlayerId(), amountTmp.negate(), amountTmp,
							BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
														  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
					}));
			agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		}catch (Exception e){
			throw newException("-1", e.getMessage());
		}



		BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
		BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
		String bankCardBranch = "USDT-TRC20";
		String bankCardAddress = !StringUtils.isEmpty(card.getUsdtTrc20Address()) ? card.getUsdtTrc20Address() : card.getUsdtErc20Address();
		String bankCardName = player.getWithdrawName();
		String bankCardId = card.getId().toString();
		Integer bankId = 0;
		int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;

		int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
		String checkUser = null;
		int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
		String lockUser = null;
		int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
		int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
		String payUser = null;
		String payBillno = null;
		Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
		String infos = null;
		Integer remitId = 0;
		Integer remitType = PlayerWithdraw.REMIT_TYPE_USDT;
		PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
				balanceAfterTmp, balanceBeforeTmp, bankCardBranch, bankCardAddress, bankCardName, bankCardId, bankId,
				billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser, orderStatus,
				orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks, pid);
		withdraw.setPids(player.getPids());



		// 开启事务
		proJPAQueryFactoryUtil.txUpdate(() -> {
			boolean result = getPlayerWithdrawDao().save(withdraw);
			if (!result) {
				throw new IllegalArgumentException("插入提现数据失败");
			}
			// 如果用户已经完成任务了，清空彩票消费量限制
			BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
			if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
				boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
				if (!clearLotteryLimit) {
					throw new IllegalArgumentException();
				}
			}
		});
		return true;
	}
	
	private boolean applyWithdrawM(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId, Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		try {
			Long playerId = player.getPlayerId();
			Long pid = player.getPid();
			pid = pid != null ? pid : 0L;
			PlayerCardM card = getPlayerCardMDao().getById(cardId);
			if (null == card) {
				throw newException("-1", "该提现地址已取消绑定");
			}
			if (0 != card.getStatus()) {
				throw newException("-1", "该提现地址已禁用");
			}
			if (!playerId.equals(card.getPlayerId())) {
				throw newException("-1", "该提现地址玩家未绑定");
			}
			// 验证锁定时间
			Date cardLockTime = card.getLockedTime();
			if (cardLockTime != null) {
				Moment lockMoment = new Moment().fromDate(cardLockTime);
				boolean isNotTime = new Moment().le(lockMoment);
				if (isNotTime) {
					// ERR:提现银行卡已被锁定
					String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
					throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
				}
			}
			
			double feeRate = 0;
			Long dailyCount = 0L;
			int freeDailyCount = 0;
			double minUnitAmount = 0;
			double maxUnitAmount = 0;
			Moment moment = new Moment();
			String agentNo = player.getAgentNo();
			String agentName = player.getAgentName();
			String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
			String M_FREE_DAILY_COUNT = dataMap.get("M_FREE_DAILY_COUNT");
			String M_FEE_RATE = dataMap.get("M_FEE_RATE");
			String M_MIN_UNIT_AMOUNT = dataMap.get("M_MIN_UNIT_AMOUNT");
			String M_MAX_UNIT_AMOUNT = dataMap.get("M_MAX_UNIT_AMOUNT");
			if (StringUtils.isEmpty(M_FREE_DAILY_COUNT)) {
				freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
			} else {
				freeDailyCount = Integer.parseInt(M_FREE_DAILY_COUNT);
			}
			
			// 比较当前总提现次数与免提现次数
			// 当前提现的总次数
			Map<Integer, Long> remitTypeCount = withdrawList.stream()
					.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
			if (!StringUtils.isEmpty(remitTypeCount.get(3))) {
				dailyCount = remitTypeCount.get(3); // 3是M提现
			}
			// 如果次数超过免提现次数就要收取手续费
			if (dailyCount >= freeDailyCount) {
				if (!StringUtils.isEmpty(M_FEE_RATE)) {
					feeRate = Double.parseDouble(M_FEE_RATE);
				}
			}
			if (!StringUtils.isEmpty(M_MIN_UNIT_AMOUNT)) {
				minUnitAmount = Double.parseDouble(M_MIN_UNIT_AMOUNT);
			}
			if (amount < minUnitAmount) {
				throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
			}
			if (!StringUtils.isEmpty(M_MAX_UNIT_AMOUNT)) {
				maxUnitAmount = Double.parseDouble(M_MAX_UNIT_AMOUNT);
			}
			if (amount > maxUnitAmount) {
				throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
			}
			double feeAmount = 0;
			if (feeRate > 0) {
				feeAmount = amount * feeRate / 100;
			}
			String billno = RandomUtils.fromTime24();
			BigDecimal amountTmp = BigDecimal.valueOf(amount);
			BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
			BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);

			String remarks = "申请提现，提现金额：" + amountTmp.toString();
			try{
				List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
						// 出账
						new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remarks, player.getPlayerId(), amountTmp.negate(), amountTmp,
								BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
															  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
						}));
				agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
			}catch (Exception e){
				throw newException("-1", e.getMessage());
			}
			
			BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
			BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
			String bankCardBranch = "M支付";
			String bankCardAddress = card.getMAddress();
			String bankCardName = player.getWithdrawName();
			String bankCardId = card.getId().toString();
			Integer bankId = 0;
			int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;
			int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
			String checkUser = null;
			int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
			String lockUser = null;
			int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
			int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
			String payUser = null;
			String payBillno = null;
			Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
			String infos = null;
			Integer remitId = 0;
			Integer remitType = PlayerWithdraw.REMIT_TYPE_M;
			PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
					balanceAfterTmp, balanceBeforeTmp, bankCardBranch, bankCardAddress, bankCardName, bankCardId, bankId,
					billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser, orderStatus,
					orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks, pid);
			withdraw.setPids(player.getPids());
			// 开启事务
			proJPAQueryFactoryUtil.txUpdate(() -> {
				boolean result = getPlayerWithdrawDao().save(withdraw);
				if (!result) {
					throw new IllegalArgumentException("插入提现数据失败");
				}
				// 如果用户已经完成任务了，清空彩票消费量限制
				BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
				if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
					boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
					if (!clearLotteryLimit) {
						throw new IllegalArgumentException();
					}
				}
			});
			return true;
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return false;
	}

	private boolean applyWithdrawHh5(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId, Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		try {
			Long playerId = player.getPlayerId();
			Long pid = player.getPid();
			pid = pid != null ? pid : 0L;
			PlayerCardHh5 card = getPlayerCardHh5Dao().getById(cardId);
			if (null == card) {
				throw newException("-1", "该提现地址已取消绑定");
			}
			if (0 != card.getStatus()) {
				throw newException("-1", "该提现地址已禁用");
			}
			if (!playerId.equals(card.getPlayerId())) {
				throw newException("-1", "该提现地址玩家未绑定");
			}
			// 验证锁定时间
			Date cardLockTime = card.getLockedTime();
			if (cardLockTime != null) {
				Moment lockMoment = new Moment().fromDate(cardLockTime);
				boolean isNotTime = new Moment().le(lockMoment);
				if (isNotTime) {
					// ERR:提现银行卡已被锁定
					String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
					throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
				}
			}
			
			double feeRate = 0;
			Long dailyCount = 0L;
			int freeDailyCount = 0;
			double minUnitAmount = 0;
			double maxUnitAmount = 0;
			Moment moment = new Moment();
			String agentNo = player.getAgentNo();
			String agentName = player.getAgentName();
			String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
			String HH5_FREE_DAILY_COUNT = dataMap.get("HH5_FREE_DAILY_COUNT");
			String HH5_FEE_RATE = dataMap.get("HH5_FEE_RATE");
			String HH5_MIN_UNIT_AMOUNT = dataMap.get("HH5_MIN_UNIT_AMOUNT");
			String HH5_MAX_UNIT_AMOUNT = dataMap.get("HH5_MAX_UNIT_AMOUNT");
			if (StringUtils.isEmpty(HH5_FREE_DAILY_COUNT)) {
				freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
			} else {
				freeDailyCount = Integer.parseInt(HH5_FREE_DAILY_COUNT);
			}
			
			// 比较当前总提现次数与免提现次数
			// 当前提现的总次数
			Map<Integer, Long> remitTypeCount = withdrawList.stream()
					.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
			if (!StringUtils.isEmpty(remitTypeCount.get(4))) {
				dailyCount = remitTypeCount.get(4); // 3是HH5提现
			}
			// 如果次数超过免提现次数就要收取手续费
			if (dailyCount >= freeDailyCount) {
				if (!StringUtils.isEmpty(HH5_FEE_RATE)) {
					feeRate = Double.parseDouble(HH5_FEE_RATE);
				}
			}
			if (!StringUtils.isEmpty(HH5_MIN_UNIT_AMOUNT)) {
				minUnitAmount = Double.parseDouble(HH5_MIN_UNIT_AMOUNT);
			}
			if (amount < minUnitAmount) {
				throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
			}
			if (!StringUtils.isEmpty(HH5_MAX_UNIT_AMOUNT)) {
				maxUnitAmount = Double.parseDouble(HH5_MAX_UNIT_AMOUNT);
			}
			if (amount > maxUnitAmount) {
				throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
			}
			double feeAmount = 0;
			if (feeRate > 0) {
				feeAmount = amount * feeRate / 100;
			}
			String billno = RandomUtils.fromTime24();
			BigDecimal amountTmp = BigDecimal.valueOf(amount);
			BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
			BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);

			String remarks = "申请提现，提现金额：" + amountTmp.toString();
			try{
				List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
						// 出账
						new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remarks, player.getPlayerId(), amountTmp.negate(), amountTmp,
								BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
															  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
						}));
				agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
			}catch (Exception e){
				throw newException("-1", e.getMessage());
			}
			
			BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
			BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
			String bankCardBranch = "HH5支付";
			String bankCardAddress = card.getHh5Address();
			String bankCardName = player.getWithdrawName();
			String bankCardId = card.getId().toString();
			Integer bankId = 0;
			int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;
			int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
			String checkUser = null;
			int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
			String lockUser = null;
			int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
			int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
			String payUser = null;
			String payBillno = null;
			Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
			String infos = null;
			Integer remitId = 0;
			Integer remitType = PlayerWithdraw.REMIT_TYPE_HH5;
			PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
					balanceAfterTmp, balanceBeforeTmp, bankCardBranch, bankCardAddress, bankCardName, bankCardId, bankId,
					billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser, orderStatus,
					orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks, pid);
			withdraw.setPids(player.getPids());
			// 开启事务
			proJPAQueryFactoryUtil.txUpdate(() -> {
				boolean result = getPlayerWithdrawDao().save(withdraw);
				if (!result) {
					throw new IllegalArgumentException("插入提现数据失败");
				}
				// 如果用户已经完成任务了，清空彩票消费量限制
				BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
				if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
					boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
					if (!clearLotteryLimit) {
						throw new IllegalArgumentException();
					}
				}
			});
			return true;
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return false;
	}
	
	private boolean applyWithdrawOkg(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId, Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		try {
			Long playerId = player.getPlayerId();
			Long pid = player.getPid();
			pid = pid != null ? pid : 0L;
			PlayerCardOkg card = getPlayerCardOkgDao().getById(cardId);
			if (null == card) {
				throw newException("-1", "该提现地址已取消绑定");
			}
			if (0 != card.getStatus()) {
				throw newException("-1", "该提现地址已禁用");
			}
			if (!playerId.equals(card.getPlayerId())) {
				throw newException("-1", "该提现地址玩家未绑定");
			}
			// 验证锁定时间
			Date cardLockTime = card.getLockedTime();
			if (cardLockTime != null) {
				Moment lockMoment = new Moment().fromDate(cardLockTime);
				boolean isNotTime = new Moment().le(lockMoment);
				if (isNotTime) {
					// ERR:提现银行卡已被锁定
					String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
					throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
				}
			}
			
			double feeRate = 0;
			Long dailyCount = 0L;
			int freeDailyCount = 0;
			double minUnitAmount = 0;
			double maxUnitAmount = 0;
			Moment moment = new Moment();
			String agentNo = player.getAgentNo();
			String agentName = player.getAgentName();
			String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
			String OKG_FREE_DAILY_COUNT = dataMap.get("OKG_FREE_DAILY_COUNT");
			String OKG_FEE_RATE = dataMap.get("OKG_FEE_RATE");
			String OKG_MIN_UNIT_AMOUNT = dataMap.get("OKG_MIN_UNIT_AMOUNT");
			String OKG_MAX_UNIT_AMOUNT = dataMap.get("OKG_MAX_UNIT_AMOUNT");
			if (StringUtils.isEmpty(OKG_FREE_DAILY_COUNT)) {
				freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
			} else {
				freeDailyCount = Integer.parseInt(OKG_FREE_DAILY_COUNT);
			}
			
			// 比较当前总提现次数与免提现次数
			// 当前提现的总次数
			Map<Integer, Long> remitTypeCount = withdrawList.stream()
					.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
			if (!StringUtils.isEmpty(remitTypeCount.get(5))) {
				dailyCount = remitTypeCount.get(5); // 5是OKG提现
			}
			// 如果次数超过免提现次数就要收取手续费
			if (dailyCount >= freeDailyCount) {
				if (!StringUtils.isEmpty(OKG_FEE_RATE)) {
					feeRate = Double.parseDouble(OKG_FEE_RATE);
				}
			}
			if (!StringUtils.isEmpty(OKG_MIN_UNIT_AMOUNT)) {
				minUnitAmount = Double.parseDouble(OKG_MIN_UNIT_AMOUNT);
			}
			if (amount < minUnitAmount) {
				throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
			}
			if (!StringUtils.isEmpty(OKG_MAX_UNIT_AMOUNT)) {
				maxUnitAmount = Double.parseDouble(OKG_MAX_UNIT_AMOUNT);
			}
			if (amount > maxUnitAmount) {
				throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
			}
			double feeAmount = 0;
			if (feeRate > 0) {
				feeAmount = amount * feeRate / 100;
			}
			String billno = RandomUtils.fromTime24();
			BigDecimal amountTmp = BigDecimal.valueOf(amount);
			BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
			BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);

			String remarks = "申请提现，提现金额：" + amountTmp.toString();
			try{
				List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
						// 出账
						new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remarks, player.getPlayerId(), amountTmp.negate(), amountTmp,
								BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
															  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
						}));
				agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
			}catch (Exception e){
				throw newException("-1", e.getMessage());
			}
			
			BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
			BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
			String bankCardBranch = "OKG支付";
			String bankCardAddress = card.getOkgAddress();
			String bankCardName = player.getWithdrawName();
			String bankCardId = card.getId().toString();
			Integer bankId = 0;
			int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;
			int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
			String checkUser = null;
			int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
			String lockUser = null;
			int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
			int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
			String payUser = null;
			String payBillno = null;
			Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
			String infos = null;
			Integer remitId = 0;
			Integer remitType = PlayerWithdraw.REMIT_TYPE_OKG;
			PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
					balanceAfterTmp, balanceBeforeTmp, bankCardBranch, bankCardAddress, bankCardName, bankCardId, bankId,
					billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser, orderStatus,
					orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks, pid);
			withdraw.setPids(player.getPids());
			// 开启事务
			proJPAQueryFactoryUtil.txUpdate(() -> {
				boolean result = getPlayerWithdrawDao().save(withdraw);
				if (!result) {
					throw new IllegalArgumentException("插入提现数据失败");
				}
				// 如果用户已经完成任务了，清空彩票消费量限制
				BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
				if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
					boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
					if (!clearLotteryLimit) {
						throw new IllegalArgumentException();
					}
				}
			});
			return true;
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return false;
	}
	private boolean applyWithdrawThirdUsdt(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId,
			Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		try {
			Long playerId = player.getPlayerId();
			Long pid = player.getPid();
			pid = pid != null ? pid : 0L;
			PlayerCardUsdt card = getPlayerCardUsdtDao().getById(cardId);
			if (null == card) {
				throw newException("-1", "该提现地址已取消绑定");
			}
			if (0 != card.getStatus()) {
				throw newException("-1", "该提现地址已禁用");
			}
			if (!playerId.equals(card.getPlayerId())) {
				throw newException("-1", "该提现地址玩家未绑定");
			}
			// 验证锁定时间
			Date cardLockTime = card.getLockedTime();
			if (cardLockTime != null) {
				Moment lockMoment = new Moment().fromDate(cardLockTime);
				boolean isNotTime = new Moment().le(lockMoment);
				if (isNotTime) {
					// ERR:提现银行卡已被锁定
					String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
					throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
				}
			}

			double feeRate = 0;
			Long dailyCount = 0L;
			int freeDailyCount = 0;
			double minUnitAmount = 0;
			double maxUnitAmount = 0;
			Moment moment = new Moment();
			String agentNo = player.getAgentNo();
			String agentName = player.getAgentName();
			String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
			String USDT_FREE_DAILY_COUNT = dataMap.get("USDT_FREE_DAILY_COUNT");
			String USDT_FEE_RATE = dataMap.get("USDT_FEE_RATE");
			String USDT_MIN_UNIT_AMOUNT = dataMap.get("USDT_MIN_UNIT_AMOUNT");
			String USDT_MAX_UNIT_AMOUNT = dataMap.get("USDT_MAX_UNIT_AMOUNT");
			
			// 获取USDT提现汇率,截取最多保留五位小数
			BigDecimal usdtWithdrawRate = usdtRateUtil.getAgentRate(agentNo).setScale(5,
					RoundingMode.DOWN);

			if (!StringUtils.isEmpty(USDT_FREE_DAILY_COUNT)) {
				freeDailyCount = Integer.parseInt(USDT_FREE_DAILY_COUNT);
			} else {
				freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
			}

			// 比较当前总提现次数与免提现次数
			// 当前提现的总次数
			Map<Integer, Long> remitTypeCount = withdrawList.stream()
					.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
			if (!StringUtils.isEmpty(remitTypeCount.get(1))) {
				dailyCount = remitTypeCount.get(1); // 1是USDT提现
			}
			// 如果次数超过免提现次数就要收取手续费
			if (dailyCount >= freeDailyCount) {
				if (!StringUtils.isEmpty(USDT_FEE_RATE)) {
					feeRate = Double.parseDouble(USDT_FEE_RATE);
				}
			}

			if (!StringUtils.isEmpty(USDT_MIN_UNIT_AMOUNT)) {
				minUnitAmount = Double.parseDouble(USDT_MIN_UNIT_AMOUNT);
			}
			if (amount < minUnitAmount) {
				throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
			}
			if (!StringUtils.isEmpty(USDT_MAX_UNIT_AMOUNT)) {
				maxUnitAmount = Double.parseDouble(USDT_MAX_UNIT_AMOUNT);
			}
			if (amount > maxUnitAmount) {
				throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
			}

			double feeAmount = 0;
			if (feeRate > 0) {
				feeAmount = amount * feeRate / 100;
			}

			String billno = RandomUtils.fromTime24();
			BigDecimal amountTmp = BigDecimal.valueOf(amount);
			BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
			BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);

			BigDecimal usdtAmount = amountTmp.divide(usdtWithdrawRate, 5, BigDecimal.ROUND_DOWN);

			String remarks = String.format("usdt提现汇率:%s,usdt提现个数:%s,提现到账金额:%s",
					usdtWithdrawRate,
					usdtAmount,
					amountTmp
					);
			try{
				List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
						// 出账
						new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remarks, player.getPlayerId(), amountTmp.negate(), amountTmp,
								BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
															  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
						}));
				agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
			}catch (Exception e){
				throw newException("-1", e.getMessage());
			}
			
			BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
			BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
			String bankCardBranch = "USDT-TRC20";
			String bankCardAddress = !StringUtils.isEmpty(card.getUsdtTrc20Address()) ? card.getUsdtTrc20Address() : card.getUsdtErc20Address();
			String bankCardName = player.getWithdrawName();
			String bankCardId = card.getId().toString();
			Integer bankId = 0;
			int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;
			int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
			String checkUser = null;
			int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
			String lockUser = null;
			int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
			int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
			String payUser = null;
			String payBillno = null;
			Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
			String infos = null;
			Integer remitId = 0;
			Integer remitType = PlayerWithdraw.REMIT_TYPE_USDT;
			PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
					balanceAfterTmp, balanceBeforeTmp, bankCardBranch, bankCardAddress, bankCardName, bankCardId, bankId,
					billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser, orderStatus,
					orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks, pid);
			withdraw.setPids(player.getPids());
			// 开启事务
			proJPAQueryFactoryUtil.txUpdate(() -> {
				boolean result = getPlayerWithdrawDao().save(withdraw);
				if (!result) {
					throw new IllegalArgumentException("插入提现数据失败");
				}
				// 如果用户已经完成任务了，清空彩票消费量限制
				BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
				if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
					boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
					if (!clearLotteryLimit) {
						throw new IllegalArgumentException();
					}
				}
			});
			return true;
		} catch (Exception e) {
			String error = ErrorUtils.getStackTraceInfo(e);
			log.info("error:" + error);
		}
		return false;
	}

	private boolean applyWithdrawAlipay(HttpServletRequest request, AgentPlayerInfo player, double amount, Long cardId,
									  Map<String, String> dataMap, List<PlayerWithdraw> withdrawList) {
		Long playerId = player.getPlayerId();
		Long pid = player.getPid();
		pid = pid == null ? 0L : pid;
		PlayerCardAlipay card = getPlayerCardAlipayDao().getById(cardId);
		if (null == card) {
			throw newException("-1", "该提现地址已取消绑定");
		}
		if (0 != card.getStatus()) {
			throw newException("-1", "该提现地址已禁用");
		}
		if (!playerId.equals(card.getPlayerId())) {
			throw newException("-1", "该提现地址玩家未绑定");
		}
		// 验证锁定时间
		Date cardLockTime = card.getLockedTime();
		if (cardLockTime != null) {
			Moment lockMoment = new Moment().fromDate(cardLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				// ERR:提现银行卡已被锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				throw newException("-1", "该提现地址已锁定，解锁时间为：" + time);
			}
		}

		double feeRate = 0;
		Long dailyCount = 0L;
		int freeDailyCount = 0;
		double minUnitAmount = 0;
		double maxUnitAmount = 0;
		Moment moment = new Moment();
		String agentNo = player.getAgentNo();
		String agentName = player.getAgentName();
		// 判断是否还有免体现次数，如果没有才收取提现手续费
		String ALIPAY_FEE_RATE = dataMap.get("ALIPAY_FEE_RATE");
		String FREE_DAILY_COUNT = dataMap.get("FREE_DAILY_COUNT");
		String ALIPAY_MIN_UNIT_AMOUNT = dataMap.get("ALIPAY_MIN_UNIT_AMOUNT");
		String ALIPAY_MAX_UNIT_AMOUNT = dataMap.get("ALIPAY_MAX_UNIT_AMOUNT");
		String ALIPAY_FREE_DAILY_COUNT = dataMap.get("ALIPAY_FREE_DAILY_COUNT");
		if (!StringUtils.isEmpty(ALIPAY_FREE_DAILY_COUNT)) {
			freeDailyCount = Integer.parseInt(ALIPAY_FREE_DAILY_COUNT);
		} else {
			freeDailyCount = Integer.parseInt(FREE_DAILY_COUNT);
		}
		// 比较当前总提现次数与免提现次数
		// 当前提现的总次数
		Map<Integer, Long> remitTypeCountMap = withdrawList.stream()
				.collect(Collectors.groupingBy(PlayerWithdraw::getRemitType, Collectors.counting()));
		if (!StringUtils.isEmpty(remitTypeCountMap.get(0))) {
			dailyCount = remitTypeCountMap.get(0); // 0是银行卡
		}
		// 如果次数超过免提现次数就要收取手续费
		if (dailyCount >= freeDailyCount) {
			if (!StringUtils.isEmpty(ALIPAY_FEE_RATE)) {
				feeRate = Double.parseDouble(ALIPAY_FEE_RATE);
			}
		}
		if (!StringUtils.isEmpty(ALIPAY_MIN_UNIT_AMOUNT)) {
			minUnitAmount = Double.parseDouble(ALIPAY_MIN_UNIT_AMOUNT);
		}
		if (amount < minUnitAmount) {
			throw newException("-1", "提现金额不能低于" + minUnitAmount + "元");
		}
		if (!StringUtils.isEmpty(ALIPAY_MAX_UNIT_AMOUNT)) {
			maxUnitAmount = Double.parseDouble(ALIPAY_MAX_UNIT_AMOUNT);
		}
		if (amount > maxUnitAmount) {
			throw newException("-1", "提现金额不能高于" + maxUnitAmount + "元");
		}
		BigDecimal amountTmp = new BigDecimal(amount);

		String billno = RandomUtils.fromTime24();

		String remark = "申请提现，提现金额：" + amountTmp.toString();
		try{
			List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
					// 出账
					new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_WITHDRAW,remark, player.getPlayerId(), amountTmp.negate(), amountTmp,
							BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
														  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
					}));
			agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		}catch (Exception e){
			throw newException("-1", e.getMessage());
		}
		
		double feeAmount = 0;
		if (feeRate > 0) {
			feeAmount = amount * feeRate / 100;
		}
		Integer bankId = 0;
		BigDecimal feeAmountTmp = BigDecimal.valueOf(feeAmount);
		BigDecimal actualAmountTmp = amountTmp.subtract(feeAmountTmp);
		BigDecimal balanceBeforeTmp = player.getPlayerAvailableBalance();
		BigDecimal balanceAfterTmp = balanceBeforeTmp.subtract(amountTmp);
		String alipayCardBranch = "支付宝";
		String alipayAccount = card.getAlipayAccount();
		String alipayName = card.getAlipayName();
		String alipayCardId = card.getId().toString();
		int orderStatus = PlayerWithdraw.ORDER_STATUS_WAITING;
		String remarks = null;
		int checkStatus = PlayerWithdraw.CHECK_STATUS_WAITING;
		String checkUser = null;
		int lockStatus = PlayerWithdraw.LOCK_STATUS_FALSE;
		String lockUser = null;
		int payStatus = PlayerWithdraw.PAY_STATUS_WAITING;
		int payMethod = PlayerWithdraw.PAY_METHOD_UNKNOW;
		String payUser = null;
		String payBillno = null;
		Date orderTime = moment.toDate(), checkTime = moment.toDate(), payTime = moment.toDate();
		String infos = null;
		Integer remitId = 0;
		Integer remitType = PlayerWithdraw.REMIT_TYPE_ALIPAY;
		PlayerWithdraw withdraw = new PlayerWithdraw(agentNo, agentName, playerId, amountTmp, actualAmountTmp,
				balanceAfterTmp, balanceBeforeTmp, alipayCardBranch, alipayAccount, alipayName, alipayCardId,
				bankId.intValue(), billno, checkStatus, checkTime, checkUser, feeAmountTmp, infos, lockStatus, lockUser,
				orderStatus, orderTime, payBillno, payMethod, payStatus, payTime, payUser, remitId, remitType, remarks,
				pid);
		withdraw.setPids(player.getPids());
		// 开启事务
		proJPAQueryFactoryUtil.txUpdate(() -> {
			boolean result = getPlayerWithdrawDao().save(withdraw);
			if (!result) {
				throw new IllegalArgumentException("插入提现数据失败");
			}
			// 如果用户已经完成任务了，清空彩票消费量限制
			BigDecimal lotteryLimitAmount = getServiceUtils().getWithdrawLimitAmount(player);
			if (lotteryLimitAmount.compareTo(BigDecimal.ZERO) == 0) {
				boolean clearLotteryLimit = getPlayerWithdrawLimitDao().clearAllLimit(playerId);
				if (!clearLotteryLimit) {
					throw new IllegalArgumentException();
				}
			}
		});
		return true;
	};
	
	

	@Override
	public boolean deleteCardUsdtById(Long cardId) {
		return getPlayerCardUsdtDao().deleteById(cardId);
	}

	@Override
	public boolean deleteCardBankById(Long cardId) {
		return getPlayerCardBankDao().deleteById(cardId);
	}

	@Override
	public boolean canceledRecharge(String billno) {
		PlayerRecharge entity = getPlayerRechargeDao().getByBillno(billno);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (PlayerRecharge.ORDER_STATUS_CANCELED == entity.getOrderStatus()) {
			return true;
		}
		if (PlayerRecharge.ORDER_STATUS_WAITING != entity.getOrderStatus()) {
			throw newException("-1", "该订单状态禁止撤单操作");
		}
		return getPlayerRechargeDao().updateOrderStatus(entity.getId(), PlayerRecharge.ORDER_STATUS_CANCELED);
	}

	@Override
	public boolean awaitConfirmedRecharge(String billno) {
		PlayerRecharge entity = getPlayerRechargeDao().getByBillno(billno);
		if (entity == null) {
			throw newException("-1", "该订单不存在");
		}
		if (PlayerRecharge.ORDER_STATUS_COMPLETED == entity.getOrderStatus()) {
			throw newException("-1", "该订单已审核通过到账");
		}
		if (PlayerRecharge.ORDER_STATUS_WAITING != entity.getOrderStatus()) {
			throw newException("-1", "该订单状态操作");
		}
		return getPlayerRechargeDao().updateOrderStatus(entity.getId(), PlayerRecharge.ORDER_STATUS_REVIEW);
	}

}
