package ph.yckj.frontend.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import lottery.utils.open.OpenTime;
import lottery.utils.prize.LotteryType;
import myutil.LzmaUtils;
import myutil.Moment;
import myutil.RandomUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ph.yckj.common.util.ServiceException;
import ph.yckj.frontend.dto.*;
import ph.yckj.frontend.service.AgentPlayerAccountBillService;
import ph.yckj.frontend.service.GameLotteryService;
import ph.yckj.frontend.util.AbstractService;
import ph.yckj.frontend.util.LevelCalculator;
import ph.yckj.frontend.vo.GameLotteryProfitRankingVo;
import ph.yckj.frontend.web.ctrl.game.GameLotteryOrderBean;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.enums.LotteryLiveStatus;
import sy.hoodle.base.common.enums.LotteryMaintainStatus;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.base.common.service.balance.vo.BalanceChangeDirection;
import sy.hoodle.base.common.service.balance.vo.PlayerBalanceUpdateParams;
import sy.hoodle.base.common.util.ProJPAQueryFactoryUtil;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class GameLotteryServiceImpl extends AbstractService implements GameLotteryService {

	@Autowired
	private AgentPlayerAccountBillService billService;

	@Autowired
	private ThreadPoolTaskExecutor orderToPushTaskExecutor;

	@Autowired
	private ThreadPoolTaskExecutor taskExecutor;

	@Autowired
	private ProJPAQueryFactoryUtil proJPAQueryFactoryUtil;

	@Autowired
	private AgentPlayerInfoLockService agentPlayerInfoLockService;


	@Override
	public String addOrder(HttpServletRequest request, AgentPlayerInfo player, List<GameLotteryOrderBean> beanList) {
		String agentNo = player.getAgentNo();

		// 单注投注金额
		String sysUnitMoneyConfig = getRedisService().getPlatformConfigValue(player.getAgentNo(), "GAME_LOTTERY",
				"SYS_UNIT_MONEY");
		if(StringUtils.isEmpty(sysUnitMoneyConfig)) {
			throw newException("115-26");
		}
		int sysUnitMoney;
		try {
			sysUnitMoney = Integer.parseInt(sysUnitMoneyConfig);
		}catch(NumberFormatException e) {
			throw newException("115-27");
		}
		// 标准盘玩法总开关
		String methodTypeStandardConfig = getRedisService().getPlatformConfigValue(player.getAgentNo(), "GAME_LOTTERY",
				"METHOD_TYPE_STANDARD");
		boolean methodTypeStandard = "true".equals(methodTypeStandardConfig);

		// 卡单
		boolean isLock = false, isLockConfig = false;
		AtomicInteger lastLockId = new AtomicInteger(0);
		// 卡单总开关
		String lockAccount = getRedisService().getPlatformConfigValue(agentNo, "GAME_LOTTERY", "LOCK_ACCOUNT");
		if("true".equals(lockAccount)) {
			List<AgentPlayerGameOrder> lockOrderList = getAgentPlayerGameOrderDao().getLockOrderList(
					player.getPlayerId());
			if(lockOrderList != null && !lockOrderList.isEmpty()) {
				isLock = true;
				lastLockId.addAndGet(lockOrderList.get(0).getLockId());
			}
		}

		// 总投注金额
		BigDecimal totalBetAmount = BigDecimal.ZERO;
		// 按照彩种、彩种名称和期号分类投注金额，key=lottery,gameName,gameRoomName,issue，value=该彩种该期总投注金额
		Map<String, BigDecimal> betAmountMap = new HashMap<>();
		// 按照彩种分类订单
		Map<String, List<GameLotteryOrderBean>> beanMap = new HashMap<>();
		for(GameLotteryOrderBean tmpBean : beanList) {
			String lottery = tmpBean.getLottery();
			if(!beanMap.containsKey(lottery)) {
				beanMap.put(lottery, new ArrayList<>());
			}
			beanMap.get(lottery).add(tmpBean);
		}
		List<AgentPlayerGameOrder> orderList = new ArrayList<>(beanList.size());

		// 循环校验订单
		for(String lottery : beanMap.keySet()) {
			// 验证彩票游戏
			GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(lottery);
			if(gameLotteryInfo == null) {
				throw newException("116-08");
			}
			if(GameLotteryInfo.GAME_STATUS_NORMAL != gameLotteryInfo.getGameStatus()) {
				String str = gameLotteryInfo.getGameName() + (StringUtils.isEmpty(gameLotteryInfo.getGameRoomName())
						? "" : ("-" + gameLotteryInfo.getGameRoomName()));
				throw newException("1", str + " 已禁用");
			}
			if(GameLotteryInfo.GAME_CLASS_DZ.equals(gameLotteryInfo.getGameClass())) {
				// 弹珠彩种才判断直播状态和维护状态
				if(LotteryMaintainStatus.MAINTAIN.getCode() == gameLotteryInfo.getMaintainStatus()) {
					String str = gameLotteryInfo.getGameName() + "-" + gameLotteryInfo.getGameRoomName();
					throw newException("1", str + "维护中...");
				}
				if(LotteryMaintainStatus.CONSTRUCT.getCode() == gameLotteryInfo.getMaintainStatus()) {
					String str = gameLotteryInfo.getGameName() + "-" + gameLotteryInfo.getGameRoomName();
					throw newException("1", str + "建设中...");
				}
				if(LotteryMaintainStatus.NORMAL.getCode() == gameLotteryInfo.getMaintainStatus() &&
						LotteryLiveStatus.END_LIVE.getCode() == gameLotteryInfo.getLiveStatus()) {
					String str = gameLotteryInfo.getGameName() + "-" + gameLotteryInfo.getGameRoomName();
					throw newException("1", str + "已下播...");
				}
			}

			AgentGameLotteryInfo agentGameLotteryInfo = getAgentGameLotteryInfoDao().getByAgentNoAndLottery(agentNo,
					lottery);
			if(null == agentGameLotteryInfo) {
				throw newException("-1", "该游戏直播间不存在！");
			}
			if(AgentGameLotteryInfo.BAN_STATUS_ENABLED != agentGameLotteryInfo.getBanStatus()) {
				throw newException("-1", "该游戏直播间已被禁用！");
			}
			// 验证彩票类型
			GameLotteryType gameLotteryType = getRedisService().getGameLotteryType(gameLotteryInfo.getGameTypeCode());
			if(gameLotteryType == null) {
				throw newException("116-09");
			}
			if(GameLotteryType.GAME_TYPE_STATUS_FORBIDDEN == gameLotteryType.getGameTypeStatus()) {
				String str = gameLotteryType.getGameTypeName();
				throw newException("-1", str + "维护中...");
			}

			// 某彩种的全部订单
			List<GameLotteryOrderBean> orderBeanList = beanMap.get(lottery);

			// 判断大富翁
			List<GameLotteryOrderBean> dfwList = orderBeanList.stream().filter(order -> "dfw".equals(order.getMethod()))
					.collect(Collectors.toList());
			if(!dfwList.isEmpty()) {
				if(dfwList.size() > 1) {
					throw newException("-1", "每一期大富翁游戏仅能参与一次");
				}else {
					Long playerId = player.getPlayerId();
					// 根据当前时间获取投注期号
					OpenTime ot = getServiceUtils().getOpenTimeByTime(gameLotteryInfo, new Moment().toSimpleTime());
					if(ot == null) {
						throw newException("116-10");
					}
					// 以获取到的期号为准
					String issue = ot.getIssue();
					List<AgentPlayerGameOrder> orders = getAgentPlayerGameOrderDao().getPlayerGameOrder(playerId,
							lottery, issue, "dfw");
					if(!CollectionUtils.isEmpty(orders)) {
						throw newException("-1", "每一期大富翁游戏仅能参与一次");
					}
				}
			}

			BigDecimal dfwDzKl8DefaultBetAmount = BigDecimal.ZERO;
			BigDecimal dfwDzPc28DefaultBetAmount = BigDecimal.ZERO;
			Set<String> methodSet = orderBeanList.stream().map(GameLotteryOrderBean::getMethod).collect(Collectors.toSet());
			if(methodSet.contains("dfw")) {
				GameMonopolyConfigInfo monopoly = getGameMonopolyConfigInfoDao().getByLottery(agentNo, lottery);
				if(null == monopoly) {
					throw newException("-1", "大富翁暂未启用！");
				}
				BigDecimal defautDfwBetAmount = monopoly.getDefaultBetAmount().setScale(2, RoundingMode.DOWN);
				if("dzkl8".equals(lottery)) {
					dfwDzKl8DefaultBetAmount = defautDfwBetAmount;
				}else if("dzpc28".equals(lottery)) {
					dfwDzPc28DefaultBetAmount = defautDfwBetAmount;
				}
			}

			// 判断限红，仅双面盘
			// 彩种双面盘总投注金额
			BigDecimal lotteryShuangMianTotalBetAmount = BigDecimal.ZERO;
			Map<String, BigDecimal> methodShuangMianTotalBetAmountMap = new HashMap<>();

			for(GameLotteryOrderBean tmpBean : orderBeanList) {
				// 玩法
				String method = tmpBean.getMethod();
				// 玩法类型
				Integer methodType = tmpBean.getMethodType();
				if(methodType == null) {
					// 兼容旧版本，玩法类型未传值时，默认为双面盘
					methodType = AgentPlayerGameOrder.METHOD_TYPE_SHUANGMIAN;
				}
				// 投注内容
				String content = tmpBean.getContent();
				// 投注金额
				BigDecimal betAmount;

				// 验证玩法
				GameLotteryMethod gameLotteryMethod = getRedisService().getGameLotteryMethod(
						agentGameLotteryInfo.getGameTypeCode(), method);
				if(null == gameLotteryMethod) {
					throw newException("-1", method + "玩法不存在");
				}
				if(!methodTypeStandard && methodType == AgentPlayerGameOrder.METHOD_TYPE_STANDAR) {
					throw newException("-1", "标准盘玩法已关闭");
				}
				GameLotteryMethodCustomize gameLotteryMethodCustomize = getGameLotteryMethodCustomizeDao().getByInfo(
						agentNo, lottery, method);
				if(null != gameLotteryMethodCustomize) {
					BeanUtils.copyProperties(gameLotteryMethodCustomize, gameLotteryMethod);
				}
				if(GameLotteryMethod.GAME_METHOD_STATUS_FORBIDDEN == gameLotteryMethod.getMethodStatus()) {
					String str = gameLotteryMethod.getMethodName();
					throw newException("-1", str + "玩法维护中...");
				}

				// 期号
				String issue = tmpBean.getIssue();
				OpenTime ot;
				if(StringUtils.isEmpty(issue)) {
					// 根据当前时间获取投注期号
					String time = new Moment().toSimpleTime();
					ot = getServiceUtils().getOpenTimeByTime(gameLotteryInfo, time);
				}else {
					// 获取投注期号所对应的开奖结束时间
					ot = getServiceUtils().getOpenTimeByIssue(gameLotteryInfo, issue);
				}
				if(ot == null) {
					throw newException("116-11");
				}
				// 以获取到的期号为准
				issue = ot.getIssue();
				// 验证投注期号是否已经过期
				getGameLotteryUtils().testOpenTime(gameLotteryInfo, issue, ot);
				// 验证有无开奖号码
				GameLotteryOpenCode lotteryOpenCode = getGameLotteryOpenCodeDao().getByLotteryAndIssue(
						gameLotteryInfo.getLottery(), issue);
				if(lotteryOpenCode != null) {
					// 有开奖号码时不允许投注
					Map<String, Object> params = new HashMap<>();
					params.put("name", gameLotteryInfo.getGameName());
					params.put("n", issue);
					throw newException("116-04", params);
				}
				Timestamp stopTime = new Moment().fromTime(ot.getStopTime()).toTimestamp();

				if(LotteryType.lhc.equals(LotteryType.valueOf(gameLotteryInfo.getGameTypeCode()))) {
					// 六合彩限制最大投注注数
					List<String> lhcMethodList = Arrays.asList("bzzm1dm", "bzzm2dm", "bzzm3dm", "bzzm4dm", "bzzm5dm",
							"bzzm6dm", "bztmdm", "smzm1sx", "smzm2sx", "smzm3sx", "smzm4sx", "smzm5sx", "smzm6sx",
							"smtmsx");
					// 判断六合彩单码
					List<GameLotteryOrderBean> lhcNeedCheckMethodOrder = beanList.stream().filter(
							order -> lhcMethodList.contains(order.getMethod())).collect(Collectors.toList());
					String finalIssue = issue;
					lhcNeedCheckMethodOrder.stream().collect(Collectors.groupingBy(GameLotteryOrderBean::getMethod,
							Collectors.mapping(GameLotteryOrderBean::getContent, Collectors.toSet()))).forEach(
							(key, value) -> checkLhcZmDm(player.getPlayerId(), key, finalIssue, value));
				}

				if(methodType == AgentPlayerGameOrder.METHOD_TYPE_SHUANGMIAN) {
					// 双面盘处理
					// 双面盘为请求参数里的投注金额
					betAmount = tmpBean.getBetAmount();
					if("dfw".equals(method)) {
						if("dzkl8".equals(lottery)) {
							if(betAmount.compareTo(dfwDzKl8DefaultBetAmount) != 0) {
								throw newException("-1", "大富翁投注金额有误！");
							}
						}
						if("dzpc28".equals(lottery)) {
							if(betAmount.compareTo(dfwDzPc28DefaultBetAmount) != 0) {
								throw newException("-1", "大富翁投注金额有误！");
							}
						}
					}

					if((gameLotteryMethod.getBetMinLimit().compareTo(BigDecimal.ZERO) > 0 && betAmount.compareTo(
							gameLotteryMethod.getBetMinLimit()) < 0) || (gameLotteryMethod.getBetMaxLimit().compareTo(
							BigDecimal.ZERO) > 0 && betAmount.compareTo(gameLotteryMethod.getBetMaxLimit()) > 0)) {
						throw newException("-1", "当前玩法投注金额区间：" + gameLotteryMethod.getBetMinLimit() + " ~ " +
								gameLotteryMethod.getBetMaxLimit());
					}

					// 验证文本内容
					getGameLotteryUtils().testContentShuangMian(gameLotteryType, method, content);

					AgentPlayerGameOrder entity = new AgentPlayerGameOrder();
					entity.setAgentNo(player.getAgentNo());
					entity.setAgentType(player.getAgentType());
					entity.setAgentName(player.getAgentName());
					entity.setPlayerId(player.getPlayerId());
					entity.setPlayerName(player.getPlayerName());
					entity.setPid(player.getPid());
					entity.setPids(player.getPids());
					entity.setOrderType(AgentPlayerGameOrder.ORDER_TYPE_GENERAL);
					entity.setMethodType(methodType);
					entity.setBetTime(new Timestamp(System.currentTimeMillis()));
					entity.setStopTime(stopTime);
					entity.setBetAmount(betAmount);
					entity.setProfitAmount(betAmount.negate());
					entity.setBetContent(content);
					entity.setBonusAmount(new BigDecimal(0));
					entity.setCreateBy(player.getPlayerName());
					entity.setGameIssue(issue);
					entity.setGameMethod(method);
					entity.setGameName(gameLotteryInfo.getGameName());
					entity.setGameOrderNo(RandomUtils.fromTime20());
					entity.setGameTypeCode(gameLotteryType.getGameTypeCode());
					entity.setIsDelete(0);
					entity.setLottery(lottery);
					entity.setOpenCode("");
					entity.setOpenResult("");
					entity.setOrderStatus(0);
					entity.setPumpAmount(new BigDecimal(0));
					entity.setRebateAmount(new BigDecimal(0));
					entity.setSettleTime(entity.getBetTime());
					entity.setCreateTime(entity.getBetTime());
					entity.setUpdateTime(entity.getCreateTime());

					log.info(entity.getGameTypeCode());
					LotteryType lotteryType = LotteryType.valueOf(entity.getGameTypeCode());
					BigDecimal[] transBonus = transBonus(gameLotteryMethod.getBonus());
					BigDecimal decimal = BigDecimal.ZERO;

					if(lotteryType == LotteryType.ssc) {
						decimal = SscBonus.inputValidate(method, content, transBonus);
					}else if(lotteryType == LotteryType.pk10) {
						decimal = Pk10Bonus.inputValidate(method, content, transBonus);
					}else if(lotteryType == LotteryType.k3) {
						decimal = K3Bonus.inputValidate(method, content, transBonus);
					}else if(lotteryType == LotteryType.kl8) {
						// TODO 后续需要把彩种、玩法等表里的弹珠快乐8类型改为pk8
						decimal = Pk8Bonus.inputValidate(method, content, transBonus);
					}else if(lotteryType == LotteryType.pk8) {
						decimal = Pk8Bonus.inputValidate(method, content, transBonus);
					}else if(lotteryType == LotteryType.lhc) {
						decimal = LhcBonus.inputValidate(method, content, transBonus);
					}else if(lotteryType == LotteryType.pc28) {
						decimal = Pc28Bonus.inputValidate(method, content, transBonus);
					}
					// 最终奖级 = 用户奖级 + 彩种浮动奖级
					int lastLotteryCode = player.getLotteryCode() + agentGameLotteryInfo.getFloatBonus();
					BigDecimal lossPercent = getServiceUtils().getLevelOdds(lastLotteryCode, decimal);
					log.info("agentNo：{}，playerName：{}，lotteryCode：{}，floatBonus：{}，lastLotteryCode：{}，" +
									"decimal：{}，lossPercent：{}", player.getAgentNo(), player.getPlayerName(),
							player.getLotteryCode(), agentGameLotteryInfo.getFloatBonus(), lastLotteryCode, decimal,
							lossPercent);
					entity.setLossPercent(lossPercent);

					// 卡单
					if(!isLock) {
						// 该用户没有卡单订单时，查询卡单配置
						Integer lockId = getLockUtils().getLockAccountReocrdId(request, player.getAgentNo(),
								player.getPlayerName(), entity.getGameOrderNo(), lottery, issue, entity.getBetAmount());
						if(lockId != null) {
							isLockConfig = true;
							lastLockId.addAndGet(lockId);
						}
					}
					entity.setLockId(lastLockId.get());

					boolean addOrder = orderList.add(entity);
					if(!addOrder) {
						throw newException("116-12");
					}
					lotteryShuangMianTotalBetAmount = lotteryShuangMianTotalBetAmount.add(betAmount);
				}else {
					// 标准盘处理
					// 标准盘投注金额 = 注数 * 倍数 * 元/角/分/厘。注数需要根据投注内容计算得出
					// 如果传出文本是压缩的，解压缩
					if(tmpBean.getCompress()) {
						content = LzmaUtils.decompress(content);
					}
					// 元角分厘模式
					String model = tmpBean.getModel();
					// 倍数
					int multiple = tmpBean.getMultiple();
					// 奖级
					int code;
					if(tmpBean.getCode() != null) {
						code = tmpBean.getCode();
					}else {
						code = player.getLotteryCode();
					}
					// 最终奖级 = 用户奖级 + 彩种浮动奖级 + 玩法浮动奖级
					code += agentGameLotteryInfo.getFloatBonus() + gameLotteryMethod.getFloatBonus();
					// 验证文本内容
					getGameLotteryUtils().testContentStandard(gameLotteryType, method, content);
					// 格式化文本内容并获取投注注数
					Object[] formatContent = getGameLotteryUtils().formatContent(gameLotteryType, method, content);
					content = (String) formatContent[0];
					Object numsObj = formatContent[1];
					long nums = 0L;
					if (numsObj instanceof Integer) {
						nums = (int) numsObj;
					} else if (numsObj instanceof Long) {
						nums = (long) numsObj;
					}
					// 注数必须大于0
					if (nums <= 0) {
						throw newException("116-13");
					}
					// 验证玩法注数限制
					boolean testLotteryMethodSpecial = getGameLotteryUtils().testLotteryMethodSpecial(gameLotteryType,
							gameLotteryMethod, content);
					if(!testLotteryMethodSpecial) {
						getGameLotteryUtils().testLotteryMethodRecord(gameLotteryType, gameLotteryMethod, nums);
					}
					// 验证奖级必须是2的倍数
					if (code % 2 != 0) {
						throw newException("116-14");
					}
					// 格式化号并获取返点
					Object[] formatCodeAndPoint = getGameLotteryUtils().formatCodeAndPoint(gameLotteryInfo, player,
							model, code);
					code = (int) formatCodeAndPoint[0];
					double point = (double) formatCodeAndPoint[1];
					// 号必须大于0
					if (code <= 0) {
						throw newException("116-15");
					}
					// 返点不能小于0
					if (point < 0) {
						throw newException("116-16");
					}
					// 倍数必须大于0
					if (multiple <= 0) {
						throw newException("116-17");
					}
					double modelMoney = getGameLotteryUtils().getModel(model);
					// 默认金额必须大于0
					if (modelMoney <= 0) {
						throw newException("116-18");
					}
					double normalExtraMulitple = new BigDecimal(Double.toString(1.0)).doubleValue();

					double money = nums * multiple * modelMoney * sysUnitMoney * normalExtraMulitple;
					// 投注金额必须大于0
					if (money <= 0) {
						throw newException("116-19");
					}
					// 投注金额不能大于1000w
					if (money > 10000000) {
						throw newException("116-20");
					}
					betAmount = BigDecimal.valueOf(money);
					// 标准盘不做投注金额限制
					// if(betAmount.compareTo(gameLotteryMethod.getBetMinLimit()) < 0 || betAmount.compareTo(
					// 		gameLotteryMethod.getBetMaxLimit()) > 0) {
					// 	throw newException("-1", "当前玩法投注金额区间：" + gameLotteryMethod.getBetMinLimit() + " ~ " +
					// 			gameLotteryMethod.getBetMaxLimit());
					// }

					int orderType = AgentPlayerGameOrder.ORDER_TYPE_GENERAL;
					int status = AgentPlayerGameOrder.GAME_STATUS_WAITING;
					Date thisTime = new Moment().toDate();
					// Date orderTime = thisTime;
					// Date stopTime = new Moment().fromTime(ot.getStopTime()).toDate();
					// Date openTime = new Moment().fromTime(ot.getOpenTime()).toDate();
					// double winMoney = 0;
					// String openCode = null;
					// Date clearTime = thisTime;
					// String reference = null;
					// // 处理产生虚拟单*******************************start
					// try {
					// 	// 不影响投注,错误不会无法投注
					// 	int haveSimulationConfig = getGameLotteryOrderSimulationConfigDao().haveSimulationConfig(accountId,
					// 			lottery, method, issue, money);
					// 	if (haveSimulationConfig > 0) {
					// 		try {
					// 			List<GameLotteryOrderSimulation> simulationsTemp = getGameLotteryUtils()
					// 					.getGameLotteryOrderSimulations(accountId, gameLotteryType, lottery, issue,
					// 							content, method, multiple, model, normalExtraMulitple, sysUnitMoney,
					// 							billno, code, point, money, nums);
					// 			simulations.addAll(simulationsTemp);
					// 		} catch (Exception ex) {
					// 			log.error("产生虚拟单错误:" + ex);
					// 		}
					// 	}
					// } catch (Exception ex) {
					// 	log.error("产生虚拟单意外错误:" + ex);
					// }
					// // 处理产生虚拟单*******************************end

					AgentPlayerGameOrder entity = new AgentPlayerGameOrder();
					entity.setAgentNo(player.getAgentNo());
					entity.setAgentType(player.getAgentType());
					entity.setAgentName(player.getAgentName());
					entity.setPlayerId(player.getPlayerId());
					entity.setPlayerName(player.getPlayerName());
					entity.setPid(player.getPid());
					entity.setPids(player.getPids());
					entity.setOrderType(orderType);
					entity.setMethodType(methodType);
					entity.setBetTime(new Timestamp(System.currentTimeMillis()));
					entity.setStopTime(stopTime);
					entity.setBetAmount(betAmount);
					entity.setProfitAmount(betAmount.negate());
					entity.setBetContent(content);
					entity.setNums(nums);
					entity.setModel(model);
					entity.setMultiple(multiple);
					entity.setLotteryCode(code);
					entity.setPoint(point);
					entity.setBonusAmount(new BigDecimal(0));
					entity.setCreateBy(player.getPlayerName());
					entity.setGameIssue(issue);
					entity.setGameMethod(method);
					entity.setGameName(gameLotteryInfo.getGameName());
					entity.setGameOrderNo(RandomUtils.fromTime20());
					entity.setGameTypeCode(gameLotteryType.getGameTypeCode());
					entity.setIsDelete(0);
					entity.setLottery(lottery);
					entity.setOpenCode("");
					entity.setOpenResult("");
					entity.setOrderStatus(status);
					entity.setPumpAmount(new BigDecimal(0));
					entity.setRebateAmount(new BigDecimal(0));
					entity.setSettleTime(entity.getBetTime());
					entity.setCreateTime(entity.getBetTime());
					entity.setUpdateTime(entity.getCreateTime());

					// 卡单
					if(!isLock) {
						// 该用户没有卡单订单时，查询卡单配置
						Integer lockId = getLockUtils().getLockAccountReocrdId(request, player.getAgentNo(),
								player.getPlayerName(), entity.getGameOrderNo(), lottery, issue, entity.getBetAmount());
						if(lockId != null) {
							isLockConfig = true;
							lastLockId.addAndGet(lockId);
						}
					}
					entity.setLockId(lastLockId.get());

					boolean addOrder = orderList.add(entity);
					if(!addOrder) {
						throw newException("116-12");
					}
				}
				totalBetAmount = totalBetAmount.add(betAmount);

				if(methodType == AgentPlayerGameOrder.METHOD_TYPE_SHUANGMIAN) {
					BigDecimal methodTotalBetAmount = methodShuangMianTotalBetAmountMap.get(method);
					if(null == methodTotalBetAmount) {
						methodTotalBetAmount = BigDecimal.ZERO;
					}
					methodShuangMianTotalBetAmountMap.put(method, methodTotalBetAmount.add(betAmount));

					// 从数据库查询该期投注金额
					List<?> objectArray = getAgentPlayerGameOrderDao().totalMethodBetAmount(player.getPlayerId(),
							lottery, issue);
					if(objectArray != null && !objectArray.isEmpty()) {
						for(Object object : objectArray) {
							Object[] ob = (Object[]) object;
							String key = (String) ob[0];
							BigDecimal value = (BigDecimal) ob[1];
							BigDecimal methodTotalBetAmount1 = methodShuangMianTotalBetAmountMap.get(key);
							if(null == methodTotalBetAmount1) {
								methodTotalBetAmount1 = BigDecimal.ZERO;
							}
							methodShuangMianTotalBetAmountMap.put(key, methodTotalBetAmount1.add(value));
						}
					}
					BigDecimal totalGameMethodBetAmount = methodShuangMianTotalBetAmountMap.get(
							gameLotteryMethod.getMethodCode());
					if(null == totalGameMethodBetAmount) {
						totalGameMethodBetAmount = BigDecimal.ZERO;
					}
					if(gameLotteryMethod.getBetMaxLimit().compareTo(BigDecimal.ZERO) > 0 &&
							totalGameMethodBetAmount.compareTo(gameLotteryMethod.getBetMaxLimit()) > 0) {
						throw newException("-1", "当前玩法最大投注" + gameLotteryMethod.getBetMaxLimit());
					}
				}

				// 彩种某期总投注额
				String lotteryIssue = lottery + "," + gameLotteryInfo.getGameName() + "," +
						gameLotteryInfo.getGameRoomName() + "," + issue;
				BigDecimal lotteryIssueBetAmount = betAmountMap.get(lotteryIssue);
				if(lotteryIssueBetAmount == null) {
					lotteryIssueBetAmount = BigDecimal.ZERO;
				}
				betAmountMap.put(lotteryIssue, lotteryIssueBetAmount.add(betAmount));
			}

			// 去掉游戏总限红
			// // 判断游戏总限红
			// BigDecimal totalGameBetAmount = lotteryShuangMianTotalBetAmount;
			// for(String key : methodShuangMianTotalBetAmountMap.keySet()) {
			// 	totalGameBetAmount.add(methodShuangMianTotalBetAmountMap.get(key));
			// 	// TODO totalGameBetAmount.add()，不会将相加后的结果赋值给totalGameBetAmount，是否需要换成下面这行代码
			// 	// totalGameBetAmount = totalGameBetAmount.add(methodShuangMianTotalBetAmountMap.get(key));
			// }
			// if(agentGameLotteryInfo.getGameMaxLimit().compareTo(BigDecimal.ZERO) > 0 && totalGameBetAmount.compareTo(
			// 		agentGameLotteryInfo.getGameMaxLimit()) > 0) {
			// 	throw newException("-1", "当前游戏最大投注" + agentGameLotteryInfo.getGameMaxLimit());
			// }
		}

		// 卡单
		if(isLock || isLockConfig) {
			orderList.forEach(t->{
				if(0 == t.getLockId()) {
					t.setLockId(lastLockId.get());
				}
			});
			// 写入卡单缓存
			getLockUtils().setLock(request, player.getAgentNo(), player.getPlayerName());
		}

		if (!orderList.isEmpty()) {
			BigDecimal availableBalance = player.getPlayerAvailableBalance();
			if(isLock || isLockConfig) {
				BigDecimal lockAmount = getAgentPlayerGameOrderDao().lockAmount(player.getPlayerId());
				availableBalance = availableBalance.subtract(lockAmount);
			}
			if(availableBalance.compareTo(totalBetAmount) < 0) {
				if(isLock || isLockConfig) {
					throw newException("9999");
				}
				// 账户余额不足
				throw newException("106-02");
			}
			// 卡单，不操作余额
			if(!(isLock || isLockConfig)) {
				// 更新金额
				boolean updateAmount = getAgentPlayerInfoDao().updatePlayerAvailableBalance(player.getPlayerId(),
						totalBetAmount.negate(), totalBetAmount, totalBetAmount);
				if(!updateAmount) {
					// ERR:账户余额不足
					throw newException("106-02");
				}
			}

//			boolean lock = redisLock.tryLock(player.getPlayerId().toString());
//            if (lock) {
//				for (AgentPlayerGameOrder order : orderList) {
//					boolean b = platformAgentPlayerCommonService.updateAvailableBalanceForGameIn(player.getPlayerId(), order.getBetAmount().negate(), order.getBetAmount(), AgentPlayerAccountBill.BILL_TYPE_CONSUME, RandomUtils.fromTime24(), player.getPlayerName(), "玩家游戏订单投注");
//                    if (!b) {
//                       throw  new RuntimeException("下注失败");
//                    }
//				}
//            }

			// 插入订单
			for (AgentPlayerGameOrder order : orderList) {
				boolean save = getAgentPlayerGameOrderDao().save(order);
				if (!save) {
					throw newException("116-21");
				}
			}
			// 卡单
			if(isLock || isLockConfig) {
				return "isLock";
			}

			// 插入账单
			for (AgentPlayerGameOrder order : orderList) {
				getTelegramService().addBetPush(order);
				billService.addAgentPlayerGameOrderConsumeBill(player, order);
				if ("dfw".equals(order.getGameMethod())) {
					getGameMonopolyService().dfwOrderDeal(order);
				}
				player.addPlayerAvailableBalance(order.getBetAmount().negate());
			}

			HashMap<String, Object> map = LevelCalculator
					.calculateLevel(player.getPlayerWealth() + totalBetAmount.intValue());

			taskExecutor.execute(() -> {
				boolean b1 = getAgentPlayerInfoDao().updatePlayerLevel(player.getPlayerId(), player.getPlayerLevelId(),
						(Integer) map.get("level"));
				if (!b1) {
					log.warn("更新用户等级失败，需要重新更新");
				}
			});

			// 聊天室通知
			for(String lotteryIssue : betAmountMap.keySet()) {
				BigDecimal finalTotalBetAmount = betAmountMap.get(lotteryIssue);
				String[] finalLotteryIssue = lotteryIssue.split(",");
				orderToPushTaskExecutor.execute(() -> {
					try {
						HashMap<String, Object> data = new HashMap<>();
						data.put("playerNick", player.getPlayerNick());
						data.put("playerLevelId", map.get("level"));
						data.put("chatNoticeType", 1);// 1-下单成功；2-中奖
						data.put("totalBetAmount", finalTotalBetAmount);
						data.put("gameName", finalLotteryIssue[1]);
						data.put("gameIssue", finalLotteryIssue[3]);
						getTcChatService().sendGroupSystemNotification(finalLotteryIssue[0], finalLotteryIssue[2],
								JSONObject.toJSONString(data));
					}catch(Exception e) {
						log.error("下单成功，聊天室通知失败，用户ID " + player.getPlayerId(), e);
					}
				});
			}

			return "true";
		}
		return "false";
	}

	private void checkLhcZmDm(Long playerId, String method, String issue, Set<String> preBetContenSet){

		List<AgentPlayerGameOrder> dzlhc = getAgentPlayerGameOrderDao().getPlayerGameOrder(playerId, "dzlhc", issue, method);
		Set<String> hasBetContenSet = dzlhc.stream().map(AgentPlayerGameOrder::getBetContent).collect(Collectors.toSet());
		hasBetContenSet.addAll(preBetContenSet);
		if(hasBetContenSet.size() > 8){
			String content = getThrowContent(method);
			throw new ServiceException("1", content);
		}
	}

	private String getThrowContent(String method){
		String content = "";

		switch (method){
			case "bzzm1dm":
				content = "正码1单码不能超过每期最大投注数";
				break;
			case "bzzm2dm":
				content = "正码2单码不能超过每期最大投注数";
				break;
			case "bzzm3dm":
				content = "正码3单码不能超过每期最大投注数";
				break;
			case "bzzm4dm":
				content = "正码4单码不能超过每期最大投注数";
				break;
			case "bzzm5dm":
				content = "正码5单码不能超过每期最大投注数";
				break;
			case "bzzm6dm":
				content = "正码6单码不能超过每期最大投注数";
				break;
			case "bztmdm":
				content = "特码单码不能超过每期最大投注数";
				break;
			case "smzm1sx":
				content = "正码1生肖不能超过每期最大投注数";
				break;
			case "smzm2sx":
				content = "正码2生肖不能超过每期最大投注数";
				break;
			case "smzm3sx":
				content = "正码3生肖不能超过每期最大投注数";
				break;
			case "smzm4sx":
				content = "正码4生肖不能超过每期最大投注数";
				break;
			case "smzm5sx":
				content = "正码5生肖不能超过每期最大投注数";
				break;
			case "smzm6sx":
				content = "正码6生肖不能超过每期最大投注数";
				break;
			case "smtmsx":
				content = "特码生肖不能超过每期最大投注数";
				break;
		}
		return content;

	}

	private BigDecimal[] transBonus(String bonusString) {
		String[] bonusStringArray = bonusString.replace("，", ",").split(",");
		BigDecimal[] bonusBigDecimalArray = new BigDecimal[bonusStringArray.length];
		for (int i = 0; i < bonusStringArray.length; i++) {
			bonusBigDecimalArray[i] = new BigDecimal(bonusStringArray[i]);
		}
		return bonusBigDecimalArray;
	}

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean cancelOrder(AgentPlayerInfo player, Long orderId) {
		AgentPlayerGameOrder order = getAgentPlayerGameOrderDao().getById(orderId);
		if (null == order) {
			throw newException("118-01");
		}
		if (AgentPlayerGameOrder.GAME_STATUS_WAITING != order.getOrderStatus()) {
			throw newException("118-02");
		}
		if(order.getStopTime() == null || new Moment().ge(new Moment().fromDate(order.getStopTime()))) {
			throw newException("118-04");
		}


		// 开启事务
		proJPAQueryFactoryUtil.txUpdate(() -> {
			if ("dfw".equals(order.getGameMethod())) {
				boolean bool = getGameMonopolyService().cancelDfwOrder(order);
				if (!bool) {
					log.error("撤单大富翁奖池金额变动失败。");
					throw newException("118-10");
				}
			}
			boolean bool = getAgentPlayerGameOrderDao().updateOrderStatus(order.getOrderId(),
					AgentPlayerGameOrder.GAME_STATUS_CANCELED);
			if (!bool) {
				throw newException("-1", "撤单失败！");
			}
			getRedisService().delGameRecordDetail(order.getOrderId());
		});


		BigDecimal betAmount = order.getBetAmount();
		try{
			List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
					// 出账
					new PlayerBalanceUpdateParams(player.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_CHANCELED, order.getGameOrderNo(), player.getPlayerId(), betAmount, betAmount.negate(),
							BalanceChangeDirection.INCR, (beforeDownPlayer, changePlayerAvailableBalance,
														  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {}));
			agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		}catch (Exception e){
			throw newException("-1", e.getMessage());
		}

		return true;
	}

	@Override
	public boolean addAgentPlayerOpinion(AgentPlayerInfo player, String link, int feedbackType,
			String feedbackContent) {
		AgentPlayerOpinion entity = new AgentPlayerOpinion();
		entity.setFeedbackType(feedbackType);
		entity.setFeedbackContent(feedbackContent);
		entity.setCreateTime(new Moment().toTimestamp());
		entity.setAgentNo(player.getAgentNo());
		entity.setAgentName(player.getAgentName());
		entity.setPlayerId(player.getPlayerId());
		entity.setPlayerName(player.getPlayerName());
		entity.setLink((null == link) ? "" : link);
		entity.setProcessStatus(0);
		entity.setProcessContent("");
		entity.setProcessTime(entity.getCreateTime());
		entity.setProcessUsername("");
		entity.setProcessAccountId(0L);
		entity.setIsDelete(0);
		return getAgentPlayerOpinionDao().save(entity);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<GameLotteryProfitRankingVo> profitRanking(String agentNo, Date sDate, Date eDate) {
		Object object = getPlatformAgentPlayerReportDao().profitRanking(agentNo, sDate, eDate);
		List<GameLotteryProfitRankingVo> list = (List<GameLotteryProfitRankingVo>) object;
		processProfitList(list);
		if (list.size() > 13) {
			list = list.subList(0, 13);
		}
		return list;
	}

	private void processProfitList(List<GameLotteryProfitRankingVo> list) {
		Iterator<GameLotteryProfitRankingVo> iterator = list.iterator();
		while (iterator.hasNext()) {
			GameLotteryProfitRankingVo vo = iterator.next();
			if (vo.getProfitAmount().compareTo(BigDecimal.ZERO) <= 0) {
				iterator.remove();
			}
		}
	}

}
