package ph.yckj.frontend.service.impl;

import lombok.extern.slf4j.Slf4j;
import myutil.Moment;
import myutil.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.frontend.service.AgentPlayerAccountBillService;
import ph.yckj.frontend.service.AgentPlayerChatService;
import ph.yckj.frontend.service.AgentPlayerRewardService;
import ph.yckj.frontend.service.TCChatService;
import ph.yckj.frontend.util.AbstractService;
import ph.yckj.frontend.util.LevelCalculator;
import ph.yckj.frontend.web.req.SendGiftReq;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.HashMap;

@Slf4j
@Service
public class AgentPlayerChatServiceImpl extends AbstractService implements AgentPlayerChatService {

//    private static final Log log = LogFactory.getLog(AgentPlayerChatServiceImpl.class);
    @Autowired
    private AgentPlayerAccountBillService billService;

    @Autowired
    private TCChatService tcChatService;

    @Autowired
    private AgentPlayerRewardService agentPlayerRewardService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private PlatformAgentPlayerCommonService platformAgentPlayerCommonService;

    @Autowired
    private AgentPlayerInfoLockService agentPlayerInfoLockService;

    @Override
    @Transactional
    public boolean sendGift(String requestId, AgentPlayerInfo player, GiftInfo giftInfo, SendGiftReq sendGiftReq) {
        GameLotteryInfo gameLotteryInfo = getRedisService().getGameLotteryInfo(sendGiftReq.getLottery());
        if (gameLotteryInfo == null) {
            throw newException("1", "游戏种类不正确");
        }


        final BigDecimal giftAmountTotal = giftInfo.getGiftAmount().multiply(new BigDecimal(sendGiftReq.getGiftCount()));
        String billRemark = "玩家打赏";

        // 🚀 使用单笔账变服务（只负责账变逻辑）
        agentPlayerInfoLockService.decr(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_SEND_GIFT,
                billRemark, player.getPlayerId(), giftAmountTotal.negate(), BigDecimal.ZERO,
                (beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
                    // 🖤 黑色部分：账变逻辑
                    log.info("送礼物扣款成功：username：{}，account id：{}，转前余额：{}，扣款金额：{}", beforePlayer.getPlayerName(),
                            beforePlayer.getPlayerId(), beforePlayer.getPlayerAvailableBalance(), giftAmountTotal);
                });

        // 异步处理代理商抽成
        taskExecutor.execute(() -> {
            // 代理商抽成逻辑
            AgentInfo agentInfo = getAgentInfoDao().getByAgentNo(player.getAgentNo());
            BigDecimal rewardRatioDecimal = BigDecimal.valueOf(agentInfo.getRewardRatio());
            if (agentInfo.getRewardRatio() == 0) {
                log.warn("代理商抽成为0，不生成账单");
                return;
            }

            BigDecimal rewardAmount = giftAmountTotal.multiply(rewardRatioDecimal).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            getAgentInfoDao().updateAvailableBalance(agentInfo.getAgentId(), rewardAmount);

            AgentAccountBill entity = new AgentAccountBill();
            entity.setAgentNo(agentInfo.getAgentNo());
            entity.setAgentName(agentInfo.getAgentName());
            entity.setAgentBillNo(RandomUtils.fromTime24());
            entity.setTransferAmount(rewardAmount);
            BigDecimal agentBalanceBefore = agentInfo.getAgentAvailableBalance();
            BigDecimal agentBalanceAfter = agentInfo.getAgentAvailableBalance().add(rewardAmount);
            entity.setAgentBalanceBefore(agentBalanceBefore);
            entity.setAgentBalanceAfter(agentBalanceAfter);
            entity.setBillType(9);
            entity.setBillRemark("玩家打赏抽成");
            entity.setReferenceBillNo(player.getPlayerName() + "_gift_" + System.currentTimeMillis()); // 使用时间戳作为关联
            entity.setIsDelete(0);
            entity.setCreateBy(player.getPlayerName());
            entity.setCreateTime(new Moment().toTimestamp());
            entity.setUpdateTime(entity.getCreateTime());
            boolean t = getAgentAccountBillDao().save(entity);
            if (!t) {
                log.warn("代理商打赏抽成失败，需要补代理商账单");
            }
        });


        taskExecutor.execute(() -> {
            HashMap<String, Object> map = LevelCalculator.calculateLevel(player.getPlayerWealth() + giftAmountTotal.intValue() * 10);
            boolean b1 = getAgentPlayerInfoDao().updatePlayerLevel(player.getPlayerId(), player.getPlayerLevelId(), (Integer) map.get("level"));
            if (!b1) {
                log.warn("更新用户等级失败，需要重新更新");
            }
        });

        try {
            boolean b1 = agentPlayerRewardService.saveAgentPlayerReward(player, gameLotteryInfo, giftInfo, sendGiftReq, giftAmountTotal);
            if (!b1) {
                log.warn("保存送礼物记录失败，需要补送礼物记录");
            }
        }catch(Exception e) {
            log.warn("保存送礼物记录异常，需要补送礼物记录, player:{}", player, e);
        }
        taskExecutor.execute(() -> {


        });

        taskExecutor.execute(() -> {
            boolean b;
            // 发通知
            if (sendGiftReq.getGiftCode().equals("send_money")) {
                b = tcChatService.sendGroupSystemNotification(player.getPlayerNick(), gameLotteryInfo.getLottery(), gameLotteryInfo.getGameRoomName(), giftInfo, sendGiftReq);
            } else {
                b = tcChatService.sendGroupMsg(requestId, player, gameLotteryInfo.getLottery(), gameLotteryInfo.getGameRoomName(), giftInfo, sendGiftReq);

            }

            if (!b) {
                log.error("发通知失败，需要补发通知或退款");
            }
        });

        taskExecutor.execute(() -> {

            // 放入背包
            AgentPlayerGiftBackpack agentPlayerGiftBackpack = new AgentPlayerGiftBackpack();
            agentPlayerGiftBackpack.setAgentNo(player.getAgentNo());
            agentPlayerGiftBackpack.setAgentName(player.getAgentName());
            agentPlayerGiftBackpack.setPlayerId(player.getPlayerId());
            agentPlayerGiftBackpack.setPlayerName(player.getPlayerName());
            agentPlayerGiftBackpack.setGiftId(giftInfo.getGiftId());
            agentPlayerGiftBackpack.setGiftCode(giftInfo.getGiftCode());
            agentPlayerGiftBackpack.setCreateBy(player.getPlayerName());
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            agentPlayerGiftBackpack.setCreateTime(timestamp);
            agentPlayerGiftBackpack.setUpdateTime(timestamp);
            agentPlayerGiftBackpack.setIsDelete(0);
            boolean b = getAgentPlayerGiftBackpackDao().saveOrUpdate(agentPlayerGiftBackpack);

            if (!b) {
                log.error("放入背包失败，需要重新放入背包");
            }
        });

        return true;
    }
}
