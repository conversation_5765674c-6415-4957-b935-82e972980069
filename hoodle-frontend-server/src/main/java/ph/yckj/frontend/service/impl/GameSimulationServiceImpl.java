package ph.yckj.frontend.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import myutil.JacksonUtils;
import myutil.Moment;
import myutil.RandomUtils;
import myutil.StringIdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import ph.yckj.common.enums.OutThirdUrlEnums;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.third.PlatformGameUtils;
import ph.yckj.frontend.service.GameSimulationService;
import ph.yckj.frontend.util.AbstractService;
import ph.yckj.frontend.vo.third.*;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.base.common.service.balance.vo.BalanceChangeDirection;
import sy.hoodle.base.common.service.balance.vo.PlayerBalanceUpdateParams;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;

@Slf4j
@Service
public class GameSimulationServiceImpl extends AbstractService implements GameSimulationService {

	@Autowired
	private AgentPlayerInfoLockService agentPlayerInfoLockService;

	@Override
	@Transactional
	public GameSimulationAccount registerSimulationGame(AgentPlayerInfo player, GameSimulationPlatPlatformType platform)
			throws ServiceException {
		GameSimulationAccount gameSimulationAccount =
				getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
		if(null != gameSimulationAccount) {
			if(GameSimulationAccount.STATUS_ACTIVE == gameSimulationAccount.getStatus()) {
				throw newException("124-03");
			}else {
				throw newException("124-06");
			}
		}
		// 调用游戏平台API开通游戏
		gameSimulationAccount = getSimulationGameSuperService().register(player, platform);
		if(null == gameSimulationAccount) {
			// 游戏开通失败
			throw newException("124-01");
		}
		return gameSimulationAccount;
	}

	@Override
	public ThirdGameUrlVo getForwardGameParams(GameSimulationAccount gameSimulationAccount, AgentPlayerInfo player,
			String platformCode, String gameCode) {
		if(null == gameSimulationAccount || GameSimulationAccount.STATUS_ACTIVE != gameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		return getSimulationGameSuperService().getGameUrl(gameSimulationAccount, player.getDevice(), platformCode,
				gameCode);
	}

	@Override
	public Map<String, Object> getForwardGameParamsAPP(GameSimulationAccount gameSimulationAccount,
			AgentPlayerInfo player, String platformCode, String gameCode) {
		if(null == gameSimulationAccount || GameSimulationAccount.STATUS_ACTIVE != gameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		return getSimulationGameSuperService().getForwardGameParamsAPP(gameSimulationAccount);
	}

	@Override
	public GameSimulationAccount updateAccountBalance(AgentPlayerInfo player, String platformCode) {
		GameSimulationAccount gameSimulationAccount = getGameSimulationAccount(player, platformCode);
		Date lastUpdateTime = gameSimulationAccount.getUpdateTime();
		if(null != lastUpdateTime) {
			Moment nowMoment = new Moment();
			Moment lastUpdateMoment = new Moment().fromDate(lastUpdateTime);
			if(nowMoment.difference(lastUpdateMoment, "second") >= 60) {// 1分钟内只允许刷新一次
				getSimulationGameSuperService().updateBalanceForce(gameSimulationAccount);
			}
		}else {
			getSimulationGameSuperService().updateBalanceForce(gameSimulationAccount);
		}
		return gameSimulationAccount;
	}

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public int balanceLottery2Game(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, BigDecimal amount,
			boolean isTransferAll) {
		amount = amount.setScale(0, RoundingMode.DOWN);
		if(amount.doubleValue() < 1) {
			if(!isTransferAll) {
				throw newException("110-02");
			}else {
				return GameSimulationTransfer.STATUS_COMPLETED;
			}
		}
		// 充值到游戏账户
		/* 校验开始 */
		GameSimulationAccount toGameSimulationAccount =
				getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
		// 如果没开通，抛出异常
		if(null == toGameSimulationAccount ||
				GameSimulationAccount.STATUS_ACTIVE != toGameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		// 验证中心账户余额
		if(player.getPlayerAvailableBalance().doubleValue() < amount.doubleValue()) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		/*  校验结束  */

		// 订单号
		String billno = RandomUtils.fromTime16();

		// 扣除我的中心账户余额
		log.info("转账到游戏扣款开始：username：{}，account id：{}，转前余额：{}，转账金额：{}", player.getPlayerName(),
				player.getPlayerId(), player.getPlayerAvailableBalance(), amount);

		String remark = "中心钱包转账到" + platform.getName() + "，金额：" + amount;
		boolean updateFromBalance =
				getPlatformAgentPlayerCommonService().updateAvailableBalanceForGameOut(player.getPlayerId(),
						amount.negate(), amount, AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_RECHARGE, billno,
						player.getPlayerName(), remark);
		if(!updateFromBalance) {
			// ERR:账户余额不足
			log.info("转账到游戏扣款余额不足：username：{}，account id：{}，转前余额：{}，转账金额：{}", player.getPlayerName(),
					player.getPlayerId(), player.getPlayerAvailableBalance(), amount);
			throw newException("106-01");
		}
		log.info("转账到游戏扣款成功：username：{}，account id：{}，转前余额：{}，转账金额：{}", player.getPlayerName(),
				player.getPlayerId(), player.getPlayerAvailableBalance(), amount);

		BigDecimal balanceBefore = player.getPlayerAvailableBalance();
		// 生成转账记录到game_simulation_transfer
		GameSimulationTransfer gameSimulationTransfer =
				getSimulationGameSuperService().buildGameSimulationTransfer4Deposit(player, toGameSimulationAccount,
						billno, amount);
		boolean saveGameTransfer = getGameSimulationTransferDao().save(gameSimulationTransfer);
		if(!saveGameTransfer) {
			throw new IllegalArgumentException();
		}

		// 调用目标游戏平台api进行充值
		gameSimulationTransfer =
				getSimulationGameSuperService().deposit(toGameSimulationAccount, amount, gameSimulationTransfer);
		if(GameSimulationTransfer.STATUS_FAILED == gameSimulationTransfer.getStatus()) {
			// 转账失败
			gameSimulationTransfer.setBalanceAfter(balanceBefore);
			getGameSimulationTransferDao().updateFail(gameSimulationTransfer.getId(), balanceBefore); // 回滚金额
			// TODO 账户余额回滚？
			remark = "中心钱包转账到" + platform.getName() + "失败，退回金额：" + amount;
			updateFromBalance =
					getPlatformAgentPlayerCommonService().updateAvailableBalanceForGameIn(player.getPlayerId(), amount,
							amount.negate(), AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_WITHDRAW,
							gameSimulationTransfer.getBillno(), player.getPlayerName(), remark);
			if(!updateFromBalance) {
				throw newException("113-08");
			}else {
				log.info("转账到游戏转账失败,进行金额回滚完成：username：{}，转账金额：{}", player.getPlayerId(), amount);
			}
			return gameSimulationTransfer.getStatus();
		}else if(GameSimulationTransfer.STATUS_PROCESSING == gameSimulationTransfer.getStatus()) {
			// gameSimulationTransfer是转账进行中状态，返回accountTransfer进行中的状态
			return GameSimulationTransfer.STATUS_PROCESSING;
		}else if(GameSimulationTransfer.STATUS_COMPLETED == gameSimulationTransfer.getStatus()) {
			// 转账完成：GameSimulationTransfer.STATUS_COMPLETED
			getGameSimulationTransferDao().updateStatus(gameSimulationTransfer);

			// 添加报表
			GameSimulationReport gameSimulationReport =
					new GameSimulationReport(player.getAgentNo(), player.getAgentName(), platform.getId(),
							platform.getCode(), platform.getName(), player.getPlayerId(), player.getPlayerName(),
							player.getPid(), player.getPids(), platform.getCode(), new Moment().toDate());
			gameSimulationReport.setDepositAmount(amount);
			getGameSimulationReportDao().upsertTransfer(gameSimulationReport);

			return gameSimulationTransfer.getStatus();
		}
		return GameSimulationTransfer.STATUS_FAILED;
	}

	/**
	 * 中心钱包转账到游戏
	 * 批量余额更新
	 */
	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public int balanceLottery2GameV2(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, BigDecimal amount,
			boolean isTransferAll) {
		final BigDecimal finalAmount = amount.setScale(0, RoundingMode.DOWN);
		if(finalAmount.doubleValue() < 1) {
			if(!isTransferAll) {
				throw newException("110-02");
			}else {
				return GameSimulationTransfer.STATUS_COMPLETED;
			}
		}

		// 充值到游戏账户
		/* 校验开始 */
		GameSimulationAccount toGameSimulationAccount =
				getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
		// 如果没开通，抛出异常
		if(null == toGameSimulationAccount ||
				GameSimulationAccount.STATUS_ACTIVE != toGameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		// 验证中心账户余额
		if(player.getPlayerAvailableBalance().doubleValue() < finalAmount.doubleValue()) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		/*  校验结束  */

		String remark = "中心钱包转账到" + platform.getName() + "，金额：" + finalAmount;

		// 批量余额更新
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
			// 扣除中心账户余额
			new PlayerBalanceUpdateParams(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_RECHARGE,
					remark, player.getPlayerId(), finalAmount.negate(), finalAmount,
					BalanceChangeDirection.DECR,
					(beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
						// 生成转账记录到game_simulation_transfer
						String billno = agentPlayerAccountBill.getPlayerBillNo();
						GameSimulationTransfer gameSimulationTransfer =
								getSimulationGameSuperService().buildGameSimulationTransfer4Deposit(beforePlayer, toGameSimulationAccount,
										billno, finalAmount);
						boolean saveGameTransfer = getGameSimulationTransferDao().save(gameSimulationTransfer);
						if(!saveGameTransfer) {
							throw new RuntimeException("保存游戏转账记录失败");
						}

						// 调用目标游戏平台api进行充值
						gameSimulationTransfer = getSimulationGameSuperService().deposit(toGameSimulationAccount, finalAmount, gameSimulationTransfer);
						if(gameSimulationTransfer.getStatus() == GameSimulationTransfer.STATUS_COMPLETED) {
							// 添加报表
							GameSimulationReport gameSimulationReport =
									new GameSimulationReport(beforePlayer.getAgentNo(), beforePlayer.getAgentName(), platform.getId(),
											platform.getCode(), platform.getName(), beforePlayer.getPlayerId(), beforePlayer.getPlayerName(),
											beforePlayer.getPid(), beforePlayer.getPids(), platform.getCode(), new Moment().toDate());
							gameSimulationReport.setDepositAmount(finalAmount);
							getGameSimulationReportDao().upsertTransfer(gameSimulationReport);
						} else if(gameSimulationTransfer.getStatus() == GameSimulationTransfer.STATUS_FAILED) {
							throw new RuntimeException("游戏平台充值失败");
						}
					})
		);

		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);

		log.info("转账到游戏成功（升级版）：username：{}，account id：{}，转账金额：{}", player.getPlayerName(),
				player.getPlayerId(), finalAmount);
		return GameSimulationTransfer.STATUS_COMPLETED;
	}

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public int balanceGame2Lottery(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, BigDecimal amount,
			boolean isTransferAll) {
		amount = amount.setScale(0, RoundingMode.DOWN);
		if(amount.doubleValue() < 1) {
			if(isTransferAll) {
				return 99;
			}else {
				throw newException("110-02");
			}
		}
		// 从游戏账户提现
		/*  校验开始  */
		GameSimulationAccount fromGameSimulationAccount =
				getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
		// 如果没开通，抛出异常
		if(null == fromGameSimulationAccount ||
				GameSimulationAccount.STATUS_ACTIVE != fromGameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		// 获取中心账户
		log.info("游戏转账到中心扣款开始：username：{}，account id：{}，转前余额：{}，转账金额：{}", player.getPlayerName(),
				player.getPlayerId(), player.getPlayerAvailableBalance(), amount);
		// 验证第三方游戏账户
		if(fromGameSimulationAccount.getBalance().doubleValue() < amount.doubleValue()) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		/*  校验结束  */

		// 添加转账记录（转账中）
		String billno = RandomUtils.fromTime16();
		BigDecimal balanceBefore = player.getPlayerAvailableBalance();

		// 生成转账记录到game_simulation_transfer
		GameSimulationTransfer gameSimulationTransfer =
				getSimulationGameSuperService().buildGameSimulationTransfer4Withdraw(player, fromGameSimulationAccount,
						billno, amount);
		boolean saveGameTransfer = getGameSimulationTransferDao().save(gameSimulationTransfer);
		if(!saveGameTransfer) {
			throw new IllegalArgumentException();
		}

		// 调用目标游戏平台api进行提款
		gameSimulationTransfer =
				getSimulationGameSuperService().withdraw(fromGameSimulationAccount, amount, gameSimulationTransfer);
		if(GameSimulationTransfer.STATUS_FAILED == gameSimulationTransfer.getStatus()) {// 提现失败
			gameSimulationTransfer.setBalanceAfter(balanceBefore);
			getGameSimulationTransferDao().updateFail(gameSimulationTransfer.getId(), balanceBefore); // 回滚金额
			return gameSimulationTransfer.getStatus();
		}else if(GameSimulationTransfer.STATUS_PROCESSING == gameSimulationTransfer.getStatus()) {
			// gameSimulationTransfer是转账进行中状态，返回accountTransfer进行中的状态
			return GameSimulationTransfer.STATUS_PROCESSING;
		}else if(GameSimulationTransfer.STATUS_COMPLETED == gameSimulationTransfer.getStatus()) {
			// 提现完成：GameSimulationTransfer.STATUS_COMPLETED
			// 给中心账户加钱
			String remark = platform.getName() + "转账到中心钱包，金额：" + amount;
			boolean updateToBalance =
					getPlatformAgentPlayerCommonService().updateAvailableBalanceForGameIn(player.getPlayerId(), amount,
							amount.negate(), AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_WITHDRAW,
							gameSimulationTransfer.getBillno(), player.getPlayerName(), remark);
			if(!updateToBalance) {
				return gameSimulationTransfer.getStatus();// 已经调用api并提现完成，应该把记录留下，如果抛异常就没有记录
				// throw new IllegalArgumentException();
			}

			// 更新转账记录
			getGameSimulationTransferDao().updateStatus(gameSimulationTransfer);

			// 添加报表
			GameSimulationReport gameSimulationReport =
					new GameSimulationReport(player.getAgentNo(), player.getAgentName(), platform.getId(),
							platform.getCode(), platform.getName(), player.getPlayerId(), player.getPlayerName(),
							player.getPid(), player.getPids(), platform.getCode(), new Moment().toDate());
			gameSimulationReport.setWithdrawAmount(amount);
			getGameSimulationReportDao().upsertTransfer(gameSimulationReport);

			return gameSimulationTransfer.getStatus();
		}
		return GameSimulationTransfer.STATUS_FAILED;
	}

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public int balanceGameToLottery(AgentPlayerInfo player, String fromPlatformCode, BigDecimal amount,
			boolean isTransferAll) {
		amount = amount.setScale(0, RoundingMode.DOWN);
		if(amount.doubleValue() < 1) {
			if(isTransferAll) {
				return 99;
			}else {
				throw newException("110-02");
			}
		}
		// 从游戏账户提现
		/*  校验开始  */
		// 第三方游戏类型
		GameSimulationPlatPlatformType platform =
				getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), fromPlatformCode);
		if(null == platform) {
			throw newException("124-06");
		}
		GameSimulationAccount fromGameSimulationAccount =
				getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
		// 如果没开通，抛出异常
		if(null == fromGameSimulationAccount ||
				GameSimulationAccount.STATUS_ACTIVE != fromGameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		// 获取中心账户
        log.info("游戏转账到中心扣款开始：username：{}，account id：{}，转前余额：{}，转账金额：{}", player.getPlayerName(),
                player.getPlayerId(), player.getPlayerAvailableBalance(), amount);
		// 验证第三方游戏账户
		if(fromGameSimulationAccount.getBalance().doubleValue() < amount.doubleValue()) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		/*  校验结束  */

		// 添加转账记录（转账中）
		String billno = RandomUtils.fromTime16();
		BigDecimal balanceBefore = player.getPlayerAvailableBalance();

		// 生成转账记录到game_simulation_transfer
		GameSimulationTransfer gameSimulationTransfer =
				getSimulationGameSuperService().buildGameSimulationTransfer4Withdraw(player, fromGameSimulationAccount,
						billno, amount);
		boolean saveGameTransfer = getGameSimulationTransferDao().save(gameSimulationTransfer);
		if(!saveGameTransfer) {
			throw new IllegalArgumentException();
		}

		// 调用目标游戏平台api进行提款
		gameSimulationTransfer =
				getSimulationGameSuperService().withdraw(fromGameSimulationAccount, amount, gameSimulationTransfer);
		if(GameSimulationTransfer.STATUS_FAILED == gameSimulationTransfer.getStatus()) {// 提现失败
			gameSimulationTransfer.setBalanceAfter(balanceBefore);
			getGameSimulationTransferDao().updateFail(gameSimulationTransfer.getId(), balanceBefore); // 回滚金额
			return gameSimulationTransfer.getStatus();
		}else if(GameSimulationTransfer.STATUS_PROCESSING == gameSimulationTransfer.getStatus()) {
			// gameSimulationTransfer是转账进行中状态，返回accountTransfer进行中的状态
			return GameSimulationTransfer.STATUS_PROCESSING;
		}else if(GameSimulationTransfer.STATUS_COMPLETED == gameSimulationTransfer.getStatus()) {
			// 提现完成：GameSimulationTransfer.STATUS_COMPLETED
			// 给中心账户加钱
			String remark = platform.getName() + "转账到中心钱包，金额：" + amount;
			boolean updateToBalance =
					getPlatformAgentPlayerCommonService().updateAvailableBalanceForGameIn(player.getPlayerId(), amount,
							amount.negate(), AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_WITHDRAW,
							gameSimulationTransfer.getBillno(), player.getPlayerName(), remark);
			if(!updateToBalance) {
				return gameSimulationTransfer.getStatus();// 已经调用api并提现完成，应该把记录留下，如果抛异常就没有记录
				// throw new IllegalArgumentException();
			}

			// 更新转账记录
			getGameSimulationTransferDao().updateStatus(gameSimulationTransfer);

			// 添加报表
			GameSimulationReport gameSimulationReport =
					new GameSimulationReport(player.getAgentNo(), player.getAgentName(), platform.getId(),
							platform.getCode(), platform.getName(), player.getPlayerId(), player.getPlayerName(),
							player.getPid(), player.getPids(), platform.getCode(), new Moment().toDate());
			gameSimulationReport.setWithdrawAmount(amount);
			getGameSimulationReportDao().upsertTransfer(gameSimulationReport);

			return gameSimulationTransfer.getStatus();
		}
		return GameSimulationTransfer.STATUS_FAILED;
	}

	private GameSimulationAccount getGameSimulationAccount(AgentPlayerInfo player, String platformCode) {
		GameSimulationAccount gameSimulationAccount =
				getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platformCode);
		// 如果没开通，抛出异常
		if(null == gameSimulationAccount || GameSimulationAccount.STATUS_ACTIVE != gameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		return gameSimulationAccount;
	}

	@Override
	public boolean allowAccountBaccarat(long accountId) {
		String allowAccoutArray = getSystemConfigDao().getValueByGroupAndKey("ACCOUNT", "BACCARAT_ALLOW_ACCOUNTS");
		List<Integer> list = StringIdUtils.toList(allowAccoutArray);
		if(null == list || list.isEmpty()) {
			// 不配置BACCARAT_ALLOW_ACCOUNTS时默认都可以玩
			return true;
		}
		for(long id : list) {
			if(accountId == id) {
				return true;
			}
		}
		return false;
	}

	@Override
	public Map<String, Object> searchGamePlatformDetailOrder(AgentPlayerInfo player, Date sTime, Date eTime, int page,
			int size) {
		int firstResult = page * size, maxResults = size;
		int totalCount = 0;
		List<GamePlatformDetailOrderVO> list = new ArrayList<>();
		List<GameSimulationPlatPlatformType> gameSimulationPlatformTypes =
				getGameSimulationPlatPlatformTypeDao().listByAgentNoEnable(player.getAgentNo());
		long accountId = player.getPlayerId();
		int currentCount = 0; // 计算游标

		for(GameSimulationPlatPlatformType gameSimulationPlatformType : gameSimulationPlatformTypes) {
			List<GamePlatformDetailOrderVO> tmpList = new ArrayList<>();
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(accountId, gameSimulationPlatformType.getCode());
			if(null == gameSimulationAccount) {
				continue;
			}

			Criteria criteria = new Criteria();
			long playerId = gameSimulationAccount.getId();
			criteria.and("playerId").is(playerId);
			Sort sort = null;
			String sTimeStr = new Moment().fromDate(sTime).toSimpleTime();
			String eTimeStr = new Moment().fromDate(eTime).toSimpleTime();
			String platformCode = gameSimulationPlatformType.getCode();
			switch(platformCode) {
				case "FT":
					criteria.and("transDate").gte(sTime).lt(eTime);
					sort = Sort.by(Direction.DESC, "transDate");
					int tmpTotalCountFT = getGameSportsRecordDetailDao().totalCount(criteria); // 只先知道有多少笔数据,做运算

					if(tmpTotalCountFT > 0 && (list.size() < maxResults) &&
							((currentCount + tmpTotalCountFT) >= firstResult + list.size())) {
						// 只算显示未满显示笔数的,
						int currentIndex =
								tmpTotalCountFT - ((currentCount + tmpTotalCountFT) - (firstResult + list.size()));
						int toIndex = currentIndex + (maxResults - list.size());
						// 取得刚好数量的数据
						if((currentIndex + maxResults) > tmpTotalCountFT) {
							toIndex = tmpTotalCountFT;
						}
						List<GameSportsRecordDetail> resultListFT =
								getGameSportsRecordDetailDao().find(criteria, sort, currentIndex,
										(toIndex - currentIndex));
						setFTBean(resultListFT, tmpList);

						// List<GamePlatformDetailOrderVO> tmpVO = tmpList.subList(currentIndex,
						// toIndex);

						list.addAll(tmpList);
					}
					currentCount += tmpTotalCountFT;
					totalCount += tmpTotalCountFT;
					break;
				case "KY":
				case "KY2":
				case "KYBY":
				case "LY":
				case "LYBY":
					Criteria criteria1 = new Criteria();
					Criteria criteria2 = new Criteria();
					criteria1.and("gameStartTime").gte(sTime).lt(eTime);
					criteria2.and("gameEndTime").gte(sTime).lt(eTime);
					criteria.orOperator(criteria1, criteria2);
					sort = Sort.by(Direction.DESC, "gameStartTime", "gameEndTime");

					int tmpTotalCountKY = getGamePlatformKYDetailOrderDao().totalCount(criteria);

					if(tmpTotalCountKY > 0 && (list.size() < maxResults) &&
							((currentCount + tmpTotalCountKY) >= firstResult + list.size())) {
						// 只算显示未满显示笔数的,
						int currentIndex =
								tmpTotalCountKY - ((currentCount + tmpTotalCountKY) - (firstResult + list.size()));
						int toIndex = currentIndex + (maxResults - list.size());
						// 取得刚好数量的数据
						if((currentIndex + maxResults) > tmpTotalCountKY) {
							toIndex = tmpTotalCountKY;
						}
						List<GamePlatformKYDetailOrder> resultListKY =
								getGamePlatformKYDetailOrderDao().find(criteria, sort, currentIndex,
										(toIndex - currentIndex));
						setKYBean(resultListKY, tmpList);
						// List<GamePlatformDetailOrderVO> tmpVO = tmpList.subList(currentIndex,
						// toIndex);

						list.addAll(tmpList);
					}
					currentCount += tmpTotalCountKY;

					totalCount += tmpTotalCountKY;
					break;
				case "WEZR":
				case "OBDZ":
				case "OBDJ":
				case "OBTY":
				case "OBZR":
				case "OBQP":
				case "OBBY":
				case "BGZR":
				case "BGBY":
				case "PGDZ":
				case "BBZR":
				case "BBTY":
				case "BBDZ":
				case "LHDJ":
					setDateParame(criteria, "gameTime", "gamePayTime", sTimeStr, eTimeStr);
					sort = Sort.by(Direction.DESC, "gameTime", "gamePayTime");
					int tmpTotalCountGoldenF = getGamePlatformGoldenFDetailOrderDao().totalCount(criteria);
					if(tmpTotalCountGoldenF > 0 && (list.size() < maxResults) &&
							((currentCount + tmpTotalCountGoldenF) >= firstResult + list.size())) {
						// 只算显示未满显示笔数的,
						int currentIndex = tmpTotalCountGoldenF -
								((currentCount + tmpTotalCountGoldenF) - (firstResult + list.size()));
						int toIndex = currentIndex + (maxResults - list.size());
						// 取得刚好数量的数据
						if((currentIndex + maxResults) > tmpTotalCountGoldenF) {
							toIndex = tmpTotalCountGoldenF;
						}
						List<GamePlatformGoldenFDetailOrder> resultListGoldenF =
								getGamePlatformGoldenFDetailOrderDao().find(criteria, sort, currentIndex,
										(toIndex - currentIndex));
						setGoldenFBean(resultListGoldenF, tmpList);
						list.addAll(tmpList);
					}
					currentCount += tmpTotalCountGoldenF;
					totalCount += tmpTotalCountGoldenF;
					break;
				case "DG":
					criteria.and("betTime").gte(sTime).lt(eTime);
					sort = Sort.by(Direction.DESC, "betTime");
					int totalCountDG = getGamePlatformDGDetailOrderDao().totalCount(criteria);

					if(totalCountDG > 0 && (list.size() < maxResults) &&
							((currentCount + totalCountDG) >= firstResult + list.size())) {
						// 只算显示未满显示笔数的,
						int currentIndex = totalCountDG - ((currentCount + totalCountDG) - (firstResult + list.size()));
						int toIndex = currentIndex + (maxResults - list.size());
						// 取得刚好数量的数据
						if((currentIndex + maxResults) > totalCountDG) {
							toIndex = totalCountDG;
						}
						List<GamePlatformDGDetailOrder> resultListDG =
								getGamePlatformDGDetailOrderDao().find(criteria, sort, currentIndex,
										(toIndex - currentIndex));
						setDGBean(resultListDG, tmpList);
						// List<GamePlatformDetailOrderVO> tmpVO = tmpList.subList(currentIndex,
						// toIndex);

						list.addAll(tmpList);
					}
					currentCount += totalCountDG;

					totalCount += totalCountDG;
					break;
				case "AG":
				case "PT":
				case "GG":
				case "DS":
				case "AT":
				case "TS":
				case "MG":
				case "DAT":
				case "SS":
				case "BBIN":
				case "BG":
				case "PJSSC":
				case "PJGFC":
				case "BBINDZ":
				case "PTDZ":
				case "LH":
				case "OG":
				case "NEWOG":
				case "PBTY":
					criteria.and("betTime").gte(sTime).lt(eTime);
					sort = Sort.by(Direction.DESC, "betTime");
					int totalCountDefault = getGamePlayerRecordDetailDao().totalCount(criteria);

					if(totalCountDefault > 0 && (list.size() < maxResults) &&
							((currentCount + totalCountDefault) >= firstResult + list.size())) {
						// 只算显示未满显示笔数的,
						int currentIndex =
								totalCountDefault - ((currentCount + totalCountDefault) - (firstResult + list.size()));
						int toIndex = currentIndex + (maxResults - list.size());
						// 取得刚好数量的数据
						if((currentIndex + maxResults) > totalCountDefault) {
							toIndex = totalCountDefault;
						}
						List<GamePlayerRecordDetail> resultListDefault =
								getGamePlayerRecordDetailDao().find(criteria, sort, currentIndex,
										(toIndex - currentIndex));
						setDefaultRecordBean(resultListDefault, tmpList);
						// List<GamePlatformDetailOrderVO> tmpVO = tmpList.subList(currentIndex,
						// toIndex);

						list.addAll(tmpList);
					}
					currentCount += totalCountDefault;

					totalCount += totalCountDefault;
					break;
				default:
					break;
			}

		}
		list.sort(Comparator.comparing(GamePlatformDetailOrderVO::getStartDate).reversed());
		Map<String, Object> data = new HashMap<>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		return data;
	}

	@Override
	public Map<String, Object> searchGamePlatformDetailOrder(AgentPlayerInfo player, String platformCode, Date sTime,
			Date eTime, int page, int size) {
		int firstResult = page * size, maxResults = size;
		int totalCount = 0;
		List<GamePlatformDetailOrderVO> list = new ArrayList<>();
		GameSimulationPlatPlatformType gameSimulationPlatformType =
				getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
		if(null == gameSimulationPlatformType ||
				gameSimulationPlatformType.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			return data;
		}
		GameSimulationAccount gameSimulationAccount = getGameSimulationAccountDao().getByAccountId(player.getPlayerId(),
				gameSimulationPlatformType.getCode());
		if(null == gameSimulationAccount) {
			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			return data;
		}
		Criteria criteria = new Criteria();
		long playerId = gameSimulationAccount.getId();
		criteria.and("playerId").is(playerId);
		Sort sort = null;
		String sTimeStr = new Moment().fromDate(sTime).toSimpleTime();
		String eTimeStr = new Moment().fromDate(eTime).toSimpleTime();
		switch(gameSimulationPlatformType.getCode()) {
			case "FT":
				criteria.and("transDate").gte(sTime).lt(eTime);
				sort = Sort.by(Direction.DESC, "transDate");
				List<GameSportsRecordDetail> resultListFT =
						getGameSportsRecordDetailDao().find(criteria, sort, firstResult, maxResults);
				totalCount = getGameSportsRecordDetailDao().totalCount(criteria);
				setFTBean(resultListFT, list);
				break;
			case "KY":
			case "KY2":
			case "KYBY":
			case "LY":
			case "LYBY":
				Criteria criteria1 = new Criteria();
				Criteria criteria2 = new Criteria();
				criteria1.and("gameStartTime").gte(sTime).lt(eTime);
				criteria2.and("gameEndTime").gte(sTime).lt(eTime);
				criteria.orOperator(criteria1, criteria2);
				sort = Sort.by(Direction.DESC, "gameStartTime", "gameEndTime");
				List<GamePlatformKYDetailOrder> resultListKY =
						getGamePlatformKYDetailOrderDao().find(criteria, sort, firstResult, maxResults);
				totalCount = getGamePlatformKYDetailOrderDao().totalCount(criteria);
				setKYBean(resultListKY, list);
				break;
			case "WEZR":
			case "OBDZ":
			case "OBDJ":
			case "OBTY":
			case "OBZR":
			case "OBQP":
			case "OBBY":
			case "BGZR":
			case "BGBY":
			case "PGDZ":
			case "BBZR":
			case "BBTY":
			case "BBDZ":
			case "LHDJ":
				setDateParame(criteria, "gameTime", "gamePayTime", sTimeStr, eTimeStr);
				sort = Sort.by(Direction.DESC, "gameTime", "gamePayTime");
				totalCount = getGamePlatformGoldenFDetailOrderDao().totalCount(criteria);
				if(totalCount > 0) {
					List<GamePlatformGoldenFDetailOrder> resultListGoldenF =
							getGamePlatformGoldenFDetailOrderDao().find(criteria, sort, firstResult, maxResults);
					setGoldenFBean(resultListGoldenF, list);
				}
				break;
			case "DG":
				criteria.and("betTime").gte(sTime).lt(eTime);
				sort = Sort.by(Direction.DESC, "betTime");
				List<GamePlatformDGDetailOrder> resultListDG =
						getGamePlatformDGDetailOrderDao().find(criteria, sort, firstResult, maxResults);
				totalCount = getGamePlatformDGDetailOrderDao().totalCount(criteria);
				setDGBean(resultListDG, list);
				break;
			case "AG":
			case "PT":
			case "GG":
			case "DS":
			case "AT":
			case "TS":
			case "MG":
			case "DAT":
			case "SS":
			case "BBIN":
			case "BG":
			case "PJSSC":
			case "PJGFC":
			case "BBINDZ":
			case "PTDZ":
			case "LH":
			case "OG":
			case "NEWOG":
			case "PBTY":
				criteria.and("betTime").gte(sTime).lt(eTime);
				sort = Sort.by(Direction.DESC, "betTime");
				List<GamePlayerRecordDetail> resultListDefault =
						getGamePlayerRecordDetailDao().find(criteria, sort, firstResult, maxResults);
				totalCount = getGamePlayerRecordDetailDao().totalCount(criteria);
				setDefaultRecordBean(resultListDefault, list);
				break;
			default:
				break;
		}
		list.sort(Comparator.comparing(GamePlatformDetailOrderVO::getStartDate).reversed());
		Map<String, Object> data = new HashMap<>();
		data.put("totalCount", totalCount);
		data.put("list", list);
		return data;
	}

	@Override
	public List<GameIconVO> getGameIconParams(AgentPlayerInfo player, String platformCode) {
		GameSimulationAccount gameSimulationAccount = getGameSimulationAccount(player, platformCode);
		return getSimulationGameSuperService().getGameIconList(gameSimulationAccount);
	}

	/**
	 * 设置日期查询条件
	 *
	 * @param criteria
	 * @param queryStartTime
	 * @param queryEndTime
	 * @param sDate
	 * @param eDate
	 */
	private void setDateParame(Criteria criteria, String queryStartTime, String queryEndTime, String sDate,
			String eDate) {
		Criteria criteria1 = new Criteria();
		Criteria criteria2 = new Criteria();
		if(!StringUtils.isEmpty(sDate) && !StringUtils.isEmpty(eDate)) {
			Date startDate = new Moment().fromDate(sDate).toDate();
			Date endDate = new Moment().fromDate(eDate).toDate();
			criteria1.and(queryStartTime).gte(startDate).lt(endDate);
			criteria2.and(queryEndTime).gte(startDate).lt(endDate);
		}else if(!StringUtils.isEmpty(sDate)) {
			Date startDate = new Moment().fromDate(sDate).toDate();
			criteria1.and(queryStartTime).gte(startDate);
			criteria2.and(queryEndTime).gte(startDate);
		}else if(!StringUtils.isEmpty(eDate)) {
			Date endDate = new Moment().fromDate(eDate).toDate();
			criteria1.and(queryStartTime).lt(endDate);
			criteria2.and(queryEndTime).lt(endDate);
		}
		criteria.orOperator(criteria1, criteria2);
	}

	/**
	 * 组装AGbean
	 *
	 * @param resultList
	 * @param list
	 */
	private void setDefaultRecordBean(List<GamePlayerRecordDetail> resultList, List<GamePlatformDetailOrderVO> list) {
		for(GamePlayerRecordDetail tmpBean : resultList) {
			String username = "unknown";
			String platformCode = "unknown";
			String platformName = "unknown";
			String additionalData = "";
			String gameName = "";
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByPlatformUser(tmpBean.getAgentNo(), tmpBean.getPlatformCode(),
							tmpBean.getPlayerName());
			if(null != gameSimulationAccount) {
				long accountId = gameSimulationAccount.getAccountId();
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(accountId);
				username = playerInfo.getPlayerName();
			}
			GameSimulationPlatPlatformType gameSimulationPlatformType =
					getGameSimulationPlatPlatformTypeDao().getByCode(tmpBean.getAgentNo(), tmpBean.getPlatformCode());
			if(null != gameSimulationPlatformType) {
				platformCode = gameSimulationPlatformType.getCode();
				platformName = gameSimulationPlatformType.getName();
				additionalData = parseAdditionalData(tmpBean.getRecordContent(),
						gameSimulationPlatformType.getDetailDescription());
			}

			if(!StringUtils.isEmpty(tmpBean.getGameName())) {
				gameName = tmpBean.getGameName();
			}else {
				gameName = "UNKOWN";
				if(platformCode.equals("PBTY")) {
					gameName = tmpBean.getGameType();
				}
			}

			GamePlatformDetailOrderVO vo =
					new GamePlatformDetailOrderVO(platformName, tmpBean.getGameCategory(), "", tmpBean.getBetAmount(),
							tmpBean.getValidBetAmount(), tmpBean.getBonusAmount(), null, tmpBean.getBetTime(),
							tmpBean.getBonusTime(), gameName);
			vo.setPlatformCode(platformCode);
			vo.setAdditionalData(additionalData);
			list.add(vo);
		}
	}

	private String parseAdditionalData(String recordContent, String detailDescription) {
		JsonNode contentNode = JacksonUtils.toJsonNode(recordContent);
		JsonNode descNode = JacksonUtils.toJsonNode(detailDescription);
		Map<String, String> resultMap = new LinkedHashMap<>();
		if(contentNode != null && descNode != null) {
			JsonNode record_define = descNode.path("record_define");
			JsonNode ref_define = descNode.path("ref_define");
			Iterator<JsonNode> iterator = record_define.iterator();

			while(iterator.hasNext()) {
				JsonNode fieldNode = iterator.next();
				String code = fieldNode.path("code").asText();
				String name = fieldNode.path("name").asText();
				// String desc = fieldNode.path("desc").asText();
				String data_type = fieldNode.path("data_type").asText();
				String data_ref = fieldNode.path("data_ref").asText();

				String value = contentNode.path(code).asText();

				if("value".equalsIgnoreCase(data_type)) {
					resultMap.put(name, value);
				}else if("map".equalsIgnoreCase(data_type)) {
					JsonNode refNode = ref_define.path(data_ref);
					String mapValue = refNode.path(value).asText();
					if(null != mapValue && !"".equals(mapValue)) {
						resultMap.put(name, mapValue);
					}else {
						resultMap.put(name, value + "（无匹配）");
					}
				}
			}
		}
		StringBuilder buff = new StringBuilder();
		for(Entry<String, String> entry : resultMap.entrySet()) {
			String name = entry.getKey();
			String value = entry.getValue();
			buff.append(name).append("：").append(value).append("<br>");
		}
		return buff.toString();
	}

	/**
	 * 组装FTbean
	 *
	 * @param resultList
	 * @param list
	 */
	private void setFTBean(List<GameSportsRecordDetail> resultList, List<GamePlatformDetailOrderVO> list) {
		for(GameSportsRecordDetail tmpBean : resultList) {
			String username = "unknown";
			String platformCode = "unknown";
			String platformName = "unknown";
			Long playerIdtmp = tmpBean.getPlayerId();
			String gameName = "";
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByPlatformUser(tmpBean.getAgentNo(), tmpBean.getPlatformCode(),
							tmpBean.getPlayerName());
			if(null != gameSimulationAccount) {
				long accountId = gameSimulationAccount.getAccountId();
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(accountId);
				username = playerInfo.getPlayerName();
			}
			GameSimulationPlatPlatformType gameSimulationPlatformType =
					getGameSimulationPlatPlatformTypeDao().getByCode(tmpBean.getAgentNo(), tmpBean.getPlatformCode());
			if(null != gameSimulationPlatformType) {
				platformCode = gameSimulationPlatformType.getCode();
				platformName = gameSimulationPlatformType.getName();
			}
			if(!StringUtils.isEmpty(tmpBean.getGameName())) {
				gameName = tmpBean.getGameName();
			}else {
				gameName = "UNKOWN";
			}
			GamePlatformDetailOrderVO vo =
					new GamePlatformDetailOrderVO(platformName, tmpBean.getGameCategory(), "", tmpBean.getBetAmount(),
							tmpBean.getValidBetAmount(), tmpBean.getBonusAmount(), null, tmpBean.getTransDate(),
							tmpBean.getStateUpdateTs(), gameName);
			vo.setPlatformCode(platformCode);
			vo.setGameSportsRecordDetail(tmpBean);
			list.add(vo);
		}
	}

	/**
	 * 组装DGbean
	 *
	 * @param resultList
	 * @param list
	 */
	private void setDGBean(List<GamePlatformDGDetailOrder> resultList, List<GamePlatformDetailOrderVO> list) {
		for(GamePlatformDGDetailOrder tmpBean : resultList) {
			System.out.println();
			String username = "unknown";
			String platformCode = "unknown";
			String platformName = "unknown";
			String gameName = "";
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByPlatformUser(tmpBean.getAgentNo(), tmpBean.getPlatformCode(),
							tmpBean.getPlayerName());
			if(null != gameSimulationAccount) {
				long accountId = gameSimulationAccount.getAccountId();
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(accountId);
				username = playerInfo.getPlayerName();
			}
			GameSimulationPlatPlatformType gameSimulationPlatformType =
					getGameSimulationPlatPlatformTypeDao().getByCode(tmpBean.getAgentNo(), tmpBean.getPlatformCode());
			if(null != gameSimulationPlatformType) {
				platformCode = gameSimulationPlatformType.getCode();
				platformName = gameSimulationPlatformType.getName();
			}
			BigDecimal betAmount = tmpBean.getBetPoints();
			BigDecimal validBetAmount = tmpBean.getAvailableBet();
			// 盈亏 = 派彩金额 - 下注注单
			// 如果为正数为赢得奖金，如果为负数为输的奖金
			// GameSimulationGameType gameSimulationGameType = getCommonDataFactory()
			// .getGameSimulationGameTypeByGameIdAndType(tmpBean.getGameId().toString(),
			// platformCode);
			if(!StringUtils.isEmpty(tmpBean.getGameName())) {
				gameName = tmpBean.getGameName();
			}else {
				gameName = "UNKOWN";
			}

			BigDecimal bonusAmount = tmpBean.getWinOrLoss().subtract(betAmount);
			GamePlatformDetailOrderVO vo =
					new GamePlatformDetailOrderVO(platformName, tmpBean.getGameCategory(), "", betAmount,
							validBetAmount, bonusAmount, null, tmpBean.getBetTime(), tmpBean.getCalTime(), gameName);
			vo.setPlatformCode(platformCode);
			vo.setGamePlatformDGDetailOrder(tmpBean);
			list.add(vo);
		}
	}

	/**
	 * 组装KYbean
	 *
	 * @param resultList
	 * @param list
	 */
	private void setKYBean(List<GamePlatformKYDetailOrder> resultList, List<GamePlatformDetailOrderVO> list) {
		for(GamePlatformKYDetailOrder tmpBean : resultList) {
			String username = "unknown";
			String platformCode = "unknown";
			String platformName = "unknown";
			String gameName = "";
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByPlatformUser(tmpBean.getAgentNo(), tmpBean.getPlatformCode(),
							tmpBean.getPlayerName());
			if(null != gameSimulationAccount) {
				long accountId = gameSimulationAccount.getAccountId();
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(accountId);
				username = playerInfo.getPlayerName();
			}
			GameSimulationPlatPlatformType gameSimulationPlatformType =
					getGameSimulationPlatPlatformTypeDao().getByCode(tmpBean.getAgentNo(), tmpBean.getPlatformCode());
			if(null != gameSimulationPlatformType) {
				platformCode = gameSimulationPlatformType.getCode();
				platformName = gameSimulationPlatformType.getName();
			}
			// GameSimulationGameType gameSimulationGameType = getCommonDataFactory()
			// .getGameSimulationGameTypeByGameIdAndType(tmpBean.getKindID().toString(),
			// platformCode);
			if(!StringUtils.isEmpty(tmpBean.getGameName())) {
				gameName = tmpBean.getGameName();
			}else {
				gameName = "UNKOWN";
			}

			GamePlatformDetailOrderVO vo =
					new GamePlatformDetailOrderVO(platformName, tmpBean.getGameCategory(), "", tmpBean.getAllBet(),
							tmpBean.getCellScore(), tmpBean.getProfit(), null, tmpBean.getGameStartTime(),
							tmpBean.getGameEndTime(), gameName);
			vo.setPlatformCode(platformCode);
			vo.setGamePlatformKYDetailOrder(tmpBean);
			list.add(vo);
		}
	}

	/**
	 * 组装GoldenFBean
	 *
	 * @param resultList
	 * @param list
	 */
	private void setGoldenFBean(List<GamePlatformGoldenFDetailOrder> resultList, List<GamePlatformDetailOrderVO> list) {
		for(GamePlatformGoldenFDetailOrder tmpBean : resultList) {
			String username = "unknown";
			String platformCode = "unknown";
			String platformName = "unknown";
			String gameName = "";
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByPlatformUser(tmpBean.getAgentNo(), tmpBean.getPlatformCode(),
							tmpBean.getPlayerName());
			if(null != gameSimulationAccount) {
				long accountId = gameSimulationAccount.getAccountId();
				AgentPlayerInfo playerInfo = getAgentPlayerInfoDao().getById(accountId);
				username = playerInfo.getPlayerName();
			}
			GameSimulationPlatPlatformType gameSimulationPlatformType =
					getGameSimulationPlatPlatformTypeDao().getByCode(tmpBean.getAgentNo(), tmpBean.getPlatformCode());
			if(null != gameSimulationPlatformType) {
				platformCode = gameSimulationPlatformType.getCode();
				platformName = gameSimulationPlatformType.getName();
			}
			if(!StringUtils.isEmpty(tmpBean.getGameName())) {
				gameName = tmpBean.getGameName();
			}else {
				gameName = "UNKOWN";
			}
			GamePlatformDetailOrderVO vo =
					new GamePlatformDetailOrderVO(platformName, tmpBean.getGameCategory(), "", tmpBean.getBetAmount(),
							tmpBean.getBetAmount(), tmpBean.getWlAmount(), null, tmpBean.getGameTime(),
							tmpBean.getGameTime(), gameName);
			vo.setPlatformCode(platformCode);
			vo.setGamePlatformGoldenFDetailOrder(tmpBean);
			list.add(vo);
		}
	}

	@Override
	public Map<String, Object> getSubGameList(AgentPlayerInfo player, String platformCode, int pageNum, int pageSize,
			String gameName) {
		return getSimulationGameSuperService().getSubGameList(player, platformCode, pageNum, pageSize, gameName);
	}

	@Override
	public String getForwardSubGameUrl(GameSimulationAccount gameSimulationAccount, AgentPlayerInfo player,
			String platformCode, String gameCode) {
		if(null == gameSimulationAccount || GameSimulationAccount.STATUS_ACTIVE != gameSimulationAccount.getStatus()) {
			throw newException("124-02");
		}
		return getSimulationGameSuperService().getSubGameUrl(gameSimulationAccount, platformCode, gameCode);
	}

	@Override
	public List<GameSimulationQueryBalanceVO> queryBalanceAll(AgentPlayerInfo player) {
		List<GameSimulationQueryBalanceVO> data = new ArrayList<>();
		// 查询玩家信息

		GameSimulation gameSimulation = getGameSimulationDao().getByAgentNo(player.getAgentNo());
		if(null == gameSimulation || gameSimulation.getStatus() != GameSimulation.STATUS_ACTIVE) {
			log.error("GameSimulation is null or inactive");
			return data;
		}
		String url = gameSimulation.getApiUrl() + OutThirdUrlEnums.UPDATE_BALANCE_ALL.getUrl();

		Map<String, String> params = new HashMap<>();
		params.put("merCode", gameSimulation.getMerchantId());
		params.put("username", player.getPlayerName());
		String signature = PlatformGameUtils.getSign(params, gameSimulation.getMd5Key());
		params.put("signature", signature);

		String jsonResult = PlatformGameUtils.sendPost(url, params);
		if(null == jsonResult) {
			log.error("get-info failed jsonResult is null");
			return data;
		}
		log.info("update-balance-all:" + jsonResult);
		JsonNode rootNode = JacksonUtils.toJsonNode(jsonResult);
		JsonNode errorNode = rootNode.path("error");
		if(null == errorNode || errorNode.isMissingNode()) {
			log.error("get-info error, no error node, the result json is: \n" + jsonResult);
			return data;
		}
		int resultType = errorNode.asInt();

		if(PlatformGameUtils.RESULT_TYPE_SUCCESS == resultType) {// 获取成功
			JsonNode dataNode = rootNode.path("data");
			JsonNode codeNode = rootNode.path("code");
			int returnCode = codeNode.asInt();
			if(!codeNode.isMissingNode() && 200 == returnCode) {
				String listNodeStr = dataNode.toString();
				List<GameSimulationQueryBalanceApiVO> detailList =
						JacksonUtils.toList(listNodeStr, GameSimulationQueryBalanceApiVO.class);
				for(GameSimulationQueryBalanceApiVO vo : detailList) {
					try {
						GameSimulationQueryBalanceVO updateResult =
								getSimulationGameSuperService().updateBalance(vo, player);
						if(updateResult != null) {
							data.add(updateResult);
						}
					}catch(Exception e) {
						log.error(vo.getPlatformCode() + "更新余额保存失败：", e);
					}
				}

				// 将api没有回传加入回传数据
				List<GameSimulationAccount> gameSimulationAccounts =
						getGameSimulationAccountDao().getAllAccountsByAccountId(player.getPlayerId());
				for(GameSimulationAccount gameSimulationAccount : gameSimulationAccounts) {
					boolean add = true;
					// 如果没开通不加入
					if(GameSimulationAccount.STATUS_ACTIVE != gameSimulationAccount.getStatus()) {
						continue;
					}
					for(GameSimulationQueryBalanceVO vo : data) {
						if(gameSimulationAccount.getPlatformCode().equals(vo.getCode())) {
							add = false;
							break;
						}
					}
					if(add) {
						GameSimulationPlatPlatformType gsppt =
								getGameSimulationPlatPlatformTypeDao().getByCode(gameSimulationAccount.getAgentNo(),
										gameSimulationAccount.getPlatformCode());
						// 回传目前数据库的
						data.add(new GameSimulationQueryBalanceVO(gsppt.getCode(), gsppt.getName(),
								gameSimulationAccount.getBalance(),
								new Moment().fromDate(gameSimulationAccount.getUpdateTime()).toSimpleTime()));
					}
				}
			}else {
				log.error("更新余额返回失败，返回数据：{}", jsonResult);
				return data;
			}
		}else {
			String code = rootNode.path("code").asText();
			String message = rootNode.path("message").asText();
			log.error("更新余额失败，失败原因：[{}|{}]", code, message);
			return data;
		}
		return data;
	}
}
