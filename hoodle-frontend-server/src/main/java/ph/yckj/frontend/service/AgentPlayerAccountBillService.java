package ph.yckj.frontend.service;

import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerGameOrder;
import sy.hoodle.base.common.entity.AgentPlayerInfo;

import java.math.BigDecimal;

public interface AgentPlayerAccountBillService {
	// 添加账变记录
	public boolean addAgentPlayerAccountBill(AgentPlayerInfo playerInfo, BigDecimal amount,
											 BigDecimal balanceBeforeAmount, BigDecimal balanceAfterAmount, int billType, String referenceBillNo,
											 String createBy, String billRemark);

	// 添加账变记录
	public boolean addAgentPlayerAccountBill(AgentPlayerInfo playerInfo, BigDecimal amount,
			BigDecimal balanceBeforeAmount, BigDecimal balanceAfterAmount, int billType, String referenceBillNo,
			String createBy, String billRemark);

	// 彩票游戏投注消费
	boolean addAgentPlayerGameOrderConsumeBill(AgentPlayerInfo player, AgentPlayerGameOrder gameLotteryOrder);

	// 彩票游戏投注撤单
	boolean addAgentPlayerOrderCancelBill(AgentPlayerInfo player, AgentPlayerGameOrder order);

	boolean addAgentPlayerTransferBill(AgentPlayerInfo player, String transferType, BigDecimal amount);

	// 送礼物消费
	AgentPlayerAccountBill addAgentPlayerBillBySendGift(AgentPlayerInfo player, BigDecimal giftAmountTotal);
}
