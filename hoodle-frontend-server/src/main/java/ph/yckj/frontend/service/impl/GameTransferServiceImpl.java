package ph.yckj.frontend.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ph.yckj.frontend.service.GameTransferService;
import ph.yckj.frontend.util.AbstractService;
import sy.hoodle.base.common.entity.AgentPlayerAccountBill;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.GameSimulationTransfer;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.common.service.PlatformAgentPlayerCommonService;

import java.math.BigDecimal;

@Service("gameTransferService")
public class GameTransferServiceImpl extends AbstractService implements GameTransferService {

    @Autowired
    private PlatformAgentPlayerCommonService platformAgentPlayerCommonService;

    @Autowired
    private AgentPlayerInfoLockService agentPlayerInfoLockService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealWithTransferResult(AgentPlayerInfo player, GameSimulationTransfer gameSimulationTransfer) {
        // 更新中心账户转账记录：AccountTransfer
        getGameSimulationTransferDao().updateDetail(gameSimulationTransfer);

        if(GameSimulationTransfer.STATUS_COMPLETED == gameSimulationTransfer.getStatus()) {
            // 🖤 黑色部分：账变逻辑（给中心账户加钱）
            final BigDecimal amount = gameSimulationTransfer.getAmount();
            String remark = gameSimulationTransfer.getPlatformName() + "转账到中心钱包，金额：" + amount;

            agentPlayerInfoLockService.incr(player.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_OUT_THIRD_WITHDRAW,
                    remark, player.getPlayerId(), amount, BigDecimal.ZERO,
                    (beforePlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterPlayer, agentPlayerAccountBill) -> {
                        // 账变成功回调
                        log.info("一键回收转账到中心成功：username：{}，account id：{}，转前余额：{}，转账金额：{}", beforePlayer.getPlayerName(),
                                beforePlayer.getPlayerId(), beforePlayer.getPlayerAvailableBalance(), amount);
                    });
        }
    }
}
