package ph.yckj.frontend.service;

import ph.yckj.frontend.vo.third.GameIconVO;
import ph.yckj.frontend.vo.third.GameSimulationQueryBalanceVO;
import ph.yckj.frontend.vo.third.ThirdGameUrlVo;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.GameSimulationAccount;
import sy.hoodle.base.common.entity.GameSimulationPlatPlatformType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GameSimulationService {

	/**
	 * 开通三方游戏
	 *
	 * @param player 玩家信息
	 * @param platform 平台场馆信息
	 * @return
	 */
	GameSimulationAccount registerSimulationGame(AgentPlayerInfo player, GameSimulationPlatPlatformType platform);

	ThirdGameUrlVo getForwardGameParams(GameSimulationAccount gameSimulationAccount, AgentPlayerInfo player,
			String platformCode, String gameCode);

	Map<String, Object> getForwardGameParamsAPP(GameSimulationAccount gameSimulationAccount, AgentPlayerInfo player,
			String platformCode, String gameCode);

	GameSimulationAccount updateAccountBalance(AgentPlayerInfo player, String platformCode);

	int balanceLottery2Game(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, BigDecimal amount,
			boolean isTransferAll);

	// 新增：批量余额转账方法（参考apply-up-ded-transfer模式）
	int balanceLottery2GameV2(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, BigDecimal amount,
			boolean isTransferAll);

	int balanceGame2Lottery(AgentPlayerInfo player, GameSimulationPlatPlatformType platform, BigDecimal amount,
			boolean isTransferAll);

	int balanceGameToLottery(AgentPlayerInfo player, String fromPlatformCode, BigDecimal amount, boolean isTransferAll);

	/**
	 * 判断该会员是否被允许玩游戏游戏
	 */
	boolean allowAccountBaccarat(long accountId);

	Map<String, Object> searchGamePlatformDetailOrder(AgentPlayerInfo player, String platformCode, Date sTime,
			Date eTime, int page, int size);

	Map<String, Object> searchGamePlatformDetailOrder(AgentPlayerInfo player, Date sTime, Date eTime, int page,
			int size);

	/**
	 * 获取游戏二级菜单
	 */
	List<GameIconVO> getGameIconParams(AgentPlayerInfo player, String platformCode);

	// 取得子游戏列表
	Map<String, Object> getSubGameList(AgentPlayerInfo player, String platformCode, int pageNum, int pageSize,
			String gameName);

	String getForwardSubGameUrl(GameSimulationAccount gameSimulationAccount, AgentPlayerInfo player,
			String platformCode, String gameCode);

	// 查询全部游戏余额
	List<GameSimulationQueryBalanceVO> queryBalanceAll(AgentPlayerInfo player);
}
