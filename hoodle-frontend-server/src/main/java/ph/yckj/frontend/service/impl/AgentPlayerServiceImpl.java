package ph.yckj.frontend.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.groups.Default;

import cn.hutool.core.collection.CollUtil;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import lombok.extern.slf4j.Slf4j;
import myutil.*;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;
import ph.yckj.common.util.ServiceException;
import ph.yckj.frontend.service.PasswordFailedCountService;
import ph.yckj.frontend.vo.agent.AddDownUserPointRangeVO;
import ph.yckj.frontend.vo.agent.player.AllowedUserTypeVO;
import sy.hoodle.base.common.dto.contract.ContractDailyConditions;
import sy.hoodle.base.common.dto.contract.ContractDailyRules;
import sy.hoodle.base.common.dto.contract.ContractPeriodConditions;
import sy.hoodle.base.common.dto.contract.ContractPeriodRules;
import sy.hoodle.base.common.entity.*;
import sy.hoodle.base.common.service.balance.AgentPlayerInfoLockService;
import sy.hoodle.base.common.service.balance.vo.BalanceChangeDirection;
import sy.hoodle.base.common.service.balance.vo.PlayerBalanceUpdateParams;
import ph.yckj.common.util.ChineseNicknameGenerator;
import ph.yckj.common.util.WebJson;
import ph.yckj.frontend.AgentPlayerType;
import ph.yckj.frontend.AgentType;
import ph.yckj.frontend.config.TrustAnyTrustManager;
import ph.yckj.frontend.service.AgentPlayerService;
import ph.yckj.frontend.util.AbstractService;
import ph.yckj.frontend.vo.LoginVo;
import ph.yckj.frontend.vo.RegisterVo;
import ph.yckj.frontend.vo.agent.AddUserVo;


@Slf4j
@Service
@Transactional
@SuppressWarnings("restriction")
public class AgentPlayerServiceImpl extends AbstractService implements AgentPlayerService {
	@Autowired
	private PasswordFailedCountService passwordFailedCountService;

	@Autowired
	private AgentPlayerInfoLockService agentPlayerInfoLockService;

	/**
	 * 上级给下级下分
	 *
	 * @param upPlayer
	 * @param downPlayer
	 * @param amount
	 * @param withdrawPassword
	 * @return
	 */
	@Override
	// 因为类上有事务,账变拒绝外部事务嵌套,标注此事务,这个方法将以无事务的方式运行
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyUpDedTransfer(AgentPlayerInfo upPlayer, AgentPlayerInfo downPlayer, BigDecimal amount,
			String withdrawPassword, String remarks) {

		String outRemarks = String.format("上级 %s 给下级 %s 下分", upPlayer.getPlayerName(), downPlayer.getPlayerName());
		String inRemarks = String.format("上级 %s 给下级 %s 下分", upPlayer.getPlayerName(), downPlayer.getPlayerName());

		if (ProFrontendBigDecimalUtil.oneMaxOneMinOrEq(BigDecimal.ZERO, amount)) {
			// ERR:转账金额填写错误
			throw newException("110-02");
		}
		if (StringUtils.isEmpty(upPlayer.getWithdrawPassword())) {
			// ERR:没有设置资金密码
			throw newException("107-02");
		}
		// 验证资金密码正确
		if (!CipherUtils.verify(upPlayer.getWithdrawPassword(), withdrawPassword)) {
			// ERR:资金密码输入错误
			throw newException("104-01");
		}
		// 验证账户锁定时间
		Date accountLockTime = downPlayer.getLockTime();
		if (accountLockTime != null) {
			Moment lockMoment = new Moment().fromDate(accountLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				// ERR:账户已经锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				Map<String, Object> prams = new HashMap<>();
				prams.put("time", time);
				throw newException("111-09", prams);
			}
		}
		// 验证中心账户余额
		if (ProFrontendBigDecimalUtil.oneMaxOneMin(amount, downPlayer.getPlayerAvailableBalance())) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		if (!StringUtils.isEmpty(remarks)) {
			outRemarks += "：" + remarks;
			inRemarks += "：" + remarks;
		}
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
				// 入账
				new PlayerBalanceUpdateParams(upPlayer.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,
						inRemarks, upPlayer.getPlayerId(), amount, amount.negate(),
						BalanceChangeDirection.INCR,
						(beforeUpPlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterUpPlayer,
								agentPlayerAccountBill) -> {
							// 给目标中心账户用户加提款限制
							boolean updateToLimit = getPlayerWithdrawLimitDao()
									.increaseLotteryBalance(upPlayer.getPlayerId(), changePlayerAvailableBalance);
							if (!updateToLimit) {
								throw new IllegalArgumentException();
							}
						}),
				// 出账
				new PlayerBalanceUpdateParams(upPlayer.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,
						outRemarks, downPlayer.getPlayerId(), amount.negate(), amount,
						BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
								changePlayerBlockedBalance, afterDownPlayer, agentPlayerAccountBill) -> {
							// 扣除我的中心账户提款限制
							boolean updateFromLimit = getPlayerWithdrawLimitDao()
									.increaseLotteryBalance(downPlayer.getPlayerId(), changePlayerAvailableBalance);
							if (!updateFromLimit) {
								throw new IllegalArgumentException();
							}
						}));
		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		return true;
	}



	/**
	 * 上级给下级下分
	 *
	 * @param upPlayer
	 * @param downPlayer
	 * @param amount
	 * @param withdrawPassword
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyUpDedTransferV2(HttpServletRequest request, AgentPlayerInfo upPlayer, AgentPlayerInfo downPlayer, BigDecimal amount,
									  String withdrawPassword, String remarks) {
		if (ProFrontendBigDecimalUtil.oneMaxOneMinOrEq(BigDecimal.ZERO, amount)) {
			// ERR:转账金额填写错误
			throw newException("110-02");
		}
		if (StringUtils.isEmpty(upPlayer.getWithdrawPassword())) {
			// ERR:没有设置资金密码
			throw newException("107-02");
		}
		// 验证资金密码正确
		verifyPaymentPassword(request, upPlayer, withdrawPassword);
		// 验证账户锁定时间
		Date accountLockTime = downPlayer.getLockTime();
		if (accountLockTime != null) {
			Moment lockMoment = new Moment().fromDate(accountLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if (isNotTime) {
				// ERR:账户已经锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				Map<String, Object> prams = new HashMap<>();
				prams.put("time", time);
				throw newException("111-09", prams);
			}
		}
		// 验证中心账户余额
		if (ProFrontendBigDecimalUtil.oneMaxOneMin(amount, downPlayer.getPlayerAvailableBalance())) {
			// ERR:账户余额不足
			throw newException("106-01");
		}

		String outRemarks = String.format("上级 %s 给下级 %s 下分", upPlayer.getPlayerName(), downPlayer.getPlayerName());
		String inRemarks = String.format("上级 %s 给下级 %s 下分", upPlayer.getPlayerName(), downPlayer.getPlayerName());

		if (!StringUtils.isEmpty(remarks)) {
			outRemarks += "：" + remarks;
			inRemarks += "：" + remarks;
		}
		String billno = RandomUtils.fromTime16();
		String tempInRemarks = inRemarks;
		String tempOutRemarks = outRemarks;
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
				// 入账
				new PlayerBalanceUpdateParams(upPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempInRemarks, upPlayer.getPlayerId(), amount, amount.negate(),
						BalanceChangeDirection.INCR,
						(beforeUpPlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterUpPlayer,agentPlayerAccountBill) -> {
							// 给目标中心账户用户加提款限制
							boolean updateToLimit = getPlayerWithdrawLimitDao()
									.increaseLotteryBalance(upPlayer.getPlayerId(), changePlayerAvailableBalance);
							if (!updateToLimit) {
								throw new IllegalArgumentException();
							}
						}),
				// 出账
				new PlayerBalanceUpdateParams(upPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempOutRemarks, downPlayer.getPlayerId(), amount.negate(), amount,
						BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
													  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
					// 扣除我的中心账户提款限制
					boolean updateFromLimit = getPlayerWithdrawLimitDao()
							.increaseLotteryBalance(downPlayer.getPlayerId(), changePlayerAvailableBalance);
					if (!updateFromLimit) {
						throw new IllegalArgumentException();
					}
				}));
		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		return true;
	}

	@Override
	public AgentPlayerInfo initPlayer(String agentNo, Integer agentType, String agentName, Long pid, String pids,
			String playerName, Integer playerType, String playerPassword) {
		return initPlayer(agentNo, agentType, agentName, pid, pids, playerName, playerType, playerPassword, "", "", 0D);
	}

	@Override
	public AgentPlayerInfo initPlayer(String agentNo, Integer agentType, String agentName, Long pid, String pids,
			String playerName, Integer playerType, String playerPassword, String email, String telephone,
			Double point) {
		AgentPlayerInfo player = new AgentPlayerInfo();
		player.setAgentNo(agentNo);
		player.setAgentType(agentType);
		player.setAgentName(agentName);
		player.setPlayerName(playerName);
		player.setPlayerPassword(playerPassword);
		player.setPlayerTelegramId(0L);
		player.setPlayerBlockedBalance(BigDecimal.ZERO);
		player.setPlayerAvailableBalance(BigDecimal.ZERO);
		player.setPid(pid);
		player.setPids(pids);
		player.setPlayerType(playerType);
		player.setPlayerNick(ChineseNicknameGenerator.generateNickname());
		player.setWithdrawName("");
		player.setWithdrawLimit(0);
		player.setWithdrawPassword("");
		player.setWithdrawLimitBalance(BigDecimal.ZERO);
		player.setDevice("");
		player.setPlayerStatus(0);
		player.setOnlineStatus(0);
		player.setMarkersStatus(0);
		player.setSkinId(0);
		player.setMusicId(0);
		player.setLanguageId(0);
		player.setUrlAuthKey("");
		player.setPlayerWealth(0);
		player.setPlayerLevelId(0);
		player.setGoogleKey("");
		player.setGoogleBind(0);
		player.setGoogleLogin(0);
		player.setLockTime(null);
		player.setSessionId("");
		player.setLastLoginTime(null);
		player.setSex(0);
		player.setEmail(email);
		player.setBirthday("");
		player.setTelephone(telephone);
		player.setOutThirdAutoTransfer(AgentPlayerInfo.AUTO_TRANSFER_ON);
		player.setCreateBy(player.getPlayerName());
		player.setCreateTime(new Moment().toTimestamp());
		player.setUpdateTime(player.getCreateTime());
		player.setIsDelete(0);
		player.setAllowTransfer(AgentPlayerInfo.ALLOW_TRANSFER_ON);
		player.setPoint(point);
		player.setLotteryCode(getLotteryCode(agentNo, point));
		AgentPlayerAccountType accountType = getAccountType(agentNo, playerType, pids, player.getLotteryCode());
		if(accountType == null) {
			throw newException("101-05");
		}
		player.setAccountType(accountType.getCode());
		player.setEqualLevel(testEqualCode(player, accountType));
		return player;
	}

	private AgentPlayerAccountType getAccountType(String agentNo, int playerType, String pids, int lotteryCode) {
		AgentPlayerAccountType accountType;
		if(AgentPlayerInfo.PLAYER_TYPE_PLAYER == playerType) {
			// 获取玩家的用户类型
			return getAgentPlayerAccountTypeDao().getByCode(agentNo, AgentPlayerAccountType.CODE_PLAYER);
		}
		// 用户层级
		int level = StringIdUtils.toArray(pids).length;
		accountType = getAgentPlayerAccountTypeDao().getAccountType(agentNo, lotteryCode, level);
		if(accountType == null) {
			// 查询默认用户类型
			accountType = getAgentPlayerAccountTypeDao().getByCode(agentNo, AgentPlayerAccountType.CODE_AGENT);
		}
		return accountType;
	}

	/**
	 * 验证同级开号
	 *
	 * @param newPlayer 新用户
	 * @param accountType 用户类型
	 * @return
	 */
	private Integer testEqualCode(AgentPlayerInfo newPlayer, AgentPlayerAccountType accountType) {
		// 计算奖级
		int lotteryCode = getLotteryCode(newPlayer.getAgentNo(), newPlayer.getPoint());

		if(newPlayer.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_AGENT) {
			// 获取系统最高奖级
			PlatformConfig autoEqualCodeConfig =
					getPlatformConfigDao().getPlatformConfigByGroupAndKey(newPlayer.getAgentNo(), "ACCOUNT",
							"AUTO_EQUAL_CODE");
			if(autoEqualCodeConfig != null) {
				String autoEqualCode = autoEqualCodeConfig.getValue();
				if(!StringUtils.isEmpty(autoEqualCode)) {
					String[] array = autoEqualCode.split(",");
					for(String tmpCode : array) {
						if(Integer.parseInt(tmpCode) == lotteryCode) {
							return AgentPlayerInfo.EQUAL_LEVEL_ON;
						}
					}
				}
			}
			if(AgentPlayerAccountType.EQUAL_LEVLEL_TRUE == accountType.getEqualLevel()) {
				return AgentPlayerInfo.EQUAL_LEVEL_ON;
			}
		}

		return AgentPlayerInfo.EQUAL_LEVEL_OFF;
	}

	public String createGoogleAuth(AgentPlayerInfo player, String agentName) {
		GoogleAuthenticatorKey credentials = GoogleAuthUtils.createCredentials();
		String googleKey = credentials.getKey();
		boolean updateGoogleKey = getAgentPlayerInfoDao().updateGoogleKey(player.getPlayerId(), googleKey);
		if (!updateGoogleKey) {
			throw new IllegalArgumentException();
		}
		String username = player.getPlayerName();
		String authUrl = GoogleAuthUtils.getOtpAuthURL(agentName, username, credentials);
		if (StringUtils.isEmpty(authUrl)) {
			throw new IllegalArgumentException();
		}
		return authUrl;
	}

	/**
	 * 账号登录
	 */
	@Override
	public WebJson login(HttpServletRequest request, LoginVo body) {
		WebJson webJson = new WebJson();
		AgentInfo agent = getAgent(request);
		String agentNo = agent.getAgentNo();
		String agentName = agent.getAgentName();
		checkValidLoginAndRegisterVo(body, true);
		AgentPlayerInfo player = null;
		String email = body.getEmail();
		String username = body.getUsername();
		String password = body.getPassword();
		String telephone = body.getTelephone();
		String countryCode = body.getCountryCode();
		String verifyCode = body.getVerifyCode();
		String ip = HttpUtils.getRemoteAddr(request);
		String address = MonipdbUtils.getIp2Region(ip);
		String url = request.getHeader("Referer");
		String userAgent = request.getHeader("User-Agent");
		if (!StringUtils.isEmpty(url)) {
			url = HtmlUtils.htmlEscape(url);
		}
		String client = HttpUtils.formatClient(userAgent);
		Moment moment = new Moment();
		int type = body.getType();
		String receiveBy = "";
		if (LoginVo.EMAIL == type) {
			receiveBy = getToolsService().encrypt(email);
			// 邮箱加密
			player = getAgentPlayerInfoDao().getByEmail(agentNo, receiveBy);
			if (null == player) {
				throw newException("-1", "该电子邮箱未被注册");
			} else {
				if (AgentPlayerInfo.PLAYER_STATUS_FORBIDDEN == player.getPlayerStatus()) {
					throw newException("0-5");
				}
				List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, receiveBy);
				if (CollectionUtils.isEmpty(verifyCodes)) {
					throw newException("-1", "邮箱验证码输入错误！");
				}
				PlayerVerifyCode verify = verifyCodes.get(0);
				if (!(null != verify && verify.getVerifyCode().equals(verifyCode))) {
					throw newException("-1", "邮箱验证码输入错误！");
				} else {
					getPlayerVerifyCodeDao().deleteByParam(type, agentNo, receiveBy);
				}
			}
		} else if (LoginVo.TELEPHONE == type) {
			PlatformCountry byAgentNoAndCountryCode = getPlatformCountryDao().getByAgentNoAndCountryCode(agentNo, countryCode);
			if (byAgentNoAndCountryCode == null) {
				throw newException("-1", "暂不支持当前国家或地区");
			}

			if(byAgentNoAndCountryCode.getEnable() == 0){
				throw newException("-1", "当前国家或地区已被禁止使用");
			}

			receiveBy = getToolsService().encrypt(telephone);
			player = getAgentPlayerInfoDao().getByTelephone(agentNo, countryCode, receiveBy);
			if (null == player) {
				throw newException("-1", "该联系电话未被注册");
			} else {
				if (AgentPlayerInfo.PLAYER_STATUS_FORBIDDEN == player.getPlayerStatus()) {
					throw newException("0-5");
				}
				List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, receiveBy);
				if (CollectionUtils.isEmpty(verifyCodes)) {
					throw newException("-1", "短信验证码输入错误！");
				}
				PlayerVerifyCode verify = verifyCodes.get(0);
				if (!(null != verify && verify.getVerifyCode().equals(verifyCode))) {
					throw newException("-1", "短信验证码输入错误！");
				} else {
					getPlayerVerifyCodeDao().deleteByParam(type, agentNo, receiveBy);
				}
			}
		} else if (LoginVo.USERNAME == type) {
			player = getAgentPlayerInfoDao().getByPlayerName(agentNo, username);
			if (null == player) {
				throw newException("-1", "用户名或登录密码输入错误");
			} else {
				if (AgentPlayerInfo.PLAYER_STATUS_FORBIDDEN == player.getPlayerStatus()) {
					throw newException("0-5");
				}
				// 验证密码
				boolean verifyPassword = CipherUtils.verify(player.getPlayerPassword(), password);
				if (!verifyPassword) {
					Long playerId = player.getPlayerId();
					String remarks = "登录密码输入错误";
					PlayerLoginLog loginLog = new PlayerLoginLog(null, agentNo, agentName, playerId, ip, address,
							client, url, moment.toDate(), remarks, false);
					getPlayerLoginLogDao().save(loginLog);
					throw newException("-1", "用户名或登录密码输入错误");
				}
				// 验证Google验证码
				Map<String, Object> data = checkGoogleVerifyCode(player, verifyCode);
				if (null != data) {
					webJson.setData(data);
					setSuccess(webJson);
					return webJson;
				}
			}
		}
		Long playerId = player.getPlayerId();
		String sessionId = HttpUtils.formatSessionId(request);
		// 设置用户在线
		boolean online = getAgentPlayerInfoDao().setOnline(playerId, client, sessionId, moment.toTimestamp());
		if (!online) {
			throw new IllegalArgumentException();
		}
		PlayerLoginLog loginLog = new PlayerLoginLog(agentNo, agentName, playerId, ip, address, client, url,
				moment.toTimestamp());
		getPlayerLoginLogDao().save(loginLog);
		// 保存用户到会话信息
		setSessionUser(request, player);
		webJson.setData(loginLog);
		setSuccess(webJson);
		return webJson;
	}









	/**
	 * 账号登录
	 */
	@Override
	public WebJson login_v2(HttpServletRequest request, LoginVo body) {
		WebJson webJson = new WebJson();
		AgentInfo agent = getAgent(request);
		String agentNo = agent.getAgentNo();
		String agentName = agent.getAgentName();
		checkValidLoginAndRegisterVo(body, true);
		AgentPlayerInfo player = null;
		String email = body.getEmail();
		String username = body.getUsername();
		String password = body.getPassword();
		String telephone = body.getTelephone();
		String countryCode = body.getCountryCode();
		String verifyCode = body.getVerifyCode();
		String ip = HttpUtils.getRemoteAddr(request);
		String address = MonipdbUtils.getIp2Region(ip);
		String url = request.getHeader("Referer");
		String userAgent = request.getHeader("User-Agent");
		if (!StringUtils.isEmpty(url)) {
			url = HtmlUtils.htmlEscape(url);
		}
		String client = HttpUtils.formatClient(userAgent);
		Moment moment = new Moment();
		int type = body.getType();
		String receiveBy = "";
		if (LoginVo.EMAIL == type) {
			receiveBy = getToolsService().encrypt(email);
			// 邮箱加密
			player = getAgentPlayerInfoDao().getByEmail(agentNo, receiveBy);
			if (null == player) {
				throw newException("-1", "该电子邮箱未被注册");
			} else {
				if (AgentPlayerInfo.PLAYER_STATUS_FORBIDDEN == player.getPlayerStatus()) {
					throw newException("0-5");
				}
				List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, receiveBy);
				if (CollectionUtils.isEmpty(verifyCodes)) {
					throw newException("-1", "邮箱验证码输入错误！");
				}
				PlayerVerifyCode verify = verifyCodes.get(0);
				if (!(null != verify && verify.getVerifyCode().equals(verifyCode))) {
					throw newException("-1", "邮箱验证码输入错误！");
				} else {
					getPlayerVerifyCodeDao().deleteByParam(type, agentNo, receiveBy);
				}
			}
		} else if (LoginVo.TELEPHONE == type) {
			PlatformCountry byAgentNoAndCountryCode = getPlatformCountryDao().getByAgentNoAndCountryCode(agentNo, countryCode);
			if (byAgentNoAndCountryCode == null) {
				throw newException("-1", "暂不支持当前国家或地区");
			}

			if(byAgentNoAndCountryCode.getEnable() == 0){
				throw newException("-1", "当前国家或地区已被禁止使用");
			}

			receiveBy = getToolsService().encrypt(telephone);
			player = getAgentPlayerInfoDao().getByTelephone(agentNo, countryCode, receiveBy);
			if (null == player) {
				throw newException("-1", "该联系电话未被注册");
			} else {
				if (AgentPlayerInfo.PLAYER_STATUS_FORBIDDEN == player.getPlayerStatus()) {
					throw newException("0-5");
				}
				List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, receiveBy);
				if (CollectionUtils.isEmpty(verifyCodes)) {
					throw newException("-1", "短信验证码输入错误！");
				}
				PlayerVerifyCode verify = verifyCodes.get(0);
				if (!(null != verify && verify.getVerifyCode().equals(verifyCode))) {
					throw newException("-1", "短信验证码输入错误！");
				} else {
					getPlayerVerifyCodeDao().deleteByParam(type, agentNo, receiveBy);
				}
			}
		} else if (LoginVo.USERNAME == type) {
			player = getAgentPlayerInfoDao().getByPlayerName(agentNo, username);
			if (null == player) {
				throw newException("-1", "用户名或登录密码输入错误");
			} else {
				if (AgentPlayerInfo.PLAYER_STATUS_FORBIDDEN == player.getPlayerStatus()) {
					throw newException("0-5");
				}

				// 获取agent-uid
				String agentUid = request.getHeader("agent-uid");
				if (StringUtils.isEmpty(agentUid)) {
					throw newException("-1", "缺少agent-uid请求头");
				}
				Long playerId = player.getPlayerId();

				PlatformConfig platformConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo,"ACCOUNT","LOGIN_FAILED_NUM");
				int currentFailedNum = (player.getLoginFailedNum() == null) ? 0 : player.getLoginFailedNum();
				int maxFailedNum = 0;
				if (platformConfig != null && platformConfig.getValue() != null) {
					maxFailedNum = Integer.parseInt(platformConfig.getValue());
					if (maxFailedNum > 0 && currentFailedNum >= maxFailedNum) {
						throw newException("-1", "连续登录错误次数已达上限" + maxFailedNum + "次");
					}
				}

				// 解密密码
				if (!StringUtils.isEmpty(body.getPassword())) {
					try {
						password = AESUtils.decrypt(password, agentUid);
					} catch (Exception e) {
						webJson.setCode("-1");
						if(maxFailedNum > 0){
							getAgentPlayerInfoDao().updateAgentPlayerInfoLoginFailedNum(playerId, currentFailedNum + 1);
							webJson.setMessage("登录密码输入错误，已错误" + (currentFailedNum + 1) +"次，" + maxFailedNum + "次将锁住账号");
						}else {
							webJson.setMessage("登录密码输入错误");
						}
						return webJson;
					}
				}

				// 验证密码
				boolean verifyPassword = CipherUtils.verify(player.getPlayerPassword(), password);
				if (!verifyPassword) {
					String remarks = "登录密码输入错误";
					PlayerLoginLog loginLog = new PlayerLoginLog(null, agentNo, agentName, playerId, ip, address,
							client, url, moment.toDate(), remarks, false);
					getPlayerLoginLogDao().save(loginLog);
					webJson.setCode("-1");
					if(maxFailedNum > 0){
						getAgentPlayerInfoDao().updateAgentPlayerInfoLoginFailedNum(playerId, currentFailedNum + 1);
						webJson.setMessage("登录密码输入错误，已错误" + (currentFailedNum + 1) +"次，" + maxFailedNum + "次将锁住账号");
					}else {
						webJson.setMessage("登录密码输入错误");
					}
					return webJson;
				}else {
					getAgentPlayerInfoDao().updateAgentPlayerInfoLoginFailedNum(playerId, 0);
				}
				// 验证Google验证码
				Map<String, Object> data = checkGoogleVerifyCode(player, verifyCode);
				if (null != data) {
					webJson.setData(data);
					setSuccess(webJson);
					return webJson;
				}
			}
		}
		Long playerId = player.getPlayerId();
		String sessionId = HttpUtils.formatSessionId(request);
		// 设置用户在线
		boolean online = getAgentPlayerInfoDao().setOnline(playerId, client, sessionId, moment.toTimestamp());
		if (!online) {
			throw new IllegalArgumentException();
		}
		PlayerLoginLog loginLog = new PlayerLoginLog(agentNo, agentName, playerId, ip, address, client, url,
				moment.toTimestamp());
		getPlayerLoginLogDao().save(loginLog);
		// 保存用户到会话信息
		setSessionUser(request, player);
		webJson.setData(loginLog);
		setSuccess(webJson);
		return webJson;
	}




	/**
	 * 独立更新登录失败次数和用户状态的方法
	 * @param playerId 用户 ID
	 * @param currentFailedNum 当前失败次数
	 * @param agentNo 代理商编号
	 */
	public void updateLoginFailedInfo(Long playerId, int currentFailedNum, String agentNo) {
		getAgentPlayerInfoDao().updateAgentPlayerInfoLoginFailedNum(playerId, currentFailedNum);
		// 获取LOGIN_FAILED_NUM配置
		PlatformConfig platformConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo,"ACCOUNT","LOGIN_FAILED_NUM");
		if (platformConfig != null && platformConfig.getValue() != null) {
			int maxFailedNum = Integer.parseInt(platformConfig.getValue());
			if (currentFailedNum >= maxFailedNum) {
				getAgentPlayerInfoDao().updateAgentPlayerInfoPlayerStatus(playerId, 1);
			}
		}
	}


	/**
	 * 验证登录注册参数
	 *
	 * @param body
	 * @return
	 */
	public boolean checkValidLoginAndRegisterVo(LoginVo body, boolean isLogin) {
		int type = body.getType();
		String username = body.getUsername();
		if (!isLogin || LoginVo.USERNAME == type) {
			if (StrUtils.isContainChinese(username)) {
				throw newException("-1", "用户名不能包含中文");
			}
			String password = body.getPassword();
			if (!isLogin && !StrUtils.isContainPassword(password)) {
				throw newException("-1", "用户密码由6~16位数字和字母组合");
			}
		}
		List<Class<?>> groupList = CollUtil.newArrayList(Default.class);
		String email = body.getEmail();
		String telephone = body.getTelephone();
		if (LoginVo.USERNAME == type) {

		} else if (LoginVo.EMAIL == type) {
			if (!StrUtils.isContainEmail(email)) {
				throw newException("-1", "联系邮箱输入格式错误");
			}
			groupList.add(LoginVo.Email.class);
		} else if (LoginVo.TELEPHONE == type) {
			if (!StrUtils.isContainTelephone(telephone)) {
				throw newException("-1", "联系电话输入格式错误");
			}
			groupList.add(LoginVo.Telephone.class);
		} else {
			throw newException("-1", "未知的操作类型");
		}
		if (LoginVo.USERNAME != type) {
			String verifyCode = body.getVerifyCode();
			if (StringUtils.isEmpty(verifyCode)) {
				throw newException("-1", "验证码输入不能为空");
			}
		}
		Class<?>[] groupArray = groupList.toArray(new Class[groupList.size()]);
		Set<ConstraintViolation<LoginVo>> violations = Validation.buildDefaultValidatorFactory().getValidator()
				.validate(body, groupArray);
		if (!CollUtil.isEmpty(violations)) {
			for (ConstraintViolation<LoginVo> violation : violations) {
				String message = violation.getMessage();
				throw newException("-1", message);
			}
		}
		return true;
	}

	/**
	 * 登录Google验证码验证
	 *
	 * @param player
	 * @param verifyCode
	 * @return
	 */
	public Map<String, Object> checkGoogleVerifyCode(AgentPlayerInfo player, String verifyCode) {
		boolean googleLogin = player.getGoogleLogin() == 1;
		if (!googleLogin) {
			return null;
		}
		boolean googleBind = player.getGoogleBind() == 1;
		if (!googleBind) {
			String googleUrl = createGoogleAuth(player, player.getAgentName());
			Map<String, Object> returnVerifyMap = new LinkedHashMap<String, Object>();
			returnVerifyMap.put("googleUrl", googleUrl);
			returnVerifyMap.put("notBindGoogleAuth", true);
			return returnVerifyMap;
		}
		if (StringUtils.isEmpty(verifyCode)) {
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("notInputGoogleKey", true);
			return data;
		}
		boolean verifyGoogleKey = GoogleAuthUtils.verify(player.getGoogleKey(), Integer.valueOf(verifyCode));
		if (!verifyGoogleKey) {
			throw newException("-1", "Google验证码输入错误");
		}
		return null;
	}

	@Override
	public WebJson register(HttpServletRequest request, RegisterVo body) {
		WebJson webJson = new WebJson();
		AgentInfo agent = getAgent(request);
		String agentNo = agent.getAgentNo();
		String agentName = agent.getAgentName();
		if (null == agent || AgentInfo.AGENT_STATUS_FORBIDDEN == agent.getAgentStatus()) {
			throw newException("-1", "该平台已停用");
		}
		Integer playerType = AgentPlayerType.PLAYER.getCode();
		// 返点
		double point = 0;
		Long pid = 0L;
		String pids = "";
		String inviteCode = body.getInviteCode();
		AgentPlayerRegistLink link = null;
		if(!StringUtils.isEmpty(inviteCode)) {
			PlayerInvite invite = getPlayerInviteDao().selectByInviteCode(inviteCode);
			if(null != invite) {
				pid = invite.getPlayerId();
				AgentPlayerInfo upPlayer = getAgentPlayerInfoDao().getByPlayerId(pid);
				if(upPlayer != null) {
					pids = StringIdUtils.addFirst(upPlayer.getPids(), upPlayer.getPlayerId());
				}
			}else {
				// 推广链接
				link = getAgentPlayerRegistLinkDao().getByInviteCode(inviteCode);
				// 推广链接存在
				if(link != null) {
					pid = link.getPlayerId();
					pids = StringIdUtils.addFirst(link.getPids(), link.getPlayerId());
					point = link.getPoint();
					playerType = link.getPlayerType();
				}
			}
		}
		LoginVo loginVo = new LoginVo();
		BeanUtils.copyProperties(body, loginVo);
		checkValidLoginAndRegisterVo(loginVo, false);
		int type = body.getType();
		String receiveBy = "";
		String email = "";
		String telephone = "";
		String countryCode = "";
		String phonePrefix = "";
		String username = body.getUsername();
		String password = body.getPassword();
		String verifyCode = body.getVerifyCode();
		AgentPlayerInfo player = null;
		// 判断用户名不能重复（同一个代理商下不可以一致）
		player = getAgentPlayerInfoDao().getByPlayerName(agentNo, username);
		if (null != player) {
			throw newException("-1", "该用户名已存在");
		}
		if (LoginVo.EMAIL == type) {
			email = body.getEmail();
			receiveBy = email;
			String encryptedReceiveBy = getToolsService().encrypt(receiveBy);
			player = getAgentPlayerInfoDao().getByEmail(agentNo, encryptedReceiveBy);
			if (null != player) {
				throw newException("-1", "该电子邮箱已被注册");
			} else {
				List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, encryptedReceiveBy);
				if (CollectionUtils.isEmpty(verifyCodes)) {
					throw newException("-1", "邮箱验证码输入错误！");
				}
				PlayerVerifyCode verify = verifyCodes.get(0);
				if (!(null != verify && verify.getVerifyCode().equals(verifyCode))) {
					throw newException("-1", "邮箱验证码输入错误！");
				} else {
					getPlayerVerifyCodeDao().deleteByParam(type, agentNo, encryptedReceiveBy);
				}
			}
		} else if (LoginVo.TELEPHONE == type) {
			telephone = body.getTelephone();
			countryCode = body.getCountryCode();
			receiveBy = telephone;
			String encryptedReceiveBy = getToolsService().encrypt(receiveBy);
			PlatformCountry byAgentNoAndCountryCode = getPlatformCountryDao().getByAgentNoAndCountryCode(agentNo, countryCode);
			if (byAgentNoAndCountryCode == null) {
				throw newException("-1", "暂不支持当前国家或地区");
			}

			if(byAgentNoAndCountryCode.getEnable() == 0){
				throw newException("-1", "当前国家或地区已被禁止使用");
			}
			phonePrefix = byAgentNoAndCountryCode.getPhonePrefix();

			player = getAgentPlayerInfoDao().getByTelephone(agentNo, countryCode, encryptedReceiveBy);

			if (null != player) {
				throw newException("-1", "该联系电话已被注册");
			} else {
				List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, encryptedReceiveBy);
				if (CollectionUtils.isEmpty(verifyCodes)) {
					throw newException("-1", "短信验证码输入错误！");
				}
				PlayerVerifyCode verify = verifyCodes.get(0);
				if (!(null != verify && verify.getVerifyCode().equals(verifyCode))) {
					throw newException("-1", "短信验证码输入错误！");
				} else {
					getPlayerVerifyCodeDao().deleteByParam(type, agentNo, encryptedReceiveBy);
				}
			}
		}

		String playerName = username;
		Integer agentType = AgentType.PLATFORM.getCode();
		email = getToolsService().encrypt(email);
		telephone = getToolsService().encrypt(telephone);
		String playerPassword = CipherUtils.encodePwd(password);

		AgentPlayerInfo entity = initPlayer(agentNo, agentType, agentName, pid, pids, playerName, playerType,
				playerPassword, email, telephone, point);
		entity.setCountryCode(countryCode);
		entity.setPhonePrefix(phonePrefix);
		boolean result = getAgentPlayerInfoDao().save(entity);
		if(result) {
			setSuccess(webJson);
			if(link != null) {
				// 推广链接注册时，增加注册人数
				getAgentPlayerRegistLinkDao().updateUseTimes(link.getId(), link.getPlayerId());
			}
			getContractService().createContractPlayerConfig(entity);
			getActivityService().inActivities(request, entity, inviteCode);
			getTcChatService().importUser(entity.getAgentNo() + "_" + entity.getPlayerName(), entity.getPlayerNick());
		}else {
			setFail(webJson);
		}
		return webJson;
	}

	@Override
	public WebJson addDownUser(HttpServletRequest request, AgentPlayerInfo upPlayer, AddUserVo body)
			throws ServiceException {
		WebJson webJson = new WebJson();
		AgentInfo agent = getAgent(request);
		if (null == agent || AgentInfo.AGENT_STATUS_FORBIDDEN == agent.getAgentStatus()) {
			throw newException("-1", "该平台已停用");
		}
		if (StrUtils.isContainChinese(body.getUsername())) {
			throw newException("-1", "用户名不能包含中文");
		}
		if (!StrUtils.isContainPassword(body.getPassword())) {
			throw newException("-1", "用户密码由6~16位数字和字母组合");
		}

		String agentNo = agent.getAgentNo();
		String agentName = agent.getAgentName();
		String email = "";
		String telephone = "";
		String username = body.getUsername();
		String password = body.getPassword();
		Integer playerType = body.getPlayerType();
		if(playerType != AgentPlayerInfo.PLAYER_TYPE_AGENT) {
			playerType = AgentPlayerInfo.PLAYER_TYPE_PLAYER;
		}
		double point = body.getPoint() == null ? 0D : body.getPoint();
		// 校验返点
		if(!checkPoint(upPlayer, playerType, point)) {
			throw newException("115-09");
		}

		AgentPlayerInfo player = null;
		// 判断用户名不能重复（同一个代理商下不可以一致）
		player = getAgentPlayerInfoDao().getByPlayerName(agentNo, username);
		if (null != player) {
			throw newException("-1", "该用户名已存在");
		}

		Long pid = upPlayer.getPlayerId();
		String pids = StringIdUtils.addFirst(upPlayer.getPids(), upPlayer.getPlayerId());
		Integer agentType = AgentType.PLATFORM.getCode();
		String playerPassword = CipherUtils.encodePwd(password);
		AgentPlayerInfo entity = initPlayer(agentNo, agentType, agentName, pid, pids, username, playerType,
				playerPassword, email, telephone, point);
		boolean result = getAgentPlayerInfoDao().save(entity);
		if (result) {
			setSuccess(webJson);
			getContractService().createContractPlayerConfig(entity);
			getActivityService().inActivities(request, entity, upPlayer.getPlayerName());
			getTcChatService().importUser(entity.getAgentNo() + "_" + entity.getPlayerName(), entity.getPlayerNick());
		} else {
			setFail(webJson);
		}
		return webJson;
	}

	@Override
	public AddDownUserPointRangeVO getDownUserPointAgentRange(AgentPlayerInfo player) throws ServiceException {
		AddDownUserPointRangeVO rangeVO = new AddDownUserPointRangeVO();
		// 获取用户奖级配置
		AgentPlayerAccountType accountType = getAccountType(player.getAgentNo(), player.getPlayerType(),
				player.getPids(), player.getLotteryCode());
		// 获取代理奖级配置
		AgentPlayerAccountType agentAccountType = getAgentPlayerAccountTypeDao().getByCode(player.getAgentNo(),
				AgentPlayerAccountType.CODE_AGENT);
		// 代理最小返点
		double agentMinPoint = getLotteryPoint(player.getAgentNo(), agentAccountType.getLotteryMinCode());
		// 用户返点
		double maxPoint = player.getPoint();
		// 如果有开启同级账号限定奖级，则使用该配置
		if(accountType.getEqualLevelCode() == AgentPlayerAccountType.EQUAL_LEVLEL_CODE_TRUE &&
				player.getEqualLevel() == AgentPlayerInfo.EQUAL_LEVEL_ON) {
			rangeVO.setMinPoint(agentMinPoint);
			rangeVO.setMaxPoint(maxPoint);
		}else {
			if(player.getEqualLevel() == AgentPlayerInfo.EQUAL_LEVEL_OFF) {
				// 没有开启同级账号，最大返点比上级用户小0.1
				maxPoint = MathUtils.subtract(maxPoint, 0.1);
			}
			rangeVO.setMaxPoint(maxPoint);
			rangeVO.setMinPoint(agentMinPoint);
		}
		return rangeVO;
	}


	@Override
	public List<AllowedUserTypeVO> getAllowedUserTypes(AgentPlayerInfo player) throws ServiceException {
		List<AllowedUserTypeVO> allowedUserTypes = new ArrayList<>();

		// 通过agentNo和accountType查找agent_player_account_type表
		AgentPlayerAccountType accountType = getAgentPlayerAccountTypeDao().getByCode(player.getAgentNo(),
				player.getAccountType());

		if (accountType == null) {
			throw newException("101-05");
		}

		// 获取allowed_user_type字段值
		String allowedUserTypeStr = accountType.getAllowedUserType();
		if (StringUtils.isEmpty(allowedUserTypeStr)) {
			return allowedUserTypes;
		}

		// 解析allowed_user_type字段（逗号分隔的用户类型）
		String[] userTypeCodes = allowedUserTypeStr.split(",");

		for (String userTypeCode : userTypeCodes) {
			userTypeCode = userTypeCode.trim();
			if (StringUtils.isEmpty(userTypeCode)) {
				continue;
			}

			// 根据用户类型代码获取对应的agent_player_account_type对象
			AgentPlayerAccountType userAccountType = getAgentPlayerAccountTypeDao().getByCode(player.getAgentNo(), userTypeCode);
			if (userAccountType == null) {
				continue;
			}

			double maxPoint = player.getPoint();
			if(player.getEqualLevel() == AgentPlayerInfo.EQUAL_LEVEL_OFF) {
				maxPoint = MathUtils.subtract(maxPoint, 0.1);
			}
			double minPoint = 0;
			// 最大返点
			double sysMaxPoint = getLotteryPoint(player.getAgentNo(), userAccountType.getLotteryMaxCode());
			// 最小返点
			double sysMinPoint = getLotteryPoint(player.getAgentNo(), userAccountType.getLotteryMinCode());
			if(maxPoint > sysMaxPoint) {
				maxPoint = sysMaxPoint;
			}
			if(minPoint < sysMinPoint) {
				minPoint = sysMinPoint;
			}

			// 创建AllowedUserTypeVO对象
			AllowedUserTypeVO allowedUserType = new AllowedUserTypeVO();
			allowedUserType.setName(userAccountType.getName());
			allowedUserType.setCode(userTypeCode);
			allowedUserType.setMinPoint(minPoint);
			allowedUserType.setMaxPoint(maxPoint);
			allowedUserTypes.add(allowedUserType);
		}

		return allowedUserTypes;
	}




	@Override
	public AddDownUserPointRangeVO getDownUserPointPlayerRange(AgentPlayerInfo player) throws ServiceException {
		AddDownUserPointRangeVO rangeVO = new AddDownUserPointRangeVO();
		// 获取玩家奖级配置
		AgentPlayerAccountType accountType = getAgentPlayerAccountTypeDao().getByCode(player.getAgentNo(),
				AgentPlayerAccountType.CODE_PLAYER);
		if(accountType == null) {
			throw newException("101-05");
		}
		double maxPoint = player.getPoint();
		if(player.getEqualLevel() == AgentPlayerInfo.EQUAL_LEVEL_OFF) {
			maxPoint = MathUtils.subtract(maxPoint, 0.1);
		}
		double minPoint = 0;
		// 最大返点
		double sysMaxPoint = getLotteryPoint(player.getAgentNo(), accountType.getLotteryMaxCode());
		// 最小返点
		double sysMinPoint = getLotteryPoint(player.getAgentNo(), accountType.getLotteryMinCode());
		if(maxPoint > sysMaxPoint) {
			maxPoint = sysMaxPoint;
		}
		if(minPoint < sysMinPoint) {
			minPoint = sysMinPoint;
		}
		rangeVO.setMaxPoint(maxPoint);
		rangeVO.setMinPoint(minPoint);
		return rangeVO;
	}

	@Override
	public double getLotteryPoint(String agentNo, int code) throws ServiceException {
		// 获取系统最高奖级
		PlatformConfig sysCodeConfig =
				getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_CODE");
		if(sysCodeConfig == null) {
			throw newException("115-15");
		}
		double sysCode;
		try {
			sysCode = Double.parseDouble(sysCodeConfig.getValue());
		}catch(NumberFormatException e) {
			throw newException("115-16");
		}
		// 获取系统最高返点
		PlatformConfig sysPointConfig =
				getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_POINT");
		if(sysPointConfig == null) {
			throw newException("115-17");
		}
		double sysPoint;
		try {
			sysPoint = Double.parseDouble(sysPointConfig.getValue());
		}catch(NumberFormatException e) {
			throw newException("115-18");
		}

		return MathUtils.subtract(sysPoint, MathUtils.divide((MathUtils.subtract(sysCode, code)), 20, 1));
	}

	@Override
	public int getLotteryCode(String agentNo, double point) throws ServiceException {
		// 获取系统最高奖级
		PlatformConfig sysCodeConfig =
				getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_CODE");
		if(sysCodeConfig == null) {
			throw newException("115-15");
		}
		double sysCode;
		try {
			sysCode = Double.parseDouble(sysCodeConfig.getValue());
		}catch(NumberFormatException e) {
			throw newException("115-16");
		}
		// 获取系统最高返点
		PlatformConfig sysPointConfig =
				getPlatformConfigDao().getPlatformConfigByGroupAndKey(agentNo, "GAME_LOTTERY", "SYS_POINT");
		if(sysPointConfig == null) {
			throw newException("115-17");
		}
		double sysPoint;
		try {
			sysPoint = Double.parseDouble(sysPointConfig.getValue());
		}catch(NumberFormatException e) {
			throw newException("115-18");
		}
		return (int) MathUtils.subtract(sysCode, MathUtils.multiply(MathUtils.subtract(sysPoint, point), 20));
	}

	@Override
	public boolean checkPoint(AgentPlayerInfo player, int playerType, double point) throws ServiceException {
		if(point == 0) {
			return true;
		}
		AddDownUserPointRangeVO rangeVO;
		if(playerType == AgentPlayerInfo.PLAYER_TYPE_AGENT) {
			rangeVO = getDownUserPointAgentRange(player);
		}else {
			rangeVO = getDownUserPointPlayerRange(player);
		}
		// 判断返点
		return point >= rangeVO.getMinPoint() && point <= rangeVO.getMaxPoint();
	}

	@Override
	public AgentPlayerInfo urlLogin(String agentNo, String playerName, String auth, String sessionId,
			String ip, String client, String url) {
		AgentInfo agent = getRedisService().getAgentInfo(agentNo);
		AgentPlayerInfo player = getRedisService().getAgentPlayerInfoUrlAuthKey(agentNo, playerName, auth);
		if (player == null) {
			throw newException("102-11");
		}
		if (player.getPlayerStatus() != AgentPlayerInfo.PLAYER_STATUS_NORMAL) {
			throw newException("0-5");
		}
		// 设置用户在线
		Long playerId = player.getPlayerId();
		Timestamp lastLoginTime = new Moment().toTimestamp();
		boolean onlineResult = getAgentPlayerInfoDao().setOnline(playerId, client, sessionId, lastLoginTime);
		if (!onlineResult) {
			throw new IllegalArgumentException();
		}
		String agentName = agent.getAgentName();
		String address = MonipdbUtils.getIp2Region(ip);
		PlayerLoginLog adminLoginLog = new PlayerLoginLog(agentNo, agentName, playerId, ip, address, client, url,
				lastLoginTime);
		getPlayerLoginLogDao().save(adminLoginLog);
		if (player.getPlayerTelegramId() == 0) {
			boolean result = getAgentPlayerInfoDao().cleanUrlAuthKey(playerId, auth);
			if (!result) {
				log.error("清除登录密钥cleanAuthKey失败");
				throw newException("600-09");
			}
		}
		return player;
	}

	@Override
	public boolean updatePassword(AgentPlayerInfo player, String password, String newPassword,
			String confirmNewPassword) {
		if (!CipherUtils.verify(player.getPlayerPassword(), password)) {
			throw newException("-1", "原始密码输入错误，请重新输入");
		}
		if (password.equals(confirmNewPassword)) {
			throw newException("-1", "原密码不能和新密码一致");
		}
		if (!StrUtils.isContainPassword(newPassword)) {
			throw newException("-1", "新密码由6~16位数字和字母组合");
		}
		if (!newPassword.equals(confirmNewPassword)) {
			throw newException("-1", "新密码与确认密码不一致，请重新输入");
		}
		Long playerId = player.getPlayerId();
		newPassword = CipherUtils.encodePwd(newPassword);
		AgentPlayerInfo agentPlayerInfo = getRedisService().getAgentPlayerInfo(playerId);
		if (agentPlayerInfo == null) {
			throw newException("-1", "用户不存在");
		}
		if (agentPlayerInfo.getPlayerPassword().equals(newPassword)) {
			throw newException("-1", "新密码与原密码一致，请重新输入");
		}
		return getAgentPlayerInfoDao().updateAgentPlayerPassword(playerId, newPassword);
	}

	@Override
	public boolean retrievePlayerPassword(AgentPlayerInfo player, int type, String receiveBy, String verifyCode,
			String newPassword, String confirmNewPassword) {
		String encryptedReceiveBy = getToolsService().encrypt(receiveBy);
		String email = player.getEmail();
		String telepphone = player.getTelephone();
		String agentNo = player.getAgentNo();
		if (RegisterVo.EMAIL == type) {
			if (!encryptedReceiveBy.equals(email)) {
				throw newException("-1", "无效的邮箱地址");
			}
		} else if (RegisterVo.TELEPHONE == type) {
			if (!encryptedReceiveBy.equals(telepphone)) {
				throw newException("-1", "无效的手机号码");
			}
		}
		List<PlayerVerifyCode> verifyCodes = getVerifyCodeService().list(type, agentNo, encryptedReceiveBy);
		if (CollectionUtils.isEmpty(verifyCodes)) {
			throw newException("-1", "验证码输入错误");
		}
		PlayerVerifyCode verify = verifyCodes.get(0);
		if (null == verify) {
			throw newException("-1", "验证码输入错误");
		}
		if (!verifyCode.equals(verify.getVerifyCode())) {
			throw newException("-1", "验证码输入错误");
		}

		if (StringUtils.isEmpty(newPassword)) {
			return true;
		} else {
			// 二次确认修改密码时，删除验证过的验证码
			getPlayerVerifyCodeDao().deleteByParam(type, agentNo, encryptedReceiveBy);
		}
		if (CipherUtils.verify(player.getPlayerPassword(), newPassword)) {
			throw newException("-1", "新密码与原密码一致，请重新输入");
		}
		if (!StrUtils.isContainPassword(newPassword)) {
			throw newException("-1", "新密码由6~16位数字和字母组合");
		}
		if (newPassword.equals(confirmNewPassword)) {
			throw newException("-1", "新密码与确认密码不一致，请重新输入");
		}
		Long playerId = player.getPlayerId();
		newPassword = CipherUtils.encodePwd(newPassword);
		return getAgentPlayerInfoDao().updateAgentPlayerPassword(playerId, newPassword);
	}

	public String createGoogleAuth(GoogleAuthenticatorKey credentials, AgentPlayerInfo player) {
		if (1 == player.getGoogleBind()) {
			// ERR: Google验证不能重复绑定，请联系客服
			throw newException("-1", "Google验证已绑定");
		}
		String googleKey = credentials.getKey();
		boolean updateGoogleKey = getAgentPlayerInfoDao().updateGoogleKey(player.getPlayerId(), googleKey);
		if (!updateGoogleKey) {
			throw new IllegalArgumentException();
		}
		String platformName = player.getAgentName();
		String playerName = player.getPlayerName();
		String authUrl = GoogleAuthUtils.getOtpAuthURL(platformName + "", playerName, credentials);
		if (StringUtils.isEmpty(authUrl)) {
			throw new IllegalArgumentException();
		}
		return authUrl;
	}

	@Override
	public String createGoogleAuth4Img(GoogleAuthenticatorKey credentials, AgentPlayerInfo player) {
		String authUrl = createGoogleAuth(credentials, player);
		log.info("authUrl:" + authUrl);
		String authImg = post(authUrl);
		return authImg;
	}

	public String post(String url) {
		String result = null;
		try {
			// System.setProperty("jsse.enableSNIExtension", "false");
			SSLContext sc = SSLContext.getInstance("SSL");
			sc.init(null, new TrustManager[] { new TrustAnyTrustManager() }, new java.security.SecureRandom());
			URL console = new URL(null, url, new sun.net.www.protocol.https.Handler());
			HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
			conn.setSSLSocketFactory(sc.getSocketFactory());
			conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
			conn.setDoOutput(true);
			conn.connect();
			DataOutputStream out = new DataOutputStream(conn.getOutputStream());
			out.flush();
			out.close();
			InputStream is = conn.getInputStream();
			if (is != null) {
				result = getImgStrByInputStream(is);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	private static class TrustAnyHostnameVerifier implements HostnameVerifier {
		@Override
		public boolean verify(String hostname, SSLSession session) {
			return true;
		}
	}

	@Override
	public Integer getActivePlayerCount(String agentNo, Long playerId, Date first, Date last, int promoteType) {
		Integer activePlayerCount = 0;

		activePlayerCount = fromReportData(agentNo, playerId, first, last, promoteType);

		return activePlayerCount;
	}

	@Override
	public Integer getActivePlayerCountForTeamAll(String agentNo, Long playerId, Date first, Date last, int promoteType) {
		Integer activePlayerCount = 0;

		activePlayerCount = fromReportDataForTeamAll(agentNo, playerId, first, last, promoteType);

		return activePlayerCount;
	}


	@Override
	public Integer getBetPlayerCountForTeamAll(String agentNo, Long playerId, Date first, Date last, int promoteType) {
		Integer betPlayerCount = 0;

		betPlayerCount = betPlayerCountFromReportDataForTeamAll(agentNo, playerId, first, last, promoteType);

		return betPlayerCount;
	}

	@Override
	public Integer getBetPlayerCountForTeamAllDistinct(String agentNo, Long playerId, Date first, Date last) {
		Set<Long> distinctPlayerIds = new HashSet<>();

		// 获取彩票投注用户
		List<Criterion> lotteryOrders = new ArrayList<>();
		lotteryOrders.add(Restrictions.eq("agentNo", agentNo));
		lotteryOrders.add(Restrictions.or(
				Restrictions.like("pids", "[" + playerId + "]", MatchMode.ANYWHERE),
				Restrictions.eq("playerId", playerId)
		));
		lotteryOrders.add(Restrictions.ge("reportDate", first));
		lotteryOrders.add(Restrictions.lt("reportDate", last));

		List<PlatformAgentPlayerReport> lotteryReports = getPlatformAgentPlayerReportDao()
				.find(lotteryOrders, new ArrayList<>());

		// 添加有投注的彩票用户
		lotteryReports.stream()
				.filter(report -> report.getBetAmount().compareTo(BigDecimal.ZERO) > 0)
				.forEach(report -> distinctPlayerIds.add(report.getPlayerId()));

		// 获取三方投注用户
		List<GameSimulationReport> thirdReports = getGameSimulationReportDao()
				.listGroupByPlayer(playerId, first, last, "", 0, 10000);

		// 添加有投注的三方用户
		thirdReports.stream()
				.filter(report -> report.getValidBetAmount().compareTo(BigDecimal.ZERO) > 0)
				.forEach(report -> distinctPlayerIds.add(report.getAccountId()));

		return distinctPlayerIds.size();
	}




	@Override
	public AgentPlayerInfo getPlayerInfo(Long playerId) {
		return getAgentPlayerInfoDao().getByPlayerId(playerId);
	}

	@Override
	public AgentPlayerInfo getPlayerInfo(String agentNo, String playerName) {
		return getAgentPlayerInfoDao().getByPlayerName(agentNo, playerName);
	}

	/**
	 * 读取输入流,转换为Base64字符串
	 */
	public String getImgStrByInputStream(InputStream input) {
		byte[] data = null;
		// 读取图片字节数组
		try {
			ByteArrayOutputStream outStream = new ByteArrayOutputStream();
			// 创建一个Buffer字符串
			byte[] buffer = new byte[1024];
			// 每次读取的字符串长度，如果为-1，代表全部读取完毕
			int len = 0;
			// 使用一个输入流从buffer里把数据读取出来
			while ((len = input.read(buffer)) != -1) {
				// 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
				outStream.write(buffer, 0, len);
			}
			// 关闭输入流
			input.close();
			data = outStream.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
		}
		// 对字节数组Base64编码
		sun.misc.BASE64Encoder encoder = new sun.misc.BASE64Encoder();
		String imgBase64 = encoder.encode(data).replaceAll("\r|\n", "");// 返回Base64编码过的字节数组字符串
		String result = "data:image/png;base64," + imgBase64;
		return result;
	}

	@Override
	public boolean confirmGoogleBind(AgentPlayerInfo player, Integer verifyCode) {
		if (1 == player.getGoogleBind()) {
			throw newException("-1", "Google验证不能重复绑定");
		}
		if (StringUtils.isEmpty(player.getGoogleKey())) {
			throw newException("-1", "请先设置谷歌秘钥！");
		}

		boolean verifyGoogleKey = GoogleAuthUtils.verify(player.getGoogleKey(), verifyCode);
		if (!verifyGoogleKey) {
			throw newException("102-04");
		}
		// 更新谷歌绑定状态
		boolean setGoogleBindIsTrue = getAgentPlayerInfoDao().setGoogleBindIsTrue(player.getPlayerId());
		if (!setGoogleBindIsTrue) {
			throw new IllegalArgumentException();
		}
		return setGoogleBindIsTrue;
	}

	@Override
	public boolean setGoogleLogin(Long playerId, Integer googleLogin) {
		boolean b = getAgentPlayerInfoDao().setGoogleLogin(playerId, googleLogin);
		if (b) {
			boolean b1 = getRedisService().delAgentPlayerInfo(playerId);
			if (!b1) {
				log.warn("删除用户redis缓存失败, playerId:" + playerId);
			}
		}
		return b;
	}

	@SuppressWarnings("unused")
	private Integer fromSourcesData(Long playerId) {
		Integer activePlayerCount = 0;
		try {
			LocalDate firstDay = LocalDate.now().withDayOfMonth(1);
			ZonedDateTime zonedDateTimeFirst = firstDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
			Date first = Date.from(zonedDateTimeFirst.toInstant());

			LocalDate lastDay = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
			ZonedDateTime zonedDateTimeLast = lastDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
			Date last = Date.from(zonedDateTimeLast.toInstant());

			List<Criterion> criterionOrders = new ArrayList<Criterion>();
			criterionOrders.add(Restrictions.eq("pid", playerId));
			criterionOrders.add(Restrictions.ge("createTime", first));
			criterionOrders.add(Restrictions.lt("createTime", last));
			criterionOrders.add(Restrictions.in("orderStatus", Arrays.asList(AgentPlayerGameOrder.GAME_STATUS_WAITING,
					AgentPlayerGameOrder.GAME_STATUS_LOSE, AgentPlayerGameOrder.GAME_STATUS_WIN)));

			List<AgentPlayerGameOrder> agentPlayerGameOrders = getAgentPlayerGameOrderDao().find(criterionOrders,
					new ArrayList<>());
			// 按 playerId 分组，计算每个 playerId 的 betAmount 总和，过滤掉总和小于 10000 的 playerId
			List<Long> playerOrderIds = agentPlayerGameOrders.stream()
					.collect(Collectors.groupingBy(AgentPlayerGameOrder::getPlayerId,
							Collectors.reducing(BigDecimal.ZERO, AgentPlayerGameOrder::getBetAmount, BigDecimal::add)))
					.entrySet().stream().filter(entry -> entry.getValue().compareTo(new BigDecimal("10000")) >= 0) // 过滤总和大于等于
					// 10000
					// 的玩家
					.map(Map.Entry::getKey).collect(Collectors.toList());

			List<Criterion> criterionRecharge = new ArrayList<Criterion>();
			criterionRecharge.add(Restrictions.eq("pid", playerId));
			criterionRecharge.add(Restrictions.ge("orderTime", first));
			criterionRecharge.add(Restrictions.lt("orderTime", last));
			criterionRecharge.add(Restrictions.eq("orderStatus", PlayerRecharge.ORDER_STATUS_COMPLETED));

			List<PlayerRecharge> playerRecharges = getPlayerRechargeDao().find(criterionRecharge, new ArrayList<>());
			List<Long> playerRechargeIds = playerRecharges.stream()
					.collect(Collectors.groupingBy(PlayerRecharge::getPlayerId,
							Collectors.reducing(BigDecimal.ZERO, PlayerRecharge::getActualAmount, BigDecimal::add)))
					.entrySet().stream().filter(entry -> entry.getValue().compareTo(new BigDecimal("1000")) >= 0) // 过滤总和大于等于
					// 10000
					// 的玩家
					.map(Map.Entry::getKey).collect(Collectors.toList());
			List<Long> mergedAndDistinct = Stream.concat(playerOrderIds.stream(), playerRechargeIds.stream()).distinct()
					.collect(Collectors.toList());
			activePlayerCount = mergedAndDistinct.size();
		} catch (Exception e) {
			log.error("获取活跃用户失败，pid：{}", playerId, e);
		}

		return activePlayerCount;
	}

	private Integer fromReportData(String agentNo, Long playerId, Date first, Date last, int promoteType) {
		Integer activePlayerCount = 0;
		try {
			//			LocalDate firstDay = LocalDate.now().withDayOfMonth(1);
			//			ZonedDateTime zonedDateTimeFirst = firstDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
			//			Date first = Date.from(zonedDateTimeFirst.toInstant());
			//
			//			LocalDate lastDay = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
			//			ZonedDateTime zonedDateTimeLast = lastDay.atStartOfDay(ZoneId.of("Asia/Shanghai"));
			//			Date last = Date.from(zonedDateTimeLast.toInstant());

			List<Criterion> criterionOrders = new ArrayList<Criterion>();
			criterionOrders.add(Restrictions.eq("pid", playerId));
			criterionOrders.add(Restrictions.ge("reportDate", first));
			criterionOrders.add(Restrictions.lt("reportDate", last));

			List<PlatformAgentPlayerReport> platformAgentPlayerReports = new ArrayList<>();
			if(promoteType == 1){
				platformAgentPlayerReports = getPlatformAgentPlayerReportDao()
						.find(criterionOrders, new ArrayList<>());
			}else {
				List<GameSimulationReport> gameSimulationReports = getGameSimulationReportDao().listGroupByPlayer(playerId, first, last, "", 0, 10000);
				for(GameSimulationReport gameSimulationReport : gameSimulationReports){
					PlatformAgentPlayerReport platformAgentPlayerReport = new PlatformAgentPlayerReport();
					platformAgentPlayerReport.setAgentNo(gameSimulationReport.getAgentNo());
					platformAgentPlayerReport.setPlayerId(gameSimulationReport.getAccountId());
					platformAgentPlayerReport.setBetAmount(gameSimulationReport.getValidBetAmount());
					platformAgentPlayerReport.setTransferInAmount(gameSimulationReport.getDepositAmount());
					platformAgentPlayerReports.add(platformAgentPlayerReport);
				}
			}

			AgentPromoteConfig promoteConfig = getAgentPromoteConfigDao().getByAgentNoAndPromoteType(agentNo, promoteType);
			if (promoteConfig == null) {
				return activePlayerCount;
			}
			int activityCondition = promoteConfig.getActivityCondition();
			BigDecimal betAmount;
			BigDecimal rechargeAmount;

			JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
			betAmount = jsonObject.getBigDecimal("betAmount");
			rechargeAmount = jsonObject.getBigDecimal("rechargeAmount");

			if(betAmount == null && rechargeAmount == null) {
				betAmount = new BigDecimal(10000);
				rechargeAmount = new BigDecimal(1000);
				activityCondition = 4;
			}

			List<Long> ids = new ArrayList<>();
			BigDecimal finalBetAmount = betAmount;
			BigDecimal finalRechargeAmount = rechargeAmount;
			switch (activityCondition){
				case 1:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
				case 2:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
				case 3:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0 && report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
				case 4:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0 || report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
			}
			activePlayerCount = ids.size();
		} catch (Exception e) {
			log.error("获取活跃用户失败，pid：{}", playerId, e);
		}
		return activePlayerCount;
	}

	private Integer fromReportDataForTeamAll(String agentNo, Long playerId, Date first, Date last, int promoteType) {
		Integer activePlayerCount = 0;
		try {
			List<Criterion> criterionOrders = new ArrayList<>();
			criterionOrders.add(Restrictions.eq("agentNo", agentNo));
			criterionOrders.add(Restrictions.like("pids", "[" + playerId + "]", MatchMode.ANYWHERE));
			criterionOrders.add(Restrictions.ge("reportDate", first));
			criterionOrders.add(Restrictions.lt("reportDate", last));

			List<PlatformAgentPlayerReport> platformAgentPlayerReports = new ArrayList<>();
			if(promoteType == 1){
				platformAgentPlayerReports = getPlatformAgentPlayerReportDao()
						.find(criterionOrders, new ArrayList<>());
			}else {
				List<GameSimulationReport> gameSimulationReports = getGameSimulationReportDao().listGroupByPlayer(playerId, first, last, "", 0, 10000);
				for(GameSimulationReport gameSimulationReport : gameSimulationReports){
					PlatformAgentPlayerReport platformAgentPlayerReport = new PlatformAgentPlayerReport();
					platformAgentPlayerReport.setAgentNo(gameSimulationReport.getAgentNo());
					platformAgentPlayerReport.setPlayerId(gameSimulationReport.getAccountId());
					platformAgentPlayerReport.setBetAmount(gameSimulationReport.getValidBetAmount());
					platformAgentPlayerReport.setTransferInAmount(gameSimulationReport.getDepositAmount());
					platformAgentPlayerReports.add(platformAgentPlayerReport);
				}
			}

			AgentPromoteConfig promoteConfig = getAgentPromoteConfigDao().getByAgentNoAndPromoteType(agentNo, promoteType);
			if (promoteConfig == null) {
				return activePlayerCount;
			}
			int activityCondition = promoteConfig.getActivityCondition();
			BigDecimal betAmount;
			BigDecimal rechargeAmount;

			JSONObject jsonObject = JSONObject.parseObject(promoteConfig.getCommissionActiveUserConfig());
			betAmount = jsonObject.getBigDecimal("betAmount");
			rechargeAmount = jsonObject.getBigDecimal("rechargeAmount");

			if(betAmount == null && rechargeAmount == null) {
				betAmount = new BigDecimal(10000);
				rechargeAmount = new BigDecimal(1000);
				activityCondition = 4;
			}

			List<Long> ids = new ArrayList<>();
			BigDecimal finalBetAmount = betAmount;
			BigDecimal finalRechargeAmount = rechargeAmount;
			switch (activityCondition){
				case 1:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
				case 2:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
				case 3:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0 && report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
				case 4:
					ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(finalBetAmount) >= 0 || report.getTransferInAmount().compareTo(finalRechargeAmount) >= 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());
					break;
			}
			activePlayerCount = ids.size();
		} catch (Exception e) {
			log.error("获取活跃用户失败，pid：{}", playerId, e);
		}
		return activePlayerCount;
	}



	private Integer betPlayerCountFromReportDataForTeamAll(String agentNo, Long playerId, Date first, Date last, int promoteType) {
		Integer activePlayerCount = 0;
		try {
			List<Criterion> criterionOrders = new ArrayList<>();
			criterionOrders.add(Restrictions.eq("agentNo", agentNo));
			criterionOrders.add(Restrictions.or(
					Restrictions.like("pids", "[" + playerId + "]", MatchMode.ANYWHERE),
					Restrictions.eq("playerId", playerId)
			));
			criterionOrders.add(Restrictions.ge("reportDate", first));
			criterionOrders.add(Restrictions.lt("reportDate", last));

			List<PlatformAgentPlayerReport> platformAgentPlayerReports = new ArrayList<>();
			if(promoteType == 1){
				platformAgentPlayerReports = getPlatformAgentPlayerReportDao()
						.find(criterionOrders, new ArrayList<>());
			}else {
				List<GameSimulationReport> gameSimulationReports = getGameSimulationReportDao().listGroupByPlayer(playerId, first, last, "", 0, 10000);
				for(GameSimulationReport gameSimulationReport : gameSimulationReports){
					PlatformAgentPlayerReport platformAgentPlayerReport = new PlatformAgentPlayerReport();
					platformAgentPlayerReport.setAgentNo(gameSimulationReport.getAgentNo());
					platformAgentPlayerReport.setPlayerId(gameSimulationReport.getAccountId());
					platformAgentPlayerReport.setBetAmount(gameSimulationReport.getValidBetAmount());
					platformAgentPlayerReport.setTransferInAmount(gameSimulationReport.getDepositAmount());
					platformAgentPlayerReports.add(platformAgentPlayerReport);
				}
			}

			List<Long> ids = platformAgentPlayerReports.stream().filter(report -> report.getBetAmount().compareTo(BigDecimal.ZERO) > 0).map(PlatformAgentPlayerReport::getPlayerId).distinct().collect(Collectors.toList());

			activePlayerCount = ids.size();
		} catch (Exception e) {
			log.error("获取活跃用户失败，pid：{}", playerId, e);
		}
		return activePlayerCount;
	}




	@Override
	public boolean updateAutoTransfer(Long playerId, Integer autoTransfer) {
		boolean b = getAgentPlayerInfoDao().updateAutoTransfer(playerId, autoTransfer);
		if (b) {
			boolean b1 = getRedisService().delAgentPlayerInfo(playerId);
			if (!b1) {
				log.warn("删除用户redis缓存失败, playerId:" + playerId);
			}
		}
		return b;
	}

	@Override
	@Transactional
	public boolean editLotteryPoint(AgentPlayerInfo player, String downPlayerName, double point, String ip) {
		AgentPlayerInfo downPlayer = getAgentPlayerInfoDao().getByPlayerName(player.getAgentNo(), downPlayerName);
		if(downPlayer == null) {
			// ERR:用户不存在
			throw newException("100-01");
		}
		// 检查是否是我的直属下级
		boolean testIsMyDirect = getServiceUtils().testIsMyDirect(player, downPlayer);
		if(!testIsMyDirect) {
			throw newException("115-04");
		}
		AddDownUserPointRangeVO lotteryPointRange;
		if(downPlayer.getPlayerType() == AgentPlayerInfo.PLAYER_TYPE_AGENT) {
			lotteryPointRange = getDownUserPointAgentRange(player);
		}else {
			lotteryPointRange = getDownUserPointPlayerRange(player);
		}
		// 1.开始验证中心账户
		int code = getLotteryCode(player.getAgentNo(), point);
		double oldPoint = downPlayer.getPoint();
		if(point <= oldPoint) {
			// ERR:调整后的下级返点必须高于当前返点
			throw newException("115-08");
		}
		// 验证不能比上级高
		if(point < lotteryPointRange.getMinPoint() || point > lotteryPointRange.getMaxPoint()) {
			throw newException("115-09");
		}
		// 验证系统限制区间
		boolean testAccountType =
				getServiceUtils().testAccountType(downPlayer.getAgentNo(), downPlayer.getPlayerType(), code);
		if(!testAccountType) {
			throw newException("115-09");
		}
		int lotteryCode = getLotteryCode(downPlayer.getAgentNo(), point);
		AgentPlayerAccountType accountType = getAccountType(downPlayer.getAgentNo(), downPlayer.getPlayerType(),
				downPlayer.getPids(), lotteryCode);
		if(accountType == null) {
			throw newException("101-05");
		}
		// 验证通过
		return getAgentPlayerInfoDao().editLotteryPoint(downPlayer.getPlayerId(), point, lotteryCode,
				accountType.getCode());
	}

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyTransferNormal(AgentPlayerInfo fromPlayer, AgentPlayerInfo toPlayer, double amount,
			String withdrawPassword, String remarks) {
		if(amount <= 0) {
			// ERR:转账金额填写错误
			throw newException("110-02");
		}
//		// 判断是否是我的团队成员
//		boolean testIsMyTeam = getServiceUtils().testIsMyTeam(fromPlayer, toPlayer);
//		if(!testIsMyTeam && fromPlayer.getPid().longValue() != toPlayer.getPlayerId()) {
//			throw newException("115-03");
//		}
		if(StringUtils.isEmpty(fromPlayer.getWithdrawPassword())) {
			// ERR:没有设置资金密码
			throw newException("107-02");
		}
		// 验证资金密码正确
		if(!CipherUtils.verify(fromPlayer.getWithdrawPassword(), withdrawPassword)) {
			// ERR:资金密码输入错误
			throw newException("104-01");
		}
		// 验证账户锁定时间
		Date accountLockTime = fromPlayer.getLockTime();
		if(accountLockTime != null) {
			Moment lockMoment = new Moment().fromDate(accountLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if(isNotTime) {
				// ERR:账户已经锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				Map<String, Object> prams = new HashMap<>();
				prams.put("time", time);
				throw newException("111-09", prams);
			}
		}
		// 验证中心账户余额
		if(fromPlayer.getPlayerAvailableBalance().doubleValue() < amount) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		String outRemarks;
		String inRemarks;
		if(fromPlayer.getPids().contains("[" + toPlayer.getPlayerId() + "]")) {
			outRemarks = "给上级充值";
			inRemarks = "来自下级" + fromPlayer.getPlayerName() + "的充值";
		}else {
			outRemarks = "给下级" + toPlayer.getPlayerName() + "充值";
			inRemarks = "来自上级代理的充值";
		}
		if(!StringUtils.isEmpty(remarks)) {
			outRemarks += "：" + remarks;
			inRemarks += "：" + remarks;
		}
		String billno = RandomUtils.fromTime16();
		BigDecimal amountDecimal = new BigDecimal(amount);

		String tempInRemarks = inRemarks;
		String tempOutRemarks = outRemarks;
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
				// 入账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempInRemarks, toPlayer.getPlayerId(), amountDecimal, amountDecimal.negate(),
						BalanceChangeDirection.INCR,
						(beforeUpPlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterUpPlayer, agentPlayerAccountBill) -> {
							// 给目标中心账户用户加提款限制
							boolean updateToLimit =
									getPlayerWithdrawLimitDao().increaseLotteryBalance(toPlayer.getPlayerId(), amountDecimal);
							if(!updateToLimit) {
								throw new IllegalArgumentException();
							}
						}),
				// 出账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempOutRemarks, fromPlayer.getPlayerId(), amountDecimal.negate(), amountDecimal,
						BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
													  changePlayerBlockedBalance, afterDownPlayer, agentPlayerAccountBill) -> {
					// 扣除我的中心账户提款限制
					boolean updateFromLimit =
							getPlayerWithdrawLimitDao().increaseLotteryBalance(fromPlayer.getPlayerId(), amountDecimal.negate());
					if(!updateFromLimit) {
						throw new IllegalArgumentException();
					}
				}));
		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		return true;
	}



	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyTransferNormalV2(HttpServletRequest request,AgentPlayerInfo fromPlayer, AgentPlayerInfo toPlayer, double amount,
									   String withdrawPassword, String remarks) {
		if(amount <= 0) {
			// ERR:转账金额填写错误
			throw newException("110-02");
		}
//		// 判断是否是我的团队成员
//		boolean testIsMyTeam = getServiceUtils().testIsMyTeam(fromPlayer, toPlayer);
//		if(!testIsMyTeam && fromPlayer.getPid().longValue() != toPlayer.getPlayerId()) {
//			throw newException("115-03");
//		}
		if(StringUtils.isEmpty(fromPlayer.getWithdrawPassword())) {
			// ERR:没有设置资金密码
			throw newException("107-02");
		}
		// 验证资金密码正确
		verifyPaymentPassword(request, fromPlayer,withdrawPassword);

		// 验证账户锁定时间
		Date accountLockTime = fromPlayer.getLockTime();
		if(accountLockTime != null) {
			Moment lockMoment = new Moment().fromDate(accountLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if(isNotTime) {
				// ERR:账户已经锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				Map<String, Object> prams = new HashMap<>();
				prams.put("time", time);
				throw newException("111-09", prams);
			}
		}
		// 验证中心账户余额
		if(fromPlayer.getPlayerAvailableBalance().doubleValue() < amount) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		String outRemarks;
		String inRemarks;
		if(fromPlayer.getPids().contains("[" + toPlayer.getPlayerId() + "]")) {
			outRemarks = "给上级充值";
			inRemarks = "来自下级" + fromPlayer.getPlayerName() + "的充值";
		}else {
			outRemarks = "给下级" + toPlayer.getPlayerName() + "充值";
			inRemarks = "来自上级代理的充值";
		}
		if(!StringUtils.isEmpty(remarks)) {
			outRemarks += "：" + remarks;
			inRemarks += "：" + remarks;
		}
		String billno = RandomUtils.fromTime16();
		BigDecimal amountDecimal = new BigDecimal(amount);


		String tempInRemarks = inRemarks;
		String tempOutRemarks = outRemarks;
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
				// 入账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempInRemarks, toPlayer.getPlayerId(), amountDecimal, amountDecimal.negate(),
						BalanceChangeDirection.INCR,
						(beforeUpPlayer, changePlayerAvailableBalance, changePlayerBlockedBalance, afterUpPlayer,agentPlayerAccountBill) -> {
							// 给目标中心账户用户加提款限制
							boolean updateToLimit =
									getPlayerWithdrawLimitDao().increaseLotteryBalance(toPlayer.getPlayerId(), amountDecimal);
							if(!updateToLimit) {
								throw new IllegalArgumentException();
							}
						}),
				// 出账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempOutRemarks, fromPlayer.getPlayerId(), amountDecimal.negate(), amountDecimal,
						BalanceChangeDirection.DECR, (beforeDownPlayer, changePlayerAvailableBalance,
													  changePlayerBlockedBalance, afterDownPlayer,agentPlayerAccountBill) -> {
					// 扣除我的中心账户提款限制
					boolean updateFromLimit =
							getPlayerWithdrawLimitDao().increaseLotteryBalance(fromPlayer.getPlayerId(), amountDecimal.negate());
					if(!updateFromLimit) {
						throw new IllegalArgumentException();
					}
				}));
		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		return true;
	}

	@Override
	public boolean verifyPaymentPassword(HttpServletRequest request, AgentPlayerInfo player,String withdrawPassword){
		// 获取agent-uid
		String agentUid = request.getHeader("agent-uid");
		if (StringUtils.isEmpty(agentUid)) {
			throw newException("-1", "缺少agent-uid请求头");
		}
		Long playerId = player.getPlayerId();

		PlatformConfig platformConfig = getPlatformConfigDao().getPlatformConfigByGroupAndKey(player.getAgentNo(),"ACCOUNT","PAYMENT_PASSWORD_FAILED_NUM");
		int currentFailedNum = (player.getPaymentPasswordFailedNum() == null) ? 0 : player.getPaymentPasswordFailedNum();
		int maxFailedNum = 0;
		if (platformConfig != null && platformConfig.getValue() != null) {
			maxFailedNum = Integer.parseInt(platformConfig.getValue());
			if (maxFailedNum > 0 && currentFailedNum >= maxFailedNum) {
				throw newException("-1", "资金密码错误次数已达上限" + maxFailedNum + "次");
			}
		}

		// 解密密码
		if (!StringUtils.isEmpty(withdrawPassword)) {
			try {
				withdrawPassword = AESUtils.decrypt(withdrawPassword, agentUid);
			} catch (Exception e) {
				if(maxFailedNum > 0){
					// 使用独立Service，确保新事务生效
					passwordFailedCountService.updatePasswordFailedCount(playerId, currentFailedNum + 1);
					throw newException("-1", "资金密码输入错误，已错误" + (currentFailedNum + 1) +"次，" + maxFailedNum + "次将锁住资金账号");
				}else {
					throw newException("-1", ("资金密码输入错误"));
				}
			}
		}

		// 验证密码
		boolean verifyPassword = CipherUtils.verify(player.getWithdrawPassword(), withdrawPassword);
		if (!verifyPassword) {
			if(maxFailedNum > 0){
				// 使用独立Service，确保新事务生效
				passwordFailedCountService.updatePasswordFailedCount(playerId, currentFailedNum + 1);
				throw newException("-1", "资金密码输入错误，已错误" + (currentFailedNum + 1) +"次，" + maxFailedNum + "次将锁住资金账号");
			}else {
				throw newException("-1", ("资金密码输入错误"));
			}
		}else {
			getAgentPlayerInfoDao().updateAgentPlayerInfoPaymentPasswordFailedNum(playerId, 0);
		}
		return true;
	}



	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyTransferSalary(AgentPlayerInfo fromPlayer, AgentPlayerInfo toPlayer, double amount,
			String withdrawPassword, String remarks) {
		if(amount <= 0) {
			// ERR:转账金额填写错误
			throw newException("110-02");
		}
//		// 判断是否是我的团队成员
//		boolean testIsMyTeam = getServiceUtils().testIsMyTeam(fromPlayer, toPlayer);
//		if(!testIsMyTeam && fromPlayer.getPid().longValue() != toPlayer.getPlayerId()) {
//			throw newException("115-03");
//		}
		if(StringUtils.isEmpty(fromPlayer.getWithdrawPassword())) {
			// ERR:没有设置资金密码
			throw newException("107-02");
		}
		// 验证资金密码正确
		if(!CipherUtils.verify(fromPlayer.getWithdrawPassword(), withdrawPassword)) {
			// ERR:资金密码输入错误
			throw newException("104-01");
		}
		// 验证账户锁定时间
		Date accountLockTime = fromPlayer.getLockTime();
		if(accountLockTime != null) {
			Moment lockMoment = new Moment().fromDate(accountLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if(isNotTime) {
				// ERR:账户已经锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				Map<String, Object> prams = new HashMap<>();
				prams.put("time", time);
				throw newException("111-09", prams);
			}
		}
		// 验证中心账户余额
		if(fromPlayer.getPlayerAvailableBalance().doubleValue() < amount) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		// 获取消费量限制
		BigDecimal fromAccountAvailableBalance = getServiceUtils().getAvailableWithdrawAmount(fromPlayer);
		// 验证彩票可用余额
		if (fromAccountAvailableBalance.doubleValue() < amount) {
			// ERR:最多只能转入amount金额
			Map<String, Object> prams = new HashMap<>();
			double fromAvailableBalance = MathUtils.doubleFormat(fromAccountAvailableBalance.doubleValue(), 2);
			prams.put("amount", fromAvailableBalance);
			throw newException("113-07", prams);
		}
		String outRemarks;
		String inRemarks;
		if(fromPlayer.getPids().contains("[" + toPlayer.getPlayerId() + "]")) {
			outRemarks = "给上级充值";
			inRemarks = "来自下级" + fromPlayer.getPlayerName() + "的充值";
		}else {
			outRemarks = "给下级" + toPlayer.getPlayerName() + "充值";
			inRemarks = "来自上级代理的充值";
		}
		if(!StringUtils.isEmpty(remarks)) {
			outRemarks += "：" + remarks;
			inRemarks += "：" + remarks;
		}
		String billno = RandomUtils.fromTime16();
		BigDecimal amountDecimal = new BigDecimal(amount);
		String tempInRemarks = inRemarks;
		String tempOutRemarks = outRemarks;
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
				// 入账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS, tempInRemarks, toPlayer.getPlayerId(), amountDecimal, amountDecimal.negate(),
						BalanceChangeDirection.INCR,
						null),
				// 出账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(), AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS, tempOutRemarks, fromPlayer.getPlayerId(), amountDecimal.negate(), amountDecimal,
						BalanceChangeDirection.DECR, null));
		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		return true;
	}


	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public boolean applyTransferSalaryV2(HttpServletRequest request,AgentPlayerInfo fromPlayer, AgentPlayerInfo toPlayer, double amount,
										 String withdrawPassword, String remarks) {
		if(amount <= 0) {
			// ERR:转账金额填写错误
			throw newException("110-02");
		}
//		// 判断是否是我的团队成员
//		boolean testIsMyTeam = getServiceUtils().testIsMyTeam(fromPlayer, toPlayer);
//		if(!testIsMyTeam && fromPlayer.getPid().longValue() != toPlayer.getPlayerId()) {
//			throw newException("115-03");
//		}
		if(StringUtils.isEmpty(fromPlayer.getWithdrawPassword())) {
			// ERR:没有设置资金密码
			throw newException("107-02");
		}
		// 验证资金密码正确
		verifyPaymentPassword(request, fromPlayer, withdrawPassword);
		// 验证账户锁定时间
		Date accountLockTime = fromPlayer.getLockTime();
		if(accountLockTime != null) {
			Moment lockMoment = new Moment().fromDate(accountLockTime);
			boolean isNotTime = new Moment().le(lockMoment);
			if(isNotTime) {
				// ERR:账户已经锁定
				String time = lockMoment.format("yyyy年MM月dd日HH时mm分ss秒");
				Map<String, Object> prams = new HashMap<>();
				prams.put("time", time);
				throw newException("111-09", prams);
			}
		}
		// 验证中心账户余额
		if(fromPlayer.getPlayerAvailableBalance().doubleValue() < amount) {
			// ERR:账户余额不足
			throw newException("106-01");
		}
		// 获取消费量限制
		BigDecimal fromAccountAvailableBalance = getServiceUtils().getAvailableWithdrawAmount(fromPlayer);
		// 验证彩票可用余额
		if (fromAccountAvailableBalance.doubleValue() < amount) {
			// ERR:最多只能转入amount金额
			Map<String, Object> prams = new HashMap<>();
			double fromAvailableBalance = MathUtils.doubleFormat(fromAccountAvailableBalance.doubleValue(), 2);
			prams.put("amount", fromAvailableBalance);
			throw newException("113-07", prams);
		}
		String outRemarks;
		String inRemarks;
		if(fromPlayer.getPids().contains("[" + toPlayer.getPlayerId() + "]")) {
			outRemarks = "给上级充值";
			inRemarks = "来自下级" + fromPlayer.getPlayerName() + "的充值";
		}else {
			outRemarks = "给下级" + toPlayer.getPlayerName() + "充值";
			inRemarks = "来自上级代理的充值";
		}
		if(!StringUtils.isEmpty(remarks)) {
			outRemarks += "：" + remarks;
			inRemarks += "：" + remarks;
		}
		String billno = RandomUtils.fromTime16();
		BigDecimal amountDecimal = new BigDecimal(amount);
		String tempInRemarks = inRemarks;
		String tempOutRemarks = outRemarks;
		List<PlayerBalanceUpdateParams> playerBalanceUpdateParamsList = Arrays.asList(
				// 入账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempInRemarks, toPlayer.getPlayerId(), amountDecimal, amountDecimal.negate(),
						BalanceChangeDirection.INCR,
						null),
				// 出账
				new PlayerBalanceUpdateParams(fromPlayer.getPlayerName(),AgentPlayerAccountBill.BILL_TYPE_ACCOUNT_TRANS,tempOutRemarks, fromPlayer.getPlayerId(), amountDecimal.negate(), amountDecimal,
						BalanceChangeDirection.DECR, null));
		agentPlayerInfoLockService.updatePlayerBalance(playerBalanceUpdateParamsList);
		return true;
	}



	/**
	 * 处理合约配置中的extraRules，比较salaryRate和scalePoint，并应用合约编辑
	 *
	 * @param player 当前用户
	 * @param downPlayerName 下级用户名
	 * @param salaryRate 工资比例
	 * @throws ServiceException 如果处理失败
	 */
	@Transactional
	public void processContractExtraRules(AgentPlayerInfo player, String downPlayerName, Double salaryRate) throws ServiceException {
		if (salaryRate == null) {
			return;
		}

		List<ContractPlayerConfig> list = getContractPlayerConfigDao()
				.listByAgentNoAndContractTypeAndAccountId(player.getAgentNo(), 3, player.getPlayerId());

		// 判空并处理合约配置
		if (list != null && !list.isEmpty()) {


			ContractPlayerConfig matchedConfig = null;
			for (ContractPlayerConfig config : list) {
				// 平台契约
				ContractPlatformConfig platformConfig = getContractPlatformConfigDao().getByContractCodeAndStatus(config.getAgentNo(),
						config.getContractCode(), ContractPlatformConfig.CONTRACT_STATUS_ENABLE);
				if (platformConfig != null) {
					matchedConfig = config;
					break; // 找到符合条件的平台契约，结束循环
				}
			}
			if (matchedConfig== null) {
				throw newException("122-01");
			}
			String extraRules = matchedConfig.getExtraRules();
			String newExtraRules = null;

			if (extraRules != null && !extraRules.isEmpty()) {
				try {
					log.info("extraRules 内容: {}", extraRules); // 打印 extraRules 内容

					// 平台契约
					ContractPlatformConfig platformConfig = getContractPlatformConfigDao().getByContractCodeAndStatus(matchedConfig.getAgentNo(),
							matchedConfig.getContractCode(), ContractPlatformConfig.CONTRACT_STATUS_ENABLE);
					if(platformConfig == null) {
						throw newException("122-19");
					}

					if(ContractPlatformConfig.CYCLE_TYPE_DAY == platformConfig.getCycleType()) {
						// 日
						// 上级契约规则
						ContractDailyConditions upConditions = JSON.parseObject(matchedConfig.getExtraRules(),
								ContractDailyConditions.class);
						if(upConditions == null || upConditions.getRules() == null || upConditions.getRules().isEmpty()) {
							throw newException("122-08");
						}
						List<ContractDailyRules> rules = upConditions.getRules();
						if(rules == null || rules.isEmpty()) {
							throw newException("122-07");
						}

						for(int i = 0; i < rules.size(); i++) {
							for(int j = i + 1; j < rules.size(); j++) {
								if(rules.get(j).getScalePoint() < rules.get(i).getScalePoint()) {
									ContractDailyRules tmp = rules.get(i);
									rules.set(i, rules.get(j));
									rules.set(j, tmp);
								}
							}
						}
						ContractDailyRules rule = rules.get(0);
						double minScalePoint = rule.getScalePoint();
						// 比较salaryRate和scalePoint
						if (salaryRate > minScalePoint) {
							throw newException("工资比例不能高于scalePoint");
						}else {
							rule.setScalePoint(salaryRate);
							List<ContractDailyRules> dailyRules = new ArrayList<>();
							dailyRules.add(rule);
							newExtraRules = JSONArray.toJSONString(dailyRules);
						}

					}else {
						// 周/半月/月
						// 上级契约规则
						ContractPeriodConditions upConditions = JSON.parseObject(matchedConfig.getExtraRules(),
								ContractPeriodConditions.class);
						if (upConditions == null || upConditions.getRules() == null || upConditions.getRules().isEmpty()) {
							throw newException("122-08");
						}
						List<ContractPeriodRules> rules = upConditions.getRules();
						if(rules == null || rules.isEmpty()) {
							throw newException("122-07");
						}
						for(int i = 0; i < rules.size(); i++) {
							for(int j = i + 1; j < rules.size(); j++) {
								if(rules.get(j).getScalePoint() < rules.get(i).getScalePoint()) {
									ContractPeriodRules tmp = rules.get(i);
									rules.set(i, rules.get(j));
									rules.set(j, tmp);
								}
							}
						}
						ContractPeriodRules rule = rules.get(0);
						double minScalePoint = rule.getScalePoint();
						// 比较salaryRate和scalePoint
						if (salaryRate > minScalePoint) {
							throw newException("工资比例不能高于scalePoint");
						}else {
							rule.setScalePoint(salaryRate);
							List<ContractPeriodRules> periodRules = new ArrayList<>();
							periodRules.add(rule);
							newExtraRules = JSONArray.toJSONString(periodRules);

						}
					}

					// 调用合约服务
					String contractCode = matchedConfig.getContractCode();
					getContractService().applyEditContract(player, downPlayerName, contractCode, newExtraRules);
				} catch (Exception e) {
					log.error("处理extraRules时发生错误: " + e.getMessage(), e);
					throw newException("合约配置处理失败");
				}
			}
		}
	}



	/**
	 * 添加用户并处理合约配置
	 *
	 * @param request HTTP请求
	 * @param player 当前用户
	 * @param addUserVo 添加用户的信息
	 * @param salaryRate 工资比例
	 * @throws ServiceException 如果处理失败
	 */
	@Transactional
	public void addUserAndProcessContract(HttpServletRequest request, AgentPlayerInfo player, AddUserVo addUserVo, Double salaryRate) throws ServiceException {
		// 添加用户
		addDownUser(request, player, addUserVo);

		// 处理合约配置
		processContractExtraRules(player, addUserVo.getUsername(), salaryRate);
	}

	/**
	 * 添加注册链接并处理合约配置
	 *
	 * @param player 当前用户
	 * @param playerType 用户类型
	 * @param point 返点
	 * @param salaryRate 工资比例
	 * @return 创建的注册链接
	 * @throws ServiceException 如果处理失败
	 */
	@Transactional
	public AgentPlayerRegistLink addRegistLinkAndProcessContract(AgentPlayerInfo player, int playerType, double point, Double salaryRate) throws ServiceException {
		// 生成邀请码
		String inviteCode = RandomUtils.generate(12, false);

		AgentPlayerRegistLink link = new AgentPlayerRegistLink();
		link.setAgentNo(player.getAgentNo());
		link.setAgentName(player.getAgentName());
		link.setPlayerId(player.getPlayerId());
		link.setPlayerName(player.getPlayerName());
		link.setPid(player.getPid());
		link.setPids(player.getPids());
		link.setInviteCode(inviteCode);
		link.setPlayerType(playerType);
		link.setPoint(point);
		link.setUseTimes(0);
		link.setCreateTime(new Date());

		boolean saved = getAgentPlayerRegistLinkDao().save(link);
		if (!saved) {
			throw newException("注册链接保存失败");
		}

		// 处理合约配置 - 这里使用inviteCode作为downPlayerName的占位符
		// 实际应用中可能需要根据业务逻辑调整
		processContractExtraRules(player, inviteCode, salaryRate);

		return link;
	}
}
