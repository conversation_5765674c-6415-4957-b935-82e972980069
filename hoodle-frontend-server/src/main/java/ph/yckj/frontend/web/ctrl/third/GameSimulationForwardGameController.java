package ph.yckj.frontend.web.ctrl.third;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import myutil.JacksonUtils;
import myutil.Moment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;
import ph.yckj.common.util.ServiceException;
import ph.yckj.frontend.util.GameSimulationUtils;
import ph.yckj.frontend.web.helper.Route;
import ph.yckj.frontend.web.helper.SuperController;
import sy.hoodle.base.common.dao.impl.GameSimulationTransferLimitDaoImpl;
import sy.hoodle.base.common.entity.AgentPlayerInfo;
import sy.hoodle.base.common.entity.GameSimulationPlatPlatformType;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * 真人游戏进入游戏请求类
 */
@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.OutForward.PATH)
public class GameSimulationForwardGameController extends SuperController {

	@Autowired
	private GameSimulationUtils gameSimulationUtils;

	@RequestMapping(Route.OutForward.FORWARD_MODE_DIRECT)
	public ModelAndView FORWARD_MODE_DIRECT(HttpServletRequest request) {
		try {
			String params = request.getParameter("params");

			String paramsJson = gameSimulationUtils.paramsDecrypt(params);
			JsonNode rootNode = JacksonUtils.toJsonNode(paramsJson);
			String timestampStr = rootNode.path("timestamp").asText();
			if(!checkTimestamp(timestampStr, 30)) {// 链接30分钟内有效
				return new ModelAndView("/error");
			}
			Map<String, String> paramsMap = new HashMap<>();
			String gameUrl = rootNode.path("game_url").asText();
			String transferAllInFlag = rootNode.path("transferAllInFlag").asText();
			if("TRUE".equals(transferAllInFlag)) {
				String platformCode = rootNode.path("platformCode").asText();
				String transferResult = transferAllBalance2Game(request, platformCode);
				if(!"SUCCESS".equals(transferResult)) {
					paramsMap.put("msg", transferResult);
					// return new ModelAndView("/error", paramsMap);
				}
			}
			String formMethod = "post";
			if(rootNode.hasNonNull("formMethod")) {
				formMethod = rootNode.path("formMethod").asText();
			}
			paramsMap.put("game_url", gameUrl);
			if(formMethod.equals("post")) {
				return new ModelAndView("/forward-game-direct", paramsMap);
			}else {
				return new ModelAndView("/forward-game-get-direct", paramsMap);
			}
		}catch(Exception e) {
			return new ModelAndView("/error");
		}
	}

	private String transferAllBalance2Game(HttpServletRequest request, String platformCode) {
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);

			// getSessionUserPasswordPass(request);
			// checkAccountHints(player);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}

			String config = getSystemConfigDao().getValueByGroupAndKey("GAME_SIMULATION", "TRANSFER_LIMIT_CONTROL");
			boolean simulationLimitControl = "1".equals(config);
			if(simulationLimitControl &&
					!getGameSimulationTransferLimitDao().addTransfer(player.getAgentNo(), player.getPlayerName(),
							GameSimulationTransferLimitDaoImpl.TRANSFER_TYPE_DEPOSIT)) {
				throw newException("124-08");
			}

			if(player.getOutThirdAutoTransfer() != AgentPlayerInfo.AUTO_TRANSFER_ON) {
				// 不需要免转
				return "SUCCESS";
			}
			BigDecimal amount = player.getPlayerAvailableBalance();
			if(amount.doubleValue() <= 0.0) {
				return "SUCCESS";// 没有钱就不需要再转
			}

			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}

			// 0处理中，1处理完成，-1处理失败
			int result = getGameSimulationService().balanceLottery2GameV2(player, platform, amount, true);
			if(-1 != result) {
				return "SUCCESS";
			}else {
				return "自动上账失败，请联系客服务";
			}
		}catch(ServiceException e) {
			log.info("FORWARD_MODE_DIRECT ServiceException:" + e);
			return e.getMessage();
		}catch(Throwable t) {
			log.info("FORWARD_MODE_DIRECT Throwable:" + t);
		}
		return "UNKNOWN ERROR";
	}

	@RequestMapping(Route.OutForward.FORWARD_MODE_AUTHORITY)
	public ModelAndView FORWARD_MODE_AUTHORITY(HttpServletRequest request) {
		try {
			String params = request.getParameter("params");

			String paramsJson = gameSimulationUtils.paramsDecrypt(params);
			JsonNode rootNode = JacksonUtils.toJsonNode(paramsJson);
			String timestampStr = rootNode.path("timestamp").asText();
			if(!checkTimestamp(timestampStr, 30)) {// 链接30分钟内有效
				return new ModelAndView("/error");
			}
			String gameUrl = rootNode.path("game_url").asText();
			String javascriptUrl = rootNode.path("javascript_url").asText();
			String username = rootNode.path("username").asText();
			String password = rootNode.path("password").asText();
			Map<String, String> paramsMap = new HashMap<>();
			paramsMap.put("game_url", gameUrl);
			paramsMap.put("javascript_url", javascriptUrl);
			paramsMap.put("username", username);
			paramsMap.put("password", password);
			return new ModelAndView("/forward-game-authority", paramsMap);
		}catch(Exception e) {
			return new ModelAndView("/error");
		}
	}

	@RequestMapping(Route.OutForward.FORWARD_GAME_ERROR)
	public ModelAndView FORWARD_GAME_ERROR() {
		return new ModelAndView("/error");
	}

	/**
	 * 时间校验
	 */
	private boolean checkTimestamp(String timestampStr, int diffMinite) {
		Moment timestampSrc = new Moment().fromTime(new Timestamp(Long.parseLong(timestampStr)));
		Moment timestampDest = new Moment();
		int timeDiff = timestampDest.difference(timestampSrc, "minute");
		if(timeDiff < 0 || timeDiff > diffMinite) {
			return false;
		}
		return true;
	}
}
