package ph.yckj.frontend.web.helper;

public class Route {

	public static final String PATH = "/api";

	// 网页登录
	public static final String WEB_LOGIN = "/web-login";

	public static final String LOGIN = "/login";

	public static final String LOGIN_V2 = "/login-v2";

	public static final String METRICS = "/metrics";

	public static final String EMAIL_LOGIN = "/emailLogin";

	public static final String TELEPHONE_LOGIN = "/telephoneLogin";

	public static final String REGISTER = "/register";

	public static final String REGISTER_AND_CONTRACT = "/register-and-contract";

	public static final String RETRIEVE_PASSWORD = "/retrieve-password";

	public static final String SEND_VERIFY_CODE = "/send-verify-code";

	// 游客登录
	public static final String GUEST_LOGIN = "/guest-login";

	// 用户退出
	public static final String LOGOUT = "/logout";

	public class WebAjax {
		public static final String PATH = "/web-ajax";

		// 是否登录
		public static final String IS_LOGIN = "/is-login";

		public static final String CONFIG = "/config";

		// 初始化数据
		public static final String INIT_DATA = "/init-data";
		// 初始化数据，增加了游戏分组功能，为了不影响前端，所以改为新版本接口。
		// TODO 待各平台各端都对接好且发布生产后，删除INIT_DATA接口
		public static final String INIT_DATA2 = "/init-data2";

		public static final String LOOP_PLAYER = "/loop-player";

		public static final String DOWNLOAD_LINK = "/download-link";

		// 首页推荐游戏列表
		public static final String GAME_RECOMMEND = "/game-recommend";
	}

	public class System {
		public static final String PATH = "/system";

		// 获取系统公告
		public static final String GET_SYSTEM_NOTICE = "/get-system-notice";
		// 添加公告已读状态
		public static final String ADD_SYSTEM_NOTICE_READED = "/add-system-notice-readed";

	}

	public class GameLottery {
		public static final String PATH = "/game-lottery";

		public static final String SEARCH_LOTTERY_TYPE = "/search-lottery-type";

		public static final String SEARCH_LOTTERY_GROUP = "/search-lottery-group";

		public static final String SEARCH_LOTTERY_INFO = "/search-lottery-info";

		public static final String SEARCH_LIVE_INFO = "/search-live-info";

		// 游戏彩票投注
		public static final String ADD_ORDER = "/add-order";
		// 撤销订单
		public static final String CANCEL_ORDER = "/cancel-order";

		// 彩票游戏开奖时间
		public static final String STATIC_OPEN_TIME = "/static-open-time";

		// 列出彩票玩法
		public static final String LIST_LOTTERY_METHOD = "/list-lottery-method";
		// 列出部分或全部彩票玩法
		public static final String LIST_LOTTERY_METHOD_ALL = "/list-lottery-method-all";

		// 列出游戏开奖号码
		public static final String LIST_LOTTERY_OPEN_CODE = "/list-lottery-open-code";

		// 列出所有生肖对应的号码
		public static final String LIST_SHENGXIAO_BALLS = "/list-shengxiao-balls";

		// 获取大富翁游戏
		public static final String GET_GAME_MONOPOLY = "/get-game-monopoly";

		public static final String PLAYER_GAME_ISSUE_DETAILS = "/player-game-issue-details";

		// 玩家意见反馈
		public static final String PLAYER_OPINION_COMMIT = "/player-opinion-commit";

		// 联系我们
		public static final String GET_CONTACT_US = "/contact-us";

		// 拉取开奖通知
		public static final String PULL_OPEN_NOTICE = "/pull-open-notice";

		// 走势数据
		public static final String TREND_DATA = "/trend-data";

		// 盈利排行榜
		public static final String PROFIT_RANKING = "/profit-ranking";

		public static final String LOAD_TASK_INFO = "/load-task-info";

	}

	public class Agent {
		public static final String PATH = "/agent";

		// 玩家信息
		public static final String GET_PLAYER_DETAILS = "/get-player-details";

		// 个人账单
		public static final String SEARCH_PLAYER_BILL = "/search-player-bill";

		// 投注记录
		public static final String SEARCH_PLAYER_GAME_RECORD = "/search-player-game-record";

		// 投注记录详情
		public static final String GET_PLAYER_GAME_RECORD_DETAILS = "/get-player-game-details";

		// 获取代理商会员转账记录
		public static final String SEARCH_PLAYER_TRANSFER_RECORD = "/search-player-transfer-record";

		// 营收情况
		public static final String GET_PLAYER_GAME_REPORT = "/get-player-game-report";

		public static final String GET_PLAYER_REPORT_TOTAL = "get-player-report-total";

		// 新版营收情况
		public static final String GET_PLAYER_GAME_REPORT_NEW = "/get-player-game-report-new";

		public static final String GET_PLAYER_REPORT_TOTAL_NEW = "/get-player-report-total-new";

		// 个人 报表 汇总
		public static final String GET_PLAYER_REPORT_ALL_TOTAL = "/get-player-report-all-total";



		public static final String UPDATE_PLAYER_NICK = "/update-player-nick";

		public static final String UPDATE_PLAYER_LANGUAGE = "/update-player-language";

		public static final String UPDATE_PLAYER_SKIN = "/update-player-skin";

		public static final String UPDATE_PLAYER_MUSIC = "/update-player-music";

		public static final String GET_PLAYER_SECURITY_INFO = "/get-player-security-info";

		public static final String SAVE_PLAYER_WITHDRAW_NAME = "/save-player-withdraw-name";

		public static final String UPDATE_PLAYER_WITHDRAW_PASSWORD = "/update-player-withdraw-password";

		public static final String UPDATE_PLAYER_WITHDRAW_PASSWORD_V2 = "/update-player-withdraw-password-v2";

		public static final String TEAM_SUMMARY_INFO = "/team-summary-info";

		public static final String TEAM_COMMISSION_RULE = "/team-commission-rule";

		public static final String TEAM_REBATE_RULE = "/team-rebate-rule";

		public static final String TEAM_COMMISSION_ISSUE = "/team-commission-issue";

		public static final String TEAM_REBATE_ISSUE = "/team-rebate-issue";

		public static final String TEAM_REPORT_INFO = "/team-report-info";

		public static final String TEAM_MEMBER_LIST = "/team-member-list";

		// 会员管理列表接口
		public static final String TEAM_MEMBER_MANAGE_LIST = "/team-member-manage-list";

		// 准备添加新用户
		public static final String PREPARE_ADD_ACCOUNT = "/prepare-add-account";
		// 代理添加下级
		public static final String ADD_USER = "/add-user";
		// 添加注册链接
		public static final String ADD_REGIST_LINK = "/add-regist-link";

		// 代理添加下级并同时签订三方游戏契约
		public static final String ADD_USER_AND_CONTRACT = "/add-user-contract";
		// 添加注册链接并同时签订三方游戏契约
		public static final String ADD_REGIST_LINK_AND_CONTRACT = "/add-regist-link-contract";

		// 列出注册链接
		public static final String LIST_REGIST_LINK = "/list-regist-link";
		// 删除注册链接
		public static final String DELETE_REGIST_LINK = "/delete-regist-link";
		// 准备彩票升点
		public static final String PREPARE_EDIT_POINT = "/prepare-edit-point";

		// 上级扣款下级
		public static final String APPLY_UP_DED_TRANSFER = "/apply-up-ded-transfer";

		public static final String APPLY_UP_DED_TRANSFER_V2 = "/apply-up-ded-transfer-v2";

		// 彩票升点
		public static final String EDIT_POINT = "/edit-point";
		// 准备上下级转账
		public static final String PREPARE_TRANSFER = "/prepare-transfer";
		// 上下级转账
		public static final String APPLY_TRANSFER = "/apply-transfer";

		public static final String APPLY_TRANSFER_V2 = "/apply-transfer-v2";

		// 获取发言词库
		public static final String GET_THESAURUS = "/thesaurus";

		// 获取手机号前缀
		public static final String PHONE_PREFIX = "/phone-prefix";

		// 获取服务可用国家或地区
		public static final String COUNTRY = "/country";
	}

	public class AgentPlayer {

		public static final String PATH = "/agent/player";

		// 盈利排行榜
		public static final String PROFIT_RANKING = "/profit-ranking";

		public static final String WALLET = "/agent/player/wallet";

		// 修改登录密码
		public static final String UPDATE_LOGIN_PASSWORD = "/update-login-password";

		// 获取Google验证码绑定信息
		public static final String GOOGLE_BIND_PICTURE = "/google-bind-picture";
		// 确认Google绑定验证
		public static final String GOOGLE_BIND_CONFIRM = "/google-bind-confirm";

		// 设置Google验证码登录
		public static final String SET_GOOGLE_LOGIN = "/set-google-login";

		public static final String PLAYER_PROFILE = "/player-profile";

		public static final String BIND_EMAIL = "/bind-email";
		public static final String BIND_TELEPHONE = "/bind-telephone";
		public static final String UPDATE_BASIC_INFO = "/update-basic-info";

		public static final String ALLOWED_USER_TYPE = "/allowed-user-type";
	}

	public class TeamReport {
		public static final String PATH = "/team/report";
		public static final String TEAM_PROFIT_AND_LOSS = "/profit-and-loss";
		public static final String SEARCH_TEAM_PLAYER_BILL = "/search-team-player-bill";
		public static final String SEARCH_TEAM_RECHARGE = "/search-team-recharge";
		public static final String SEARCH_TEAM_WITHDRAW = "/search-team-withdraw";
		public static final String SEARCH_TEAM_GAME_RECORD = "/search-team-game-record";
		public static final String SEARCH_TEAM_ACTIVITY_RECORD = "/search-team-activity-record";
		public static final String SEARCH_TEAM_REBATE_RECORD = "/search-team-rebate-record";
		public static final String SEARCH_TEAM_COMMISSION_RECORD = "/search-team-commission-record";

	}

	public static class Contract {
		// 契约
		public static final String PATH = "/contract";
		// 显示契约
		public static final String LOAD_CONTRACT_STATUS = "/load-contract-status";
		// 我的契约
		public static final String LOAD_CONTRACT = "/load-contract";
		// 契约下级
		public static final String LIST_CONTRACT_ACCOUNT = "/list-contract-account";
		// 准备签订契约
		public static final String PREPARE_EDIT_CONTRACT = "/prepare-edit-contract";
		// 签订契约
		public static final String APPLY_EDIT_CONTRACT = "/apply-edit-contract";
		// 删除下级契约
		public static final String REMOVE_DOWN_CONTRACT = "/remove-down-contract";
		// 契约发放记录
		public static final String LIST_CONTRACT_RECORD = "/list-contract-record";
		// 发放契约
		public static final String DRAW_CONTRACT_RECORD = "/draw-contract-record";
	}

	public class Chat {
		public static final String PATH = "/chat";

		// 玩家信息
		public static final String GET_PLAYER_CHAT_INFO = "/get-player-chat-info";

		// 初始化
		public static final String INIT_PLAYER_CHAT_INFO = "/init-player-chat";

		public static final String INIT_PLAYER_CHAT_GROUP = "/init-player-chat-group";

		// 礼物lie
		public static final String LIST_GIFT_INFO = "/list-gift-info";
		// 礼物列表分类版
		public static final String LIST_GIFT_INFO_CATEGORY = "/list-gift-info-category";

		// 送礼物
		public static final String SEND_GIFT = "/send-gift";

		// 打赏
		public static final String REWARD = "/reward";

	}

	public class External {
		public static final String PATH = "/external";

		// 验证登录链接登录
		public static final String URL_VERIFY_LOGIN = "/url-verify-login";
	}

	public class Activity {
		public static final String PATH = "/activity";

		// 活动列表
		public static final String LIST_ACTIVITY = "/list-activity";
		public static final String LIST_ACTIVITY_TYPE = "/list-activity-type";
		public static final String LIST_ACTIVITY_BANNER = "/list-activity-banner";
		public static final String LIST_ACTIVITY_BANNER_LIST = "/list-activity-banner-list";
		public static final String ACTIVITY_BANNER_LIST_DETAIL = "/activity-banner-list-detail";
		public static final String ACTIVITY_RECORD = "/activity-record";
		public static final String ACTIVITY_INVITE_RECORD = "/activity-invite-record";
		public static final String CONFIRM_JOIN = "/confirm-join";
	}

	public class Payment {
		public static final String PATH = "/payment";

		public static final String CONFIG = "/config";

		public static final String LIST_BANK = "/list-bank";

		public static final String ADD_CARD_BANK = "/add-card-bank";
		public static final String DELETE_CARD_BANK = "/delete-card-bank";
		public static final String ADD_CARD_BANK_V2 = "/add-card-bank-v2";
		public static final String DELETE_CARD_BANK_V2 = "/delete-card-bank-v2";
		public static final String LIST_CARD_BANK = "/list-card-bank";
		public static final String SET_DEFAULT_BANK = "/set-default-bank";

		public static final String ADD_CARD_USDT = "/add-card-usdt";
		public static final String UPDATE_CARD_USDT = "/update-card-usdt";
		public static final String DELETE_CARD_USDT = "/delete-card-usdt";
		public static final String ADD_CARD_USDT_V2= "/add-card-usdt-v2";
		public static final String UPDATE_CARD_USDT_V2 = "/update-card-usdt-v2";
		public static final String DELETE_CARD_USDT_V2 = "/delete-card-usdt-v2";
		public static final String LIST_CARD_USDT = "/list-card-usdt";
		public static final String SET_DEFAULT_USDT = "/set-default-usdt";

		public static final String ADD_CARD_ALIPAY = "/add-card-alipay";
		public static final String UPDATE_CARD_ALIPAY = "/update-card-alipay";
		public static final String DELETE_CARD_ALIPAY = "/delete-card-alipay";
		public static final String ADD_CARD_ALIPAY_V2 = "/add-card-alipay-v2";
		public static final String UPDATE_CARD_ALIPAY_V2 = "/update-card-alipay-v2";
		public static final String DELETE_CARD_ALIPAY_V2 = "/delete-card-alipay-v2";
		public static final String LIST_CARD_ALIPAY = "/list-card-alipay";
		public static final String SET_DEFAULT_ALIPAY = "/set-default-alipay";

		public static final String ADD_CARD_M = "/add-card-m";
		public static final String UPDATE_CARD_M = "/update-card-m";
		public static final String DELETE_CARD_M = "/delete-card-m";
		public static final String ADD_CARD_M_V2 = "/add-card-m-v2";
		public static final String UPDATE_CARD_M_V2 = "/update-card-m-v2";
		public static final String DELETE_CARD_M_V2 = "/delete-card-m-v2";
		public static final String LIST_CARD_M = "/list-card-m";
		public static final String SET_DEFAULT_M = "/set-default-m";

		public static final String ADD_CARD_HH5 = "/add-card-hh5";
		public static final String UPDATE_CARD_HH5 = "/update-card-hh5";
		public static final String DELETE_CARD_HH5 = "/delete-card-hh5";
		public static final String ADD_CARD_HH5_V2 = "/add-card-hh5-v2";
		public static final String UPDATE_CARD_HH5_V2 = "/update-card-hh5-v2";
		public static final String DELETE_CARD_HH5_V2 = "/delete-card-hh5-v2";
		public static final String LIST_CARD_HH5 = "/list-card-hh5";
		public static final String SET_DEFAULT_HH5 = "/set-default-hh5";

		public static final String ADD_CARD_OKG = "/add-card-okg";
		public static final String UPDATE_CARD_OKG = "/update-card-okg";
		public static final String DELETE_CARD_OKG = "/delete-card-okg";
		public static final String ADD_CARD_OKG_V2 = "/add-card-okg-v2";
		public static final String UPDATE_CARD_OKG_V2 = "/update-card-okg-v2";
		public static final String DELETE_CARD_OKG_V2 = "/delete-card-okg-v2";
		public static final String LIST_CARD_OKG = "/list-card-okg";
		public static final String SET_DEFAULT_OKG = "/set-default-okg";

		public static final String PREPARE_PAYMENT_USDT_RATE = "/prepare-payment-usdt-rate";

		public static final String PREPARE_ALL_PAY = "/prepare-all-pay";
		public static final String APPLY_THIRD_PAY = "/apply-third-pay";
		public static final String APPLY_TRANSFER_PAY = "/apply-transfer-pay";

		public static final String PREPARE_WITHDRAW = "/prepare-withdraw";
		public static final String APPLY_WITHDRAW = "/apply-withdraw";
		public static final String APPLY_WITHDRAW_V2 = "/apply-withdraw-v2";
		public static final String APPLY_WITHDRAW_M = "/apply-withdraw-m";
		public static final String APPLY_WITHDRAW_HH5 = "/apply-withdraw-hh5";
		public static final String APPLY_WITHDRAW_OKG = "/apply-withdraw-okg";

		public static final String SEARCH_RECHARGE = "/search-recharge";
		public static final String SEARCH_WITHDRAW = "/search-withdraw";

		public static final String GO_PAY_RECHARGE = "/go-pay-recharge";
		public static final String CANCELED_RECHARGE = "/canceled-recharge";
		public static final String AWAIT_CONFIRMED_RECHARGE = "/await-confirmed-recharge";

	}
	// 推广模块
	public class Promote {
		// 推广模块根路径
		public static final String PATH = "/promote";

		// 查询佣金记录
		public static final String SEARCH_COMMISSION_RECORD = "/search-commission-record";
		// 查询返水记录
		public static final String SEARCH_REBATE_RECORD = "/search-rebate-record";

		// 佣金规则文案
		public static final String COMMISSION_RULE_TEXT = "/commission-rule-text";

		// 返水规则文案
		public static final String REBATE_RULE_TEXT = "/rebate-rule-text";

	}

	// 三方游戏
	public static class Third {
		// 根路径
		public static final String PATH = "/out";
		// 三方游戏场馆类型列表
		public static final String LIST_THIRD_TYPE = "/list-third-type";
		// 三方游戏场馆类型列表
		public static final String LIST_THIRD_PLATFORM = "/list-third-platform";
		// 三方游戏列表
		public static final String LIST_THIRD_GAME = "/list-third-game";
		// 免转配置
		public static final String AUTO_TRANSFER = "/auto-transfer";

		// 列出所有生效的真人游戏
		public static final String LIST_ALL_GAMES = "/list-all-games";
		// 开通游戏
		public static final String REGISTER_GAME = "/register-game";
		// 进入游戏（获取游戏url及参数）
		public static final String ENTER_GAME = "/enter-game";
		// 进入游戏V2版本（使用批量余额更新）
		public static final String ENTER_GAME_V2 = "/enter-game-v2";
		// 退出游戏
		public static final String LEAVE_GAME = "/leave-game";
		// app进入游戏（获取游戏用户名密码等参数，不同第三方游戏要求的参数不一样）
		public static final String ENTER_GAME_APP = "/enter-game-app";
		// 进入游戏（获取游戏url及参数）
		public static final String ENTER_GAME_RELATIVELY = "/enter-game-relatively";
		// 查询余额
		public static final String QUERY_BALANCE = "/query-balance";
		// 游戏转账（中心账户到游戏账户）
		public static final String TRANSFER_LOTTERY_TO_GAME = "/transfer-lottery-to-game";
		// 游戏转账V2版本（中心账户到游戏账户，使用批量余额更新）
		public static final String TRANSFER_LOTTERY_TO_GAME_V2 = "/transfer-lottery-to-game-v2";
		// 游戏转账（游戏账户到中心账户）
		public static final String TRANSFER_GAME_TO_LOTTERY = "/transfer-game-to-lottery";
		// 搜索转账记录
		public static final String SEARCH_TRANSFER = "/search-transfer";
		// 搜索团队转账记录
		public static final String TEAM_SEARCH_TRANSFER = "/team-search-transfer";
		// 搜索游戏记录
		public static final String SEARCH_PLAYRECORD_DETAIL = "/search-playrecord-detail";
		// 搜索游戏记录（10分钟统计为一条记录）
		public static final String SEARCH_PLAYRECORD = "/search-playrecord";
		// 搜索游戏记录
		public static final String SEARCH_PLAYRECORD_REPORT = "/search-playrecord-report";
		// 查询Flash Tech订单
		public static final String SEARCH_FLASH_TECH_REPORT = "/search-flash-tech-report";
		// 查询游戏平台详细订单
		public static final String SEARCH_GAME_PLATFORM_DETAIL_ORDER = "/search-game-platform-detail-order";
		// 查询游戏二级菜单
		public static final String SEARCH_GAME_ICON = "/search-game-icon";
		// 一键游戏转账（所有游戏账户到中心账户）
		public static final String TRANSFER_ALL_GAME_TO_LOTTERY = "/transfer-all-game-to-lottery";
		// 取得游戏平台子游戏列表
		public static final String SEARCH_PLATFORM_SUB_GAME_LIST = "/search-platform-sub-game-list";
		// 获取子游戏链接
		public static final String ENTER_SUB_GAME = "/enter-sub-game";
		// 查询全游戏余额
		public static final String QUERY_BALANCE_ALL = "/query-balance-all";
		// 回收所有游戏余额
		public static final String RECYCLE_BALANCE_ALL = "/recycle-balance-all";
		public static final String RECYCLE_BALANCE_BY_GAME = "/recycle-balance-by-game";

		//团队总览
		public static final String TEAM_OVERVIEW_INFO = "/team-overview-info";
		//汇总详情
		public static final String TEAM_OVERVIEW_DETAIL = "/team-overview-detail";
		//彩票总览
		public static final String TEAM_LOTTERY_INFO = "/team-lottery-info";
		//彩票总览详情
		public static final String TEAM_LOTTERY_DETAIL = "/team-lottery-detail";
		//三方总览
		public static final String TEAM_THIRD_VIEW_INFO = "/team-third-view-info";
		//三方总览详情
		public static final String TEAM_THIRD_VIEW_DETAIL = "/team-third-view-detail";
		//查询三方佣金方案
		public static final String SEARCH_COMMISSION_RECORD = "/search-commission-record";
		//查询三方返水方案
		public static final String SEARCH_REBATE_RECORD = "/search-rebate-record";
		//查询三方投注
		public static final String SEARCH_THIRD_GAME_RECORD = "/search-third-game-record";
		//查询场馆明细
		public static final String SEARCH_THIRD_VENUE_DETAIL = "/search-third-venue-detail";

		public static final String MANUAL_SYNC_GAME_RECORD = "/manual-sync-game-record";

		// 彩票总览
		public static final String TEAM_LOTTERY_INFO_V2 = "/team-lottery-info-v2";
	}

	// 进入真人游戏
	public static class OutForward {
		public static final String PATH = "/out-forward";
		// 只需url进入游戏
		public static final String FORWARD_MODE_DIRECT = "/forward-mode-direct";
		// 需要用户名和密码进入游戏
		public static final String FORWARD_MODE_AUTHORITY = "/forward-mode-authority";
		// 错误页面
		public static final String FORWARD_GAME_ERROR = "/forward-game-error";
	}

	// 外部三方模块
	public class OutThird {
		// 推广模块根路径
		public static final String PATH = "/out-third";

		// 查询佣金记录
		public static final String SEARCH_PLAYER_GAME_RECORD = "/search-player-game-record";
		// 三方盈亏
		public static final String GET_PLAYER_GAME_REPORT = "/get-player-game-report";

		// 新版三方盈亏
		public static final String GET_PLAYER_GAME_REPORT_SUMMARY_NEW = "/get-player-game-report-summary-new";
		public static final String GET_PLAYER_GAME_REPORT_DETAIL_NEW = "/get-player-game-report-detail-new";

		// 佣金规则文案
		public static final String COMMISSION_RULE_TEXT = "/commission-rule-text";

		// 返水规则文案
		public static final String REBATE_RULE_TEXT = "/rebate-rule-text";

		public static final String TEAM_COMMISSION_RULE = "/team-commission-rule";

		public static final String TEAM_REBATE_RULE = "/team-rebate-rule";
	}

	// 代理模块
	public class Team {
		// 代理模块根路径
		public static final String PATH = "/team";

		// 团队彩票盈亏（层级版）
		public static final String TEAM_LOTTERY_PROFIT_LIST_LEVEL = "/team-lottery-profit-list-level";

		// 团队三方盈亏（层级版）
		public static final String TEAM_THIRD_PROFIT_LIST_LEVEL = "/team-third-profit-list-level";

		//查询场馆明细
		public static final String TEAM_THIRD_PROFIT_VENUE_DETAIL = "/team-third-profit-venue-detail";


		//查询总计的场馆明细
		public static final String TEAM_THIRD_PROFIT_SUM_VENUE_DETAIL = "/team-third-profit-sum-venue-detail";
	}

}
