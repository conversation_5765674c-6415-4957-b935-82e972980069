package ph.yckj.frontend.web.ctrl.third;

import lombok.extern.slf4j.Slf4j;
import myutil.HttpUtils;
import myutil.JacksonUtils;
import myutil.Moment;
import myutil.ObjectUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import ph.yckj.common.util.ServiceException;
import ph.yckj.common.util.WebJson;
import ph.yckj.frontend.vo.PageVo;
import ph.yckj.frontend.vo.third.*;
import ph.yckj.frontend.web.helper.Route;
import ph.yckj.frontend.web.helper.SuperController;
import sy.hoodle.base.common.dao.impl.GameSimulationTransferLimitDaoImpl;
import sy.hoodle.base.common.entity.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 真人游戏请求处理类
 */
@Slf4j
@RestController
@RequestMapping(Route.PATH + Route.Third.PATH)
public class GameSimulationController extends SuperController {

	/**
	 * 三方游戏场馆类型列表
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(Route.Third.LIST_THIRD_TYPE)
	public WebJson LIST_THIRD_TYPE(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
//			getSessionUser(request);

			List<GameSimulationPlatformGroup> groupList = getGameSimulationPlatformGroupDao().listAll();

			List<ThirdTypeVo> voList = new ArrayList<>();
			if(groupList != null) {
				for(GameSimulationPlatformGroup group : groupList) {
					voList.add(new ThirdTypeVo(group.getId(), group.getName()));
				}
			}

			webJson.setData(voList);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
			return webJson;
		}catch(Exception e) {
			setFail(webJson);
			log.error("三方游戏场馆类型列表: ", e);
		}
		return webJson;
	}

	/**
	 * 三方游戏场馆列表
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(Route.Third.LIST_THIRD_PLATFORM)
	public WebJson LIST_THIRD_PLATFORM(HttpServletRequest request,
			@RequestParam(name = "groupId", required = false) Integer groupId) {
		WebJson webJson = new WebJson();
		try {
//			AgentPlayerInfo player = getSessionUser(request);

			AgentInfo agent = getAgent(request);
            if (agent == null) {
				setFail(webJson);
				return webJson;
            }

			List<GameSimulationPlatPlatformType> list =
					getGameSimulationPlatPlatformTypeDao().listByGroup(agent.getAgentNo(), groupId);

			List<ThirdPlatformVo> voList = new ArrayList<>();
			if(list != null) {
				for(GameSimulationPlatPlatformType platform : list) {
					voList.add(new ThirdPlatformVo(platform.getGroupId(), platform.getCode(), platform.getName(),
							platform.getIsOnlyChildGameEnter() ? 1 : 0, platform.getRecommend(),platform.getStatus()));
				}
			}

			webJson.setData(voList);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
			return webJson;
		}catch(Exception e) {
			setFail(webJson);
			log.error("三方游戏场馆列表: ", e);
		}
		return webJson;
	}

	/**
	 * 三方游戏列表
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(Route.Third.LIST_THIRD_GAME)
	public WebJson LIST_THIRD_GAME(HttpServletRequest request,
			@RequestParam(name = "groupId", required = false) Integer groupId,
			@RequestParam(name = "platformCode") String platformCode,
			@RequestParam(name = "gameName", required = false) String gameName,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			AgentInfo agent = getAgent(request);
            if (agent == null) {
				setFail(webJson);
				return webJson;
            }
//			AgentPlayerInfo player = getSessionUser(request);

			List<Criterion> criterions = new ArrayList<>();
			criterions.add(Restrictions.eq("agentNo", agent.getAgentNo()));
			// 状态正常
			criterions.add(Restrictions.eq("status", 0));
			criterions.add(Restrictions.eq("parentCode", platformCode));
			if(groupId != null) {
				criterions.add(Restrictions.eq("groupId", groupId));
			}

			if(!StringUtils.isEmpty(gameName)) {
				criterions.add(Restrictions.like("name", "%" + gameName + "%"));
			}

			List<Order> orders = new ArrayList<>();
			orders.add(Order.asc("sort"));

			int firstResult = page * size;

			List<GameSimulationPlatPlatformType> list;
			int totalCount = getGameSimulationPlatPlatformTypeDao().totalCount(criterions);
			if(totalCount > 0) {
				list = getGameSimulationPlatPlatformTypeDao().find(criterions, orders, firstResult, size);
			}else {
				list = new ArrayList<>();
			}

			List<ThirdGameVo> voList = new ArrayList<>();
			if(list != null) {
				for(GameSimulationPlatPlatformType game : list) {
					voList.add(new ThirdGameVo(game.getGroupId(), game.getParentCode(), game.getId(), game.getCode(),
							game.getName()));
				}
			}

			PageVo<ThirdGameVo> pageVo = new PageVo<>();
			pageVo.setTotalCount(totalCount);
			pageVo.setList(voList);

			webJson.setData(pageVo);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
			return webJson;
		}catch(Exception e) {
			setFail(webJson);
			log.error("三方游戏列表: ", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.LIST_ALL_GAMES)
	public WebJson listAllGames(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getSessionUser(request);

			// 所有游戏
			List<GameSimulationPlatPlatformType> allGameSimulations =
					getGameSimulationPlatPlatformTypeDao().listByAgentNoEnable(player.getAgentNo());
			List<GameSimulationPlatformGroup> allGameSimulationGroups = getGameSimulationPlatformGroupDao().listAll();
			List<GameSimulationVO> allGameSimulationsVOs = new ArrayList<>();

			Set<Integer> groupIds = new LinkedHashSet<>();
			for(GameSimulationPlatPlatformType tmpBean : allGameSimulations) {
				if(GameSimulationPlatPlatformType.STATUS_NORMAL == tmpBean.getStatus()) {
					groupIds.add(tmpBean.getGroupId());
					GameSimulationVO gameSimulationVO =
							new GameSimulationVO(tmpBean, getGameSimulationPlatPlatformTypeDao());
					allGameSimulationsVOs.add(gameSimulationVO);
				}
			}

			List<GameSimulationPlatformGroup> allGameSimulationScreenGroups = new ArrayList<>();
			allGameSimulationGroups.forEach(t -> {
				if(groupIds.contains(t.getId())) {
					allGameSimulationScreenGroups.add(t);
				}
			});

			Map<String, Object> data = new HashMap<>();
			data.put("gameList", allGameSimulationsVOs);
			data.put("groupList", allGameSimulationScreenGroups);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.REGISTER_GAME)
	public WebJson registerGame(HttpServletRequest request, @RequestParam(name = "platformCode") String platformCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationService().registerSimulationGame(player, platform);
			if(gameSimulationAccount == null) {
				throw newException("124-01");
			}
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.ENTER_GAME)
	public WebJson enterGame(HttpServletRequest request, @RequestParam(name = "platformCode") String platformCode,
			@RequestParam(name = "gameCode", required = false) String gameCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if (null == platform) {
				log.error("GameSimulationPlatPlatformType is null platformCode: {}", platformCode);
				throw newException("124-06");
			}
			if (platform.getStatus() == GameSimulationPlatPlatformType.STATUS_FORBIDDEN) {
				log.error("GameSimulationPlatPlatformType is disable platformCode: {}", platformCode);
				throw newException("124-11");
			} else if (platform.getStatus() == GameSimulationPlatPlatformType.STATUS_CLOSE) {
				log.error("GameSimulationPlatPlatformType is close platformCode: {}", platformCode);
				throw newException("124-12");
			}
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
			if(gameSimulationAccount == null) {
				// 如果还没有第三方账号,进行开通
				gameSimulationAccount = getGameSimulationService().registerSimulationGame(player, platform);
				if(gameSimulationAccount == null) {
					throw newException("124-01");
				}
			}else if(gameSimulationAccount.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				throw newException("124-10");
			}
			ThirdGameUrlVo urlVo =
					getGameSimulationService().getForwardGameParams(gameSimulationAccount, player, platform.getCode(),
							gameCode);

			try {
				// 开启免转时，将余额全部转到三方游戏
				if(player.getOutThirdAutoTransfer() == AgentPlayerInfo.AUTO_TRANSFER_ON) {
					transferAllBalance2Game(player, platform);
				}
			}catch(Exception e) {
				log.error("服务器异常", e);
			}

			webJson.setData(urlVo);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.LEAVE_GAME)
	public WebJson LEAVE_GAME(HttpServletRequest request, @RequestParam(name = "platformCode") String platformCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.warn("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}

			if(player.getOutThirdAutoTransfer() == AgentPlayerInfo.AUTO_TRANSFER_ON) {
				// 更新余额
				GameSimulationAccount simulationAccount =
						getGameSimulationService().updateAccountBalance(player, platform.getCode());
				// 如果余额大于1，转出到平台
				if(simulationAccount != null && simulationAccount.getBalance().doubleValue() >= 1) {
					getGameSimulationService().balanceGame2Lottery(player, platform, simulationAccount.getBalance(),
							false);
				}
			}

			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.ENTER_GAME_APP)
	public WebJson ENTER_GAME_APP(HttpServletRequest request, @RequestParam(name = "platformCode") String platformCode,
			@RequestParam(name = "gameCode", required = false) String gameCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
			if(gameSimulationAccount == null) {
				// 如果还没有第三方账号,进行开通
				gameSimulationAccount = getGameSimulationService().registerSimulationGame(player, platform);
				if(gameSimulationAccount == null) {
					throw newException("124-01");
				}
			}else if(gameSimulationAccount.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				throw newException("124-10");
			}
			Map<String, Object> gameParams =
					getGameSimulationService().getForwardGameParamsAPP(gameSimulationAccount, player,
							platform.getCode(), gameCode);

			Map<String, Object> data = new HashMap<>();
			data.put("gameParams", gameParams);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.ENTER_GAME_RELATIVELY)
	public WebJson ENTER_GAME_RELATIVELY(HttpServletRequest request,
			@RequestParam(name = "platformCode") String platformCode,
			@RequestParam(name = "gameCode", required = false) String gameCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
			if(gameSimulationAccount == null) {
				// 如果还没有第三方账号,进行开通
				gameSimulationAccount = getGameSimulationService().registerSimulationGame(player, platform);
				if(gameSimulationAccount == null) {
					throw newException("124-01");
				}
				GameSimulationPlatPlatformType gameSimulationPlatformType =
						getGameSimulationPlatPlatformTypeDao().getByCode(gameSimulationAccount.getAgentNo(),
								gameSimulationAccount.getPlatformCode());
				String config = getSystemConfigDao().getValueByGroupAndKey("GAME_SIMULATION", "CREATE_LOGIN_DELAY");
				if(!StringUtils.isEmpty(config)) {
					String[] configs = config.split(",");
					for(String code : configs) {
						if(gameSimulationPlatformType.getCode().equals(code)) {
							Thread.sleep(3500);
							break;
						}
					}
				}
			}
			ThirdGameUrlVo urlVo =
					getGameSimulationService().getForwardGameParams(gameSimulationAccount, player, platform.getCode(),
							gameCode);

			try {
				// 开启免转时，将余额全部转到三方游戏
				if(player.getOutThirdAutoTransfer() == AgentPlayerInfo.AUTO_TRANSFER_ON) {
					transferAllBalance2Game(player, platform);
				}
			}catch(Exception e) {
				log.error("服务器异常", e);
			}

			webJson.setData(urlVo);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	private String transferAllBalance2Game(AgentPlayerInfo player, GameSimulationPlatPlatformType platform) {
		try {
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}

			String config = getSystemConfigDao().getValueByGroupAndKey("GAME_SIMULATION", "TRANSFER_LIMIT_CONTROL");
			boolean simulationLimitControl = "1".equals(config);
			if(simulationLimitControl &&
					!getGameSimulationTransferLimitDao().addTransfer(player.getAgentNo(), player.getPlayerName(),
							GameSimulationTransferLimitDaoImpl.TRANSFER_TYPE_DEPOSIT)) {
				throw newException("124-08");
			}

			if(player.getOutThirdAutoTransfer() != AgentPlayerInfo.AUTO_TRANSFER_ON) {
				// 不需要免转
				return "SUCCESS";
			}
			BigDecimal amount = player.getPlayerAvailableBalance();
			if(amount.doubleValue() <= 0) {
				return "SUCCESS";// 没有钱就不需要再转
			}
			// 批量余额更新
			int result = getGameSimulationService().balanceLottery2GameV2(player, platform, amount, true);
			// 0处理中，1处理完成，-1处理失败
			if(-1 != result) {
				return "SUCCESS";
			}else {
				return "自动上账失败，请联系客服务";
			}
		}catch(ServiceException e) {
			log.info("FORWARD_MODE_DIRECT ServiceException:", e);
			return e.getMessage();
		}catch(Throwable t) {
			log.info("FORWARD_MODE_DIRECT Throwable:", t);
		}
		return "UNKNOWN ERROR";
	}

	@RequestMapping(Route.Third.QUERY_BALANCE)
	public WebJson queryBalance(HttpServletRequest request, @RequestParam(name = "platformCode") String platformCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);

			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}

			GameSimulationAccount gameSimulationAccount =
					getGameSimulationService().updateAccountBalance(player, platform.getCode());
			Map<String, Object> data = new HashMap<>();
			data.put("autoTransfer", player.getOutThirdAutoTransfer());
			data.put("balance", gameSimulationAccount.getBalance());
			String lastUpdate =
					new Moment().fromDate(gameSimulationAccount.getUpdateTime()).format("yyyy-MM-dd HH:mm:ss");
			data.put("lastUpdate", lastUpdate);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.TRANSFER_LOTTERY_TO_GAME)
	public WebJson TRANSFER_LOTTERY_TO_GAME(HttpServletRequest request,
			@RequestParam(name = "platformCode") String platformCode, @RequestParam(name = "amount") double amount) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			// getSessionUserPasswordPass(request);
			// checkAccountHints(player);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			log.info("TRANSFER_LOTTERY_TO_GAME转账amount:{}", amount);

			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}

			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
			if(gameSimulationAccount == null) {
				// 如果还没有第三方账号,进行开通
				gameSimulationAccount = getGameSimulationService().registerSimulationGame(player, platform);
				if(gameSimulationAccount == null) {
					throw newException("124-01");
				}
			}else if(gameSimulationAccount.getStatus() != GameSimulationAccount.STATUS_ACTIVE) {
				throw newException("124-10");
			}
			// 批量余额更新
			int result = getGameSimulationService().balanceLottery2GameV2(player, platform, new BigDecimal(amount), true);
			Map<String, Object> data = new HashMap<>();
			data.put("result", result);// 0处理中，1处理完成，-1处理失败
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.TRANSFER_GAME_TO_LOTTERY)
	public WebJson TRANSFER_GAME_TO_LOTTERY(HttpServletRequest request,
			@RequestParam(name = "platformCode") String platformCode, @RequestParam(name = "amount") double amount) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			// getSessionUserPasswordPass(request);
			// checkAccountHints(player);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}

			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}

			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
			if(gameSimulationAccount == null) {
				throw newException("124-02");
			}else if(gameSimulationAccount.getStatus() != GameSimulationAccount.STATUS_ACTIVE) {
				throw newException("124-10");
			}

			log.info("TRANSFER_GAME_TO_LOTTERY转账amount:{}platformCode:{}", amount, platformCode);
			// if (!getGameSimulationTransferLimitDao().addTransfer(player.getUsername(),
			/// GameSimulationTransferLimitDaoImpl.TRANSFER_TYPE_WITHDRAW)) {
			// throw newException("124-08");
			// }
			int result =
					getGameSimulationService().balanceGame2Lottery(player, platform, new BigDecimal(amount), false);
			Map<String, Object> data = new HashMap<>();
			data.put("result", result);// 0处理中，1处理完成，-1处理失败
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_TRANSFER)
	public WebJson searchTransfer(HttpServletRequest request,
			@RequestParam(name = "billno", required = false) String billno,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "platformCode", required = false) String platformCode,
			@RequestParam(name = "direction", required = false) String direction,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
			Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

			int firstResult = page * size;
            AgentPlayerInfo player = getSessionUser(request);

			List<Criterion> criterions = new ArrayList<>();
			criterions.add(Restrictions.eq("agentNo", player.getAgentNo()));
			criterions.add(Restrictions.eq("accountId", player.getPlayerId()));
			criterions.add(Restrictions.ge("startTime", sDate));
			criterions.add(Restrictions.lt("startTime", eDate));
			if(!StringUtils.isEmpty(billno)) {
				criterions.add(Restrictions.ilike("billno", billno, MatchMode.ANYWHERE));
			}
			if(!StringUtils.isEmpty(platformCode)) {
				criterions.add(Restrictions.eq("platformCode", platformCode));
			}
			if(!StringUtils.isEmpty(direction)) {
				criterions.add(Restrictions.eq("direction", direction));
			}
			if(status != null) {
				criterions.add(Restrictions.eq("status", status));
			}

			List<Order> orders = new ArrayList<>();
			orders.add(Order.desc("id"));
			List<GameSimulationTransfer> resultList =
					getGameSimulationTransferDao().find(criterions, orders, firstResult, size);
			int totalCount = getGameSimulationTransferDao().totalCount(criterions);
			List<GameSimulationTransferVO> list = new ArrayList<>();
			for(GameSimulationTransfer tmpBean : resultList) {
				list.add(new GameSimulationTransferVO(tmpBean));
			}

			PageVo<GameSimulationTransferVO> pageVo = new PageVo<>();
			pageVo.setTotalCount(totalCount);
			pageVo.setList(list);

			webJson.setData(pageVo);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.TEAM_SEARCH_TRANSFER)
	public WebJson TEAM_SEARCH_TRANSFER(HttpServletRequest request,
			@RequestParam(name = "billno", required = false) String billno,
			@RequestParam(name = "username", required = false) String username,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "platformCode", required = false) String platformCode,
			@RequestParam(name = "direction", required = false) String direction,
			@RequestParam(name = "status", required = false) Integer status,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			Date sDate = HttpUtils.prepareQueryDate(sTime, 0).toDate();
			Date eDate = HttpUtils.prepareQueryDate(eTime, 1).toDate();

			int firstResult = page * size;
            AgentPlayerInfo player = getSessionUser(request);

			long targetId = player.getPlayerId();
			Set<Long> directSet = new HashSet<>();
			if(!StringUtils.isEmpty(username)) {
				AgentPlayerInfo targetAccount = getAgentPlayerInfoDao().getByPlayerName(player.getAgentNo(), username);
				if(targetAccount == null) {
					throw newException("115-05");
				}
				// 判断是否是团队成员
				boolean testIsMyTeam = getServiceUtils().testIsMyTeam(player, targetAccount);
				if(!testIsMyTeam) {
					throw newException("115-03");
				}
				targetId = targetAccount.getPlayerId();
				directSet.add(targetId);
			}

			List<AgentPlayerInfo> pathList = getAgentPlayerInfoDao().listTeamPath(player.getAgentNo(), targetId);
			for(AgentPlayerInfo tmpPlayer : pathList) {
				directSet.add(tmpPlayer.getPlayerId());
			}

			List<Criterion> criterions = new ArrayList<>();
			criterions.add(Restrictions.eq("agentNo", player.getAgentNo()));
			criterions.add(Restrictions.in("accountId", directSet));
			criterions.add(Restrictions.ge("startTime", sDate));
			criterions.add(Restrictions.lt("startTime", eDate));
			if(!StringUtils.isEmpty(billno)) {
				criterions.add(Restrictions.ilike("billno", billno, MatchMode.ANYWHERE));
			}
			if(!StringUtils.isEmpty(platformCode)) {
				criterions.add(Restrictions.eq("platformCode", platformCode));
			}
			if(!StringUtils.isEmpty(direction)) {
				criterions.add(Restrictions.eq("direction", direction));
			}
			if(status != null) {
				criterions.add(Restrictions.eq("status", status));
			}

			List<Order> orders = new ArrayList<>();
			orders.add(Order.desc("id"));
			List<GameSimulationTransfer> resultList =
					getGameSimulationTransferDao().find(criterions, orders, firstResult, size);
			int totalCount = getGameSimulationTransferDao().totalCount(criterions);
			List<GameSimulationTransferVO> list = new ArrayList<>();
			for(GameSimulationTransfer tmpBean : resultList) {
				list.add(new GameSimulationTransferVO(tmpBean));
			}

			PageVo<GameSimulationTransferVO> pageVo = new PageVo<>();
			pageVo.setTotalCount(totalCount);
			pageVo.setList(list);

			webJson.setData(pageVo);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_PLAYRECORD_DETAIL)
	public WebJson SEARCH_PLAYRECORD_DETAIL(HttpServletRequest request, @RequestParam(name = "gameId") int gameId,
			@RequestParam(name = "gameType", required = false) String gameType,
			@RequestParam(name = "sBetTime", required = false) String sBetTime,
			@RequestParam(name = "eBetTime", required = false) String eBetTime,
			@RequestParam(name = "sBonusTime", required = false) String sBonusTime,
			@RequestParam(name = "eBonusTime", required = false) String eBonusTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size, maxResults = size;
			AgentPlayerInfo player = getSessionUser(request);
			long accountId = player.getPlayerId();
			Criteria criteria = new Criteria();
			criteria.and("accountId").is(accountId);
			criteria.and("gameId").is(gameId);
			if(!StringUtils.isEmpty(gameType)) {
				criteria.and("gameType").regex(".*?\\" + gameType + ".*");
			}
			if(!StringUtils.isEmpty(sBetTime)) {
				if(!StringUtils.isEmpty(eBetTime)) {
					Date sThisDate = HttpUtils.prepareQueryDate(sBetTime, 0).toDate();
					Date eThisDate = HttpUtils.prepareQueryDate(eBetTime, 1).toDate();
					criteria.and("betTime").gte(sThisDate).lt(eThisDate);
				}else {
					Date thisDate = HttpUtils.prepareQueryDate(sBetTime, 0).toDate();
					criteria.and("betTime").gte(thisDate);
				}
			}else if(!StringUtils.isEmpty(eBetTime)) {
				Date thisDate = HttpUtils.prepareQueryDate(eBetTime, 1).toDate();
				criteria.and("betTime").lt(thisDate);
			}

			if(!StringUtils.isEmpty(sBonusTime)) {
				if(!StringUtils.isEmpty(eBonusTime)) {
					Date sThisDate = HttpUtils.prepareQueryDate(sBonusTime, 0).toDate();
					Date eThisDate = HttpUtils.prepareQueryDate(eBonusTime, 1).toDate();
					criteria.and("bonusTime").gte(sThisDate).lt(eThisDate);
				}else {
					Date sThisDate = HttpUtils.prepareQueryDate(sBonusTime, 0).toDate();
					criteria.and("bonusTime").gte(sThisDate);
				}
			}else if(!StringUtils.isEmpty(eBonusTime)) {
				Date eThisDate = HttpUtils.prepareQueryDate(eBonusTime, 1).toDate();
				criteria.and("bonusTime").lt(eThisDate);
			}

			Sort sort = Sort.by(Direction.DESC, "id");
			List<GameSimulationPlayrecord> resultList =
					getGameSimulationPlayrecordDao().find(criteria, sort, firstResult, maxResults);
			int totalCount = getGameSimulationPlayrecordDao().totalCount(criteria);
			List<String> fieldNameList = new ArrayList<>();
			List<GameSimulationPlayrecordVO> list = new ArrayList<>();
			for(GameSimulationPlayrecord tmpBean : resultList) {
				Map<String, String> recordContent = new LinkedHashMap<>();
				recordContent.put("记录ID", tmpBean.getRecordId());
				String recordContentStr = tmpBean.getRecordContent();
				recordContent.putAll(JacksonUtils.toMap(recordContentStr, String.class, String.class));
				list.add(new GameSimulationPlayrecordVO(recordContent));
				if(fieldNameList.size() == 0) {
					fieldNameList.addAll(recordContent.keySet());
				}
			}

			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("fieldNames", fieldNameList);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_PLAYRECORD)
	public WebJson SEARCH_PLAYRECORD(HttpServletRequest request,
			@RequestParam(name = "gameId", required = false) Integer gameId,
			@RequestParam(name = "sTime", required = false) String sTime,
			@RequestParam(name = "eTime", required = false) String eTime,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size, maxResults = size;
			AgentPlayerInfo player = getSessionUser(request);
			long accountId = player.getPlayerId();
			Criteria criteria = new Criteria();
			criteria.and("accountId").is(accountId);

			Date sThisDateTime = HttpUtils.prepareQueryDate(sTime, 0).toDate();
			criteria.and("startTime").gte(sThisDateTime);
			Date eThisDateTime = HttpUtils.prepareQueryDate(eTime, 1).toDate();
			criteria.and("endTime").lte(eThisDateTime);
			if(null != gameId) {
				criteria.and("gameId").is(gameId);
			}
			Sort sort = Sort.by(Direction.DESC, "startTime");
			List<GameSimulationStatistic> resultList =
					getGameSimulationStatisticDao().find(criteria, sort, firstResult, maxResults);
			int totalCount = getGameSimulationStatisticDao().totalCount(criteria);

			List<GameSimulationReportDetailVO> list = new ArrayList<>();
			for(GameSimulationStatistic statistic : resultList) {
				GameSimulationPlatPlatformType platform =
						getGameSimulationPlatPlatformTypeDao().getByCode(statistic.getAgentNo(),
								statistic.getPlatformCode());
				GameSimulationReportDetailVO gameSimulationReportDetailVO =
						new GameSimulationReportDetailVO(platform.getName(), platform.getCode(),
								statistic.getBetAmount(), statistic.getValidBetAmount(), statistic.getBonusAmount(),
								statistic.getStartTime(), statistic.getEndTime());
				list.add(gameSimulationReportDetailVO);
			}
			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_PLAYRECORD_REPORT)
	public WebJson SEARCH_PLAYRECORD_REPORT(HttpServletRequest request,
			@RequestParam(name = "username", required = false) String username,
			@RequestParam(name = "platformCode", required = false) String platformCode,
			@RequestParam(name = "sDate", required = false) String sDate,
			@RequestParam(name = "eDate", required = false) String eDate,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			int firstResult = page * size, maxResults = size;
			AgentPlayerInfo player = getSessionUser(request);
			long accountId = player.getPlayerId();

			long targetId = player.getPlayerId();
			Set<Long> directSet = new HashSet<>();
			directSet.add(accountId);
			if(!StringUtils.isEmpty(username)) {
				directSet.remove(accountId);
				AgentPlayerInfo targetAccount = getAgentPlayerInfoDao().getByPlayerName(player.getAgentNo(), username);
				if(targetAccount == null) {
					throw newException("115-05");
				}
				// 判断是否是团队成员
				boolean testIsMyTeam = getServiceUtils().testIsMyTeam(player, targetAccount);
				if(!testIsMyTeam) {
					throw newException("115-03");
				}
				targetId = targetAccount.getPlayerId();
				directSet.add(targetId);
			}

			List<AgentPlayerInfo> pathList = getAgentPlayerInfoDao().listTeamPath(player.getAgentNo(), targetId);
			for(AgentPlayerInfo tmpPlayer : pathList) {
				directSet.add(tmpPlayer.getPlayerId());
			}

			String sDateMomentStr = HttpUtils.prepareQueryDate(sDate, 0).format("yyyy-MM-dd");
			Moment sDateMoment = new Moment().fromTimeExact(sDateMomentStr + " 00:00:00.000");
			Date sThisDateTime = sDateMoment.toDate();
			String eDateMomentStr = HttpUtils.prepareQueryDate(eDate, 1).format("yyyy-MM-dd");
			Moment eDateMoment = new Moment().fromTimeExact(eDateMomentStr + " 00:00:00.000");
			Date eThisDateTime = eDateMoment.add(1, "days").toDate();
			Map<String, Object> parameters = new HashMap<>();

			String queryString =
					"select platform_code,platform_name,sum(deposit_amount), sum(withdraw_amount), sum(bet_amount)," +
							" sum(valid_bet_amount),sum(bonus_amount) , report_date, account_id from game_simulation_report" +
							" WHERE ";
			parameters.put("sThisDateTime", sThisDateTime);
			parameters.put("eThisDateTime", eThisDateTime);
			parameters.put("directSet", directSet);
			String countString = "select count(1) from ( select platform_code from game_simulation_report where ";
			String conditionString =
					" report_date >= :sThisDateTime and report_date < :eThisDateTime and account_id in (:directSet) ";
			if(!StringUtils.isEmpty(platformCode)) {
				conditionString += " and platform_code = :platformCode ";
				parameters.put("platformCode", platformCode);
			}
			String groupString = " GROUP BY account_id, platform_code,platform_name ";
			String orderString = "  order by account_id, report_date desc ";
			String querySqlByCount = countString + conditionString + groupString + " ) tb";
			int tempTotalCount = getGameSimulationReportDao().countByParamWithSql(querySqlByCount, parameters);
			List<GameSimulationReportVO> list = new ArrayList<>();
			if(tempTotalCount > 0) {
				String querySqlByList = queryString + conditionString + groupString + orderString;
				List<?> resultList =
						getGameSimulationReportDao().findBySql(querySqlByList, parameters, firstResult, maxResults);
				GameSimulationReportVO totalVO = new GameSimulationReportVO();
				totalVO.setField("总计");
				Object[] totalAmounts =
						getGameSimulationReportDao().countByParam(directSet, platformCode, sThisDateTime,
								eThisDateTime);
				BigDecimal depositAmountTotal = BigDecimal.valueOf(ObjectUtils.toDouble(totalAmounts[0].toString()));
				BigDecimal withdrawAmountTotal = BigDecimal.valueOf(ObjectUtils.toDouble(totalAmounts[1].toString()));
				BigDecimal betAmountTotal = BigDecimal.valueOf(ObjectUtils.toDouble(totalAmounts[2].toString()));
				BigDecimal validBetAmountTotal = BigDecimal.valueOf(ObjectUtils.toDouble(totalAmounts[3].toString()));
				BigDecimal bonusAmountTotal = BigDecimal.valueOf(ObjectUtils.toDouble(totalAmounts[4].toString()));
				totalVO.setDepositAmount(depositAmountTotal);
				totalVO.setWithdrawAmount(withdrawAmountTotal);
				totalVO.setBetAmount(betAmountTotal);
				totalVO.setValidBetAmount(validBetAmountTotal);
				totalVO.setBonusAmount(bonusAmountTotal);
				for(Object object : resultList) {
					Object[] columns = (Object[]) object;
					String platformName = ObjectUtils.toString(columns[0]); // platformCode
					String gameName = ObjectUtils.toString(columns[1]); // platformName
					BigDecimal depositAmount = BigDecimal.valueOf(ObjectUtils.toDouble(columns[2]));
					BigDecimal withdrawAmount = BigDecimal.valueOf(ObjectUtils.toDouble(columns[3]));
					BigDecimal betAmount = BigDecimal.valueOf(ObjectUtils.toDouble(columns[4]));
					BigDecimal validBetAmount = BigDecimal.valueOf(ObjectUtils.toDouble(columns[5]));
					BigDecimal bonusAmount = BigDecimal.valueOf(ObjectUtils.toDouble(columns[6]));
					String reportDateTmp = ObjectUtils.toString(columns[7]);
					String accontId = ObjectUtils.toString(columns[8]);
					AgentPlayerInfo agentPlayerInfo = getAgentPlayerInfoDao().getById(Long.parseLong(accontId));
					String accontName = agentPlayerInfo.getPlayerName();
					Date reportDate = new Moment().fromDate(reportDateTmp).toDate();
					GameSimulationReportVO gameSimulationReportVO =
							new GameSimulationReportVO(gameName, platformName, depositAmount, withdrawAmount, betAmount,
									validBetAmount, bonusAmount, reportDate);
					gameSimulationReportVO.setUsername(accontName);
					list.add(gameSimulationReportVO);
				}
				list.add(0, totalVO);
			}
			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", tempTotalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_FLASH_TECH_REPORT)
	public WebJson SEARCH_FLASH_TECH_REPORT(HttpServletRequest request,
			@RequestParam(name = "sDate", required = false) String sDate,
			@RequestParam(name = "eDate", required = false) String eDate,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			Criteria criteria = new Criteria();
			criteria.and("playerId").is(player.getPlayerId());

			if(!StringUtils.isEmpty(sDate) && !StringUtils.isEmpty(eDate)) {
				Date startDate = new Moment().fromDate(sDate).toDate();
				Date endDate = new Moment().fromDate(eDate).toDate();
				criteria.and("stateUpdateTs").gte(startDate).lt(endDate);
			}else if(!StringUtils.isEmpty(sDate)) {
				Date startDate = new Moment().fromDate(sDate).toDate();
				criteria.and("stateUpdateTs").gte(startDate);
			}else if(!StringUtils.isEmpty(eDate)) {
				Date endDate = new Moment().fromDate(eDate).toDate();
				criteria.and("stateUpdateTs").lt(endDate);
			}
			int firstResult = page * size, maxResults = size;
			Sort sort = Sort.by(Direction.DESC, "stateUpdateTs");

			List<GameSportsRecordDetail> resultList =
					getGameSportsRecordDetailDao().find(criteria, sort, firstResult, maxResults);
			int totalCount = getGameSportsRecordDetailDao().totalCount(criteria);
			List<GameSportsRecordDetailVO> list = new ArrayList<>(totalCount);

			for(GameSportsRecordDetail tmpBean : resultList) {
				String entityUsername = player.getPlayerName();
				String entityPlatformName = "unknown";
				String entityAgentName = "";
				GameSimulationAccount gameSimulationAccount =
						getGameSimulationAccountDao().getByPlatformUser(tmpBean.getAgentNo(), tmpBean.getPlatformCode(),
								tmpBean.getPlayerName());
				if(null != gameSimulationAccount) {
					GameSimulationPlatPlatformType gameSimulationPlatformType =
							getGameSimulationPlatPlatformTypeDao().getByCode(gameSimulationAccount.getAgentNo(),
									gameSimulationAccount.getPlatformCode());
					if(null != gameSimulationPlatformType) {
						entityPlatformName = gameSimulationPlatformType.getName();
					}
				}

				GameSportsRecordDetailVO gamePlayerReportVO =
						new GameSportsRecordDetailVO(tmpBean, entityUsername, entityPlatformName, entityAgentName);
				list.add(gamePlayerReportVO);
			}

			Map<String, Object> data = new HashMap<>();
			data.put("totalCount", totalCount);
			data.put("list", list);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_GAME_PLATFORM_DETAIL_ORDER)
	public WebJson SEARCH_GAME_PLATFORM_DETAIL_ORDER(HttpServletRequest request,
			@RequestParam(name = "platformCode", required = false) String platformCode,
			@RequestParam(name = "sDate", required = false) String sDate,
			@RequestParam(name = "eDate", required = false) String eDate,
			@RequestParam(name = "page", defaultValue = "0") int page,
			@RequestParam(name = "size", defaultValue = "10") int size) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			Date sTime = HttpUtils.prepareQueryDate(sDate, 0).toDate();
			Date eTime = HttpUtils.prepareQueryDate(eDate, 1).toDate();
			if(size > 100) {
				size = 100;
			}
			Map<String, Object> data;
			if(!StringUtils.isEmpty(platformCode)) {
				data = getGameSimulationService().searchGamePlatformDetailOrder(player, platformCode, sTime, eTime,
						page, size);
			}else {
				// 全游戏
				data = getGameSimulationService().searchGamePlatformDetailOrder(player, sTime, eTime, page, size);
			}

			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_GAME_ICON)
	public WebJson SEARCH_GAME_ICON(HttpServletRequest request,
			@RequestParam(name = "platformCode") String platformCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}
			List<GameIconVO> iconList = getGameSimulationService().getGameIconParams(player, platformCode);
			webJson.setData(iconList);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	/**
	 * 取游戏服务的地址，直接取前台地址（或者直接返回相对地址）
	 */
	private static String getMainUrl(HttpServletRequest request) {
		String referer = request.getHeader("referer");
		String tempReferer = referer;
		int firstSlashIndex = referer.indexOf("/", "https://x".length());
		if(firstSlashIndex > -1) {
			tempReferer = referer.substring(0, firstSlashIndex);
		}
		return tempReferer;
	}

	@RequestMapping(Route.Third.RECYCLE_BALANCE_ALL)
	public WebJson RECYCLE_BALANCE_ALL(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}
			// getSessionUserPasswordPass(request);
			// checkAccountHints(player);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}

			/*
			 * if (!getGameSimulationTransferLimitDao().addTransfer(player.getUsername(),
			 * GameSimulationTransferLimitDaoImpl.TRANSFER_TYPE_WITHDRAW)) { throw
			 * newException("124-08"); }
			 */

			List<GameSimulationAccount> gameSimulationAccounts =
					getGameSimulationAccountDao().getAllAccountsByAccountId(player.getPlayerId());
			int success = 0;
			int fail = 0;
			int processing = 0;
			int notExecue = 0;
			for(GameSimulationAccount gameSimulationAccount : gameSimulationAccounts) {
				if(gameSimulationAccount.getBalance().doubleValue() > 0) {
					try {
						log.info("TRANSFER_ALL_GAME_TO_LOTTERY,GameSimulationAccount amount:{}, platformCode:{}",
								gameSimulationAccount.getBalance(), gameSimulationAccount.getPlatformCode());

						GameSimulationPlatPlatformType platform =
								getGameSimulationPlatPlatformTypeDao().getByCode(gameSimulationAccount.getAgentNo(),
										gameSimulationAccount.getPlatformCode());
						if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
							continue;
						}
						int result = getGameSimulationService().balanceGame2Lottery(player, platform,
								gameSimulationAccount.getBalance(), true);
						switch(result) {
							case GameSimulationTransfer.STATUS_FAILED:
								fail++;
								break;
							case GameSimulationTransfer.STATUS_PROCESSING:
								processing++;
								break;
							case GameSimulationTransfer.STATUS_COMPLETED:
								success++;
								break;
							default:
								notExecue++;
								break;
						}
					}catch(Exception e) {
						log.error("服务器异常", e);
						fail++;
					}
				}
			}

			Map<String, Object> data = new HashMap<>();
			data.put("success", success); // 回收成功个数
			data.put("fail", fail); // 回收失败个数
			data.put("processing", processing); // 处理中
			data.put("notExecue", notExecue);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.SEARCH_PLATFORM_SUB_GAME_LIST)
	public WebJson SEARCH_PLATFORM_SUB_GAME_LIST(HttpServletRequest request,
			@RequestParam(name = "platformCode") String platformCode,
			@RequestParam(name = "gameName", required = false, defaultValue = "") String gameName,
			@RequestParam(name = "page", defaultValue = "1") int page,
			@RequestParam(name = "size", defaultValue = "20") int size) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getSessionUser(request);
			// game-platform-api page从1
			Map<String, Object> result =
					getGameSimulationService().getSubGameList(player, platformCode, page, size, gameName);
			webJson.setData(result);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.ENTER_SUB_GAME)
	public WebJson ENTER_SUB_GAME(HttpServletRequest request, @RequestParam(name = "platformCode") String platformCode,
			@RequestParam(name = "gameCode") String gameCode) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			if(!getGameSimulationService().allowAccountBaccarat(player.getPlayerId())) {
				throw newException("124-06");
			}

			GameSimulationPlatPlatformType platform =
					getGameSimulationPlatPlatformTypeDao().getByCode(player.getAgentNo(), platformCode);
			if(null == platform || platform.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				log.error("GameSimulationPlatPlatformType is null or inactive with the platformCode: {}", platformCode);
				throw newException("124-06");
			}

			if(!platform.getIsOnlyChildGameEnter()) {
				throw newException("124-09");
			}
			GameSimulationAccount gameSimulationAccount =
					getGameSimulationAccountDao().getByAccountId(player.getPlayerId(), platform.getCode());
			if(gameSimulationAccount == null) {
				// 如果还没有第三方账号,进行开通
				gameSimulationAccount = getGameSimulationService().registerSimulationGame(player, platform);
				if(gameSimulationAccount == null) {
					throw newException("124-01");
				}
			}else if(gameSimulationAccount.getStatus() != GameSimulationPlatPlatformType.STATUS_NORMAL) {
				throw newException("124-10");
			}

			String gameURIParams =
					getGameSimulationService().getForwardSubGameUrl(gameSimulationAccount, player, platform.getCode(),
							gameCode);

			String gameUrl = Route.PATH + Route.OutForward.PATH + gameURIParams;

			Map<String, Object> forwardGameParams = new LinkedHashMap<>();
			forwardGameParams.put("game_url", gameUrl);

			Map<String, Object> data = new HashMap<>();
			data.put("forwardGameParams", forwardGameParams);
			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.QUERY_BALANCE_ALL)
	public WebJson queryBalanceAll(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);

			List<GameSimulationQueryBalanceVO> balanceVoList = getGameSimulationService().queryBalanceAll(player);

			// 三方总余额
			BigDecimal totalBalance = BigDecimal.ZERO;
			if(balanceVoList != null) {
				for(GameSimulationQueryBalanceVO balanceVo : balanceVoList) {
					totalBalance = totalBalance.add(balanceVo.getBalance());
				}
			}

			Map<String, Object> data = new LinkedHashMap<>();
			data.put("autoTransfer", player.getOutThirdAutoTransfer());
			data.put("totalBalance", totalBalance);
			data.put("list", balanceVoList);

			webJson.setData(data);
			setSuccess(webJson);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.TRANSFER_ALL_GAME_TO_LOTTERY)
	public WebJson TRANSFER_ALL_GAME_TO_LOTTERY(HttpServletRequest request) {
		WebJson webJson = new WebJson();
		try {
			// 基础用户信息
			AgentPlayerInfo player = getSessionUser(request);
			int[] result = getGamePlatformService().recycleBalanceAll(player);
			int success = result[0];
			int fail = result[1];
			int processing = result[2];
			int not_execue = result[3];

			Map<String, Object> data = new HashMap<>();
			data.put("success", success); // 成功回收个数
			data.put("fail", fail); // 回传失败次数
			data.put("processing", processing); // 处理中
			data.put("not_execue", not_execue);
			webJson.setData(data);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}

	@RequestMapping(Route.Third.RECYCLE_BALANCE_BY_GAME)
	public WebJson RECYCLE_BALANCE_BY_GAME(HttpServletRequest request,
			@RequestParam(name = "platformCode") String platformCode) {
		WebJson webJson = new WebJson();
		try {
			AgentPlayerInfo player = getSessionUser(request);
			if(getLockUtils().isLockUser(player)) {
				// 卡单
				throw newException("9999");
			}

            log.info("开始回收处理[{}]游戏账号余额...", platformCode);
			List<GameSimulationAccount> gameSimulationAccounts =
					getGameSimulationAccountDao().getAllAccountsByPlatformCode(player.getAgentNo(), platformCode);
			int success = 0;
			int fail = 0;
			int processing = 0;
			int notExecue = 0;
			for(GameSimulationAccount gameSimulationAccount : gameSimulationAccounts) {
				if(gameSimulationAccount.getBalance().doubleValue() > 0) {
					try {
						long accountId = gameSimulationAccount.getAccountId();
						AgentPlayerInfo account = getAgentPlayerInfoDao().getById(accountId);
                        log.info(
                                "TRANSFER_ALL_GAME_TO_LOTTERY,GameSimulationAccount amount:{}, platformCode:{}, accountId:{}",
                                gameSimulationAccount.getBalance(), platformCode, accountId);
						int result = getGameSimulationService().balanceGameToLottery(account,
								gameSimulationAccount.getPlatformCode(), gameSimulationAccount.getBalance(), true);
						switch(result) {
							case GameSimulationTransfer.STATUS_FAILED:
								fail++;
								break;
							case GameSimulationTransfer.STATUS_PROCESSING:
								processing++;
								break;
							case GameSimulationTransfer.STATUS_COMPLETED:
								success++;
								break;
							default:
								notExecue++;
								break;
						}
					}catch(Exception e) {
						log.error("服务器异常", e);
						fail++;
					}
				}
			}

			Map<String, Object> data = new HashMap<>();
			data.put("success", success); // 成功回收个数
			data.put("fail", fail); // 回传失败次数
			data.put("processing", processing); // 处理中
			data.put("notExecue", notExecue);
			webJson.setData(data);
		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}


	@RequestMapping(Route.Third.MANUAL_SYNC_GAME_RECORD)
	public WebJson MANUAL_SYNC_GAME_RECORD(HttpServletRequest reques,@RequestParam(name = "date") String date,@RequestParam(name = "key") String key) {

		WebJson webJson = new WebJson();
		if (!"A3b9D2eF6gH8jK5m".equals(key) || StringUtils.isEmpty(date)){
			setFail(webJson);
			return webJson;
		}
		try {
			getSimulationGameSuperService().manualSyncRecord(date);

		}catch(ServiceException e) {
			setError(webJson, e);
		}catch(Exception e) {
			setFail(webJson);
			log.error("服务器异常", e);
		}
		return webJson;
	}
}
