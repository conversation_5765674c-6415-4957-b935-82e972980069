<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">
	<!-- ========= 1. 属性 ========= -->
	<Properties>
		<!-- 额外备份目录 -->
		<Property name="logPathExtra">/home/<USER>/hoodle-manager-job</Property>
	</Properties>

	<!-- ========= 2. Appenders ========= -->
	<Appenders>

		<!-- Console 输出 -->
		<Console name="Console" target="SYSTEM_OUT">
			<Filters>
				<ThresholdFilter level="TRACE" onMatch="ACCEPT" onMismatch="DENY"/>
			</Filters>
			<PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
		</Console>

		<!-- ========== 额外目录：INFO / WARN / ERROR ========== -->
		<!-- INFO 日志 -->
		<RollingRandomAccessFile name="InfoRollingFileExtra"
								 fileName="${logPathExtra}/a-info.log"
								 filePattern="${logPathExtra}/info-%d{yyyy-MM-dd}-%i.log">
			<PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n"/>
			<ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
			<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true"/>
				<SizeBasedTriggeringPolicy size="10MB"/>
			</Policies>
		</RollingRandomAccessFile>

		<!-- WARN 日志 -->
		<RollingRandomAccessFile name="WarnRollingFileExtra"
								 fileName="${logPathExtra}/a-warn.log"
								 filePattern="${logPathExtra}/warn-%d{yyyy-MM-dd}-%i.log">
			<PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n"/>
			<ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
			<ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true"/>
				<SizeBasedTriggeringPolicy size="100MB"/>
			</Policies>
		</RollingRandomAccessFile>

		<!-- ERROR 日志 -->
		<RollingRandomAccessFile name="ErrorRollingFileExtra"
								 fileName="${logPathExtra}/a-error.log"
								 filePattern="${logPathExtra}/error-%d{yyyy-MM-dd-HH}-%i.log">
			<PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n"/>
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			<Policies>
				<TimeBasedTriggeringPolicy interval="6" modulate="true"/>
				<SizeBasedTriggeringPolicy size="100MB"/>
			</Policies>
		</RollingRandomAccessFile>


		<!-- ========= 额外目录：GZ 压缩日志副本 ========= -->
		<RollingRandomAccessFile name="GZRollingFileExtra"
								 fileName="${logPathExtra}/z.log"
								 filePattern="${logPathExtra}/zip-%d{yyyy-MM-dd}.log.zip">
			<PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n"/>
			<Policies>
				<!-- 每天 00:10 触发日志压缩 -->
				<CronTriggeringPolicy schedule="0 12 2 * * ?"/>
			</Policies>
			<CustomRolloverStrategy/>
		</RollingRandomAccessFile>

	</Appenders>

	<!-- ========= 3. Loggers ========= -->
	<Loggers>

		<!-- Root logger：默认日志，控制台 + 文件 -->
		<Root level="INFO">
			<AppenderRef ref="Console"/>
			<AppenderRef ref="GZRollingFileExtra"/>
			<AppenderRef ref="InfoRollingFileExtra">
				<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			</AppenderRef>
			<AppenderRef ref="WarnRollingFileExtra">
				<ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
			</AppenderRef>
			<AppenderRef ref="ErrorRollingFileExtra">
				<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			</AppenderRef>
		</Root>

		<!-- 精确配置 ph.yckj 包日志：分类写文件 + 控制台，关闭冒泡 -->
		<Logger name="ph.yckj" level="INFO" additivity="false">
			<AppenderRef ref="Console"/>
			<AppenderRef ref="GZRollingFileExtra"/>
			<AppenderRef ref="InfoRollingFileExtra">
				<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			</AppenderRef>
			<AppenderRef ref="WarnRollingFileExtra">
				<ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
			</AppenderRef>
			<AppenderRef ref="ErrorRollingFileExtra">
				<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			</AppenderRef>
		</Logger>


	</Loggers>
</Configuration>
