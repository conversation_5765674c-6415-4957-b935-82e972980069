package ph.yckj.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.core.appender.rolling.AbstractRolloverStrategy;
import org.apache.logging.log4j.core.appender.rolling.RollingFileManager;
import org.apache.logging.log4j.core.appender.rolling.RolloverDescription;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 自定义日志压缩策略：
 * - 每天凌晨 1 点由 CronTrigger 触发
 * - 将“昨日”产生的 *.log 文件压缩为 zip-YYYY-MM-DD.log.gz
 * - 压缩成功且文件大小 > 0 时删除源日志
 * ⬆️ 同一个策略类可被不同目录的 RollingRandomAccessFile 复用
 */
@Slf4j
@Plugin(name = "CustomRolloverStrategy",
        category = "Core",
        elementType = "rolloverStrategy",
        printObject = true)
public class CustomRolloverStrategy extends AbstractRolloverStrategy {

    protected CustomRolloverStrategy() {
        super(null);
    }

    /* ===== 创建插件实例 ===== */
    @PluginFactory
    public static CustomRolloverStrategy createStrategy() {
        return new CustomRolloverStrategy();
    }

    /* ===== 核心入口 ===== */
    @Override
    public RolloverDescription rollover(RollingFileManager manager) {
        // 1️⃣ 解析当前 RollingFile 所在目录
        String baseDir = Paths.get(manager.getFileName()).getParent().toString();
        log.info("CustomRolloverStrategy start, baseDir={}", baseDir);

        String yesterday = LocalDateTime.now()
                .minusDays(1)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String archivePath = baseDir + File.separator + "zip-" + yesterday + ".log.gz";

        try {
            // 2️⃣ 找到昨日产生的日志文件
            List<Path> logFiles = findLogFiles(baseDir, yesterday);
            if (logFiles.isEmpty()) {
                log.info("No log file to compress: baseDir={}, date={}", baseDir, yesterday);
                return null;
            }

            // 3️⃣ 压缩
            compressLogsToZip(Paths.get(archivePath), logFiles);

            // 4️⃣ 压缩成功后删除源文件
            if (Files.exists(Paths.get(archivePath)) && Files.size(Paths.get(archivePath)) > 0) {
                deleteLogs(logFiles);
                log.info("Compress success & delete source logs: {}", logFiles);
            } else {
                log.warn("Archive {} is empty, keep source logs: {}", archivePath, logFiles);
            }
        } catch (Exception e) {
            log.error("CustomRolloverStrategy error, baseDir={}", baseDir, e);
        }

        // 返回 null 表示不需要额外的 log4j2 rollover 行为
        return null;
    }

    /* ======= 工具方法 ======= */

    /** 查找 baseDir 下指定日期的 *.log（不包含 .gz）文件 */
    private List<Path> findLogFiles(String baseDir, String date) throws IOException {
        try (Stream<Path> stream = Files.walk(Paths.get(baseDir))) {
            return stream.filter(Files::isRegularFile)
                    .filter(p -> p.getFileName().toString().endsWith(".log"))
                    .filter(p -> p.toString().contains(date))
                    .filter(p -> !p.toString().endsWith(".gz"))
                    .collect(Collectors.toList());
        }
    }

    /** 把所有日志压缩到单个 zip 文件 */
    private void compressLogsToZip(Path target, List<Path> logFiles) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(
                new BufferedOutputStream(Files.newOutputStream(target)))) {

            byte[] buffer = new byte[4096];
            for (Path logFile : logFiles) {
                ZipEntry entry = new ZipEntry(logFile.getFileName().toString());
                zos.putNextEntry(entry);

                try (InputStream in = new BufferedInputStream(Files.newInputStream(logFile))) {
                    int len;
                    while ((len = in.read(buffer)) != -1) {
                        zos.write(buffer, 0, len);
                    }
                }

                zos.closeEntry();
            }
        }
        log.info("Archive created: {}", target);
    }

    /** 删除已压缩的源日志 */
    private void deleteLogs(List<Path> logFiles) {
        for (Path path : logFiles) {
            try {
                Files.deleteIfExists(path);
            } catch (IOException e) {
                log.warn("Failed to delete {}", path, e);
            }
        }
    }
}
