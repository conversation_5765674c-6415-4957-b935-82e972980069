<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">
    <!-- ========= 1. 属性 ========= -->
    <Properties>
        <!-- 本地 logs -->
        <Property name="logPath">${web:rootDir}/logs</Property>
        <!-- 额外备份目录 -->
        <Property name="logPathExtra">/home/<USER>/hoodle-codesource-server</Property>
    </Properties>

    <!-- ========= 2. Appenders ========= -->
    <Appenders>

        <!-- Console 输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <Filters>
                <ThresholdFilter level="TRACE" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
        </Console>

        <!-- ========== 本地目录：INFO / WARN / ERROR ========== -->
        <RollingRandomAccessFile name="InfoRollingFile"
                                 filePattern="${logPath}/info-%d{yyyy-MM-dd-HH}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
            <Filters>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="6" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="WarnRollingFile"
                                 filePattern="${logPath}/warn-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ErrorRollingFile"
                                 filePattern="${logPath}/error-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <!-- ========== 额外目录：INFO / WARN / ERROR ========== -->
        <RollingRandomAccessFile name="InfoRollingFileExtra"
                                 filePattern="${logPathExtra}/info-%d{yyyy-MM-dd-HH}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
            <Filters>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="6" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="WarnRollingFileExtra"
                                 filePattern="${logPathExtra}/warn-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ErrorRollingFileExtra"
                                 filePattern="${logPathExtra}/error-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %c %L %M - %msg%xEx%n"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <!-- ========= 本地目录：GZ 压缩日志 ========= -->
<!--        <RollingRandomAccessFile name="GZRollingFile"-->
<!--                                 filename="${logPath}/gz.log"-->
<!--                                 filePattern="${logPath}/log-%d{yyyy-MM-dd}.log.gz">-->
<!--            <Policies>-->
<!--                &lt;!&ndash; 每天凌晨 1 点 &ndash;&gt;-->
<!--                &lt;!&ndash;				<CronTriggeringPolicy schedule="0 0 1 * * ?"/>&ndash;&gt;-->
<!--                <CronTriggeringPolicy schedule="0 5 0 * * ?"/>-->

<!--            </Policies>-->
<!--            <CustomRolloverStrategy/>-->
<!--        </RollingRandomAccessFile>-->

        <!-- ========= 额外目录：GZ 压缩日志（副本） ========= -->
        <RollingRandomAccessFile name="GZRollingFileExtra"
                                 filename="${logPathExtra}/gz.log"
                                 filePattern="${logPathExtra}/log-%d{yyyy-MM-dd}.log.gz">
            <Policies>
                <!--				<CronTriggeringPolicy schedule="0 0 1 * * ?"/>-->
                <CronTriggeringPolicy schedule="0 10 0 * * ?"/>

            </Policies>
            <CustomRolloverStrategy/>
        </RollingRandomAccessFile>

    </Appenders>

    <!-- ========= 3. Loggers ========= -->
    <Loggers>

        <Root level="INFO">
            <AppenderRef ref="Console"/>

            <!-- INFO/WARN/ERROR 本地 -->
            <AppenderRef ref="InfoRollingFile"/>
            <AppenderRef ref="WarnRollingFile"/>
            <AppenderRef ref="ErrorRollingFile"/>

            <!-- INFO/WARN/ERROR 额外 -->
            <AppenderRef ref="InfoRollingFileExtra"/>
            <AppenderRef ref="WarnRollingFileExtra"/>
            <AppenderRef ref="ErrorRollingFileExtra"/>

            <!-- GZ 压缩双写 -->
            <AppenderRef ref="GZRollingFile"/>
            <AppenderRef ref="GZRollingFileExtra"/>
        </Root>

        <!-- 若 ph.yckj 包也要写 GZ，可把 GZRollingFile/GZRollingFileExtra 再加到下面 Loggers 中 -->
        <Logger name="ph.yckj.*" level="INFO" additivity="false">
            <AppenderRef ref="InfoRollingFile"/>
            <AppenderRef ref="InfoRollingFileExtra"/>
        </Logger>

        <Logger name="ph.yckj.*" level="WARN" additivity="false">
            <AppenderRef ref="WarnRollingFile"/>
            <AppenderRef ref="WarnRollingFileExtra"/>
        </Logger>

        <Logger name="ph.yckj.*" level="ERROR" additivity="false">
            <AppenderRef ref="ErrorRollingFile"/>
            <AppenderRef ref="ErrorRollingFileExtra"/>
        </Logger>
    </Loggers>
</Configuration>