<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd 
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.2.xsd">

	<!-- 配置连接池 -->
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<!-- 最大连接数 -->
		<property name="maxTotal" value="100" />
		<!-- 最大空闲连接数 -->
		<property name="maxIdle" value="20" />
		<!-- 最小空闲连接数 -->
		<property name="minIdle" value="5" />
		<!-- 获取连接的最大等待时间（毫秒） -->
		<property name="maxWaitMillis" value="10000" />
		<!-- 在获取连接时是否校验有效性 -->
		<property name="testOnBorrow" value="true" />
	</bean>

	<bean id="jedis1"
		class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory" primary="true">
		<property name="hostName" value="${redis.host}" />
		<property name="password" value="${redis.password}" />
		<property name="port" value="${redis.port}" />
		<property name="timeout" value="${redis.timeout}" />
		<property name="database" value="1" />
	</bean>
	
</beans>