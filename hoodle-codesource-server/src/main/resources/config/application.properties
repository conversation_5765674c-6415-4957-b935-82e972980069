final.key=4a0fc28640fa47868e95fed42faf3d96

server.host=**************

# db
hikari.driverClassName=com.mysql.cj.jdbc.Driver
hikari.url=jdbc:mysql://${server.host:devlocalhost.cc}:3306/hoodle_zonghe?useUnicode=true&characterEncoding=utf-8
hikari.username=Jt2wyGGHHmI=
hikari.password=pWuRvXc54arJwDwTthkQWg==
hikari.connectionTestQuery=SELECT 1
hikari.validationTimeout=3000
hikari.readOnly=false
hikari.connectionTimeout=60000
hikari.idleTimeout=60000
hikari.maxLifetime=60000
hikari.maximumPoolSize=30

# mongo db
mongo.dbname=hoodle_zonghe
mongo.username=xfTCN6TGpxQ=
mongo.password=pWuRvXc54aoCubY86r0G1w==
mongo.host=${server.host:devlocalhost.cc}
mongo.port=27017
mongo.connectionsPerHost=100
mongo.threadsAllowedToBlockForConnectionMultiplier=200
mongo.connectTimeout=1000
mongo.maxWaitTime=1500
mongo.autoConnectRetry=true
mongo.socketKeepAlive=true
mongo.socketTimeout=30000
mongo.slaveOk=true

# redis
redis.host=${server.host:devlocalhost.cc}
redis.password=XXXX_2025
redis.port=6379
redis.timeout=0

# hibernate
hibernate.dialect=org.hibernate.dialect.MySQLDialect
hibernate.show_sql=false
hibernate.cache.use_query_cache=false
hibernate.cache.use_second_level_cacle=true
hibernate.cache.provider_class=org.hibernate.cache.EhCacheProvider
hibernate.temp.use_jdbc_metadata_defaults=false

spring.mvc.hiddenmethod.filter.enabled=true
amazon.s3.access.key=********************
amazon.s3.secret.key=kVKFhHiNlq6pYQ+51hYQ9HJQbdbhA9iZ0P7HWy/K
amazon.s3.region=ap-east-1